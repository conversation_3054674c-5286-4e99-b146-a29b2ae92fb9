# 表格渲染问题调试

## 问题现象
`taskList.value` 有值，但表格没有展示出来。

## 添加的调试功能

### 1. 控制台日志增强
```typescript
console.log('taskList.value.length:', taskList.value.length);
// 检查第一个任务的字段
if (taskList.value.length > 0) {
  console.log('第一个任务的字段:', Object.keys(taskList.value[0]));
  console.log('第一个任务的数据:', taskList.value[0]);
}
```

### 2. 测试数据注入
当API返回空数据时，自动添加测试数据：
```typescript
if (taskList.value.length === 0) {
  taskList.value = [{
    id: 1,
    name: '测试任务',
    regionId: 1,
    env: 1,
    fromZone: 'primary',
    toZone: 'standby',
    status: 10,
    created: '2023-12-25 10:30:00',
    // ...
  }];
}
```

### 3. 页面调试信息
在表格上方显示实时调试信息：
```vue
<div class="debug-info">
  <p>taskList.length: {{ taskList.length }}</p>
  <p>loading: {{ loading }}</p>
  <p v-if="taskList.length > 0">第一个任务: {{ taskList[0]?.name }}</p>
</div>
```

### 4. 格式化函数错误捕获
为所有格式化函数添加错误处理：
```typescript
const formatRegion = (row: AbcAPI.CreateZoneTaskReq) => {
  try {
    const result = regionOptions.find(item => item.value === row.regionId)?.label || row.regionId;
    console.log('formatRegion:', row.regionId, '->', result);
    return result;
  } catch (error) {
    console.error('formatRegion error:', error, row);
    return row.regionId;
  }
};
```

## 排查步骤

### 步骤1：检查页面调试信息
查看页面上的调试信息框：
- `taskList.length` 是否大于0
- `loading` 是否为false
- 是否显示第一个任务的名称

### 步骤2：检查控制台日志
查看以下日志输出：
1. **数据获取**：
   ```
   taskList.value.length: X
   第一个任务的字段: [...]
   第一个任务的数据: {...}
   ```

2. **格式化函数**：
   ```
   formatRegion: 1 -> 上海
   formatEnv: 1 -> 灰度
   formatZone: primary -> 主用区
   formatStatus: 10 -> 进行中
   ```

3. **测试数据**：
   ```
   添加测试数据...
   测试数据已添加: [...]
   ```

### 步骤3：分析可能的问题

#### 问题A：数据字段不匹配
**现象**：控制台显示字段名与表格列的 `prop` 不匹配
**解决**：检查API返回的字段名，调整表格列的 `prop` 属性

#### 问题B：格式化函数报错
**现象**：控制台显示格式化函数错误
**解决**：检查 `regionOptions`、`envOptions`、`zoneOptions` 是否正确导入

#### 问题C：Vue响应式问题
**现象**：数据有值但页面不更新
**解决**：检查 `taskList` 的响应式绑定

#### 问题D：表格组件问题
**现象**：Element Plus表格组件异常
**解决**：检查Element Plus版本和配置

## 常见解决方案

### 1. 字段名不匹配
```vue
<!-- 如果API返回的字段是 taskName 而不是 name -->
<el-table-column prop="taskName" label="任务名称" />
```

### 2. 数据类型问题
```typescript
// 确保数据类型正确
taskList.value = (res.data?.rows || []).map(item => ({
  ...item,
  id: Number(item.id),
  regionId: Number(item.regionId),
  // ...
}));
```

### 3. 强制重新渲染
```vue
<el-table :data="taskList" :key="tableKey">
```

```typescript
const tableKey = ref(0);
// 在数据更新后
tableKey.value++;
```

## 下一步操作

1. **运行页面**，查看调试信息
2. **检查控制台**，分析日志输出
3. **对比字段名**，确保数据结构匹配
4. **测试格式化**，验证函数是否正常
5. **报告结果**，提供具体的日志信息

## 预期结果

修复后应该看到：
- 页面调试信息显示正确的数据长度
- 表格正常显示任务列表
- 格式化函数正常工作
- 无控制台错误
