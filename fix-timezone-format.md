# 修复时间格式问题

## 问题描述

后台Java使用`Instant`类型接收时间数据，需要ISO 8601格式的时间字符串（带时区信息），但前端发送的是本地时间格式。

## 问题分析

1. **前端时间格式**：`YYYY-MM-DD HH:mm:ss` (本地时间，无时区信息)
2. **后端期望格式**：ISO 8601格式，如 `2023-12-25T10:30:00.000Z`
3. **Java Instant**：需要UTC时间或带时区的ISO格式

## 修复方案

### 1. 时间转换逻辑

```typescript
// 将本地时间转换为ISO 8601格式（带时区）
let startedISO = '';
if (detail.started) {
    const date = new Date(detail.started);
    startedISO = date.toISOString();
}
```

### 2. 转换过程说明

1. **用户选择时间**：`2023-12-25 10:30:00` (本地时间)
2. **创建Date对象**：`new Date('2023-12-25 10:30:00')`
3. **转换为ISO格式**：`date.toISOString()` → `2023-12-25T02:30:00.000Z` (UTC时间)

### 3. 时区处理

- `toISOString()` 方法会自动将本地时间转换为UTC时间
- 输出格式：`YYYY-MM-DDTHH:mm:ss.sssZ`
- 符合ISO 8601标准，Java Instant可以直接解析

## 示例转换

| 用户输入 | 前端存储 | 转换后发送给后端 |
|---------|---------|-----------------|
| 2023-12-25 10:30:00 | 2023-12-25 10:30:00 | 2023-12-25T02:30:00.000Z |
| 2023-12-25 14:15:30 | 2023-12-25 14:15:30 | 2023-12-25T06:15:30.000Z |

*注：假设本地时区为UTC+8*

## 前端日期选择器配置

```vue
<el-date-picker
    v-model="row.started"
    type="datetime"
    placeholder="请选择开始时间"
    format="YYYY-MM-DD HH:mm:ss"      // 显示格式
    value-format="YYYY-MM-DD HH:mm:ss" // 存储格式
/>
```

## 后端Java处理

```java
// 后端可以直接解析ISO格式的时间字符串
Instant instant = Instant.parse("2023-12-25T02:30:00.000Z");
```

## 优势

1. **标准化**：使用ISO 8601国际标准时间格式
2. **时区安全**：自动处理时区转换，避免时区混乱
3. **兼容性**：Java Instant原生支持ISO格式解析
4. **精确性**：包含毫秒精度和时区信息

## 测试验证

1. 选择一个本地时间
2. 提交表单
3. 检查网络请求中的时间格式
4. 验证后端能正确解析时间
5. 确认时区转换正确
