import BaseAPI from './base-api';

/**
* Kf Controller
*/
export class KfAPI extends BaseAPI {
    /**
    * 一对一客服通道侧边栏
    * @param {string} conversationId - 会话id    
    */
    static getOneByOneCustomerServiceChannelSidebarUsingGET(
        conversationId:string,
    ) {
        return this.get<AbcAPI.GptAnswerRsp>('/api/kf/ai/1v1/customer/sidebar', {
            params: {
                conversationId,
            },
        });
    }
    
    /**
    * 通过会话ID更新会话总结
    * @param {string} conversationId - 会话ID    
    */
    static summaryConversationByIdUsingGET(
        conversationId:string,
    ) {
        return this.get<AbcAPI.BaseOaSuccessRsp>(`/api/kf/conversation-summary/by-id/${conversationId}`);
    }
    
    /**
    * 获取客户首次进入会话，最后一句问题的答案
    * @param {string} externalUserid - 外部联系人id    
    * @param {string} openKfid - 客服账号id    
    * @param {string} servicerUserid - 接待人员id    
    */
    static getGptAnswerForCustomerFirstEnterUsingGET(
        externalUserid:string,
        openKfid:string,
        servicerUserid:string,
    ) {
        return this.get<AbcAPI.GptAnswerRsp>('/api/kf/gpt/answer/customer-first-enter', {
            params: {
                externalUserid,
                openKfid,
                servicerUserid,
            },
        });
    }
    
    /**
    * 为接待员获取用户问题的答案，只用于大群，无多伦对话能力
    * @param {string} question - 问题    
    */
    static getGptAnswerForServicerUsingGET(
        question:string,
    ) {
        return this.get<AbcAPI.GptAnswerRsp>('/api/kf/gpt/answer/for-servicer', {
            params: {
                question,
            },
        });
    }
    
    /**
    * 保存客户问题
    * @param {AbcAPI.SaveQuestionFromCustomerReq} req - req    
    */
    static saveQuestionFromCustomerUsingPOST(
        req:AbcAPI.SaveQuestionFromCustomerReq,
    ) {
        return this.post<AbcAPI.SaveQuestionFromCustomerRsp>(
            '/api/kf/gpt/question-from-customer',
            req,
        );
    }
    
    /**
    * 查询客户人员排班客户通道列表
    * @param {string} servicerId - 客服人员id    
    */
    static getScheduleUsingGET(
        servicerId:string,
    ) {
        return this.get<AbcAPI.AbcListPageKfServicerScheduleView>(`/api/kf/schedule/${servicerId}`);
    }
}