<template>
    <div class="audio-player">
        <el-icon v-if="!isPlaying" class="control-icon play-icon" @click="play">
            <VideoPlay />
        </el-icon>
        <el-icon v-else class="control-icon pause-icon" @click="pause">
            <VideoPause />
        </el-icon>
        <div class="progress">
            <input
                v-model="currentTime"
                type="range"
                min="0"
                :max="duration"
                class="progress-bar"
                @input="seek"
            />
            <span class="time">{{ formatAudioTime(currentTime) }} / {{ formatAudioTime(duration) }}</span>
        </div>
        <el-tooltip content="下载录音" placement="top">
            <el-icon class="control-icon download-icon" @click="downloadAudio">
                <Download />
            </el-icon>
        </el-tooltip>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, defineProps } from 'vue';
import { ElIcon, ElTooltip, ElMessage } from 'element-plus';
import { VideoPlay, VideoPause, Download } from '@element-plus/icons-vue';
import { globalAudioState } from './state';
import { formatDate } from '@abc-oa/utils';

const props = defineProps<{ 
    src: string;
    callTime?: string; // 拔打时间
    phoneNumber?: string; // 拔打手机号
}>();

const audio = new Audio(props.src);
const currentTime = ref(0);
const duration = ref(0);
const isPlaying = ref(false);

const play = () => {
    if (globalAudioState.currentAudio.value && globalAudioState.currentAudio.value !== audio) {
        globalAudioState.currentAudio.value.pause();
    }
    audio.play();
    isPlaying.value = true;
    globalAudioState.currentAudio.value = audio;
};

const pause = () => {
    audio.pause();
    isPlaying.value = false;
    if (globalAudioState.currentAudio.value === audio) {
        globalAudioState.currentAudio.value = null;
    }
};

const seek = (event: Event) => {
    const target = event.target as HTMLInputElement;
    audio.currentTime = Number(target.value);
};

const updateProgress = () => {
    currentTime.value = audio.currentTime;
};

onMounted(() => {
    audio.addEventListener('timeupdate', updateProgress);
    audio.addEventListener('loadedmetadata', () => {
        duration.value = audio.duration;
    });
});

onUnmounted(() => {
    audio.removeEventListener('timeupdate', updateProgress);
    audio.pause();
    if (globalAudioState.currentAudio.value === audio) {
        globalAudioState.currentAudio.value = null;
    }
});

watch(() => props.src, (newSrc) => {
    audio.src = newSrc;
    audio.load();
    isPlaying.value = false;
});

watch(globalAudioState.currentAudio, (newAudio) => {
    if (newAudio !== audio && isPlaying.value) {
        isPlaying.value = false;
    }
});

// Format time filter
const formatAudioTime = (value: number) => {
    const minutes = Math.floor(value / 60);
    const seconds = Math.floor(value % 60).toString().padStart(2, '0');
    return `${minutes}:${seconds}`;
};

// 下载音频文件
const downloadAudio = async () => {
    if (!props.src) return;
    
    try {
        // 根据拔打时间和手机号格式化文件名
        let fileName = '';
        
        if (props.callTime || props.phoneNumber) {
            // 格式化拔打时间，如果是ISO格式则转换为YYYY-MM-DD HH:mm:ss格式
            let formattedTime = formatDate(props.callTime);
            fileName = `${formattedTime}-${props.phoneNumber}.wav`;
        } else {
            // 如果没有提供拔打时间和手机号，则使用默认名称或从URL提取
            fileName = props.src.split('/').pop() || `录音_${new Date().toISOString().slice(0, 10)}.wav`;
        }
        
        // 使用fetch获取文件内容
        const response = await fetch(props.src);
        if (!response.ok) {
            throw new Error(`网络响应错误: ${response.status}`);
        }
        
        // 获取blob数据
        const blob = await response.blob();
        
        // 创建URL对象
        const url = window.URL.createObjectURL(blob);
        
        // 创建下载链接
        const downloadLink = document.createElement('a');
        downloadLink.href = url;
        downloadLink.download = fileName; // 强制下载而不是打开
        downloadLink.style.display = 'none';
        
        // 添加到DOM并触发点击
        document.body.appendChild(downloadLink);
        downloadLink.click();
        
        // 清理
        setTimeout(() => {
            document.body.removeChild(downloadLink);
            window.URL.revokeObjectURL(url); // 释放URL对象
        }, 100);
    } catch (error) {
        console.error('下载录音失败:', error);
        // 使用Element Plus的消息提示替代alert
        ElMessage.error('下载录音失败，请重试');
    }
};
</script>

<style scoped>
.audio-player {
    display: flex;
    align-items: center;
    background: #fff;
    border-radius: 8px;
    padding: 4px; /* 更紧凑的内边距 */
    width: 100%;
    max-width: 350px;
    border: 1px solid rgba(225, 225, 225, .36);
    gap: 6px; /* 更小的间距 */
    height: 36px; /* 更小的面板高度 */
}

.control-icon {
    font-size: 20px;
    cursor: pointer;
    transition: color .3s, transform .3s ease;
    color: #459eff;
}

.control-icon:hover {
    color: #1e3a8a;
}

.play-icon {
    color: #459eff;
}

.pause-icon {
    color: #4b8f99;
}

.progress {
    display: flex;
    align-items: center;
    gap: 6px; /* 更小的间距 */
    flex-grow: 1;
    justify-content: space-between;
    width: 100%;
}

.time {
    font-size: 10px;
    color: var(--oa-text-color-2); /* 灰色字体 */
    white-space: nowrap;
}

.progress-bar {
    flex-grow: 1;
    -webkit-appearance: none;
    appearance: none; /* 添加标准appearance属性以提高兼容性 */
    height: 4px; /* 缩小进度条高度 */
    background: #e1e1e1;
    border-radius: 4px;
    outline: none;
    transition: background .3s ease;
    width: calc(100% - 32px); /* 适应更紧凑的尺寸 */
}

.progress-bar:hover {
    background: #b2bec3;
}

.progress-bar::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 12px; /* 缩小滑块 */
    height: 12px;
    background: #459eff;
    border-radius: 50%;
    cursor: pointer;
    transition: background .3s ease;
}

.progress-bar::-webkit-slider-thumb:hover {
    background: #007bff;
}

.progress-bar::-moz-range-thumb {
    width: 12px;
    height: 12px;
    background: #459eff;
    border-radius: 50%;
    cursor: pointer;
    transition: background .3s ease;
}

.progress-bar::-moz-range-thumb:hover {
    background: #007bff;
}

.download-icon {
    color: #459eff;
    margin-left: 5px;
    font-size: 18px;
}

.download-icon:hover {
    color: #1e3a8a;
    cursor: pointer;
}
</style>
