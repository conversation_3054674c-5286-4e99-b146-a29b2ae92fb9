import { Extension } from '@tiptap/core';
import { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state';
import { uploadFile } from '@/components/editor/components/upload-files';

export const CustomFileExtension = Extension.create({
    addProseMirrorPlugins() {
        const { editor } = this;
        return [
            new Plugin({
                key: new PluginKey('customFilePastePlugin'),
                props: {
                    handlePaste: (view: any, event: any) => {
                        // 获取paste的file
                        const items = (event.clipboardData || event.originalEvent.clipboardData).items;
                        let blob = null;
                        let pasteFileHandled = true;
                        for (let i = 0; i < items.length; i++) {
                            blob = items[i].getAsFile();
                            const allowedFileTypes = [
                                'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // word
                                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // excel
                                'application/vnd.openxmlformats-officedocument.presentationml.presentation', // ppt
                                'application/pdf', // pdf
                                'application/zip', // zip
                            ];
                            if (allowedFileTypes.includes(blob.type) && blob !== null) {
                                (async (blob) => {
                                    const uploadRes: any = await uploadFile(blob, this.options.moduleName, this.options.rootDir);
                                    if (uploadRes.status === 'done') {
                                        editor.commands.insertContent({
                                            type: 'text',
                                            text: uploadRes.name,
                                            marks: [
                                                {
                                                    type: 'link',
                                                    inclusive: true,
                                                    attrs: {
                                                        href: uploadRes.url,
                                                    },
                                                },
                                            ],
                                        });
                                        editor.commands.insertContent({
                                            type: 'text',
                                            text: '  ',
                                        });
                                    }
                                })(blob);
                                pasteFileHandled = false;
                            }
                        }
                        return !pasteFileHandled;
                    },
                },
            }),
        ];
    },
});
