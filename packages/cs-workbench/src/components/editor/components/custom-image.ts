import { mergeAttributes, Node, nodeInputRule } from '@tiptap/core';
import { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state';
import { handleImageSrc } from '@/components/editor/components/upload-image';

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        image: {
            /**
             * Add an image
             */
            setImage: (options: { src: string, alt?: string }) => ReturnType,
        }
    }
}
export interface ImageOptions {
    inline: boolean,
    allowBase64: boolean,
    HTMLAttributes: Record<string, any>,
    imageWrapperTypeName: string,
    moduleName: string,
    rootDir: string,
}

export const inputRegex = /(?:^|\s)(!\[(.+|:?)]\((\S+)(?:(?:\s+)["'](\S+)["'])?\))$/;

export const CustomImageExtension = Node.create<ImageOptions>({
    name: 'image',

    addOptions() {
        return {
            inline: false,
            allowBase64: false,
            HTMLAttributes: {},
            imageWrapperTypeName: 'imageWrapper',
            moduleName: '',
            rootDir: '',
        };
    },

    inline: true,

    group: 'inline',

    draggable: true,

    addAttributes() {
        return {
            src: {
                default: null,
            },
            alt: {
                default: null,
            },
            width: {
                default: null,
            },
            class: {
                default: null,
            },
        };
    },

    parseHTML(): any {
        return [
            {
                tag: 'img',
                getAttrs: (dom: HTMLElement) => ({
                    width: dom.getAttribute('width'),
                    src: dom.getAttribute('src'),
                    alt: dom.getAttribute('alt'),
                    class: dom.className,
                }),
            },
        ];
    },

    renderHTML({ HTMLAttributes }): any {
        const imageAttributes = mergeAttributes(
            HTMLAttributes,
            {
                src: HTMLAttributes.src,
                alt: HTMLAttributes.alt,
                class: 'abc-editor__custom-image' + (HTMLAttributes.class ? ` ${HTMLAttributes.class}` : ''),
                style: `max-width: 100%;${HTMLAttributes.width ? `width: ${HTMLAttributes.width}` : ''}`,
            },
        );

        return ['img', imageAttributes];
    },

    addCommands() {
        return {
            setImage: options => ({ commands }) => commands.insertContent({
                type: this.name,
                attrs: options,
            }),
        };
    },

    addInputRules() {
        return [
            nodeInputRule({
                find: inputRegex,
                type: this.type,
                getAttributes: match => {
                    const [,, alt, src] = match;

                    return { src, alt };
                },
            }),
        ];
    },

    addProseMirrorPlugins() {
        const { editor } = this;
        return [
            new Plugin({
                key: new PluginKey('customImagePastePlugin'),
                props: {
                    handleClickOn: (view: any, pos: number, node: any, nodePos: number, event: MouseEvent) => {
                        if (node.type.name === 'image') {
                            const { src } = node.attrs;
                            const { tr } = view.state;
                            const parentNode = view.state.doc.resolve(nodePos).parent;
                            if (src.startsWith('data:image')) {
                                handleImageSrc(src, this.options.moduleName, this.options.rootDir).then((newSrc) => {
                                    if (newSrc) {
                                        const { tr } = view.state;
                                        const newNode = editor.view.state.schema.nodes.image.create({
                                            src: newSrc,
                                            alt: newSrc,
                                        });
                                        const parentNode = view.state.doc.resolve(nodePos).parent;
                                        if (parentNode.type.name !== 'imageWrapper') {
                                            const wrapperNode = editor.view.state.schema.nodes.imageWrapper.create(
                                                {},
                                                [newNode],
                                            );
                                            view.dispatch(tr.replaceRangeWith(nodePos, nodePos + node.nodeSize, wrapperNode));
                                            return true;
                                        }
                                        view.dispatch(tr.replaceRangeWith(nodePos, nodePos + node.nodeSize, newNode));
                                    }
                                });
                                return true;
                            }
                            if (parentNode.type.name !== 'imageWrapper') {
                                const wrapperNode = editor.view.state.schema.nodes.imageWrapper.create(
                                    {},
                                    [node],
                                );
                                view.dispatch(tr.replaceRangeWith(nodePos, nodePos + node.nodeSize, wrapperNode));
                                return true;
                            }
                            return false;
                        }
                        return false;
                    },
                    handlePaste: (view: any, event: any) => {
                        // 获取paste的image，如果image的src为data:image开头，则调用handleImageSrc方法
                        const items = (event.clipboardData || event.originalEvent.clipboardData).items;
                        let blob = null;
                        let pasteImageHandled = true;
                        for (let i = 0; i < items.length; i++) {
                            if (items[i].type.indexOf('image') === 0) {
                                blob = items[i].getAsFile();
                                if (blob !== null) {
                                    const reader = new FileReader();
                                    reader.onload = async (event) => {
                                        const src = event.target?.result as string;
                                        const newSrc = src.startsWith('data:image')
                                            ? await handleImageSrc(src, this.options.moduleName, this.options.rootDir) : src;
                                        if (newSrc) {
                                            const newNode = editor.view.state.schema.nodes.image.create({
                                                src: newSrc,
                                                alt: newSrc,
                                            });
                                            const { tr } = editor.view.state;
                                            if (tr.selection.$from.node().type.name === 'imageWrapper') {
                                                editor.view.dispatch(tr.replaceSelectionWith(newNode));
                                            } else {
                                                const wrapperNode = editor.view.state.schema.nodes.imageWrapper.create(
                                                    {},
                                                    [newNode],
                                                );
                                                editor.view.dispatch(tr.replaceSelectionWith(wrapperNode));
                                            }
                                        }
                                    };
                                    reader.readAsDataURL(blob);
                                }
                                pasteImageHandled = false;
                            }
                        }
                        return !pasteImageHandled; // 只有在未处理粘贴的图片时返回 true，阻止默认规则
                    },
                },
            }),
        ];
    },
});
