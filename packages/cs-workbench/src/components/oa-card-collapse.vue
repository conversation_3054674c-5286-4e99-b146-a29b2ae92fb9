<script setup lang="ts">
import { ref, watch } from 'vue';

const props = defineProps({
    title: {
        type: String,
    },
    autoCollapse: {
        type: Boolean,
        default: false,
    },
});
const activeNames = ref<string[]>([]);

watch(
    () => props.autoCollapse,
    (val) => {
        if (val) {
            activeNames.value = ['1'];
        }
    },
    { immediate: true },
);
</script>
<template>
    <div class="oa-card-wrapper oa-card-collapse-wrapper">
        <el-collapse v-model="activeNames">
            <el-collapse-item name="1">
                <template #title>
                    <div v-if="title" class="oa-card__title">{{ title }}</div>
                    <slot v-else name="title">{{ title }}</slot>
                </template>
                <slot></slot>
            </el-collapse-item>
        </el-collapse>
    </div>
</template>

<style lang="scss">
.oa-card-collapse-wrapper {
    .el-collapse-item__header {
        border: none;

        .oa-card__title {
            align-items: center;
        }
    }

    .el-collapse-item__content {
        padding-bottom: 0;
    }

    .el-collapse {
        border: none;
    }
}
</style>
