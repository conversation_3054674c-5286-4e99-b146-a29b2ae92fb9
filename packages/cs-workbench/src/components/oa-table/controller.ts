import { computed, ComputedRef, Ref, ref } from 'vue';

export type Pagination = {
    page: number;
    pageSize: number;
    total: number;
};
export type PageConfig = {
    offset: number;
    limit: number;
};

interface UseTable {
    columns: Ref<any[]>;
    tableData: Ref<any[]>;
    loading: Ref<boolean>;
    pagination: Ref<Pagination>;
    fetchData: () => Promise<void>;
    updateColumns: (columns: any[]) => void;
    destroy: () => void;
}

let instance: UseTable | null = null;
export function useTable(getData: (pageConfig: PageConfig) => Promise<any>) {
    if (instance) {
        return instance;
    }

    const columns = ref<any[]>([]);
    const tableData = ref<any[]>([]);
    const loading = ref(false);
    const pagination = ref<Pagination>({
        page: 1,
        pageSize: 10,
        total: 0,
    });

    const pageConfig: ComputedRef<PageConfig> = computed(() => ({
        offset: (pagination.value.page - 1) * pagination.value.pageSize,
        limit: pagination.value.pageSize,
    }));

    const fetchData = async () => {
        loading.value = true;
        try {
            const { rows, total } = await getData(pageConfig.value);
            tableData.value = rows;
            pagination.value.total = total;
        } catch (error) {
            console.log(error);
        } finally {
            loading.value = false;
        }
    };

    const updateColumns = (column: any[]) => {
        columns.value = column;
    };
    function destroy() {
        instance = null;
    }
    instance = {
        columns,
        tableData,
        loading,
        pagination,
        destroy,
        fetchData,
        updateColumns,
    };
    return instance;
}
