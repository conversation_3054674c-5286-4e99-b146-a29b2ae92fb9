<script setup lang="ts">
const props = defineProps({
    uploadIcon: {
        type: String,
        default: 'plus',
    },
});
</script>
<template>
    <van-uploader class="oa-uploader-wrapper" :upload-icon="uploadIcon">
    </van-uploader>
</template>

<style lang="scss">

    .van-uploader__wrapper {
        --van-uploader-size: 85px;

        .van-uploader__upload,
        .van-uploader__preview-image {
            border-radius: var(--oa-border-radius-default);
        }

        .van-uploader__preview-delete {
            border-radius: 100%;

            >.van-uploader__preview-delete-icon {
                right: 1px;
                top: 1px;
            }
        }
    }

</style>
