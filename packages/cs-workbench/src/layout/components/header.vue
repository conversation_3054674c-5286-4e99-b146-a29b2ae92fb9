<script setup lang="ts">
import { useThemeConfigStore } from '@/store/theme-config';
import { computed } from 'vue';
import { useUserStore } from '@/store/user';
import { useRoute } from 'vue-router';
import { isElectron, openVoiceRecorder } from '@shared/utils';

const route = useRoute();
const breadRoutes = computed(() => route.matched.map(r => ({
    label: r.meta.name,
    route: {
        name: r.name,
    },
})));

const userStore = useUserStore();
const userInfo = userStore.userInfo;
const themeConfig = useThemeConfigStore();
const isCollapse = computed(() => themeConfig.isCollapse);

function handleMenuToggle() {
    themeConfig.setCollapse(!isCollapse.value);
}

async function handleDropdownCommand(command: string) {
    if (command === 'logout') {
        await userStore.logout();
    }
}

function handleVoiceRecorder() {
    openVoiceRecorder();
}

</script>
<template>
    <el-row class="layout-header-wrapper" align="middle">
        <el-icon class="layout-header__menu-toggle" @click="handleMenuToggle">
            <expand v-if="isCollapse" />
            <fold v-else />
        </el-icon>
        <el-breadcrumb>
            <el-breadcrumb-item v-for="breadRoute in breadRoutes" :to="breadRoute.route">
                {{ breadRoute.label }}
            </el-breadcrumb-item>
        </el-breadcrumb>
        <div class="layout-header__right">
            <!-- 麦克风图标，仅在electron环境下显示 -->
            <el-icon 
                v-if="isElectron()" 
                class="layout-header__voice-recorder" 
                title="语音采集"
                @click="handleVoiceRecorder"
            >
                <microphone />
            </el-icon>
            <el-dropdown trigger="click" @command="handleDropdownCommand">
                <div class="layout-header__user-info">
                    <el-avatar size="small" :src="userInfo.thumbAvatar"></el-avatar>
                    <span class="user-name">{{ userInfo.name }}</span>
                    <el-icon class="el-icon--right">
                        <arrow-down />
                    </el-icon>
                </div>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item command="logout">退出登录</el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
        </div>
    </el-row>
</template>

<style scoped>
.layout-header__voice-recorder {
    margin-right: 16px;
    cursor: pointer;
    font-size: 18px;
    color: var(--app-text-color-regular-dark);
    transition: color .3s;
}

.layout-header__voice-recorder:hover {
    color: var(--app-text-color-primary-dark);
    background: rgba(0, 0, 0, .1);
}
</style>
