<script setup lang='ts'>
import LayoutH5 from '@/layout/layout-h5.vue';
import LayoutPc from '@/layout/layout-pc.vue';
import { useThemeConfigStore } from '@/store/theme-config';
import { onMounted, onUnmounted, ref } from 'vue';

const themeConfig = useThemeConfigStore();

function onLayoutResize() {
    const clientWidth = document.body.clientWidth;
    // 适配移动端
    if (clientWidth < 768) {
        // 默认折叠侧边栏
        themeConfig.setCollapse(true);
        themeConfig.setIsMobile(true);
    } else {
        themeConfig.setCollapse(false);
        themeConfig.setIsMobile(false);
    }
}
const isMounted = ref(false);

onMounted(() => {
    onLayoutResize();
    isMounted.value = true;
    window.addEventListener('resize', onLayoutResize);
});

onUnmounted(() => {
    window.removeEventListener('resize', onLayoutResize);
});

</script>

<template>
    <template v-if="isMounted">
        <layout-h5 v-if="themeConfig.isMobile"></layout-h5>
        <layout-pc v-else></layout-pc>
    </template>
</template>

<style scoped></style>
