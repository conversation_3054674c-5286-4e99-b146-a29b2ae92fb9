import { IUser } from '@/common/model/user';
import { dynamicRoutes } from '@/router/dynamic-routes';
import { useUserStore } from '@/store/user';
import { Router, RouteRecord, RouteRecordRaw } from 'vue-router';
import { OaRouteRecord } from '@/common/OaRouteRecord';
import { useMenuStore } from '@/store/menu';

/**
 * @desc 是否存在必要的路由，防止重复添加动态路由
 * <AUTHOR>
 * @date 2021/12/21 13:01:34
 * @params
 * @return
 */
function hasNecessaryRoute(routes: RouteRecord[]) {
    let has = false;
    routes.forEach(route => {
        if (route.path === '/') {
            has = true;
        }
    });
    return has;
}

/**
 * 根据角色，生成路由
 * @param fullRoutes
 * @param roles
 */
function generateRouteByRoles(fullRoutes: Array<OaRouteRecord>, roles: Array<string>, userInfo: IUser): OaRouteRecord[] {
    return fullRoutes.filter(route => {
        if (!route.meta) {
            return true;
        }
        // 优先判断 auth
        if (route.meta.auth) {
            return route.meta.auth(roles, userInfo);
        }
        if (route.meta.roles && route.meta.roles.length) {
            return route.meta.roles.some(role => roles.includes(role));
        }
        return true;
    }).map(route => {
        if (!route.children) {
            return route;
        }
        return {
            ...route,
            children: generateRouteByRoles(route.children, roles, userInfo),
        };
    });
}

export default function (router: Router) {
    return router.beforeEach(async (to, from, next) => {
        const userStore = useUserStore();
        if (to.meta.skipAuth) {
            next();
            return;
        }
        await userStore.fetchUserInfo();
        if (!hasNecessaryRoute(router.getRoutes())) {
            const authedRoutes = generateRouteByRoles(dynamicRoutes, userStore.roles, userStore.userInfo);
            useMenuStore().setRoutes(<RouteRecordRaw[]>authedRoutes, router);
            next({
                path: to.path,
                query: to.query,
                params: to.params,
                replace: true,
            });
        } else {
            next();
        }
    });
}
