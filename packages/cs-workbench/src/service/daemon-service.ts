import logger from '@/common/logger.ts';

/**
 * 后台守护服务，监听外部消息并分发处理，全局单例
 */
export default class DaemonService {
    static TAG = 'DaemonService';

    private static instance: DaemonService;

    private msgChannel: any;

    private externalMessageHandlers: any[] = [];

    private constructor() {
        this.msgChannel = window.electron?.remote?.app?.mainWindow?.webContents || {
            on: () => {},
        };
        this.handleExternalMessage = this.handleExternalMessage.bind(this);
    }

    /**
     * 获取守护服务实例
     */
    static getInstance() {
        if (!this.instance) {
            this.instance = new DaemonService();
        }
        return this.instance;
    }

    /**
     * 启动守护服务
     */
    async start() {
        logger.info(DaemonService.TAG, 'start');
        this.listenExternalMessage();
    }

    /**
     * 停止守护服务
     */
    async stop() {
        logger.info(DaemonService.TAG, 'stop');
        this.msgChannel.off('tab_message_external_message', this.handleExternalMessage);
        this.externalMessageHandlers = [];
    }

    addExternalMessageHandler(handler) {
        this.externalMessageHandlers.push(handler);
    }

    removeExternalMessageHandler(handler) {
        this.externalMessageHandlers = this.externalMessageHandlers.filter((h) => h !== handler);
    }

    /**
     * 监听外部消息
     * @private
     */
    private listenExternalMessage() {
        this.msgChannel.on('tab_message_external_message', this.handleExternalMessage);
    }

    private handleExternalMessage(originMsg) {
        logger.info(DaemonService.TAG, 'tab_message_external_message', originMsg);
        try {
            const msg = JSON.parse(originMsg);
            const {
                body = {},
            } = msg;
            const {
                type,
                payload,
            } = body;
            const dispatchMsg = { type, payload };
            logger.info(DaemonService.TAG, 'handleExternalMessage dispatchMsg', dispatchMsg);
            this.externalMessageHandlers.forEach((handler) => {
                handler(dispatchMsg);
            });
        } catch (e) {
            logger.error(DaemonService.TAG, 'handleExternalMessage', e);
        }
    }
}
