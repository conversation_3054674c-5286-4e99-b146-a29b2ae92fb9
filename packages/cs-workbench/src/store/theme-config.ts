import { defineStore } from 'pinia';

export const useThemeConfigStore = defineStore('themeConfig', {
    state: () => ({
        isCollapse: false, // 侧边栏折叠状态
        isMobile: false, // 是否是移动端
    }),
    getters: {
        // 移动端且侧边栏展开，则显示遮罩
        visibleMobileMask: state => state.isMobile && !state.isCollapse,
    },
    actions: {
        setCollapse(isCollapse: boolean) {
            this.isCollapse = isCollapse;
        },

        setIsMobile(isMobile: boolean) {
            this.isMobile = isMobile;
        },
    },
});
