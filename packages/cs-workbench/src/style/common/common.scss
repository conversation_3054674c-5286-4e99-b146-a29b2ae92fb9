@import "../mixins/mixins";

// flex
.flex-center { @include flexLayout; }
.flex-between { @include flexLayout(space-between); }
.flex-around { @include flexLayout(space-around); }
.flex-start { @include flexLayout(flex-start); }
.flex-column { @include flex-direction(column); }

// margin-bottom
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 4px; }
.mb-2 { margin-bottom: 8px; }
.mb-3 { margin-bottom: 12px; }
.mb-4 { margin-bottom: 16px; }
.mb-14 { margin-bottom: 14px; }

// font-size
.text-12 { font-size: 12px; }
.text-14 { font-size: 14px; }

// color
.text-block { color: rgb(0, 0, 0); }
.text-white { color: rgb(255, 255, 255); }

.text-green-50 { color: rgb(240, 253, 244); }
.text-green-100 { color: rgb(220, 252, 231); }
.text-green-200 { color: rgb(187, 247, 208); }
.text-green-300 { color: rgb(134, 239, 172); }
.text-green-400 { color: rgb(74, 222, 128); }
.text-green-500 { color: rgb(34, 197, 94); }
.text-green-600 { color: rgb(22, 163, 74); }
.text-green-700 { color: rgb(21, 128, 61); }
.text-green-800 { color: rgb(22, 101, 52); }
.text-green-900 { color: rgb(20, 83, 45); }
.text-green-950 { color: rgb(5, 46, 22); }

.text-gray-50 { color: rgb(249, 250, 251); }
.text-gray-100 { color: rgb(243, 244, 246); }
.text-gray-200 { color: rgb(229, 231, 235); }
.text-gray-300 { color: rgb(209, 213, 219); }
.text-gray-400 { color: rgb(156, 163, 175); }
.text-gray-500 { color: rgb(107, 114, 128); }
.text-gray-600 { color: rgb(75, 85, 99); }
.text-gray-700 { color: rgb(55, 65, 81); }
.text-gray-800 { color: rgb(31, 41, 55); }
.text-gray-900 { color: rgb(17, 24, 39); }
.text-gray-950 { color: rgb(3, 7, 18); }

// width
.w-full { width: 100%; }
