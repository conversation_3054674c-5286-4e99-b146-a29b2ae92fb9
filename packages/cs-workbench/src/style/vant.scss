:root {
    --van-blue: var(--oa-blue) !important;
    --van-button-border-radius: var(--oa-border-radius-4) !important;
    --van-tabbar-item-font-size: var(--oa-font-size-14);
    --van-tabs-bottom-bar-color: var(--oa-primary-color);
}

.van-cell__title {
    text-align: initial;
}

.van-skeleton {
    padding: 0 !important;

    --van-skeleton-row-background-color: var(--oa-gray-4);
}

.van-empty {
    --van-empty-image-size: 80px;
}

// 确认弹窗
.van-dialog__confirm {
    --van-dialog-confirm-button-text-color: var(--oa-primary-color);
}

// tabs 底部 active 颜色
.van-tabs__line {
    --van-tabs-bottom-bar-height: 2px;
}

.van-radio--horizontal {
    margin-right: var(--oa-padding-8);
}

.van-checkbox__icon .van-icon {
    --van-checkbox-checked-icon-color: white;

    border-radius: 3px;
    border-color: #ced0da;
}

.van-checkbox__icon--disabled.van-checkbox__icon--checked {
    .van-icon {
        color: var(--oa-primary-color) !important;
    }
}

.van-checkbox__icon--checked .van-icon {
    color: var(--oa-primary-color) !important;
    border-color: #ced0da !important;
}

.van-calendar {
    height: 96%;
}
