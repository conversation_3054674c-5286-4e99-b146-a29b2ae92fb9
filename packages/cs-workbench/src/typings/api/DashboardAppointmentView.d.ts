declare namespace  AbcAPI {
    
    type DashboardAppointmentView = {    
        //实施人基本信息
        implementerUser?:CorpUserBasicView    
        //排班日期
        scheduleDate:string    
        //排班列表
        scheduleList?:Array<SupportScheduleView>    
        //安排列表
        taskList?:Array<SupportTicketFormView>    
        //已预约占用总时长
        totalAppointmentHours:number    
        //排班总工作时长
        totalWorkHours:number    
    }
}
