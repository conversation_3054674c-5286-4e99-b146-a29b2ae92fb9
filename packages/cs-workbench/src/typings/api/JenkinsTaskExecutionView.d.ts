declare namespace  AbcAPI {
    
    type JenkinsTaskExecutionView = {    
        //任务执行id
        buildNumber:string    
        //jenkins输出页面
        consoleOutputPage:string    
        //创建人ID
        createdBy:string    
        //创建人名
        createdByName:string    
        //任务执行结束时间
        endTime:string    
        //任务执行id
        id:number    
        //任务名
        name:string    
        //任务执行结果
        result:string    
        //任务执行开始时间
        startTime:string    
        //任务执行状态，0：未开始，10：已开始，20：已完成，30：取消，40：异常
        status:number    
        //任务类型
        type:string    
    }
}
