declare namespace  AbcAPI {
    
    type KfMsgDetail = {    
        
        id:number    
        //客服帐号ID
        openKfId:string    
        //会话id
        conversationId:string    
        //客户UserID
        externalUserId:string    
        //会话最后的接待人员userid
        servicerUserId:string    
        
        sendTime:any    
        //消息来源。3-微信客户发送的消息 4-系统推送的事件消息 5-接待人员在企业微信客户端发送的消息
        origin:number    
        //消息id
        msgId:string    
        //event , text, image ...
        msgType:string    
        //文本消息 msg_type 为text时存在 {"content":"hello world","menu_id":"101"}
        msgText:Array<any>    
        //文件id msg_type为image、voice、video、file {"media_id":"2iSLeVyqzk4eX0IB5kTi9Ljfa2rt9dwfq5WKRQ4Nvvgw","oss_filename":"kf/202205"}
        msgMedia:Array<any>    
        //地理位置消息 msg_type为location 时存在 {"latitude":23.106021881103501,"longitude":113.320503234863,"name":"广州国际媒体港(广州市海珠区)","address":"广东省广州市海珠区滨江东路"}
        msgLocation:Array<any>    
        //链接消息 msg_type为link 时存在 {"title":"TITLE","desc":"DESC","url":"URL","pic_url":"PIC_URL"}
        msgLink:Array<any>    
        //名片消息 msg_type为business_card 时存在 {"userid":"USERID"}
        msgBusinessCard:Array<any>    
        //菜单消息 msg_type为msgmenu 时存在 {"head_content":"您对本次服务是否满意呢? ","list":[{"type":"click","click":{"id":"101","content":"满意"}},{"type":"click","click":{"id":"102","content":"不满意"}},{"type":"view","view":{"url":"https://work.weixin.qq.com","content":"点击跳转到自助查询页面"}},{"type":"miniprogram","miniprogram":{"appid":"wx123123123123123","pagepath":"pages/index?userid=zhangsan&orderid=123123123","content":"点击打开小程序查询更多"}}],"tail_content":"欢迎再次光临"}
        msgMenu:Array<any>    
        //事件消息 msg_type为event 时存在 {"event_type":"enter_session","open_kfid":"wkAJ2GCAAASSm4_FhToWMFea0xAFfd3Q","external_userid":"wmAJ2GCAAAme1XQRC-NI-q0_ZM9ukoAw","scene":"123","scene_param":"abc","welcome_code":"aaaaaa","wechat_channels":{"nickname":"进入会话的视频号名称"}}
        msgEvent:Array<any>    
        //语音转化文字taskId
        mediaTransTaskId:string    
        //删除状态，1，删除，0，未删除
        isDeleted:number    
        //创建人
        createdBy:string    
        
        created:any    
        //最后修改人
        lastModifiedBy:string    
        
        lastModified:any    
    }
}
