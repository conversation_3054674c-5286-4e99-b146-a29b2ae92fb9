declare namespace  AbcAPI {
    
    type MallGoodsSkuAbstractVO = {    
        
        approvedCode:string    
        
        barCode:string    
        
        cadn:string    
        
        categoryId:string    
        
        categoryName:string    
        
        componentContentNum:number    
        
        componentContentUnit:string    
        
        desc:string    
        
        dosage:number    
        
        dosageForm:string    
        
        dosageFormUnit:string    
        
        dosageUnit:string    
        
        dosageUnitWeight:number    
        
        expireDate:string    
        
        id:string    
        
        images?:Array<Image>    
        
        limitUnit:string    
        
        manufacturer:string    
        
        manufacturerId:string    
        
        medicalFeeGrade:number    
        
        medicineLevel:string    
        
        model:string    
        
        originSalesUnitPrice:number    
        
        packageType:string    
        
        packaging:string    
        
        postDiscountPrice:number    
        
        posterImageUrl:string    
        
        provenance:string    
        
        recommendView?:Array<MallGoodsRecommendView>    
        
        seckillPromotion?:MallGoodsPromotionSkuDto    
        
        shebaoCode:string    
        
        shebaoCodeCurrentPriceLimited:number    
        
        shelfTime:string    
        
        shortId:string    
        
        skuGoodsName:string    
        
        specification:string    
        
        spuGoodsId:string    
        
        spuName:string    
        
        status:number    
        
        stockCount:number    
        
        tradeName:string    
        
        unit:string    
        
        vendorId:string    
        
        vendorName:string    
    }
}
