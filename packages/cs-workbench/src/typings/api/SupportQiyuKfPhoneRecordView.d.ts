declare namespace  AbcAPI {
    
    type SupportQiyuKfPhoneRecordView = {    
        //accountId
        accountId:number    
        //咨询分类
        category:string    
        
        chainId:string    
        
        clinicId:string    
        //诊所名称
        clinicName:string    
        //接通时间
        connectionBegineTime:string    
        //挂断时间
        connectionEndTime:string    
        //通话创建时间
        createTime:string    
        //通话时长
        duration:string    
        //his系统内成员id
        employeeId:string    
        //employee_id不为空显示his系统内成员姓名，否则显示七鱼定义的客户名称
        employeeName:string    
        //通话结束时间
        endTime:string    
        //满意度评价
        evaluation:string    
        //挂机方，客服、用户、--
        firstEndDirection:string    
        //主叫号码
        from:string    
        //id
        id:number    
        //ivr路径
        ivrPath:string    
        //号码归属地
        mobileArea:string    
        //溢出来源
        overflowFrom:string    
        //通话录音文件地址
        recordUrl:string    
        //振铃时长
        ringDuration:string    
        //通话记录id
        sessionId:number    
        //转接至该会话的上一通会话id
        sessionIdFrom:number    
        //分流客服组
        shuntGroupName:string    
        //坐席名称
        staffName:string    
        //会话状态
        status:string    
        //被叫号码
        to:string    
        //语音转文字状态：0未申请 1已申请 2已成功 3已失败
        transVoiceStatus:number    
        //语音转文字任务id
        transVoiceTaskId:string    
        //接待人员id
        userId:number    
        //接待人员名称
        userName:string    
        //重复咨询次数
        visitTimes:number    
        //排队等待时长
        waitDuration:string    
    }
}
