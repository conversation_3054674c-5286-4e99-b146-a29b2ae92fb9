declare namespace  AbcAPI {
    
    type SupportTicketFormView = {    
        //实施任务预约单
        appointmentList?:Array<SupportTicketFormAppointmentView>    
        //clinic_id
        clinicId:string    
        //门店名称: 按需返回
        clinicName:string    
        //创建时间
        created:string    
        //创建人手机号: 按需返回
        creatorMobile:string    
        //创建人姓名: 按需返回
        creatorName:string    
        //客户预约确认时间
        customerConfirmAppointmentTime:string    
        //客户实施完成确认时间
        customerConfirmImplementedTime:string    
        //对接客户手机号
        customerMobile:string    
        //对接客户姓名
        customerName:string    
        //延期日期
        delayDate:string    
        //id
        id:string    
        //实施人id
        implementer:string    
        //实施人名
        implementerName:string    
        //实施人类型：0销售 1技术支持工程师 3ABC支付助理 4:延期占用
        implementerType:number    
        //实施人基本信息
        implementerUser?:CorpUserBasicView    
        //实施单任务明细
        items?:Array<SupportTicketItemView>    
        //备注
        remark:string    
        //sheet_id
        sheetId:string    
        //0未确认 10已确认 20已实施 30已评价 90已取消
        status:number    
        
        totalAppointmentHours:number    
    }
}
