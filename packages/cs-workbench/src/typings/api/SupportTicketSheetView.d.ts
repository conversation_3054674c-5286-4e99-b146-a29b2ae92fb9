declare namespace  AbcAPI {
    
    type SupportTicketSheetView = {    
        //已安排任务总数
        arrangeFormCount:number    
        //门店id
        clinicId:string    
        //客户信息
        customerBaseView?:CustomerBaseView    
        //客户实施完成确认时间
        customerConfirmImplementedTime:string    
        //客户生命周期 100待新装，200已新装 300待激活 400已激活 500待促活 600活跃中
        customerLifeCycle:number    
        //版本开始时间快照
        editionBeginDateSnapshot:string    
        //版本
        editionSnapshot:string    
        //评价内容
        evaluateContent:string    
        //评价 1~5分
        evaluateScore:number    
        //完成实施时间
        finishImplementTime:string    
        //安排列表
        formList?:Array<SupportTicketFormView>    
        //0诊所1口腔2眼科10药店100医院
        hisTypeSnapshot:number    
        //id
        id:string    
        //实施项目列表
        implementFormCount:number    
        //是否购买社保
        isBuyShebao:number    
        //能否发送客户确认 0:不可发送 1:可发送
        isImplementerConfirmFinish:number    
        //已安排项目总数
        itemArrangeCount:number    
        //实施项目列表
        itemList?:Array<SupportTicketItemView>    
        //销售
        sellerId:number    
        //0待处理 10部分实施 20已实施 30已评价
        status:number    
        //对接类型 20企微管家 21体检系统 22微商城 23儿保
        subType:number    
        //实施工单标题
        title:string    
        //实施工单类型0新装，1升级2增购90其他{@linkSupportTicketSheet.Type}
        type:number    
    }
}
