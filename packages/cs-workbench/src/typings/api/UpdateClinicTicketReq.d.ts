declare namespace  AbcAPI {
    
    type UpdateClinicTicketReq = {    
        //连锁id
        chainId?:string    
        //门店id
        clinicId?:string    
        //处理人名字
        dealerName?:string    
        //反馈人id
        employeeId?:string    
        //跟进人id
        followerId?:string    
        //产品
        hisType?:number    
        //工单id
        id?:string    
        //备注
        remark?:string    
        //状态: 0处理中 10已完成 11已拒绝{@linkClinicTicket.Status}
        status?:number    
        //标签
        tagList?:Array<any>    
        //tapd 处理人的id
        tapdDealerId?:string    
        //tapd单链接
        tapdLick?:string    
        //标题
        title?:string    
        //工单类型 0bug 1需求 2实施 4支持(已废弃) 5商务 6专网
        type?:number    
    }
}
