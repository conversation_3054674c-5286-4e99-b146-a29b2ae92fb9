<script lang="ts" setup>
import { ClinicTicketAPI } from '@/api/clinic-ticket-api';
import { ElMessage } from 'element-plus';
import { computed, onMounted, ref } from 'vue';
import { TicketStatusEnum, formatTicketStatus, TAB_LIST, formatTicketType } from '../constant';
import Editor from '@/components/editor/index.vue';

const props = defineProps({
    visible: {
        type: Boolean,
        default: false,
    },
    currentRow: {
        type: Object,
        required: true,
    },
});
const emit = defineEmits(['update:visible']);
const loading = ref<Boolean>(false);
const showViewer = ref(false);
const replyContent = ref<string>('');
const previewList = ref<any>([]);

const dialogVisible = computed({
    get() {
        return props.visible;
    },
    set(val) {
        emit('update:visible', val);
    },
});

const commentDetail = async () => {
    const ticketId = props.currentRow.id;
    let res: any = {};
    try {
        res = await ClinicTicketAPI.getReplyUsingGET(ticketId);
    } catch (err: any) {
        ElMessage.error(err.message || err);
    }
    replyContent.value = res.content || '';
};

const formatTagType = (type: number) => {
    switch (type) {
        case TicketStatusEnum.PROCESSING:
            return 'success';
        case TicketStatusEnum.ACCEPTED:
            return 'success';
        case TicketStatusEnum.COMPLETED:
            return 'info';
        case TicketStatusEnum.ADOPT_SUBSEQUENT_SCHEDULING:
            return 'info';
        case TicketStatusEnum.REJECTED:
            return 'danger';
        default:
            return '';
    }
};

const replyCustomer = async () => {
    loading.value = true;
    const params = {
        content: replyContent.value,
        ticketId: props.currentRow.id,
    };
    let res: any = {};
    try {
        res = await ClinicTicketAPI.replyUsingPOST(params);
    } catch (e: any) {
        ElMessage.error(`${e.message || e}`);
    }
    loading.value = false;
    if (res.id) {
        ElMessage.success('回复成功');
        dialogVisible.value = false;
    }
};

const getImg = (e: any) => {
    if (e.target.localName !== 'img') return;
    previewList.value = [e.target.currentSrc];
    showViewer.value = true;
};

onMounted(() => {
    commentDetail();
});
</script>

<template>
    <el-dialog
        v-model="dialogVisible"
        v-loading="loading"
        :close-on-press-escape="false"
        custom-class="ticket__comment-dialog"
        width="60%"
        append-to-body
        @close="dialogVisible = false"
    >
        <div class="ticket__comment-dialog-header">
            <span class="ticket-type">{{ formatTicketType(currentRow.type) }}</span>
            <span class="ticket-code">{{ currentRow.code }}</span>
            <div class="ticket-message">
                <span>{{ currentRow.title }}</span>
                <el-tag size="large" :type="formatTagType(currentRow.status)">{{ formatTicketStatus(currentRow.status) }}</el-tag>
            </div>
        </div>
        <el-divider />
        <div class="ticket__comment-dialog-content">
            <div
                v-if="currentRow.type === TAB_LIST.BUSINESS"
                class="business-ticket"
                @click="getImg"
                v-html="replyContent"
            ></div>
            <editor
                v-else
                ref="abcEditorRef"
                v-model="replyContent"
                module-name="after-ticket"
                root-dir="oa/after-ticket/files"
            ></editor>
        </div>
        <template v-if="currentRow.type !== TAB_LIST.BUSINESS" #footer>
            <div style="width: 95%;">
                <el-button
                    type="primary"
                    plain
                    :loading="loading"
                    @click="replyCustomer"
                >
                    回复
                </el-button>
            </div>
        </template>
    </el-dialog>
    <div class="after-ticket__img__popup__wrapper">
        <el-image-viewer
            v-if="showViewer"
            hide-on-click-modal
            :url-list="previewList"
            @close="showViewer = false"
        />
    </div>
</template>

<style lang="scss">
    .ticket__comment-dialog {
        height: 80%;
        max-height: 750px;

        .el-dialog__body {
            width: 95%;
            height: 87%;
        }

        &-header {
            .ticket-type {
                color: black;
                font-weight: 500;
                padding-right: 8px;
            }

            .ticket-code {
                color: rgb(122, 135, 148);
            }

            .ticket-message {
                display: flex;
                justify-content: space-between;
                align-items: flex-end;
            }
        }

        &-content {
            height: 85%;

            .business-ticket {
                overflow-y: auto;
                height: 100%;

                img {
                    cursor: pointer;
                    width: 100px;
                    height: 100px;
                    margin: 8px 0;
                }

                video {
                    cursor: pointer;
                    max-width: 100%;
                    height: 300px;
                    width: 100%;
                    margin: 8px 0;
                }

                a {
                    color: #005ed9;
                }
            }
        }
    }

</style>