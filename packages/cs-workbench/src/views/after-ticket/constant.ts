// 工单状态： 0处理中、 5已采纳、 10已完成、 11已拒绝
export enum TicketStatusEnum {
    PROCESSING = 0,
    ACCEPTED = 5,
    COMPLETED = 10,
    REJECTED = 11,
    ADOPT_SUBSEQUENT_SCHEDULING = 12,
}

export enum TAB_LIST {
    BUG = 0,
    DEMANDS = 1,
    SUPPORT = 4,
    BUSINESS = 5,
}
export enum FROM_WAY {
    ONEVSONE = 0, // 1v1客服
    OUTSIDE_GROUP = 1, // 外部群
    IN_SYSTEM = 2, // 客户于系统提单
    INSIDER = 3, // 内部人员提单
    OTHER = 90, // 其他
}
export const TicketStatusOptions: readonly { label: string; value: number }[] = Object.freeze([
    { label: '处理中', value: TicketStatusEnum.PROCESSING },
    { label: '已采纳', value: TicketStatusEnum.ACCEPTED },
    { label: '采纳后续排期', value: TicketStatusEnum.ADOPT_SUBSEQUENT_SCHEDULING },
    { label: '已完成', value: TicketStatusEnum.COMPLETED },
    { label: '已拒绝', value: TicketStatusEnum.REJECTED },
]);
export const ticketTypeList: readonly { label: string; value: number; }[] = Object.freeze([
    { label: 'Bug', value: TAB_LIST.BUG },
    { label: '需求', value: TAB_LIST.DEMANDS },
    { label: '商务', value: TAB_LIST.BUSINESS },
]);
export const PRODUCT_LIST: readonly { label: string; value: number }[] = Object.freeze([
    { label: 'ABC诊所管家', value: 0 },
    { label: 'ABC口腔管家', value: 1 },
    { label: 'ABC眼视光管家', value: 2 },
    { label: 'ABC药店管家', value: 10 },
    { label: 'ABC医院', value: 100 },
]);
export const formatTicketType = (type: number) => {
    switch (type) {
        case TAB_LIST.BUG:
            return 'Bug';
        case TAB_LIST.DEMANDS:
            return '需求';
        case TAB_LIST.SUPPORT:
            return '支持';
        case TAB_LIST.BUSINESS:
            return '商务';
        default:
            return '';
    }
};
export const formatFromWay = (type: number) => {
    switch (type) {
        case FROM_WAY.ONEVSONE:
            return '1v1客服';
        case FROM_WAY.OUTSIDE_GROUP:
            return '外部群';
        case FROM_WAY.IN_SYSTEM:
            return '客户于系统提单';
        case FROM_WAY.INSIDER:
            return '内部人员提单';
        case FROM_WAY.OTHER:
            return '其他';
        default:
            return '';   
    }
};

export const formatTicketStatus = (status: number | undefined) => TicketStatusOptions.find((item) => item.value === status)?.label || '';
