<script setup lang="ts">
import LayoutPageContainer from '@/layout/components/page-container/layout-page-container.vue';
import LayoutPageMain from '@/layout/components/page-container/layout-page-main.vue';
import LayoutPageElement from '@/layout/components/page-container/layout-page-element.vue';
import { ref, onMounted, watch, nextTick, markRaw } from 'vue';
import { debounce } from 'lodash';
import { ElMessage } from 'element-plus';
import { TicketStatusOptions, ticketTypeList, TAB_LIST, formatTicketStatus, formatTicketType } from '@/views/after-ticket/constant';
import { ClinicTicketAPI } from '@/api/clinic-ticket-api';
import { CorpAPI } from '@/api/corp-api';
import dayjs from 'dayjs';
import { useRoute } from 'vue-router';
import OrganPicker from '@/components/organ-picker.vue';
import { NodeTypeFilter } from '@/utils/index';
import TicketDetail from './components/detail.vue';
import CommentDialog from './components/comment-dialog.vue';
import { isNone } from '@abc-oa/utils';

const props = defineProps({
    clinicId: {
        type: String,
        default: '',
    },
    isClinicDimension: {
        type: Boolean,
        default: false, 
    },
});

const formData = ref({
    keyword: '',
    statusList: [],
    clinicId: '',
    dealerId: '',
    employeeMobile: '',
    createTimeRange: [dayjs().startOf('date').subtract(1, 'month').format('YYYY-MM-DD HH:mm:ss'), dayjs().endOf('date').format('YYYY-MM-DD HH:mm:ss')],
    type: '',
});
const isClinicTicketManage = ref(false); // 是否为跟进中的工单管理
const currentPage = ref(1);
const pageSize = ref(10);
const totalRecords = ref(0);
const tableDataList = ref([]);
const loading = ref(false);
const route = useRoute();
const ticketOption = ref<any>({});
const activeName = ref<any>(TAB_LIST.BUG);
const visibleDetailDialog = ref(false);
const visibleCommentDialog = ref(false);
const isCreatedDialog = ref(false);
const currentRow = ref<any>({});
const corpList = ref<any[]>([]);

/**
 * @description: 获取企业微信人员列表
 * @date: 2023-11-23 13:45:07
 * @author: Horace
 * @param null:
 * @return
*/
const getCorpList = async () => {
    let res: any = {};
    try {
        res = await CorpAPI.listCorpUserUsingGET();
    } catch (e: any) {
        ElMessage.error(e.message || e);
    }
    corpList.value = res.rows?.map((item: any) => ({
        ...item,
        label: item.name,
        value: item.id,
    }));
};

/**
 * 获取不同类型工单的数量统计
 * 该函数通过调用 ClinicTicketAPI.processingCountUsingGET 方法获取工单统计数据，
 * 并将结果按照需求、bug 和业务类型分类存储在 ticketOption.value 中。
 * 如果请求失败，会显示错误信息。
 */
const getDiffTicketTypeCount = async () => {
    let res: any = {};
    try {
        res = await ClinicTicketAPI.processingCountUsingGET();
        ticketOption.value = {
            [TAB_LIST.DEMANDS]: res.rows?.find((item: any) => item.type === TAB_LIST.DEMANDS) || {},
            [TAB_LIST.BUG]: res.rows?.find((item: any) => item.type === TAB_LIST.BUG) || {},
            [TAB_LIST.BUSINESS]: res.rows?.find((item: any) => item.type === TAB_LIST.BUSINESS) || {},
        };
    } catch (e: any) {
        ElMessage.error(e.message || e);
    }
};

/**
 * 查看详情
 * @param row
 */
const onHandleView = (row: any) => {
    !Array.isArray(row.auditResults) && (row.auditResults = row.auditResults ? [row.auditResults] : []);
    currentRow.value = row || {};
    isCreatedDialog.value = false;
    visibleDetailDialog.value = true;
};

/**
 * @description 查看回复点击事件
 * @param row
 */
const onCommentView = (row: any) => {
    currentRow.value = row || {};
    visibleCommentDialog.value = true;
};
const addTicket = () => {
    visibleDetailDialog.value = true;
    isCreatedDialog.value = true;
};

const disabledDate = (date: Date) => date.getTime() > Date.now();

const handleRefresh = async () => {
    await nextTick();
    getDiffTicketTypeCount();
    fetchData();
};

const fetchData = async () => {
    loading.value = true;
    try {
        const limit = pageSize.value;
        const offset = (currentPage.value - 1) * pageSize.value;
        const clinicId = formData.value.clinicId;
        let res: any = [];
        if (isClinicTicketManage.value) {
            res = await ClinicTicketAPI.listClinicTicketByClinicIdUsingGET(
                clinicId,
                formData.value.keyword,
                limit,
                isNone(formData.value.type) ? 1 : undefined,
                1,
                offset,
                formData.value.type,
            );
        } else {
            const params = {
                clinicId,
                employeeMobile: formData.value.employeeMobile,
                createdEndTime: formData.value.createTimeRange[1] || '',
                createdStartTime: formData.value.createTimeRange[0] || '',
                dealerId: formData.value.dealerId,
                keyword: formData.value.keyword,
                statusList: formData.value.statusList || [],
                type: activeName.value,
                offset,
                limit,
            };
            res = await ClinicTicketAPI.pageListUsingPOST(params);
        }
        const { rows, total } = res;
        totalRecords.value = total;
        tableDataList.value = rows || [];
    } catch (error) {
        ElMessage.error('Failed to fetch after ticket. Please try again later.');
        console.error('Error fetching after ticket:', error);
    } finally {
        loading.value = false;
    }
};

const handlePageChange = (page: number) => {
    currentPage.value = page;
    fetchData();
};

const debouncedFetchData = debounce(fetchData, 300);

watch(() => formData.value, async () => {
    debouncedFetchData();
}, { deep: true });

watch(() => props.clinicId, async () => {
    if (!isClinicTicketManage.value && !props.clinicId) return;
    formData.value.clinicId = props.clinicId;
});

onMounted(async () => {
    if (props.isClinicDimension) { // 是否为诊所纬度的工单管理
        isClinicTicketManage.value = true;
        formData.value.clinicId = props.clinicId;
    } else {
        getCorpList();
        getDiffTicketTypeCount();
        fetchData();
    }
    if (route.query?.type !== undefined) { // tapd工单回复客户穿透过来
        activeName.value = Number(route.query.type);
        let res: any = {};
        try {
            res = await ClinicTicketAPI.getByIdUsingGET(route.query.ticketId as string);
        } catch (e: any) {
            ElMessage.error(e.message || e);
        }
        if (res.id) {
            formData.value.keyword = res.code;
            onCommentView(res);
        }
        return;
    }
    if (route.query?.ticketId) { // tapd工单查看会话记录穿透过来
        const data = {
            id: route.query.ticketId,
        };
        onHandleView(data);
    }
});

// 使用 markRaw 标记组件
const TicketDetailComponent = markRaw(TicketDetail);
const CommentDialogComponent = markRaw(CommentDialog);
</script>

<template>
    <layout-page-container>
        <layout-page-main>
            <template #header>
                <layout-page-element>
                    <el-space v-if="!isClinicTicketManage" wrap>
                        <el-input
                            v-model="formData.keyword"
                            class="search-input"
                            style="width: 230px;"
                            placeholder="请输入工单号/标题/描述"
                            @input="debouncedFetchData"
                        />
                        <el-select
                            v-model="formData.statusList"
                            multiple
                            collapse-tags
                            collapse-tags-tooltip
                            placeholder="请选择工单状态"
                            style="width: 230px;"
                        >
                            <el-option
                                v-for="item in TicketStatusOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                        <el-select
                            v-model="formData.dealerId"
                            placeholder="请选择处理人"
                            style="width: 230px;"
                            clearable
                        >
                            <el-option
                                v-for="item in corpList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                        <organ-picker
                            v-model="formData.clinicId"
                            :node-type-filter="NodeTypeFilter.NO_FILTER"
                            :placeholder="'请选择反馈门店'"
                            :is-trial="0"
                            clearable
                        >
                        </organ-picker>
                        <el-input
                            v-model="formData.employeeMobile"
                            class="search-input"
                            style="width: 230px;"
                            placeholder="请输入反馈用户手机"
                            @input="debouncedFetchData"
                        />
                        <el-date-picker
                            v-model="formData.createTimeRange"
                            type="datetimerange"
                            start-placeholder="工单创建开始时间"
                            end-placeholder="工单创建结束时间"
                            value-format="YYYY-MM-DD HH:mm:ss"
                            :disabled-date="disabledDate"
                        />
                        <el-button type="primary" style="width: 100px;" @click="addTicket">新建工单</el-button>
                    </el-space>
                    <!-- 工单管理页面的筛选栏 -->
                    <el-space v-else wrap>
                        <el-radio-group v-model="formData.type" @change="debouncedFetchData">
                            <el-radio-button label="">全部</el-radio-button>
                            <el-radio-button
                                v-for="type in ticketTypeList"
                                :key="type.value"
                                :label="type.value"
                            >
                                {{ type.label }}
                            </el-radio-button>
                        </el-radio-group>
                        <el-input
                            v-model="formData.keyword"
                            class="search-input"
                            style="width: 230px;"
                            placeholder="请输入工单标题"
                            @change="debouncedFetchData"
                        >
                            <template #prefix>
                                <el-icon><Search /></el-icon>
                            </template>
                        </el-input>
                    </el-space>
                </layout-page-element>
            </template>
            <layout-page-element>
                <el-tabs v-if="!isClinicTicketManage" v-model="activeName" @tab-click="handleRefresh">
                    <el-tab-pane
                        v-for="(tab, index) in ticketTypeList"
                        :key="index"
                        :label="`${tab.label}(${ticketOption?.[tab.value]?.count || 0})`"
                        :name="tab.value"
                    ></el-tab-pane>
                </el-tabs>
                <el-table
                    v-loading="loading"
                    border
                    :data="tableDataList"
                    style="width: 100%;"
                >
                    <!-- 工单号 -->
                    <el-table-column prop="code" label="工单号">
                        <template #default="{ row }">
                            <div class="flex-between">
                                <span>{{ row.code }}</span>
                                <el-tag
                                    v-if="row.isDraft"
                                    size="small"
                                    type="warning"
                                    style="margin-left: 5px;"
                                >
                                    草稿
                                </el-tag>
                            </div>
                        </template>
                    </el-table-column>
                    <!-- 工单创建时间 -->
                    <el-table-column prop="created" label="工单创建时间" />
                    <!-- 类型 -->
                    <el-table-column label="类型">
                        <template #default="{ row }">
                            {{ formatTicketType(row.type) }}
                        </template>
                    </el-table-column>
                    <!-- 状态 -->
                    <el-table-column label="状态">
                        <template #default="{ row }">
                            <el-link :underline="false" :type="row.status === 0 ? 'primary' : 'info'">{{ formatTicketStatus(row.status) }}</el-link>
                        </template>
                    </el-table-column>
                    <!-- 工单标题 -->
                    <el-table-column prop="title" label="工单标题" />
                    <!-- 反馈门店 -->
                    <el-table-column v-if="!isClinicTicketManage" prop="clinicName" label="反馈门店" />
                    <!-- 反馈用户 -->
                    <el-table-column prop="employeeName" label="反馈用户" />
                    <!-- 处理人 -->
                    <el-table-column prop="dealerName" label="处理人" />
                    <!-- 操作方法 -->
                    <el-table-column label="操作" width="130">
                        <template #default="{ row }">
                            <div class="table-column-operate">
                                <el-link type="primary" :underline="false" @click="onHandleView(row)">
                                    详情
                                </el-link>
                                <el-link type="primary" :underline="false" @click="onCommentView(row)">
                                    查看回复
                                </el-link>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                    :current-page="currentPage"
                    :page-size="pageSize"
                    layout="total, prev, pager, next"
                    :total="totalRecords"
                    @current-change="handlePageChange"
                />
            </layout-page-element>
        </layout-page-main>
    </layout-page-container>
    <TicketDetailComponent
        v-if="visibleDetailDialog"
        v-model:visible="visibleDetailDialog"
        :is-created-dialog="isCreatedDialog"
        :current-row="currentRow"
        :ticket-id="currentRow.id"
        @refresh="handleRefresh"
    ></TicketDetailComponent>
    <CommentDialogComponent
        v-if="visibleCommentDialog"
        v-model:visible="visibleCommentDialog"
        :current-row="currentRow"
    ></CommentDialogComponent>
</template>

<style lang="scss">
.page-table {
    flex-direction: column;
    gap: 16px;
}

.long-table-text {
    display: inline-block;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: middle;
}

.table-column-operate {
    display: flex;
    gap: 8px;
}
</style>
