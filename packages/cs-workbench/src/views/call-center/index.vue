<script setup lang="ts">
import { OaCard } from '@abc-oa/components';
import LayoutPageContainer from '@/layout/components/page-container/layout-page-container.vue';
</script>
<template>
    <layout-page-container>
        <oa-card style="height: 100%;">
            <el-container>
                <el-aside width="200px">
                    <el-input placeholder="搜索"></el-input>
                    <!--  远程用户列表，带头像，用户名，状态等                      -->
                    <ul>
                        <li>
                            <span>张三</span>
                        </li>
                        <li>
                            <span>李四</span>
                        </li>
                        <li>
                            <span>王五</span>
                        </li>
                    </ul>
                </el-aside>
                <el-container>
                    <el-header>Header</el-header>
                    <el-main>Main</el-main>
                </el-container>
            </el-container>
        </oa-card>
    </layout-page-container>
</template>
