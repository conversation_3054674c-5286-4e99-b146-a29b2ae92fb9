<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import ReplyInput from '@/components/reply-input.vue';
import dayjs from 'dayjs';
import { SupportClinicFollowRecordAPI } from '@/api/support-clinic-follow-record-api.ts';
import { ElMessage } from 'element-plus';
import { useUserStore } from '@/store/user';

const props = defineProps({
    clinicId: {
        type: String,
        required: true,
    },
    currentRow: {
        type: Object,
        default: () => {},
    },
    visible: {
        type: Boolean,
        required: true,
        default: false,
    },
});

const emit = defineEmits(['update:visible', 'refresh']);

const userInfo = useUserStore()?.userInfo;

const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => {
        if (value === false) {
            emit('update:visible', false);
        }
    },
});

const form: any = ref({
    contactUser: '',
});

const echoValues = ref<any[]>([]);
const chatInputValue: any = ref([]);
const handleChatInputChange = (nodes: any[]) => {
    console.log('输入框的值变化', nodes);
    chatInputValue.value = [];
    if (nodes && nodes.length) {
        nodes.forEach((node: any) => {
            console.log(node);
            let msg: AbcAPI.QwConversationMsg = {
                origin: 1,
                msgTime: dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss'),
            };
            if (node.type === 'text') {
                msg.msgType = 'text';
                msg.msgText = {
                    content: node.content.trim(),
                };
            } else if (node.type === 'image') {
                msg.msgType = node.type;
                msg.msgImage = {
                    ossUrl: node.url,
                };
            } else if (node.type === 'video') {
                msg.msgType = node.type;
                msg.msgVideo = {
                    ossUrl: node.url,
                };
            } else if (node.type === 'file') {
                msg.msgType = 'file';
                msg.msgFile = {
                    ossUrl: node.url,
                    filename: node.filename,
                };
            } else { return; }
            chatInputValue.value.push(msg);
        });
        console.log('chatInputValue', chatInputValue.value);
    }
};

const getParams = () => ({
    clinicId: props.clinicId,
    contactUser: form.value.contactUser,
    description: chatInputValue.value,
    id: props.currentRow?.id || undefined,
});

const handleSubmit = async () => {
    if (!props.clinicId) {
        ElMessage.error('诊所id不存在');
        return;
    }
    if (!chatInputValue.value.length) {
        ElMessage.error('请输入需求描述');
        return;
    }
    const params = getParams();
    try {
        const res: any = await SupportClinicFollowRecordAPI.upsertDemandUsingPOST(params);
        if (res.id) {
            ElMessage.success('提交成功');
            emit('refresh');
        }
    } catch (err: any) {
        ElMessage.error(err.message || err);
    }
};

const handleDelete = async () => {
    if (!props.currentRow?.id) {
        ElMessage.error('id 不存在');
        return;
    }
    try {
        const res: any = await SupportClinicFollowRecordAPI.deleteDemandUsingDELETE(props.currentRow?.id || '');
        if (res.code === 200) {
            ElMessage.success('删除成功');
            emit('refresh');
        }
    } catch (err: any) {
        ElMessage.error(err.message || err);
    }
};
const canIUseDelete = computed(() => props.currentRow.createdBy === userInfo?.id);
onMounted(() => {
    form.value.contactUser = props.currentRow.contactUser;
    echoValues.value = props.currentRow.description;
});
</script>

<template>
    <el-dialog
        v-model="dialogVisible"
        title="历史对接需求"
        width="500px"
        custom-class="history-demand-dialog"
    >
        <el-form :model="form" label-width="auto">
            <el-form-item label="需求描述">
                <reply-input
                    ref="replyInputRef"
                    :chat-input-value="chatInputValue"
                    :echo-value="echoValues"
                    :show-submit-btn="false"
                    placeholder="请描述对接的需求（可直接粘贴tapd需求单链接），以便其他接待同学快速了解相关情况。"
                    :editor-style="{width: '100%'}"
                    :is-watch-value="false"
                    :enter="true"
                    @change="handleChatInputChange"
                >
                </reply-input>
            </el-form-item>
            <el-form-item label="对接人">
                <el-input v-model="form.contactUser" placeholder="请输入相关对接人，以便有问题时快速找到对应负责人沟通" maxlength="50"></el-input>
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button type="primary" @click="handleSubmit">提交</el-button>
            <el-button :disabled="!canIUseDelete" @click="handleDelete">删除</el-button>
        </template>
    </el-dialog>
</template>
<style lang="scss">
</style>
