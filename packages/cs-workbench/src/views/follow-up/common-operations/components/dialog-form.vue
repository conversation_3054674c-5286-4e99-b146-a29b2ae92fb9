<template>
    <div v-bind="$attrs">
        <!-- 操作表单弹窗 -->
        <el-dialog
            v-model="dialogVisible"
            :title="formConfig?.title || '操作'"
            width="500px"
            :close-on-click-modal="false"
            @close="handleClose"
        >
            <el-form
                ref="formRef"
                :model="formData"
                :rules="formConfig?.rules"
                label-width="6em"
                label-position="right"
                :validate-on-rule-change="false"
                class="operation-form"
            >
                <template v-for="(item, index) in formConfig?.items || []">
                    <!-- 输入框 -->
                    <el-form-item
                        v-if="item.type === 'input' && (!item.visible || item.visible(formData))"
                        :key="`input-${index}`"
                        :label="item.label"
                        :label-width="item.labelWidth"
                        :prop="item.prop"
                        :required="typeof item.required === 'function' ? item.required(formData) : item.required"
                    >
                        <el-input
                            v-model="formData[item.prop]"
                            :placeholder="item.placeholder"
                            :type="item.inputType || 'text'"
                            :disabled="typeof item.disabled === 'function' ? item.disabled(formData) : item.disabled"
                            @change="item.onChange?.(formData[item.prop], formData, formRef)"
                        />
                    </el-form-item>
                    <!-- 单选框组 -->
                    <el-form-item
                        v-if="item.type === 'radio' && (!item.visible || item.visible(formData))"
                        :key="`radio-${index}`"
                        :label="item.label"
                        :label-width="item.labelWidth"
                        :prop="item.prop"
                        :required="typeof item.required === 'function' ? item.required(formData) : item.required"
                    >
                        <el-radio-group
                            v-model="formData[item.prop]"
                            :disabled="typeof item.disabled === 'function' ? item.disabled(formData) : item.disabled"
                            @change="item.onChange?.(formData[item.prop], formData, formRef)"
                        >
                            <el-radio
                                v-for="option in item.options"
                                :key="option.value"
                                :label="option.value"
                            >
                                {{ option.label }}
                            </el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <!-- 多选框组 -->
                    <el-form-item
                        v-if="item.type === 'checkbox' && (!item.visible || item.visible(formData))"
                        :key="`checkbox-${index}`"
                        :label="item.label"
                        :label-width="item.labelWidth"
                        :prop="item.prop"
                        :required="typeof item.required === 'function' ? item.required(formData) : item.required"
                    >
                        <el-checkbox-group
                            v-model="formData[item.prop]"
                            :disabled="typeof item.disabled === 'function' ? item.disabled(formData) : item.disabled"
                            @change="item.onChange?.(formData[item.prop], formData, formRef)"
                        >
                            <el-checkbox
                                v-for="option in item.options"
                                :key="option.value"
                                :label="option.value"
                            >
                                {{ option.label }}
                            </el-checkbox>
                        </el-checkbox-group>
                    </el-form-item>

                    <!-- 下拉选择 -->
                    <el-form-item
                        v-if="item.type === 'select' && (!item.visible || item.visible(formData))"
                        :key="`select-${index}`"
                        :label="item.label"
                        :label-width="item.labelWidth"
                        :prop="item.prop"
                        :required="typeof item.required === 'function' ? item.required(formData) : item.required"
                    >
                        <el-select
                            v-model="formData[item.prop]"
                            :placeholder="item.placeholder"
                            :multiple="item.multiple"
                            :remote="item.remote"
                            :remote-method="item.remoteMethod"
                            :filterable="item.filterable"
                            :disabled="typeof item.disabled === 'function' ? item.disabled(formData) : item.disabled"
                            @change="item.onChange?.(formData[item.prop], formData, formRef)"
                        >
                            <el-option
                                v-for="option in item.options"
                                :key="option.value"
                                :label="option.label"
                                :value="option.value"
                            />
                        </el-select>
                    </el-form-item>

                    <!-- 下拉选择 -->
                    <el-form-item
                        v-if="item.type === 'selectV2' && (!item.visible || item.visible(formData))"
                        :key="`select-${index}`"
                        :label="item.label"
                        :label-width="item.labelWidth"
                        :prop="item.prop"
                        :required="typeof item.required === 'function' ? item.required(formData) : item.required"
                    >
                        <el-select-v2
                            v-model="formData[item.prop]"
                            :placeholder="item.placeholder"
                            :options="item.options"
                            :multiple="item.multiple"
                            :value-key="item.valueKey"
                            :disabled="typeof item.disabled === 'function' ? item.disabled(formData) : item.disabled"
                            @change="item.onChange?.(formData[item.prop], formData, formRef)"
                        >
                        </el-select-v2>
                    </el-form-item>

                    <!-- 日期选择 -->
                    <el-form-item
                        v-if="item.type === 'datePicker' && (!item.visible || item.visible(formData))"
                        :key="`date-${index}`"
                        :label="item.label"
                        :label-width="item.labelWidth"
                        :prop="item.prop"
                        :required="typeof item.required === 'function' ? item.required(formData) : item.required"
                    >
                        <el-date-picker
                            v-model="formData[item.prop]"
                            :type="item.dateType || 'date'"
                            :placeholder="item.placeholder"
                            :format="item.format || 'YYYY-MM-DD HH:mm:ss'"
                            :value-format="item.format || 'YYYY-MM-DD HH:mm:ss'"
                            :shortcuts="item.pickerOptions?.shortcuts"
                            :disabled-date="item.pickerOptions?.disabledDate"
                            :disabled="typeof item.disabled === 'function' ? item.disabled(formData) : item.disabled"
                            @change="item.onChange?.(formData[item.prop], formData, formRef)"
                        />
                    </el-form-item>

                    <!-- 提示信息 -->
                    <div
                        v-if="item.type === 'tips' && (!item.visible || item.visible(formData))"
                        :key="`tips-${index}`"
                        class="confirm-error"
                    >
                        <el-alert
                            v-if="item.alertType"
                            :title="item.content"
                            :type="item.alertType"
                            show-icon
                            :closable="false"
                        />
                        <span v-else :style="item.style">{{ item.content }}</span>
                    </div>
                </template>
            </el-form>

            <template #footer>
                <span class="dialog-footer">
                    <el-button :loading="loading" @click="handleClose">取消</el-button>
                    <el-button
                        :loading="loading"
                        type="primary"
                        @click="handleSubmit"
                    >
                        确定
                    </el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 二次确认弹窗 -->
        <el-dialog
            v-model="confirmDialogVisible"
            :title="formConfig?.confirmConfig?.title || '操作确认'"
            width="450px"
            :close-on-click-modal="false"
        >
            <div class="confirm-content">
                <template v-if="formConfig?.confirmConfig?.content && formConfig.confirmConfig.content.length > 0">
                    <div v-for="(item, index) in formConfig.confirmConfig.content" :key="index" class="confirm-item">
                        <span class="confirm-label">{{ item.label }}:</span>
                        <span class="confirm-value">{{ getConfirmValue(item.key) }}</span>
                    </div>
                </template>

                <div v-if="formConfig?.confirmConfig?.warning" class="confirm-warning">
                    <el-alert
                        :title="formConfig.confirmConfig.warning"
                        type="warning"
                        show-icon
                        :closable="false"
                    />
                </div>
            </div>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="cancelConfirm">取消</el-button>
                    <el-button :loading="confirmLoading" type="primary" @click="confirmSubmit">
                        确认
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import type { FormInstance } from 'element-plus';
import { ElMessage } from 'element-plus';
import { FormConfig } from '../model';
import { commonOperationState } from '../hook';
import { useLayoutQuickListHook } from '@/views/follow-up/hook/quick-list';

// 获取当前门店信息
const { activeItem } = useLayoutQuickListHook();
const clinicId = computed(() => activeItem.value?.clinicId || '');
const { confirmDialogVisible, formData } = commonOperationState;

const props = defineProps<{
    visible?: boolean;
    formConfig?: FormConfig;
}>();

const emit = defineEmits([
    'update:visible',
    'submit',
    'clear-cache-completed',
]);

const dialogVisible = computed({
    get: () => props.visible,
    set: (val) => emit('update:visible', val),
});

const formRef = ref<FormInstance>();
const loading = ref(false);

const confirmLoading = ref(false);

// 监听弹窗打开关闭状态
watch(() => props.visible, (newVal) => {
    if (newVal) {
        // 弹窗打开时，初始化表单数据但不触发校验
        nextTick(async () => {
            // 设置默认值和加载选项
            if (props.formConfig?.items) {
                // 使用for...of替代forEach，以便正确处理异步操作
                for (const item of props.formConfig.items) {
                    // 设置默认值
                    if (item.prop && item.defaultValue !== undefined) {
                        formData[item.prop] = typeof item.defaultValue === 'function' ? await item.defaultValue() : item.defaultValue;
                    }
                }
            }
            // 防止在表单初始化时触发校验
            if (formRef.value) {
                formRef.value.clearValidate();
            }
        });
    }
}, { immediate: true });

watch(() => props.formConfig, async (newVal: any) => {
    if (newVal) {
        for (const item of newVal.items) {
            if (item.optionsFn && typeof item.optionsFn === 'function') {
                try {
                    item.options = await item.optionsFn(clinicId.value, formData);
                } catch (error) {
                    console.error('加载选项失败:', error);
                    item.options = [];
                }
            }
            if (item.placeholder && typeof item.placeholder === 'function') {
                item.placeholder = item.placeholder(formData);
            }
        }
        // 防止在表单初始化时触发校验
        if (formRef.value) {
            formRef.value.clearValidate();
        }
    }
}, { immediate: true, deep: true });

// 重置表单数据
const resetForm = () => {
    // 先清空数据，避免触发验证
    Object.keys(formData).forEach((key) => {
        delete formData[key];
    });

    // 然后重置表单字段
    nextTick(() => {
        if (formRef.value) {
            formRef.value.resetFields();
            formRef.value.clearValidate();
        }
    });
};

// 关闭弹窗
const handleClose = () => {
    resetForm();
    dialogVisible.value = false;
};

// 获取确认值，用于在确认弹窗中显示
const getConfirmValue = (key: string): string => {
    if (!key || !formData) return '-';

    // 如果是嵌套属性，使用点符号分割
    if (key.includes('.')) {
        const keys = key.split('.');
        let value: any = formData;
        for (const k of keys) {
            if (value && typeof value === 'object' && k in value) {
                value = value[k];
            } else {
                return '-';
            }
        }
        return value !== undefined && value !== null ? String(value) : '-';
    }

    return formData[key] !== undefined && formData[key] !== null ? String(formData[key]) : '-';
};

// 取消确认
const cancelConfirm = () => {
    confirmDialogVisible.value = false;
};

// 确认提交
const confirmSubmit = async () => {
    confirmLoading.value = true;
    try {
        // 处理表单提交
        emit('submit', formData);
        confirmDialogVisible.value = false;
        dialogVisible.value = false;
        resetForm();
    } catch (error) {
        ElMessage.error('操作失败：' + (error instanceof Error ? error.message : String(error)));
    } finally {
        confirmLoading.value = false;
    }
};

// 提交表单
const handleSubmit = async () => {
    if (!formRef.value) return;

    try {
        await formRef.value.validate();
        loading.value = true;

        // 需要二次确认的情况
        if (props.formConfig?.needConfirm) {
            confirmDialogVisible.value = true;
            loading.value = false;
            return;
        }

        // 处理其他普通表单提交
        emit('submit', formData);
        dialogVisible.value = false;
        resetForm();
    } catch (error) {
        // 表单验证失败
        console.error('表单验证失败:', error);
    } finally {
        loading.value = false;
    }
};
</script>

<style lang="scss" scoped>
/* 表单弹窗样式优化 */
.operation-form {
    max-height: 60vh;
    overflow-y: auto;
    padding: 10px 5px 10px 0;

    ::v-deep .el-form-item__content {
        flex: 1;
    }

    ::v-deep .el-select-v2 {
        width: 100%;
    }

    ::v-deep .el-form-item {
        margin-bottom: 22px;
    }

    /* 表单标签样式优化 */
    ::v-deep .el-form-item__label {
        font-weight: 500;
        color: #303133;
        text-align: justify;
        text-align-last: justify;
        padding-right: 12px;
    }

    /* 输入框样式优化 */
    ::v-deep .el-input__wrapper,
    ::v-deep .el-select__wrapper,
    ::v-deep .el-date-editor.el-input__wrapper {
        border-radius: 4px;
        transition: all .2s;
        box-shadow: 0 0 0 1px #dcdfe6 inset;

        &:hover {
            box-shadow: 0 0 0 1px #409eff inset;
        }
    }

    /* 单选按钮样式 */
    ::v-deep .el-radio {
        margin-right: 15px;
        margin-bottom: 5px;
    }

    /* 下拉选择器宽度 */
    ::v-deep .el-select,
    ::v-deep .el-date-editor {
        width: 100%;
    }
}

/* 弹窗样式 */
::v-deep .el-dialog {
    &__header {
        padding: 16px 20px;
        margin-right: 0;
        border-bottom: 1px solid #e4e7ed;
        background-color: #f5f7fa;
    }

    &__body {
        padding: 20px;
    }

    &__footer {
        padding: 12px 20px;
        border-top: 1px solid #e4e7ed;
        background-color: #f5f7fa;
    }
}

/* 提示信息样式 */
::v-deep .el-alert {
    margin: 10px 0;
}

/* 确认弹窗内容样式 */
.confirm {
    &-content {
        padding: 15px 5px;
    }

    &-item {
        margin-bottom: 15px;
        display: flex;
    }

    &-label {
        font-weight: 500;
        color: #303133;
        min-width: 6em;
        text-align: justify;
        text-align-last: justify;
        padding-right: 12px;
    }

    &-value {
        flex: 1;
        word-break: break-all;
        color: #606266;
    }

    /* 警告信息样式 */
    &-warning {
        margin-top: 16px;
        color: #e6a23c;
        font-size: 14px;
        line-height: 1.5;
        background-color: #fdf6ec;
        border-radius: 4px;
        border-left: 3px solid #e6a23c;
    }

    &-error {
        margin-top: 16px;
        color: #f56c6c;
        font-size: 14px;
        line-height: 1.5;
        background-color: #fee2e2;
        border-radius: 4px;
        border-left: 3px solid #f56c6c;
    }
}

/* 按钮样式优化 */
.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

/* 提示信息样式 */
[role="alert"] {
    margin-bottom: 16px;
}
</style>
