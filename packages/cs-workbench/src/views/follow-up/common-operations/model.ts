import { ElMessage } from 'element-plus';
import type { FormRules } from 'element-plus';

// 操作历史记录项
export interface OperationHistoryItem {
    id: string;
    type: string;
    name: string;
    buildNumber: string;
    status: number;
    result: string;
    startTime: string;
    endTime: string;
    createdBy: string;
    createdByName: string;
    consoleOutputPage: string;
}

// 表单项配置接口
export interface FormItem {
    type: 'input' | 'radio' | 'checkbox' | 'select' | 'datePicker' | 'tips' | 'selectV2';
    label: string;
    prop: string;
    labelWidth?: string;
    placeholder?: (() => string) | string;
    defaultValue?: (() => FormItemValue) | FormItemValue | FormItemValue[];
    options?: FormItemOption[];
    optionsFn?: (clinicId: string, formData: Record<string, any>) => Promise<{ label: string; value: any }[]>;
    multiple?: boolean; // 用于select多选
    inputType?: string; // 用于input类型
    dateType?: string; // 用于日期选择器类型
    format?: string; // 用于日期选择器格式
    content?: string; // 用于tips内容
    style?: Record<string, any>;
    alertType?: 'success' | 'warning' | 'info' | 'error'; // 用于tips类型
    visible?: VisibilityFunction; // 控制表单项是否显示的函数
    required?: VisibilityFunction | boolean; // 控制表单项是否必填
    disabled?: VisibilityFunction | boolean; // 控制表单项是否禁用
    pickerOptions?: Record<string, any>;
    onChange?: (value: FormItemValue, formData: Record<string, any>, formRef: any) => void;
}

// 确认弹窗内容项接口
export interface ConfirmContentItem {
    label: string;
    key: string;
}

// 二次确认弹窗配置接口
export interface ConfirmDialogConfig {
    title: string;
    content: ConfirmContentItem[];
    warning?: string;
}

// 表单配置接口
export interface FormConfig {
    title: string;
    items: FormItem[];
    rules?: FormRules;
    needConfirm?: boolean; // 是否需要二次确认
    confirmConfig?: ConfirmDialogConfig; // 二次确认弹窗配置
}

// 操作项接口类型
export interface OperationItemType {
    id: string;
    key: OperationEnum;
    name: string;
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    handler: (formData?: FormDataType) => void | Promise<void>;
    disabled?: boolean;
    formConfig?: FormConfig; // 新增：表单配置
}

// 操作项接口（带统计信息）
export interface OperationItem extends OperationItemType {
    count: number; // 操作次数统计
    inProgress?: boolean; // 是否进行中
    history?: OperationHistoryItem[]; // 存储执行历史记录
}

// 定义类型别名
export type FormDataType = Record<string, any>;
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export type VisibilityFunction = (formData: FormDataType) => boolean | undefined;

// 定义表单项值类型
export type FormItemValue = string | number | boolean | Date;

// 定义表单项选项类型
export interface FormItemOption {
    label: string;
    value: FormItemValue;
}

// 操作类型枚举
// 所有操作类型定义，即使目前未在渲染逻辑中使用也保留，便于后续扩展功能
// 下面的枚举值在实际操作中会被使用，请勿删除
export enum OperationEnum {
    // 已在当前实现的操作
    SOCIAL_CODE = 'SOCIAL_CODE', // 补录社保码

    // 下列操作暂未在页面上实现完整功能，但在 API 模拟和场景规划中需要使用
    /* eslint-disable @typescript-eslint/no-unused-vars */
    CLEAR_REDIS = 'CLEAR_REDIS', // 清理GoodsRedis缓存
    SWITCH_SETTING = 'MODIFY_PROPERTY', // 开关设置
    ADJUST_CHARGE = 'ADJUST_CHARGE', // 调整医保收费单为异常
    TREATMENT_UPGRADE = 'TREATMENT_UPGRADE', // 理疗预约升级开通
    MODIFY_DEPARTMENT = 'MODIFY_DEPARTMENT', // 修改科室编码
    MODIFY_PHONE = 'MODIFY_PHONE', // 修改手机号
    SYNC_DRUG_INFO_GB = 'SYNC_DRUG_INFO_GB', // 同步国标药品资料
    SYNC_DRUG_INFO_JINAN = 'SYNC_DRUG_INFO_JINAN', // 同步药品资料-济南
    UNBIND_WECHAT_ACCOUNT = 'UNBIND_WECHAT_ACCOUNT', // 解绑系统成员微信
    UPDATE_TWO_YEAR_AGO_SHEET_CAN_OPERATE = 'UPDATE_TWO_YEAR_AGO_SHEET_CAN_OPERATE', // 修改两年前的收费单可退
    EXPORT_STOCK = 'EXPORT_STOCK', // 库存导出
    SET_ADMIN = 'SET_ADMIN', // 设置管理员
    MODIFY_VERSION = 'MODIFY_VERSION', // 修改版本时长
    UPDATE_LOCATION = 'UPDATE_LOCATION', // 更新微诊所定位
    CLINIC_VERSION = 'CLINIC_VERSION', // 门店独立产品版本
    CANCEL_AUTH = 'CANCEL_AUTH', // 取消微诊所授权
    SOCIAL_EXPORT = 'SHEBAO_COMMON_EXPORT', // 社保结算数据导出
    QUERY_USAGE_OF_TRACE_CODE='QUERY_USAGE_OF_TRACE_CODE', // 查询追溯码使用情况
    /* eslint-enable @typescript-eslint/no-unused-vars */
}

export enum OperationSubTypeEnum {
    MEDICINE = 'SOCIAL_CODE_MEDICINE',
    MATERIAL = 'SOCIAL_CODE_MATERIAL',
    TREATMENT = 'SOCIAL_CODE_TREATMENT',
    TCM = 'SOCIAL_CODE_TCM',
}

// 操作配置列表
export const operationOptions: OperationItemType[] = [
    {
        id: '1',
        key: OperationEnum.SOCIAL_CODE,
        name: '补录社保码',
        handler: (formData?: Record<string, any>) => {
            ElMessage.success(`执行补录社保码操作${formData ? `，社保码：${formData.socialCode}` : ''}`);
        },
    },
    {
        id: '2',
        key: OperationEnum.CLEAR_REDIS,
        name: '清理GoodsRedis缓存',
        handler: () => {
            ElMessage.success('执行清理GoodsRedis缓存操作');
        },
    },
    {
        id: '3',
        key: OperationEnum.SWITCH_SETTING,
        name: '开关设置',
        handler: (formData?: Record<string, any>) => {
            ElMessage.success(`执行开关设置操作${formData ? `，选中开关：${formData.switches?.join('、')}` : ''}`);
        },
    },
    {
        id: '4',
        key: OperationEnum.ADJUST_CHARGE,
        name: '调整医保收费单为异常',
        handler: () => {
            ElMessage.success('执行调整医保收费单为异常操作');
        },
    },
    {
        id: '5',
        key: OperationEnum.TREATMENT_UPGRADE,
        name: '理疗预约升级开通',
        handler: () => {
            ElMessage.success('执行理疗预约升级开通操作');
        },
    },
    {
        id: '6',
        key: OperationEnum.MODIFY_DEPARTMENT,
        name: '修改科室编码',
        handler: () => {
            ElMessage.success('执行修改科室编码操作');
        },
    },
    {
        id: '7',
        key: OperationEnum.MODIFY_PHONE,
        name: '修改手机号',
        handler: () => {
            ElMessage.success('执行修改手机号操作');
        },
    },
    {
        id: '8',
        key: OperationEnum.SYNC_DRUG_INFO_GB,
        name: '同步国标药品资料',
        handler() {
            // 同步国标药品资料
            ElMessage.info('正在调用同步国标药品资料Jenkins任务...');
        },
    },
    {
        id: '9',
        key: OperationEnum.SYNC_DRUG_INFO_JINAN,
        name: '同步济南药品资料',
        handler() {
            // 同步济南药品资料
            ElMessage.info('正在调用同步济南药品资料Jenkins任务...');
        },
    },
    {
        id: '10',
        key: OperationEnum.SET_ADMIN,
        name: '设置管理员',
        handler: () => {
            ElMessage.success('执行设置管理员操作');
        },
    },
    {
        id: '11',
        key: OperationEnum.MODIFY_VERSION,
        name: '修改版本时长',
        handler: (formData?: Record<string, any>) => {
            ElMessage.success(`执行修改版本时长操作${formData ? `，到期时间：${formData.expireDate}，版本类型：${formData.versionType}` : ''}`);
        },
    },
    {
        id: '12',
        key: OperationEnum.UPDATE_LOCATION,
        name: '更新微诊所定位',
        handler: () => {
            ElMessage.success('执行更新微诊所定位操作');
        },
    },
    {
        id: '13',
        key: OperationEnum.CLINIC_VERSION,
        name: '门店独立产品版本',
        handler: () => {
            ElMessage.success('执行门店独立产品版本操作');
        },
    },
    {
        id: '14',
        key: OperationEnum.CANCEL_AUTH,
        name: '取消微诊所授权',
        handler: () => {
            ElMessage.success('执行取消微诊所授权操作');
        },
    },
    {
        id: '15',
        key: OperationEnum.SOCIAL_EXPORT,
        name: '通用社保结算数据导出',
        handler: (formData?: Record<string, any>) => {
            ElMessage.success(`执行通用社保结算数据导出操作${formData ? `，开始日期：${formData.startDate}，结束日期：${formData.endDate}` : ''}`);
        },
    },
    {
        id: '16',
        key: OperationEnum.QUERY_USAGE_OF_TRACE_CODE,
        name: '查询追溯码使用情况',
        handler: () => {
            ElMessage.success('执行取消查询追溯码使用情况操作');
        },
    },
    {
        id: '17',
        key: OperationEnum.UNBIND_WECHAT_ACCOUNT,
        name: '解绑系统成员微信',
        handler: (formData?: Record<string, any>) => {
            ElMessage.info('正在调用解绑系统成员微信Jenkins任务...');
        },
    },
    {
        id: '18',
        key: OperationEnum.UPDATE_TWO_YEAR_AGO_SHEET_CAN_OPERATE,
        name: '修改两年前的收费单可退',
        handler: (formData?: Record<string, any>) => {
            ElMessage.info('正在调用修改两年前的收费单可退Jenkins任务...');
        },
    },
    {
        id: '19',
        key: OperationEnum.EXPORT_STOCK,
        name: '库存导出',
        handler: (formData?: Record<string, any>) => {
            ElMessage.info('正在调用库存导出Jenkins任务...');
        },
    },
];
