<script lang="ts" setup>
import LayoutPageContainer from '@/layout/components/page-container/layout-page-container.vue';
import LayoutPageMain from '@/layout/components/page-container/layout-page-main.vue';
import LayoutPageElement from '@/layout/components/page-container/layout-page-element.vue';
import type { PageConfig } from '@/components/oa-table/controller.ts';
import { AfterSalesApi } from '@/api/after-sales-api.ts';
import { ElMessage } from 'element-plus';
import { computed, defineAsyncComponent, onMounted, ref, watch } from 'vue';
import { useFormat } from '@/composables/date.ts';
import { useUserStore } from '@/store/user.ts';
import { PermissionAfterSalesAudit } from './permission.ts';
import { useRoute } from 'vue-router';
import {
    AUDIT_LEVEL_OPTIONS, formatAuditLevel,
    formatServiceScore,
    SERVICE_SCORE_OPTIONS,
    TABLE_COLUMNS,
    TABLE_COLUMNS_V2,
} from './constant.ts';
import AfterSalesDetail from './components/detail.vue';
import { OaOrganSelect } from '@abc-oa/components';
import { ClinicTicketAPI } from '@/api/clinic-ticket-api.ts';
import { useLayoutQuickListHook } from '../hook/quick-list.ts';

import _ from 'lodash';
import { KfAPI } from '@/api/kf-api.ts';

const {
    parseUniqueKey,
} = useLayoutQuickListHook();
const OaTable = defineAsyncComponent(() => import('@/components/oa-table/index.vue'));

const form = ref({
    clinicId: '',
    accountId: '',
    servicerId: '',
    keyword: '',
    auditLevel: '',
    serviceScore: [],
    tagId: '',
    range: [useFormat(new Date(), 'YYYY-MM-DD', 'start'), useFormat(new Date(), 'YYYY-MM-DD', 'end')],
});

const accountOptions = ref<any[]>([]);
/**
 * @description: 获取客服通道
 * @date: 2024-12-05 11:18:31
 * @author: Horace
 * @return
*/
const getAccountList = async () => {
    let res: any = {};
    try {
        res = await AfterSalesApi.getApiLowCodeAfterSalesAccount();
    } catch (e: any) {
        ElMessage({
            type: 'error',
            message: e.message || e,
        });
    }
    if (res && res.rows) {
        const rows = res.rows.map((item: any) => ({
            label: item.name,
            value: item.id,
        }));
        // 默认选中ABC工程师
        form.value.accountId = rows?.find((item: {
            label: string, value: string
        }) => item.value === 'wkao5DBgAAcYGyOV3ka2FG59Lw-AjebQ')?.value || '';
        accountOptions.value = rows;
    }
};
const tagListOptions = ref<any[]>([]);
/**
 * @description: 获取咨询分类选项
 * @Date: 2025-04-21 17:30:34
 * @author: yaoyongpeng
 * @return {*}
 */
const getTagList = async () => {
    try {
        const res: any = await ClinicTicketAPI.tagListUsingGET();
        if (res && res.rows && res.rows.length) {
            tagListOptions.value = res.rows.filter((item: any) => item.type === 2).map((item: any) => ({
                ...item,
                label: item.name,
                value: item.id,
            }));
        }
    } catch (e: any) {
        ElMessage({
            type: 'error',
            message: e.message || e,
        });
    }
};
/**
 * @description: 格式化通道
 * @date: 2024-12-05 14:29:29
 * @author: Horace
 * @param {string} accountId 通道id
 * @return
*/
const formatAccountId = (accountId: string) => {
    const account = accountOptions.value.find((item) => item.value === accountId);
    return account?.label || '';
};
const servicerOptions = ref<any[]>([]);
/**
 * @description: 获取客服接待人
 * @date: 2024-12-05 11:21:56
 * @author: Horace
 * @return
*/
const getServicerList = async () => {
    let res: any = {};
    try {
        res = await AfterSalesApi.getApiLowCodeAfterSalesServicer();
    } catch (e: any) {
        ElMessage({
            type: 'error',
            message: e.message || e,
        });
    }
    if (res && res.rows) {
        servicerOptions.value = res.rows.filter((item) => item.id).map((item: any) => ({
            label: item.name,
            value: item.id,
        }));
    }
};

/**
 * @description: 格式化接待人
 * @date: 2024-12-05 14:23:57
 * @author: Horace
 * @param {string} servicerId 接待人id
 * @return
*/
const formatServicer = (servicerId: string) => {
    const servicer = servicerOptions.value.find((item) => item.value === servicerId);
    return servicer?.label || '';
};

const isShowAudit = ref(false);
/**
 * @description: 根据身份验证显示审核
 * @date: 2024-12-05 11:23:37
 * @author: Horace
 * @return
*/
const setShoeAuditByRoles = () => {
    const userStore = useUserStore();
    const roles = userStore?.roles;
    if (!roles || !roles.length) {
        isShowAudit.value = false;
    }
    isShowAudit.value = PermissionAfterSalesAudit.some((role: string) => roles.includes(role));
};
const route = useRoute();
const conversationId = computed(() => route.query?.conversationId as string || '');
const servicerId = computed(() => route.query?.servicerId as string || '');
const clientId = computed(() => route.query?.clientId as string || '');
const auditType = computed(() => route.query?.auditType as string || '');
const clinicIdFromUrl = computed(() => {
    if (route.params?.id) {
        const currentItem = parseUniqueKey(route.params.id as string);
        return currentItem.clinicId;
    }
    // 只有全局的咨询记录去获取
    getTagList();

    return '';
});

// 监听 clinicIdFromUrl 的变化
watch(clinicIdFromUrl, (newValue: string, oldValue: string) => {
    // 仅当新值和旧值不同时输出日志
    if (oldValue && newValue !== oldValue) {
        fetchData();
    }
});

/**
 * @description: 从链接跳转到详情
 * @date: 2024-12-05 11:25:05
 * @author: Horace
 * @return
*/
const linkToDetail = () => {
    if (conversationId.value && servicerId.value && clientId.value) {
        const data = {
            conversationId: conversationId.value,
            servicerId: servicerId.value,
            clientId: clientId.value,
        };
        if (+auditType.value === 1) {
            onHandleAudit({}, data);
        } else {
            onHandleView({}, data);
        }
    }
};

const fetchRecordList = async (pageConfig: PageConfig) => {
    let res: any = {};
    const { range } = form.value;
    let beginDate = range[0];
    let endDate = range[1];
    let clinicId = form.value.clinicId;
    if (clinicIdFromUrl.value) {
        clinicId = clinicIdFromUrl.value;
        beginDate = '';
        endDate = '';
    }
    console.log(beginDate, endDate);
    try {
        res = await AfterSalesApi.getApiLowCodeAfterSalesList(
            clinicId,
            form.value.accountId,
            form.value.servicerId,
            form.value.keyword,
            form.value.auditStatus,
            form.value.auditLevel,
            form.value.serviceScore,
            beginDate,
            endDate,
            form.value.tagId || undefined,
            pageConfig.offset || 0,
            pageConfig.limit,
        );
    } catch (e: any) {
        ElMessage({
            type: 'error',
            message: e.message || e,
        });
    }
    if (res && res.rows) {
        return { rows: res.rows, total: res.total };
    }
    return {
        rows: [],
        total: 0,
    };
};

const disabledDate = (time: any) => time.getTime() > new Date();
onMounted(() => {
    Promise.all([getAccountList(), getServicerList()]);
    setShoeAuditByRoles();
    linkToDetail();
});

const visibleDetailDialog = ref(false);
const visibleDetailAudit = ref(false);
const currentRows = ref<any>({});

/**
 * 查看详情
 * @param column
 * @param row
 */
const onHandleView = (column: any, row: any) => {
    currentRows.value = _.cloneDeep(row || []);
    currentRows.value.auditResults = [];
    visibleDetailDialog.value = true;
    visibleDetailAudit.value = false;
};

/**
 * 审核
 * @param column
 * @param row
 */
const onHandleAudit = (column: any, row: any) => {
    if (!isShowAudit.value) return;
    currentRows.value = _.cloneDeep(row || []);
    !Array.isArray(currentRows.value.auditResults)
    && (currentRows.value.auditResults = currentRows.value.auditResults ? [currentRows.value.auditResults] : []);
    currentRows.value.auditResults = currentRows.value.auditResults.filter((item: number) => item);
    visibleDetailAudit.value = true;
    visibleDetailDialog.value = true;
};

/**
 * AI总结功能
 * @param column 列信息
 * @param row 行数据
 */
const onHandleAISummary = async (column: any, row: any) => {
    try {
        row.isAiSummaryLoading = true;
        // 调用API生成AI总结
        const res = await KfAPI.summaryConversationByIdUsingGET(row.conversationId);

        if (res && res.code === 200) {
            ElMessage({
                type: 'success',
                message: 'AI总结生成成功！',
            });
            // 刷新数据
            fetchData();
        } else {
            ElMessage({
                type: 'error',
                message: res?.message || 'AI总结生成失败',
            });
        }
    } catch (e: any) {
        ElMessage({
            type: 'error',
            message: e.message || 'AI总结请求失败',
        });
    } finally {
        row.isAiSummaryLoading = false;
    }
};

const oaTableRef = ref<any>(null);
const fetchData = () => {
    oaTableRef.value?.fetchData();
};
</script>
<template>
    <layout-page-container class="communication-records__wrapper">
        <layout-page-main>
            <template #header>
                <layout-page-element v-if="!clinicIdFromUrl">
                    <el-space wrap>
                        <el-select
                            v-model="form.accountId"
                            placeholder="请选择通道"
                            filterable
                            clearable
                            @change="fetchData"
                        >
                            <el-option
                                v-for="item in accountOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            ></el-option>
                        </el-select>

                        <el-select
                            v-model="form.servicerId"
                            placeholder="请选择接待人"
                            filterable
                            clearable
                            @change="fetchData"
                        >
                            <el-option
                                v-for="item in servicerOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            ></el-option>
                        </el-select>

                        <el-select
                            v-model="form.tagId"
                            placeholder="请选择咨询分类"
                            filterable
                            clearable
                            @change="fetchData"
                        >
                            <el-option
                                v-for="item in tagListOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            ></el-option>
                        </el-select>

                        <el-input
                            v-model="form.keyword"
                            placeholder="请输入沟通信息关键词"
                            @change="fetchData"
                        ></el-input>

                        <oa-organ-select
                            v-model="form.clinicId"
                            style="min-width: 260px;"
                            @change="fetchData"
                        ></oa-organ-select>

                        <el-select
                            v-model="form.auditLevel"
                            placeholder="请选择审核等级"
                            filterable
                            clearable
                            @change="fetchData"
                        >
                            <el-option
                                v-for="item in AUDIT_LEVEL_OPTIONS"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>

                        <el-select
                            v-model="form.serviceScore"
                            placeholder="请选择客户满意等级"
                            filterable
                            clearable
                            multiple
                            collapse-tags
                            @change="fetchData"
                        >
                            <el-option
                                v-for="item in SERVICE_SCORE_OPTIONS"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                                :type="item.type"
                            >
                            </el-option>
                        </el-select>

                        <el-date-picker
                            v-model="form.range"
                            type="daterange"
                            :clearable="false"
                            start-placeholder="开始时间"
                            end-placeholder="结束时间"
                            format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD"
                            :disabled-date="disabledDate"
                            @change="fetchData"
                        >
                        </el-date-picker>

                        <el-button type="primary" @click="fetchData">搜索</el-button>
                    </el-space>
                </layout-page-element>
            </template>
            <layout-page-element>
                <oa-table ref="oaTableRef" :columns="clinicIdFromUrl ? TABLE_COLUMNS_V2 : TABLE_COLUMNS" :get-data="fetchRecordList">
                    <template #operate="{column, row}">
                        <el-space>
                            <el-button @click="onHandleView(column, row)">详情</el-button>
                            <el-button v-if="isShowAudit" type="primary" @click="onHandleAudit(column, row)">审核</el-button>
                            <el-button type="primary" :loading="row.isAiSummaryLoading" @click="onHandleAISummary(column, row)">AI总结</el-button>
                        </el-space>
                    </template>
                    <template #servicerId="{column, row}">
                        {{ formatServicer(row[column.prop]) }}
                    </template>
                    <template #accountId="{column, row}">
                        {{ formatAccountId(row[column.prop]) }}
                    </template>
                    <template #formatTime="{column, row}">
                        {{ useFormat(row[column.prop],'YYYY-MM-DD HH:mm:ss') }}
                    </template>
                    <template #serviceScore="{column, row}">
                        <el-tag :type="formatServiceScore(row[column.prop] || 0).type">
                            {{ formatServiceScore(row[column.prop] || 0).label }}
                        </el-tag>
                    </template>
                    <template #auditResults="{column, row}">
                        <el-tag
                            v-if="!row[column.prop] ||
                                !row[column.prop].length"
                            type="info"
                        >
                            未审核
                        </el-tag>
                        <div v-else class="audit-level-tags">
                            <template v-for="status in row[column.prop]">
                                <el-tag v-if="status" :key="status" :type="formatAuditLevel(status)?.type">
                                    {{ formatAuditLevel(status)?.label }}
                                </el-tag>
                            </template>
                        </div>
                    </template>
                    <template #tagName="{column, row}">
                        <el-tooltip
                            effect="dark"
                            :popper-options="{
                                modifiers: [
                                    {
                                        name: 'flip',
                                        enabled: true,
                                        options: {
                                            fallbackPlacements: ['top', 'right', 'left', 'bottom'],
                                        }
                                    }
                                ]
                            }"
                            trigger="click"
                        >
                            <template #default>
                                <span class="truncate-text">{{ row[column.prop] }}</span>
                            </template>
                            <template #content>
                                {{ row[column.prop] }}
                            </template>
                        </el-tooltip>
                    </template>
                    <template #coreQuestion="{column, row}">
                        <el-tooltip
                            effect="dark"
                            :popper-options="{
                                modifiers: [
                                    {
                                        name: 'flip',
                                        enabled: true,
                                        options: {
                                            fallbackPlacements: ['top', 'right', 'left', 'bottom'],
                                        }
                                    }
                                ]
                            }"
                            trigger="click"
                        >
                            <template #default>
                                <span class="truncate-text">{{ row[column.prop] }}</span>
                            </template>
                            <template #content>
                                <span v-html="row['replaceCoreQuestion']"></span>
                            </template>
                        </el-tooltip>
                    </template>
                </oa-table>
            </layout-page-element>
        </layout-page-main>
    </layout-page-container>
    <after-sales-detail
        :id="currentRows.id"
        v-model:visible="visibleDetailDialog"
        :conversation-id="currentRows.conversationId"
        :servicer-id="currentRows.servicerId"
        :current-rows="currentRows"
        :is-audit="visibleDetailAudit"
        @refresh="fetchData"
    ></after-sales-detail>
</template>
<style lang="scss">
@use "@/style/mixins/mixins.scss" as mixins;

.communication-records__wrapper {
    .truncate-text {
        cursor: pointer;

        @include mixins.text-ellipsis();
    }
}
</style>
