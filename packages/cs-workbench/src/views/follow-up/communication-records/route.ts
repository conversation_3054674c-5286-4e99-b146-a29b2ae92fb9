import { RouteRecordRaw } from 'vue-router';
import { PermissionAfterSalesView } from './permission.ts';

const Index = () => import('./index.vue');

export default [
    {
        path: 'communication-records',
        name: '@communication-records',
        component: Index,
        meta: {
            name: '咨询记录',
            icon: 'ChatLineSquare',
            sort: 101,
            roles: PermissionAfterSalesView,
            hidden: true,
        },
    },
] as RouteRecordRaw[];
