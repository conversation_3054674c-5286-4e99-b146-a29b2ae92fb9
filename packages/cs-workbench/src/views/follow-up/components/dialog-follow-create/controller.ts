/*
 * <AUTHOR>
 * @DateTime 2023-11-14 14:49:05
 */
import { reactive, computed, ref } from 'vue';
import { createLoadingModel, createPageModel, createDialogModel } from '@abc-oa/social';
import { CrmClientApi } from '@/api/crm-client-api.ts';
// @ts-ignore
import { ElMessage } from 'element-plus';
import { validateMobilePhone } from '@abc-oa/utils';
import { NodeTypeFilter } from '@/utils';

export const createClassifyListController = () => {
    const loadingModel = createLoadingModel();
    const pageModel = createPageModel();
    const dialogModelRemarkEdit = createDialogModel();

    const toolsParams = reactive({
        keyword: '',
        employeeId: '',
        openId: '',
        chainId: '',
        isAdmin: undefined,
        nodeTypeFilter: undefined,
        isTrial: undefined,
    });
    const tableColumns = reactive([
        { prop: 'clinicName', name: '诊所名称', 'min-width': 120, align: 'center', fixed: 'left' },
        { prop: 'clinicShortName', name: '诊所简称', 'min-width': 120, align: 'center' },
        { prop: 'chainName', name: '总部名称', 'min-width': 120, align: 'center' },
        { prop: 'chainShortName', name: '总部简称', 'min-width': 120, align: 'center' },
        { prop: 'nodeTypeName', name: '门店类型', 'min-width': 120, align: 'center' },
        { prop: 'employeeName', name: '用户名称', 'min-width': 160, align: 'center', slot: 'employeeName' },
        { prop: 'sellerName', name: '客户经理', 'min-width': 120, align: 'center' },
        { prop: 'hisType', name: '诊所类型', 'min-width': 120, align: 'center', slot: 'hisType' },
        { prop: 'editionId', name: '诊所版本', 'min-width': 120, align: 'center', slot: 'editionId' },
        { prop: 'isTrial', name: '是否为试用门店', 'min-width': 140, align: 'center', slot: 'isTrial' },
        { prop: 'organEnv', name: '运行环境', 'min-width': 100, align: 'center', slot: 'organEnv' },
        { prop: 'employeeOpenId', name: 'openId', 'min-width': 180, align: 'center', slot: 'employeeOpenId' },
        { prop: 'employeeMobile', name: '电话', 'min-width': 180, align: 'center', slot: 'employeeMobile' },
        { prop: 'orderEditionCreatedDate:', name: '门店启用时间', 'min-width': 180, align: 'center', slot: 'orderEditionCreatedDate' },
        { prop: 'editionEndDate', name: '门店到期时间', 'min-width': 180, align: 'center', slot: 'editionEndDate' },
        { prop: 'operate', name: '操作', 'min-width': 120, align: 'center', slot: 'operate', fixed: 'right' },
    ]);
    const tableData = ref([]);
    // 展示的数据列表
    const showDataList = computed(() => (tableData.value));
    const parseKeyword = (keyword: string): {
        mobile: string;
        name: string;
        id: string;
    } => {
        // 判定keyword为手机号、门店名称、门店ID
        const result = {
            mobile: '',
            name: '',
            id: '',
        };
        if (validateMobilePhone(keyword)) {
            result.mobile = keyword;
        } else if (/[\u4e00-\u9fa5]/.test(keyword)) {
            result.name = keyword;
        } else {
            result.id = keyword;
        }
        return result;
    };
    async function fetchTableData() {
        const { offset, limit } = pageModel.createFetchParams();
        let res: any = {};
        loadingModel.loading.value = true;
        const { mobile, name: clinicName, id: clinicId } = parseKeyword(toolsParams.keyword);
        try {
            res = await CrmClientApi.getApiLowCodeCrmClinicInfo(
                toolsParams.openId,
                clinicId,
                clinicName,
                toolsParams.chainId,
                toolsParams.employeeId as any,
                mobile,
                toolsParams.isAdmin,
                NodeTypeFilter.NOT_SINGLE_HEAD,
                toolsParams.isTrial,
                limit,
                offset,
            );
        } catch (e: any) {
            ElMessage({
                type: 'error',
                message: e.message || e,
            });
        } finally {
            loadingModel.loading.value = false;
        }
        if (res?.rows) {
            tableData.value = res.rows;
            pageModel.setTotal(res.total);
        }
    }
    return {
        loadingModel,
        tableColumns,
        pageModel,
        dialogModelRemarkEdit,
        toolsParams,
        showDataList,
        fetchTableData,
    };
};
