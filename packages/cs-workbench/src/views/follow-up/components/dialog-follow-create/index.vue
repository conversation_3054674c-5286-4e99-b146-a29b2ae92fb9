<template>
    <el-dialog
        v-model="isShowDialogFollowCreate"
        title="新增跟进"
        custom-class="home-page__dialog-follow-create"
    >
        <el-space class="tools-wrapper">
            <el-input
                v-model="toolsParams.keyword"
                :suffix-icon="Search"
                placeholder="门店ID/门店名称/手机号"
                clearable
                @change="fetchTableData"
            />
            <el-input
                v-model="toolsParams.chainId"
                :suffix-icon="Search"
                placeholder="连锁ID"
                clearable
                @change="fetchTableData"
            />
            <oa-employee-select
                v-model="toolsParams.employeeId"
                @change="fetchTableData"
            ></oa-employee-select>
            <el-input
                v-model="toolsParams.openId"
                :suffix-icon="Search"
                placeholder="openId"
                clearable
                @change="fetchTableData"
            />
            <el-select
                v-model="toolsParams.isAdmin"
                clearable
                placeholder="是否为管理员"
                @change="fetchTableData"
            >
                <el-option label="是" :value="1"></el-option>
                <el-option label="否" :value="0"></el-option>
            </el-select>
            <el-select
                v-model="toolsParams.isTrial"
                clearable
                placeholder="是否为试用"
                @change="fetchTableData"
            >
                <el-option label="是" :value="1"></el-option>
                <el-option label="否" :value="0"></el-option>
            </el-select>
        </el-space>
        <el-table
            v-loading="loadingModel.loading.value"
            :data="showDataList"
            :height="530"
            style="width: 100%; margin: 16px 0;"
        >
            <el-table-column
                v-for="column in tableColumns"
                :key="column.prop"
                :prop="column.prop"
                :label="column.name"
                :min-width="column['min-width']"
                :fixed="column.fixed"
                show-overflow-tooltip
            >
                <template #default="{row}">
                    <template v-if="column.slot === 'hisType'">
                        {{ formatHisTypeName(row.hisType) }}
                    </template>
                    <template v-else-if=" column.slot==='editionId'">
                        {{
                            formatEdition(row.editionId)
                        }}
                    </template>
                    <template v-else-if="column.slot === 'employeeName'">
                        <el-tag
                            v-if="row.roleId=== 1"
                            style="margin-right: 4px;"
                            effect="dark"
                            type="danger"
                            size="small"
                        >
                            管
                        </el-tag>
                        <span>
                            {{ row.employeeName || '' }}
                            {{ row.employeeWechatName ? `(${row.employeeWechatName })` : '' }}
                        </span>
                    </template>
                    <template v-else-if="column.slot === 'employeeOpenId'">
                        <el-link
                            type="primary"
                            target="_blank"
                            @click="quickLoginAbc({ openId: row.employeeOpenId })"
                        >
                            {{ row.employeeOpenId }}
                        </el-link>
                    </template>
                    <template v-else-if="column.slot === 'employeeMobile'">
                        <el-link
                            type="primary"
                            target="_blank"
                            @click="quickLoginAbc({ openId: row.employeeMobile })"
                        >
                            {{ row.employeeMobile }}
                        </el-link>
                    </template>
                    <template v-else-if="column.slot === 'orderEditionCreatedDate'">
                        <span>{{ row.orderEditionCreatedDate ? dayjs(row.orderEditionCreatedDate).format('YYYY-MM-DD') : '' }}</span>
                    </template>
                    <template v-else-if="column.slot === 'editionEndDate'">
                        <span>{{ row.editionEndDate ? dayjs(row.editionEndDate).format('YYYY-MM-DD') : '' }}</span>
                    </template>
                    <template v-else-if="column.slot === 'organEnv'">
                        <span>{{ row.regionId }}-{{ row.organEnv }}</span>
                    </template>
                    <template v-else-if="column.slot === 'isTrial'">
                        <span>{{ row.isTrial === 1 ? '是' : row.isTrial === 0 ? '否' : '' }}</span>
                    </template>
                    <template v-else-if="column.slot === 'operate'">
                        <el-button 
                            type="primary" 
                            plain 
                            :loading="loadingRow === row.id" 
                            @click="handleSelect(row)"
                        >
                            跟进
                        </el-button>
                    </template>
                    <template v-else>{{ row[column.prop] }}</template>
                </template>
            </el-table-column>
        </el-table>
        <!-- <el-pagination
            v-model:current-page="pageModel.params.page"
            :page-size="pageModel.params.pageSize"
            :total="pageModel.params.total"
            background
            layout="total, prev, pager, next"
            @current-change="fetchTableData"
        /> -->
    </el-dialog>
</template>

<script lang="ts" setup>
import { Search } from '@element-plus/icons-vue';
import { createClassifyListController } from './controller';
import { computed, ref } from 'vue';
import { formatEdition, formatHisTypeName } from '@abc-oa/utils';
import dayjs from 'dayjs';
import { OaEmployeeSelect } from '@abc-oa/components';
// @ts-ignore
import { ElMessage } from 'element-plus';
import { quickLoginAbc } from '@abc-oa/utils/src/utils.ts';

const props = defineProps({
    modelValue: {
        type: Number,
        default: 0,
    },
});

const $emit = defineEmits([
    'update:modelValue', // 更新显示状态
    'select', // 选择
]);

const isShowDialogFollowCreate = computed({
    get() {
        return !!props.modelValue;
    },
    set(val:boolean) {
        $emit('update:modelValue', +val);
    },
});

const {
    loadingModel,
    pageModel,
    toolsParams,
    showDataList,
    tableColumns,
    fetchTableData,
} = createClassifyListController();

// 记录当前正在加载的行ID
const loadingRow = ref<number | string | null>(null);

// 处理选择按钮点击
const handleSelect = (row: any) => {
    // 如果已经在加载中，不允许再次点击
    if (loadingRow.value !== null) return;
    
    // 设置当前行为加载状态
    loadingRow.value = row.id;
    
    // 延迟发送事件，模拟请求过程
    setTimeout(() => {
        $emit('select', row);
        // 请求完成后，重置加载状态
        loadingRow.value = null;
    }, 800); // 添加延迟，防止频繁点击
};

const hisDomain = import.meta.env.VITE_APP_HIS_DOMAIN; </script>

<style lang="scss">
    .home-page__dialog-follow-create {
        width: 1200px !important;

        .el-dialog__body {
            padding-top: 8px;
        }

        .tools-wrapper {
            .search-wrapper {
                width: 320px;
            }
        }

        .align-center {
            display: flex;
            align-items: center;
        }

        .explain-wording {
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: keep-all;
            white-space: nowrap;
        }

        .text-zhan {
            display: inline-block;
            min-width: 50px;
        }

        .el-pagination {
            justify-content: flex-end;
        }
    }
</style>
