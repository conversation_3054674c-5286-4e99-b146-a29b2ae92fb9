/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import { reactive, computed } from 'vue';
import { createLoadingModel, createPageModel, createDialogModel } from '@abc-oa/social';

export const createWorkOrderController = () => {
    const dialogModelFeature = createDialogModel();
    const loadingModelSearch = createLoadingModel();
    const loadingModelTarget = createLoadingModel();
    const pageModel = createPageModel();

    // 请求参数
    const toolsParams = reactive({
        keyword: '', // 搜索关键词
        type: '', // 成员类型
        offset: 0,
        limit: 100,
    });

    // 请求返回数据
    const queryResponse = reactive({
        searchResult: <any> null,
        timelineData: <any> null,
        selectedItem: <any> null,
    });

    // 成员类型选项
    const typeOptions = [
        { value: '1', label: '全部角色' },
        { value: '2', label: '医师' },
        { value: '3', label: '护士' },
        { value: '4', label: '检验技师' },
        { value: '5', label: '理疗师' },
        { value: '6', label: '医助' },
        { value: '7', label: '检查技师' },
        { value: '8', label: '管理员' },
        { value: '9', label: '其他' },
    ];

    // 展示数据列表
    const showDataList = computed(() => ([
        {
            employeeName: '唐启涛',
            roles: '管理员，医生',
            departments: '中医科',
            employeeMobile: '18190004085',
            nationalDoctorCode: 'D1123891231',
            openId: 'ogT1C04u0iZThDPaUfsKWPgTkkI4',
            isAdmin: true,
            modules: '所有模块',
        },
        {
            employeeName: '唐启涛',
            roles: '管理员，医生',
            departments: '中医科',
            employeeMobile: '18190004085',
            nationalDoctorCode: 'D1123891231',
            openId: 'ogT1C04u0iZThDPaUfsKWPgTkkI4',
            isAdmin: true,
            modules: '所有模块',
        },
        {
            employeeName: '唐启涛',
            roles: '管理员，医生',
            departments: '中医科',
            employeeMobile: '18190004085',
            nationalDoctorCode: 'D1123891231',
            openId: 'ogT1C04u0iZThDPaUfsKWPgTkkI4',
            isAdmin: true,
            modules: '所有模块',
        },
        {
            employeeName: '唐启涛',
            roles: '管理员，医生',
            departments: '中医科',
            employeeMobile: '18190004085',
            nationalDoctorCode: 'D1123891231',
            openId: 'ogT1C04u0iZThDPaUfsKWPgTkkI4',
            isAdmin: true,
            modules: '所有模块',
        },
        {
            employeeName: '唐启涛',
            roles: '管理员，医生',
            departments: '中医科',
            employeeMobile: '18190004085',
            nationalDoctorCode: 'D1123891231',
            openId: 'ogT1C04u0iZThDPaUfsKWPgTkkI4',
            isAdmin: true,
            modules: '所有模块',
        },
        {
            employeeName: '唐启涛',
            roles: '管理员，医生',
            departments: '中医科',
            employeeMobile: '18190004085',
            nationalDoctorCode: 'D1123891231',
            openId: 'ogT1C04u0iZThDPaUfsKWPgTkkI4',
            isAdmin: true,
            modules: '所有模块',
        },
        {
            employeeName: '唐启涛',
            roles: '管理员，医生',
            departments: '中医科',
            employeeMobile: '18190004085',
            nationalDoctorCode: 'D1123891231',
            openId: 'ogT1C04u0iZThDPaUfsKWPgTkkI4',
            isAdmin: true,
            modules: '所有模块',
        },
        {
            employeeName: '唐启涛',
            roles: '管理员，医生',
            departments: '中医科',
            employeeMobile: '18190004085',
            nationalDoctorCode: 'D1123891231',
            openId: 'ogT1C04u0iZThDPaUfsKWPgTkkI4',
            isAdmin: true,
            modules: '所有模块',
        },
        {
            employeeName: '唐启涛',
            roles: '管理员，医生',
            departments: '中医科',
            employeeMobile: '18190004085',
            nationalDoctorCode: 'D1123891231',
            openId: 'ogT1C04u0iZThDPaUfsKWPgTkkI4',
            isAdmin: true,
            modules: '所有模块',
        },
        {
            employeeName: '唐启涛',
            roles: '管理员，医生',
            departments: '中医科',
            employeeMobile: '18190004085',
            nationalDoctorCode: 'D1123891231',
            openId: 'ogT1C04u0iZThDPaUfsKWPgTkkI4',
            isAdmin: true,
            modules: '所有模块',
        },
    ]));

    return {
        dialogModelFeature,
        loadingModelSearch,
        loadingModelTarget,
        pageModel,
        toolsParams,
        queryResponse,
        typeOptions,
        showDataList,
    };
};