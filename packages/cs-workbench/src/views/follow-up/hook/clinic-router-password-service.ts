import { ref } from 'vue';
import { CrmClientApi } from '@/api/crm-client-api.ts';
import { ElMessage } from 'element-plus';
import { createCache } from '@abc-oa/utils/src/cache.ts';

const clinicCache = createCache({
    max: 20,
});

interface ClinicRouterPasswordService {
    clinicRouterPasswordInfo: any;
    isLoading: any;
    fetchClinicRouterPasswordInfo: (clinicId: string) => Promise<void>;
    destroy: () => void;
}

let instance: ClinicRouterPasswordService | null = null;
export const useClinicRouterPasswordService = () => {
    if (instance) {
        return instance;
    }
    const clinicRouterPasswordInfo = ref({});
    const isLoading = ref(false);

    async function fetchClinicRouterPasswordInfo(clinicId: string) {
        isLoading.value = true;
        try {
            // 优先从缓存取
            const cacheKey = `${clinicId}-router-password`;
            const cacheData = clinicCache.get(cacheKey);
            if (cacheData) {
                clinicRouterPasswordInfo.value = cacheData;
                return;
            }
            const res: any = await CrmClientApi.getApiLowCodeCrmDeviceInfo(clinicId);
            clinicCache.set(cacheKey, res?.[0] || []);
            clinicRouterPasswordInfo.value = res?.[0] || [];
        } catch (e) {
            ElMessage.error('获取诊所前置机密码失败', e);
        } finally {
            isLoading.value = false;
        }
    }

    const destroy = () => {
        instance = null;
    };

    instance = {
        clinicRouterPasswordInfo,
        isLoading,
        fetchClinicRouterPasswordInfo,
        destroy,
    };
    return instance;
};
