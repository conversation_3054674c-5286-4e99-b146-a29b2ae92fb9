import { ref } from 'vue';
import { ClinicTicketAPI } from '@/api/clinic-ticket-api';
import { ElMessage } from 'element-plus';

interface ClinicTicketService {
    clinicTicketsProcessingCount: any;
    isLoading: any;
    fetchClinicTicketsProcessingCount: (clinicId: string) => Promise<void>;
    destroy: () => void;
}

let instance: ClinicTicketService | null = null;
export const useClinicTicketService = () => {
    if (instance) {
        return instance;
    }
    const isLoading = ref(false);
    const clinicTicketsProcessingCount = ref(0);

    async function fetchClinicTicketsProcessingCount(clinicId: string) {
        isLoading.value = true;
        try {
            const res: any = await ClinicTicketAPI.processingCountUsingGET(clinicId);
            clinicTicketsProcessingCount.value = res?.rows.reduce((acc: any, item: any) => acc + item.count, 0) ?? 0;
        } catch (e: any) {
            ElMessage.error('获取未完成工单数量失败', e);
        } finally {
            isLoading.value = false;
        }
    }

    const destroy = () => {
        instance = null;
    };

    instance = {
        clinicTicketsProcessingCount,
        isLoading,
        fetchClinicTicketsProcessingCount,
        destroy,
    };
    return instance;
};
