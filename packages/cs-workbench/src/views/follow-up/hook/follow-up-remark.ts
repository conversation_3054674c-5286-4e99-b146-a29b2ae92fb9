import { computed, ComputedRef, ref, Ref, watch } from 'vue';
import { useLayoutQuickListHook } from '@/views/follow-up/hook/quick-list.ts';
import { SupportClinicFollowRecordAPI } from '@/api/support-clinic-follow-record-api.ts';
// @ts-ignore
import { ElMessage } from 'element-plus';

interface TodoRemark {
    id: string,
    time: string,
    remark: string
}
interface RecordItem {
    id: string
    remark: string
    status: number
    createTime: string
}
interface FollowUpRemark {
    followRemarkRef: (el: any) => void;
    currentRemark: Ref<TodoRemark>;
    remarkModelValue: Ref<string>;
    recordList: Ref<RecordItem[]>;
    handleRemarkSubmit: (remark: string) => void;
    handleRemarkEdit: (remark?: string) => void;
    handleRemarkEditCancel: () => void;
    handleRemarkFinish: (todoId?: string) => void;
    handleRemarkDeleted: (todoId?: string) => void;
    fetchRecordList: () => void;
    remarkEditing: Ref<boolean>;
    showRemarkSign: ComputedRef<boolean>;
    showRemarkSignItem: (item: any) => boolean;
    destroy: () => void;
}

let instance: FollowUpRemark;
export const useFollowUpRemark = (): FollowUpRemark => {
    if (instance) return instance;

    const { currentFollowRecord, fetchFollowRecord } = useLayoutQuickListHook();

    // 当前跟进列表生效的备注
    const currentRemark = ref<TodoRemark>({
        id: '',
        time: '',
        remark: '',
    });
    // 备注编辑框的值
    const remarkModelValue = ref<string>('');
    // 是否正在编辑备注
    const remarkEditing = ref<boolean>(false);
    // 是否显示备注标记
    // const showRemarkSign = computed(() => !!currentRemark.value.remarkStatus);
    const showRemarkSign = computed(() => !!currentRemark.value.remark);
    // 记录列表
    const recordList = ref<RecordItem[]>([]);
    // 获取焦点
    const followRemarkRef = (el: any) => {
        el && el.focus();
    };
    watch(currentFollowRecord, (record) => {
        if (!record) {
            currentRemark.value = {
                time: '',
                remark: '',
                id: '',
            };
            recordList.value = [];
            return;
        }
        currentRemark.value = {
            time: record.todoUpdateTime,
            remark: record.todoRemark,
            id: record.todoId,
        };
        fetchRecordList();
    }, {
        immediate: true,
        deep: true,
    });

    /**
     * @description: 销毁实例
     * @date: 2024-12-10 16:34:34
     * @author: Horace
     * @return
    */
    function destroy() {
        // @ts-ignore
        instance = null;
    }
    /**
     * @description: 处理备注编辑
     * @date: 2024-12-10 16:34:46
     * @author: Horace
     * @param {string} remark 备注
     * @return
   */
    function handleRemarkEdit(remark?: string) {
        remarkModelValue.value = remark || currentRemark.value.remark;
        remarkEditing.value = true;
    }
    /**
     * @description: 处理备注编辑取消
     * @date: 2024-12-10 16:35:43
     * @author: Horace
     * @return
    */
    function handleRemarkEditCancel() {
        remarkModelValue.value = '';
        remarkEditing.value = false;
    }
    /**
     * @description: 处理备注提交
     * @date: 2024-12-10 16:35:54
     * @author: Horace
     * @param {string} remark 备注
     * @return
    */
    async function handleRemarkSubmit(remark: string) {
        let res: any = {};
        try {
            res = await SupportClinicFollowRecordAPI.upsertTodoUsingPOST({
                id: currentFollowRecord.value.id,
                todoRemark: remark,
            });
        } catch (e: any) {
            ElMessage.error(e.message || e);
        }
        if (res?.code === 200) {
            await fetchFollowRecord();
            handleRemarkEditCancel();
        }
    }
    /**
     * @description: 处理备注完成
     * @date: 2024-12-10 16:36:23
     * @author: Horace
     * @param {string} todoId 待办id
     * @return
    */
    async function handleRemarkFinish(todoId?: string) {
        let res: any = {};
        try {
            res = await SupportClinicFollowRecordAPI.completedTodoUsingPUT(todoId || currentRemark.value.id);
        } catch (e: any) {
            ElMessage.error(e.message || e);
        }
        if (res?.code === 200) {
            await fetchFollowRecord();
            ElMessage.success('已完成待办！');
        }
    }
    /**
     * @description: 处理备注删除
     * @date: 2024-12-10 16:36:38
     * @param {string} todoId 待办id
     * @return
    */
    async function handleRemarkDeleted(todoId?: string) {
        let res: any = {};
        try {
            res = await SupportClinicFollowRecordAPI.deleteTodoUsingDELETE(todoId || currentRemark.value.id);
        } catch (e: any) {
            ElMessage.error(e.message || e);
        }
        if (res?.code === 200) {
            await fetchFollowRecord();
            ElMessage.success('已删除待办！');
        }
    }
    /**
     * @description: 显示备注标记
     * @date: 2024-12-10 16:36:46
     * @author: Horace
     * @param {any} item
     * @return
    */
    function showRemarkSignItem(item: any) {
        return !!item.todoRemark;
    }
    /**
     * @description: 获取记录列表
     * @date: 2024-12-10 16:36:54
     * @author: Horace
     * @return
    */
    async function fetchRecordList() {
        let res: any = {};
        try {
            res = await SupportClinicFollowRecordAPI.pageTodoListUsingGET(currentFollowRecord.value.id);
        } catch (error) {
            console.error('获取记录列表失败:', error);
        }
        if (res) {
            recordList.value = res.rows?.map((item: any) => ({
                id: item.id,
                remark: item.remark,
                status: item.status,
                created: item.todoUpdateTime,
            })) || [];
        }
    }

    instance = {
        currentRemark,
        remarkModelValue,
        remarkEditing,
        showRemarkSign,
        recordList,
        followRemarkRef,
        handleRemarkEdit,
        handleRemarkEditCancel,
        handleRemarkDeleted,
        handleRemarkSubmit,
        handleRemarkFinish,
        showRemarkSignItem,
        fetchRecordList,
        destroy,
    };
    return instance;
};
