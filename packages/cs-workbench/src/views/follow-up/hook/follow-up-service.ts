import logger from '@/common/logger.ts';
import { ref } from 'vue';
import { SupportClinicFollowRecordAPI } from '@/api/support-clinic-follow-record-api.ts';
import { useRouter } from 'vue-router';
import { FollowUpRouteKey } from '@/views/follow-up/route.ts';

interface FollowUpService {
    followUpRecordList: any;
    addFollowUpRecord: (record: any) => any;
    fetchFollowUpRecordList: (keyword) => Promise<void>;
    navigateToFollowUpRecordDetail: (record: any) => void;
}
let instance: FollowUpService;
export const useFollowUpService = () => {
    if (instance) return instance;

    const router = useRouter();
    const followUpRecordList = ref<any[]>([]);

    async function addFollowUpRecord(record) {
        logger.info('addFollowUpRecord', record);
        await SupportClinicFollowRecordAPI.upsertUsingPOST(record);
        await fetchFollowUpRecordList();
        return record;
    }

    function updateFollowUpRecordList(list) {
        followUpRecordList.value = list;
    }

    function createFetchParams(keyword = '') {
        const fetchParams = {
            keyword,
            limit: 200,
            offset: 0,
        };
        return fetchParams;
    }

    async function fetchFollowUpRecordList(keyword = '') {
        const params = createFetchParams(keyword);
        const fetchResponse = await SupportClinicFollowRecordAPI.pageListUsingGET(params.keyword, params.limit, params.offset);
        const dataList = fetchResponse.rows || [];
        updateFollowUpRecordList(dataList);
        return fetchResponse;
    }

    function navigateToFollowUpRecordDetail(record) {
        return router.push({
            name: FollowUpRouteKey.Main,
            params: {
                id: `${record.clinicId}_${record.employeeId}`,
            },
        });
    }

    instance = {
        followUpRecordList,
        fetchFollowUpRecordList,
        addFollowUpRecord,
        navigateToFollowUpRecordDetail,
    };
    return instance;
};
