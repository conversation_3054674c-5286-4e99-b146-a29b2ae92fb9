import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import { SupportClinicFollowRecordAPI } from '@/api/support-clinic-follow-record-api.ts';

interface HistoricalDemandService {
    historicalDemandInfo: any;
    isLoading: any;
    fetchHistoricalDemandInfo: (clinicId: string) => Promise<void>;
    destroy: () => void;
}

let instance: HistoricalDemandService | null = null;
export const useHistoricalDemandService = () => {
    if (instance) {
        return instance;
    }
    const historicalDemandInfo: any = ref([]);
    const isLoading = ref(false);

    async function fetchHistoricalDemandInfo(clinicId: string) {
        isLoading.value = true;
        try {
            const res: any = await SupportClinicFollowRecordAPI.getFollowDemandUsingGET(clinicId, 20, 0);
            historicalDemandInfo.value = res.rows || [];
        } catch (e: any) {
            ElMessage.error('获取历史对接需求失败', e);
        } finally {
            isLoading.value = false;
        }
    }

    const destroy = () => {
        instance = null;
    };

    instance = {
        historicalDemandInfo,
        isLoading,
        fetchHistoricalDemandInfo,
        destroy,
    };
    return instance;
};
