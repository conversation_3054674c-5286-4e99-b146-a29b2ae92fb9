<script setup lang="ts">
import { defineAsyncComponent, onUnmounted, ref, watch } from 'vue';

import LayoutPageContainer from '@/layout/components/page-container/layout-page-container.vue';
import { createDialogModel } from '@abc-oa/social';
import DialogFollowCreate from './components/dialog-follow-create/index.vue';
import { useLayoutQuickListHook } from './hook/quick-list.ts';
import { useRouter, useRoute } from 'vue-router';
import { useQiyu } from '@/views/tccc/hooks/useQiyu.ts';
import { FollowUpRouteKey } from '@/views/follow-up/route.ts';
import { useFollowUpService } from '@/views/follow-up/hook/follow-up-service.ts';
import { ElMessage } from 'element-plus';
import { useFollowUpRemark } from '@/views/follow-up/hook/follow-up-remark.ts';

const LayoutQuickListSidebar = defineAsyncComponent(() => import('@/layout/components/quick-list-wrapper/layout-quick-list-sidebar.vue'));

const router = useRouter();
const route = useRoute();

const { isCallingNumber } = useQiyu();
const { showRemarkSignItem, destroy: remarkDestroy } = useFollowUpRemark();

const {
    quickListShow,
    quickListTotal,
    updateKeyword,
    setQuickListSelectedItem,
    createUniqueKey,
    parseUniqueKey,
    fetchFollowRecord,
    destroy,
    showOnlyWithRemark,
    currentFollowRecord,
} = useLayoutQuickListHook();
const {
    addFollowUpRecord,
} = useFollowUpService();
let isInit = true;

// 监听备忘开关
watch(showOnlyWithRemark, async (newVal) => {
    if (newVal && route.params.id) {
        const currentItem = parseUniqueKey(route.params.id as string);
        const currentRecord = quickListShow.value.find(
            (record: any) => record.clinicId === currentItem.clinicId && record.employee.id === currentItem.employeeId,
        );
        
        // 如果当前选中项没有备忘，则切换到第一个有备忘的记录
        if (!currentRecord?.todoRemark) {
            const firstWithRemark = quickListShow.value.find((record: any) => record.todoRemark);
            if (firstWithRemark) {
                const newItem = {
                    clinicId: firstWithRemark.clinicId,
                    employeeId: firstWithRemark.employee.id,
                };
                // 更新路由
                await router.replace({
                    name: FollowUpRouteKey.Main,
                    params: {
                        id: createUniqueKey(newItem),
                    },
                });
            }
        }
    }
});

// 监听备忘删除
watch(() => currentFollowRecord.value?.todoRemark, async (newVal, oldVal) => {
    // 如果之前有备忘，现在没有备忘，说明是删除了备忘
    if (oldVal && !newVal && showOnlyWithRemark.value) {
        // 找到当前项的索引
        const currentIndex = quickListShow.value.findIndex(
            (record: any) => record.clinicId === currentFollowRecord.value.clinicId 
                           && record.employee.id === currentFollowRecord.value.employee.id,
        );
        
        // 找到下一个有备忘的记录
        const nextWithRemark = quickListShow.value.find((record: any, index) => index > currentIndex && record.todoRemark);

        // 如果找到了下一个有备忘的记录，就切换到那个记录
        if (nextWithRemark) {
            const newItem = {
                clinicId: nextWithRemark.clinicId,
                employeeId: nextWithRemark.employee.id,
            };
            // 更新路由
            await router.replace({
                name: FollowUpRouteKey.Main,
                params: {
                    id: createUniqueKey(newItem),
                },
            });
        } else {
            // 如果没有找到下一个有备忘的记录，检查是否还有任何有备忘的记录
            const hasAnyRemark = quickListShow.value.some(record => record.todoRemark);
            if (!hasAnyRemark) {
                // 关闭备忘筛选，但保持当前选中项
                showOnlyWithRemark.value = false;
            }
        }
    }
});

onUnmounted(() => {
    destroy();
    remarkDestroy();
});

watch(() => route.params.id, async (id) => {
    // 判断是否是当前路由
    if (route.matched?.findIndex((item) => item.name === FollowUpRouteKey.Index) === -1) {
        return;
    }
    if (isInit) {
        await fetchFollowRecord();
        isInit = false;
    }
    if (id) {
        setQuickListSelectedItem(parseUniqueKey(id));
    } else {
        const item = quickListShow.value[0];
        if (!item) {
            return;
        }
        // 将 id 设置到 路由的 :id 参数中
        await router.replace({
            name: FollowUpRouteKey.Main,
            params: {
                id: createUniqueKey({
                    clinicId: item.clinicId,
                    employeeId: item.employee.id,
                }),
            },
        });
        setQuickListSelectedItem({
            clinicId: item.clinicId,
            employeeId: item.employee.id,
        });
    }
}, {
    immediate: true,
});

async function handleQuickListItemClick(item: any) {
    await router.push({
        name: FollowUpRouteKey.Main,
        params: {
            id: createUniqueKey({
                clinicId: item.clinicId,
                employeeId: item.employee?.id || item.employeeId,
            }),
        },
    });
}

const dialogModelExplain = createDialogModel();
const handleCreateFollow = async () => {
    dialogModelExplain.show();
};
const handleCreateFollowSubmit = async (item: any) => {
    const data = {
        clinicId: item.clinicId,
        employeeId: item.employeeId,
    };
    if (quickListShow.value.find((i) => i.clinicId === data.clinicId && i.employee.id === data.employeeId)) {
        ElMessage.error('该门店用户已存在跟进记录');
        return;
    }
    await addFollowUpRecord(data);
    dialogModelExplain.hide();
    showOnlyWithRemark.value = false; // 关闭只看备忘
    await handleQuickListItemClick(data);
};

</script>
<template>
    <layout-page-container>
        <template #sidebar>
            <layout-quick-list-sidebar
                :need-create="true"
                :quick-list="quickListShow"
                :quick-list-total="quickListTotal"
                search-placeholder="门店名/成员手机号"
                @search="updateKeyword"
                @create="handleCreateFollow"
                @item-click="handleQuickListItemClick"
            >
                <template #toolbar>
                    <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                        <span>共 {{ quickListTotal }}</span>
                        <el-switch
                            v-model="showOnlyWithRemark"
                            active-text="只看备忘"
                            size="small"
                        />
                    </div>
                </template>
                <template #search-extra>
                </template>
                <template #list-item="{item}">
                    <div class="follow-up-list-item">
                        <div class="line1">
                            <oa-icon icon="fa-solid:clinic-medical" :color="item.isActive ? 'white':'#287ef1'"></oa-icon>
                            <span class="follow-up-list-item-clinic-name">{{ item.clinicName }}</span>
                            <el-tooltip v-if="showRemarkSignItem(item)">
                                <template #content>您有一个待办备注待处理</template>
                                <el-icon class="follow-up-list-item-remark-sign" color="#F56C6C"><BellFilled /></el-icon>
                            </el-tooltip>
                        </div>
                        <div class="line2">
                            <el-space class="name">
                                <span class="employee-name">{{ item.employee.name }}</span>
                                <el-space v-if="item.employee.wechatNickName" :size="2">
                                    <oa-icon icon="mdi:wechat" color="#1fd170"></oa-icon>
                                    <span class="wechat-nickname">{{ item.employee.wechatNickName }}</span>
                                </el-space>
                            </el-space>
                            <el-space>
                                <el-tag
                                    v-if="isCallingNumber(item.employee.mobile)"
                                    class="status"
                                    size="small"
                                    type="success"
                                >
                                    通话中
                                </el-tag>
                                <span class="time">{{ item.lastModifiedWording }}</span>
                            </el-space>
                        </div>
                    </div>
                </template>
            </layout-quick-list-sidebar>
        </template>
        <router-view></router-view>
    </layout-page-container>

    <dialog-follow-create
        v-if="dialogModelExplain.visible.value"
        v-model="dialogModelExplain.visible.value"
        @select="handleCreateFollowSubmit"
    ></dialog-follow-create>
</template>

<style lang="scss">
@use "@/style/mixins/mixins.scss" as mixins;

.follow-up-list-item {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 4px;

    .line1 {
        display: flex;
        align-items: center;
        gap: 4px;

        .follow-up-list-item-remark-sign {
            max-width: 24px;
            margin-right: 8px;
        }

        .follow-up-list-item-clinic-name {
            flex: 1;
        }
    }

    .line2 {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .employee-name {
            flex: 1;

            @include mixins.text-ellipsis(1);
        }

        .wechat-nickname {
            font-size: 13px;
            align-items: center;
            max-width: 100px;

            @include mixins.text-ellipsis(1);
        }
    }
}
</style>
