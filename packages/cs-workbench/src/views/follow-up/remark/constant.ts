export enum RemarkStatusEnum {
    Processing = 0,
    Processed = 10,
    Cancel = 90,
    Deleted = 99,
}
export const RemarkStatus = {
    [RemarkStatusEnum.Processing]: '处理中',
    [RemarkStatusEnum.Processed]: '已处理',
    [RemarkStatusEnum.Cancel]: '已取消',
    [RemarkStatusEnum.Deleted]: '已删除',
};

export function getRemarkStatusText(status: RemarkStatusEnum) {
    return RemarkStatus[status];
}
export function getRemarkStatusType(status: RemarkStatusEnum) {
    switch (status) {
        case RemarkStatusEnum.Processing:
            return 'primary';
        case RemarkStatusEnum.Processed:
            return 'success';
        case RemarkStatusEnum.Cancel:
            return 'info';
        case RemarkStatusEnum.Deleted:
            return 'info';
        default:
            return 'info';
    }
}
