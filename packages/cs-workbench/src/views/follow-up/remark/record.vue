<script lang="ts" setup>
import { onMounted } from 'vue';
// @ts-ignore
import { ElTimeline, ElTimelineItem } from 'element-plus';
import dayjs from 'dayjs';
import { getRemarkStatusText, getRemarkStatusType, RemarkStatusEnum } from '@/views/follow-up/remark/constant.ts';
import { useFollowUpRemark } from '@/views/follow-up/hook/follow-up-remark.ts';
import CreateRemark from '@/views/follow-up/remark/create.vue';

const {
    recordList,
    remarkEditing,
    fetchRecordList,
    handleRemarkEdit,
    handleRemarkFinish,
    handleRemarkDeleted,
} = useFollowUpRemark();

onMounted(() => {
    fetchRecordList();
});
</script>

<template>
    <div class="record-timeline">
        <el-timeline v-if="recordList?.length">
            <el-timeline-item
                v-for="(record, index) in recordList"
                :key="record.id"
                :type="getRemarkStatusType(record.status)"
                :hollow="index !== 0"
                :timestamp="dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss')"
                placement="top"
            >
                <div class="record-content">
                    <div class="record-left">
                        <span class="record-text">{{ record.remark }}</span>
                        <div class="record-actions">
                            <el-button
                                link
                                type="primary"
                                @click="handleRemarkEdit(record.remark)"
                            >
                                编辑
                            </el-button>
                            <el-button
                                v-if="record.status === RemarkStatusEnum.Processing"
                                link
                                type="success"
                                @click="handleRemarkFinish(record.id)"
                            >
                                完成
                            </el-button>
                            <el-button
                                v-if="record.status === RemarkStatusEnum.Processing"
                                link
                                type="danger"
                                @click="handleRemarkDeleted(record.id)"
                            >
                                删除
                            </el-button>
                        </div>
                    </div>
                    <el-tag size="small" :type="getRemarkStatusType(record.status)">{{ getRemarkStatusText(record.status) }}</el-tag>
                </div>
            </el-timeline-item>
        </el-timeline>
        <el-empty v-else />
        <create-remark v-if="remarkEditing"></create-remark>
    </div>
</template>

<style lang="scss" scoped>
.record-timeline {
    max-height: 100%;
    overflow-y: auto;
    padding: 16px;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
        width: 6px;
    }

    &::-webkit-scrollbar-thumb {
        background-color: #dcdfe6;
        border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
        background-color: #f5f7fa;
    }

    .record-content {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        padding: 8px 0;

        .record-left {
            flex: 1;
            margin-right: 12px;

            .record-text {
                color: #303133;
                display: block;
                margin-bottom: 8px;
            }

            .record-actions {
                .el-button {
                    padding: 0 4px;
                    font-size: 13px;

                    & + .el-button {
                        margin-left: 8px;
                    }
                }
            }
        }
    }

    :deep(.el-timeline-item__node) {
        &.is-hollow {
            background-color: #fff;
        }
    }

    :deep(.el-timeline-item__timestamp) {
        color: #909399;
        font-size: 13px;
    }
}
</style>
