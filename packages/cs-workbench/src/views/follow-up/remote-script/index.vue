<template>
    <div class="follow-up__remote-script">
        <div class="tools-wrapper">
            <label>远程设备</label>
            <el-select
                v-model="formData.deviceCode"
                placeholder="选择设备码远程执行"
                style="width: 200px;"
                popper-class="follow-up__remote-script__select-device-code"
                @change="onClickSuggestionDevice"
            >
                <el-option
                    v-for="item in deviceCodeOptions"
                    :key="item.identity"
                    :label="item.identity"
                    :value="item.identity"
                >
                    <span class="code">{{ item.identity }}</span>
                    <el-tag
                        v-if="item.employeeInfo"
                        class="status"
                        type="primary"
                    >
                        {{ item.employeeInfo?.name || '' }}
                    </el-tag>
                    <div class="track"></div>
                    <el-tag
                        v-if="item.isSocialDevice"
                        class="status"
                        type="primary"
                    >
                        医保
                    </el-tag>
                    <el-tag
                        class="status"
                        :type="item.statusType"
                    >
                        {{ item.statusWording }}
                    </el-tag>
                </el-option>
            </el-select>
        </div>
        <div
            v-loading="loadingModelTarget.loading.value"
            class="content-wrapper"
        >
            <template v-if="!!target.socialInfo">
                <el-descriptions
                    class="margin-top"
                    title="门店信息"
                    :column="3"
                    border
                >
                    <template #extra>
                        <el-button 
                            type="primary"
                            @click="onClickRefresh"
                        >
                            刷新
                        </el-button>
                    </template>
                    <el-descriptions-item label="医保电脑">
                        <el-tag
                            size="small"
                            :type="displayInfo.socialCom.type"
                        >
                            {{ displayInfo.socialCom.text }}
                        </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="网络模式">{{ displayInfo.networkMode }}</el-descriptions-item>
                    <el-descriptions-item label="系统类型">{{ displayInfo.systemType }}</el-descriptions-item>
                    <el-descriptions-item label="开通状态">
                        <el-tag
                            size="small"
                            :type="displayInfo.openStatus.type"
                        >
                            {{ displayInfo.openStatus.text }}
                        </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="研发人员">{{ displayInfo.engineer }}</el-descriptions-item>
                    <el-descriptions-item label="运行模式">{{ displayInfo.runMode }}</el-descriptions-item>
                    <el-descriptions-item label="所属省级">{{ displayInfo.provinceName }}</el-descriptions-item>
                    <el-descriptions-item label="所属市级">{{ displayInfo.regionName }}</el-descriptions-item>
                    <el-descriptions-item label="经办机构">{{ displayInfo.setlOptins }}</el-descriptions-item>
                    <el-descriptions-item label="机构类型">{{ displayInfo.hospitalType }}</el-descriptions-item>
                    <el-descriptions-item label="机构代码">{{ displayInfo.hospitalCode }}</el-descriptions-item>
                    <el-descriptions-item label="机构名称">{{ displayInfo.hospitalName }}</el-descriptions-item>
                    <el-descriptions-item label="地区标识">{{ displayInfo.region }}</el-descriptions-item>
                    <el-descriptions-item label="协议类型">{{ displayInfo.protocolType }}</el-descriptions-item>
                    <el-descriptions-item label="接口厂商">{{ displayInfo.apiVersion }}</el-descriptions-item>
                </el-descriptions>
                <div v-if="!!target.$national" class="feature-box">
                    <div class="feature-wrapper">
                        <div
                            v-for="item in featureList"
                            :key="item.id"
                            class="item-wrapper"
                        >
                            <span class="name">{{ item.name }}</span>
                            <el-button type="primary" plain @click="onClickOpenFeature(item)">打开功能</el-button>
                        </div>
                    </div>
                </div>
            </template>
            <template v-else>
                <div class="kong">
                    <span>请选择远端设备</span>
                </div>
            </template>
        </div>
        <component
            :is="FeatureComponent"
            v-if="dialogModelFeature.visible.value"
            v-model="dialogModelFeature.visible.value"
            :device-code="deviceCode"
            :social-info="target.socialInfo"
        ></component>
    </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { ElMessage } from 'element-plus';
import { utils, createPageRemoteController } from '@abc-oa/social';
import { createRemoteScriptController } from './controller';
import { useClinicInfoService } from '@/views/follow-up/hook/clinic-info-service.ts';

const {
    formData,
    queryResponse,
    clinicId,
    deviceCodeOptions,
    deviceCodeItem,
    initFormData,
    setClinicInfo,
    requestDeviceCodeData,
    requestSocialDevice,
} = createRemoteScriptController();

const {
    dialogModelFeature,
    loadingModelTarget,
    target,
    deviceCode,
    region,
    featureList,
    displayInfo,
} = createPageRemoteController();

const autocompleteDeviceCodeRef = ref(null);
const FeatureComponent = ref(null);

onMounted(async () => {
    const { clinicInfo } = await useClinicInfoService();
    setClinicInfo(clinicInfo);
    if (clinicId) {
        // 有门店ID，搜索门店下的全部设备码，再去选择
        await fetchDeviceCodeData();
    }
    initFormData();
    onClickSelectRemote();
});

/**
 * 请求设备码列表
 * <AUTHOR>
 * @date 2024-12-25
 */
const fetchDeviceCodeData = async () => {
    const params = {
        clinicId: clinicId.value, // 门店ID
    };
    const response1 = await requestDeviceCodeData(params);
    if (response1.status === false) {
        return;
    }
    queryResponse.originData = response1.data;
    const response2 = await requestSocialDevice(clinicId.value);
    queryResponse.deviceSocial = response2.data?.deviceSocial || [];
};

/**
 * 当点击建议设备时
 * <AUTHOR>
 * @date 2024-09-06
 * @param {Object} item
 */
const onClickSuggestionDevice = async () => {
    if (autocompleteDeviceCodeRef.value) {
        // @ts-ignore
        autocompleteDeviceCodeRef.value.close();
        // @ts-ignore
        autocompleteDeviceCodeRef.value.inputRef.blur();
    }
    onClickSelectRemote();
};

/**
 * 当点击刷新时
 * <AUTHOR>
 * @date 2024-08-14
 */
const onClickRefresh = () => {
    onClickSelectRemote();
};

/**
 * 当点击选择远端时
 * <AUTHOR>
 * @date 2024-08-14
 */
const onClickSelectRemote = async () => {
    if (!deviceCodeItem.value) {
        return;
    }
    target.remoteInfo = null;
    target.socialInfo = null;
    if (deviceCodeItem.value.status === 0) {
        return ElMessage.error('提示: 离线设备不允许选中');
    }
    loadingModelTarget.setLoading(true);
    target.remoteInfo = deviceCodeItem.value;
    const response = await utils.requestSocialInfo(deviceCode.value);
    loadingModelTarget.setLoading(false);
    if (response.status === false) {
        ElMessage.error('拉取数据失败: ' + response.message);
        return response;
    }
    target.socialInfo = response.data;
    target.$national = null;
    const localSocialResponse = await utils.requestLoadSocial(region.value);
    if (localSocialResponse.status === false) {
        ElMessage.error('拉取$national失败: ' + localSocialResponse.message);
        return localSocialResponse;
    }
    target.$national = window.$national;
};

/**
 * 当点击打开功能时
 * <AUTHOR>
 * @date 2024-08-07
 * @param {Object} item
 */
const onClickOpenFeature = (item: any) => {
    if (
        item.checkSocialReg
            && !target.socialInfo.region
    ) {
        return ElMessage.error('功能不可用：当前门店没有region，或地区医保未对接');
    }
    if (
        item.checkSoicalOpe
            && !target.socialInfo.isOpenSocial
    ) {
        return ElMessage.error('功能不可用：当前门店还未开通医保');
    }
    if (
        item.checkSocialCom
            && !target.socialInfo.isSocialCom
    ) {
        return ElMessage.error('功能不可用：当前电脑不是医保电脑');
    }
    FeatureComponent.value = item.Component;
    dialogModelFeature.show();
};
</script>

<style lang="scss">
    .follow-up__remote-script {
        display: flex;
        flex-direction: column;
        align-items: stretch;

        > .tools-wrapper {
            display: flex;
            align-items: center;
            border-radius: 8px;
            padding: 16px;
            background-color: #fff;

            > label {
                font-weight: 600;
                margin-right: 8px;
            }
        }

        .content-wrapper {
            flex: 1;
            background-color: #fff;
            border-radius: 8px;
            padding: 16px;
            min-height: calc(100vh - 282px);
            margin-top: 16px;
            overflow: hidden;

            .kong {
                margin-top: 120px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;

                span {
                    color: #aab4bf;
                    font-size: 14px;
                    line-height: 20px;
                }
            }

            .feature-wrapper {
                display: flex;
                flex-wrap: wrap;
                align-items: center;

                .item-wrapper {
                    width: 192px;
                    padding: 18px 0;
                    margin-right: 16px;
                    margin-top: 16px;
                    border: 1px dashed #dadbe0;
                    border-radius: 2px;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;

                    .name {
                        font-size: 14px;
                        font-weight: bold;
                    }

                    .el-button {
                        margin-top: 12px;
                    }

                    &:hover {
                        background-color: rgba(0, 0, 0, .04);
                    }
                }
            }
        }
    }

    .follow-up__remote-script__select-device-code {
        width: 280px !important;

        .el-select-dropdown__item {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding: 0 12px;

            .code {
                flex-shrink: 0;
                display: inline-block;
                min-width: 52px;
            }

            .track {
                flex: 1;
            }

            .el-tag {
                margin-left: 4px;
            }
        }
    }
</style>
