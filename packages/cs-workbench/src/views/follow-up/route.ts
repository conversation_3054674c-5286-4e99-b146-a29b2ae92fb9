import { RouteRecordRaw } from 'vue-router';
import Blank from '@/views/follow-up/blank.vue';
import Main from '@/views/follow-up/main.vue';
import { PermissionRemotelySet } from '@/views/follow-up/permission.ts';
import CalloutHistoryRoute from './callout-history/route.ts';
import CommunicationRecordsRoute from './communication-records/route.ts';
import OperationRecordsRoute from './operation-records/route.ts';

export enum FollowUpRouteKey {
    Entry = '@follow-up',
    Index = '@follow-up-records',
    Blank = '@follow-up-records-blank',
    Main = '@follow-up-records-main',
}

const Entry = () => import('./entry.vue');
const Records = () => import('./records.vue');

export default [
    {
        path: 'follow-up',
        name: FollowUpRouteKey.Entry,
        component: Entry,
        meta: {
            isEntry: true,
            name: '跟进',
            icon: 'service',
            roles: PermissionRemotelySet,
        },
        redirect: { name: FollowUpRouteKey.Index },
        children: [
            {
                path: 'records',
                name: FollowUpRouteKey.Index,
                component: Records,
                redirect: { name: FollowUpRouteKey.Blank },
                meta: {
                    name: '我的跟进',
                    icon: 'service',
                    roles: PermissionRemotelySet,
                    hidden: true,
                },
                children: [
                    {
                        // 空白路由
                        path: '',
                        name: FollowUpRouteKey.Blank,
                        component: Blank,
                        meta: {
                            name: '空白',
                            hidden: true,
                        },
                    },
                    {
                        path: ':id',
                        name: FollowUpRouteKey.Main,
                        component: Main,
                        meta: {
                            name: '详情',
                            hidden: true,
                        },
                    },
                ],
            },
            ...CalloutHistoryRoute,
            ...CommunicationRecordsRoute,
            ...OperationRecordsRoute,
        ],
    },
] as RouteRecordRaw[];
