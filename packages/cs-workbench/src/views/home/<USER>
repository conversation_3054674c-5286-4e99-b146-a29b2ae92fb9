<script setup lang="ts">
import { useMenuStore } from '@/store/menu';
import { useUserStore } from '@/store/user.ts';
import { computed } from 'vue';
import { useRoute } from 'vue-router';
import { OaCard } from '@abc-oa/components';
import LayoutPageContainer from '@/layout/components/page-container/layout-page-container.vue';
import LayoutPageMain from '@/layout/components/page-container/layout-page-main.vue';
import useGreeting from '@/views/home/<USER>';
import MyTicketList from '@/views/home/<USER>';
import { useMyTicketService } from './hook/my-ticket-service.ts';

const userStore = useUserStore();

const menuStore = useMenuStore();
const { totalRecords: myTicketTotal } = useMyTicketService();
const route = useRoute();
const menus = computed(() => menuStore.menus.filter(menu => menu.name !== route.name));
const { greeting, dateTime } = useGreeting();

</script>
<template>
    <div class="home-page-container">
        <oa-card>
            <h1>
                {{ greeting }}{{ userStore.userInfo.name }}
                <span class="tips">{{ dateTime }}</span>
            </h1>
        </oa-card>

        <oa-card v-if="myTicketTotal" class="my-ticket-card">
            <h1 style="margin-bottom: 8px;">我的待办工单</h1>
            <my-ticket-list></my-ticket-list>
        </oa-card>

        <oa-card class="problem-handling-process-card">
            <h1>问题处理流程</h1>
            <div style="text-align: center; padding: 16px 0 30px 0;">
                <img src="@/assets/imgs/problem-handling-process.png">
            </div>
        </oa-card>

        <oa-card class="value-principles-card">
            <img src="@/assets/imgs/value-principles.png">
        </oa-card>
    </div>
</template>

<style lang="scss">
.home-page-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
    height: 100%;

    .tips {
        font-size: 12px;
        color: var(--oa-text-color-2);
        font-weight: normal;
        margin-left: var(--oa-padding-8);
    }

    .problem-handling-process-card {
        img {
            width: 92%;
        }
    }

    .value-principles-card {
        background:
            linear-gradient(187deg, rgba(34, 166, 255, .14) -51.57%, rgba(236, 246, 255, .14) 45.52%, rgba(247, 251, 255, .14) 94.7%),
            #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        flex: 1;

        img {
            width: 72%;
            object-fit: cover;
        }
    }
}

</style>
