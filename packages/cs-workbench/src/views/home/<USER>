// 根据时间，输出上午好，下午好，晚上好
import { ref } from 'vue';
import { formatDate } from '@/common/utils.ts';

export default function useGreeting() {
    const now = new Date();
    const hour = now.getHours();
    let greeting = ref('');
    // 日期 星期：2021-09-01 星期三
    const dateTime = `${formatDate(now, 'YYYY-MM-DD')} 星期${'日一二三四五六'.charAt(now.getDay())}`;

    if (hour < 12) {
        greeting.value = '上午好！';
    } else if (hour < 18) {
        greeting.value = '下午好！';
    } else {
        greeting.value = '晚上好！';
    }
    return {
        greeting,
        dateTime,
    };
}
