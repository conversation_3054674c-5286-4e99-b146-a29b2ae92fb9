<script setup lang="ts">
import LayoutPageContainer from '@/layout/components/page-container/layout-page-container.vue';
import { ref, onMounted, computed } from 'vue';
import { debounce } from 'lodash';
import { ElMessage, Action, ElMessageBox } from 'element-plus';
import LayoutPageMain from '@/layout/components/page-container/layout-page-main.vue';
import LayoutPageElement from '@/layout/components/page-container/layout-page-element.vue';
import { Search } from '@element-plus/icons-vue';
import { usePrintConfigStore } from '@/views/implementation-tool/print-config/store';
import { handleDeleteOSSFile } from '@/views/implementation-tool/print-config/print-driver/utils';
import { PrinterDriverAPI } from '@/api/printer-driver-api';
import AddDriverDialog from '@/views/implementation-tool/print-config/print-driver/components/add-driver-dialog.vue';
import EditBrandDialog from '@/views/implementation-tool/print-config/print-driver/components/edit-brand-dialog.vue';
import EditModelDialog from '@/views/implementation-tool/print-config/print-driver/components/edit-model-dialog.vue';

const SupportSystemEnum = {
    0: 'Win7',
    1: 'Win8',
    2: 'Win10及以上',
};

const formData = ref({
    brand: '',
    key: '',
});
const currentPage = ref(1);
const pageSize = ref(12);
const totalRecords = ref(0);
const tableDataList = ref([]);
const loading = ref(false);
const addDriverDialogVisible = ref(false);
const editBrandDialogVisible = ref(false);
const editModelDialogVisible = ref(false);
const isEditDialog = ref(false);
const editRowItem = ref({});

const printConfigStore = usePrintConfigStore();
const { fetchBrandOptions } = printConfigStore;
const currentBrandOptions = computed(() => printConfigStore.currentBrandOptions);
const currentSelectedBrandId = computed(() => printConfigStore.currentSelectedBrandId);

/**
 * 打开新增驱动弹窗
 */
function openAddDriverDialog() {
    editRowItem.value = {};
    isEditDialog.value = false;
    addDriverDialogVisible.value = true;
}

/**
 * 下载驱动
 */
async function handleDownload(item: any) {
    const { driverUrl, ossId } = item;
    if (!driverUrl) return;
    const aTag = document.createElement('a');
    aTag.style.display = 'none';
    aTag.href = driverUrl;
    aTag.download = ossId;
    document.body.appendChild(aTag);
    aTag.click();
    document.body.removeChild(aTag);
}

/**
 * 删除驱动
 */
function handleDelete(item: any) {
    ElMessageBox.alert('驱动删除后无法恢复，是否确认删除？', '删除驱动', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        showCancelButton: true,
        callback: async (action: Action) => {
            if (action === 'confirm') {
                const { id, ossId } = item;
                if (!ossId) {
                    ElMessage.error('删除失败, 没有 OSS 文件名');
                    return;
                }
                try {
                    await PrinterDriverAPI.deleteDriverUsingPUT(id);
                    const isDeleteSuccess = await handleDeleteOSSFile(ossId);
                    if (isDeleteSuccess) {
                        ElMessage.success('删除成功');
                        // 刷新表格, 页数回到第1页
                        currentPage.value = 1;
                    } else {
                        ElMessage.error('删除失败');
                    }
                } catch (e: any) {
                    console.error(e);
                    ElMessage.error('删除失败，原因：' + e.message);
                } finally {
                    // 刷新表格
                    await fetchData();
                }
            }
        },
    });
}

/**
 * 打开编辑品牌或型号弹窗
 * @param type 1:品牌 2:型号
 */
function editBrandsOrModels(type: number) {
    switch (type) {
        case 1:
            editBrandDialogVisible.value = true;
            break;
        case 2:
            if (!currentSelectedBrandId.value) {
                ElMessage.warning('请先选择品牌');
                break;
            }
            editModelDialogVisible.value = true;
            break;
        default:
            break;
    }
}

function handleRowClick(row: any) {
    const { id = '', name = '', brandId = '', modelViewList = [], supportSystem = [], remark = '', driverUrl = '', ossId = '' } = row;
    const modelIds = (modelViewList || []).map((it: any) => it.id);
    editRowItem.value = {
        id, // 驱动id
        name, // 驱动名称
        brandId, // 品牌id
        modelIds, // 型号ids
        supportSystem, // 支持的操作系统
        remark, // 备注
        driverUrl, // 驱动OSS链接
        ossId, // 驱动文件名称，OSS存储的文件名
    };
    isEditDialog.value = true;
    addDriverDialogVisible.value = true;
}

const fetchData = async () => {
    loading.value = true;
    try {
        const offset = (currentPage.value - 1) * pageSize.value;
        const res: any = await PrinterDriverAPI.driverListUsingGET(pageSize.value, offset, formData.value.brand, formData.value.key);
        const { rows, total } = res;
        totalRecords.value = total;
        tableDataList.value = rows || [];
    } catch (error) {
        ElMessage.error('Failed to fetch print driver. Please try again later.');
        console.error('Error fetching print driver:', error);
    } finally {
        loading.value = false;
    }
};

const handlePageChange = (page: number) => {
    currentPage.value = page;
    fetchData();
};

const debouncedFetchData = debounce(fetchData, 300);

onMounted(() => {
    fetchBrandOptions();
    fetchData();
    window.onbeforeunload = (e) => {
    };
});

</script>

<template>
    <layout-page-container>
        <layout-page-main>
            <template #header>
                <layout-page-element>
                    <el-space>
                        <el-select
                            v-model="formData.brand"
                            clearable
                            filterable
                            placeholder="品牌"
                            style="width: 230px;"
                            @change="fetchData"
                        >
                            <el-option
                                v-for="item in currentBrandOptions"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            />
                        </el-select>
                        <el-input
                            v-model="formData.key"
                            placeholder="名称、型号、品牌"
                            clearable
                            :prefix-icon="Search"
                            style="width: 230px;"
                            @input="debouncedFetchData"
                        />
                        <el-button type="primary" style="width: 100px;" @click="openAddDriverDialog">新增驱动</el-button>
                    </el-space>
                </layout-page-element>
            </template>
            <layout-page-element>
                <el-table
                    v-loading="loading"
                    border
                    :data="tableDataList"
                    style="width: 100%;"
                    @row-click="handleRowClick"
                >
                    <!-- 打印机名称 -->
                    <el-table-column prop="name" label="打印机名称" width="158px" />
                    <!-- 品牌 -->
                    <el-table-column prop="brandName" label="品牌" width="158px" />
                    <!-- 型号 -->
                    <el-table-column label="型号">
                        <template #default="{ row }">
                            <div class="long-table-text" :title="row.modelViewList.map((it: any) => it.name).join('、')">
                                {{ row.modelViewList.map((it: any) => it.name).join('、') }}
                            </div>
                        </template>
                    </el-table-column>
                    <!-- 支持的操作系统 -->
                    <el-table-column label="支持的操作系统">
                        <template #default="{ row }">
                            <div
                                class="long-table-text"
                                :title="row.supportSystem.map((it: any) => SupportSystemEnum[it]).join('、')"
                            >
                                {{ row.supportSystem.map((it: any) => SupportSystemEnum[it]).join('、') }}
                            </div>
                        </template>
                    </el-table-column>
                    <!-- 上传者 -->
                    <el-table-column prop="createdByName" label="上传者" />
                    <!-- 上传时间 -->
                    <el-table-column prop="createdTime" label="上传时间" />
                    <!-- 备注 -->
                    <el-table-column label="备注">
                        <template #default="{ row }">
                            <div class="long-table-text" :title="row.remark">
                                {{ row.remark }}
                            </div>
                        </template>
                    </el-table-column>
                    <!-- 操作方法 -->
                    <el-table-column label="操作" width="100">
                        <template #default="{ row }">
                            <div class="table-column-operate">
                                <el-link
                                    v-if="row.driverUrl"
                                    type="primary"
                                    :href="row.driverUrl"
                                    target="_blank"
                                    @click.stop
                                >
                                    下载
                                </el-link>
                                <el-link type="danger" @click.stop.prevent="handleDelete(row)">删除</el-link>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                    :current-page="currentPage"
                    :page-size="pageSize"
                    layout="total, prev, pager, next"
                    :total="totalRecords"
                    @current-change="handlePageChange"
                />
            </layout-page-element>
        </layout-page-main>
    </layout-page-container>
    <add-driver-dialog
        v-model="addDriverDialogVisible"
        :is-edit-dialog="isEditDialog"
        :edit-row-item="editRowItem"
        @editBrandsOrModels="editBrandsOrModels"
        @submitAddDriver="fetchData"
    ></add-driver-dialog>

    <edit-brand-dialog v-model="editBrandDialogVisible"></edit-brand-dialog>

    <edit-model-dialog v-model="editModelDialogVisible"></edit-model-dialog>
</template>

<style lang="scss">
.page-table {
    flex-direction: column;
    gap: 16px;
}

.long-table-text {
    display: inline-block;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: middle;
}

.table-column-operate {
    display: flex;
    gap: 8px;
}
</style>
