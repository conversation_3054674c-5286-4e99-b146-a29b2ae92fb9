import { RouteRecordRaw } from 'vue-router';
import { PermissionPrintConfig } from './permission';

const Index = () => import('./index.vue');

export default [
    {
        path: '/print-config',
        name: '@print-config',
        component: Index,
        meta: {
            name: '打印工具',
            icon: 'iconamoon:printer-light',
            roles: PermissionPrintConfig,
            hidden: true,
        },
        redirect: {
            name: '@print-config/print-driver',
        },
        children: [
            {
                path: 'print-driver',
                name: '@print-config/print-driver',
                component: () => import('./print-driver/index.vue'),
                meta: {
                    name: '打印机驱动管理',
                    roles: PermissionPrintConfig,
                    hidden: true,
                },
            },
        ],
    },
] as RouteRecordRaw;
