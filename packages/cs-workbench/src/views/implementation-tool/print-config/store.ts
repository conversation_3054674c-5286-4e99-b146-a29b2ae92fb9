import { defineStore } from 'pinia';
import { PrinterDriverAPI } from '@/api/printer-driver-api';

interface PrintConfigState {
    selectedBrandId: string,
    brandOptions: any[],
    modelOptions: any[],
}

export const usePrintConfigStore = defineStore('print-config', {
    state: () => ({
        selectedBrandId: '',
        brandOptions: [],
        modelOptions: [],
    } as PrintConfigState),
    
    getters: {
        currentSelectedBrandId(state) {
            return state.selectedBrandId;
        },
        currentBrandOptions(state) {
            return state.brandOptions;
        },
        currentModelOptions(state) {
            return state.modelOptions;
        },
    },

    actions: {
        changeSelectedBrandId(brandId: string) {
            this.selectedBrandId = brandId;
        },
        async fetchBrandOptions() {
            try {
                const res: any = await PrinterDriverAPI.brandListUsingGET();
                const { rows } = res;
                this.brandOptions = rows || [];
            } catch (e: any) {
                console.error(e);
            }
        },
        async fetchModelOptions(brandId: string) {
            if (!brandId) {
                this.modelOptions = [];
                return;
            }
            try {
                const res: any = await PrinterDriverAPI.modelListUsingGET(brandId);
                const { rows } = res;
                this.modelOptions = rows || [];
            } catch (e: any) {
                console.error(e);
            }
        },
    },
});
