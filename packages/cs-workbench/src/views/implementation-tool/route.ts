import { RouteRecordRaw } from 'vue-router';
import { PermissionAfterSalesView, PermissionDataTransferSet, PermissionRouterPassword, PermissionSichuanKey } from './permission.ts';
import PrintRoute from './print-config/route.ts';

const Index = () => import('./index.vue');

export default [
    {
        path: '/implementation-tool',
        name: '@implementation-tool',
        component: Index,
        meta: {
            isEntry: true,
            name: '实施工具',
            icon: 'carbon:tool-kit',
            roles: PermissionDataTransferSet,
            // 值越大，排序越靠后
            sort: 300,
        },
        redirect: {
            name: '@data-transfer',
        },
        children: [
            {
                path: 'data-transfer',
                name: '@data-transfer',
                component: () => import('./data-transfer/index.vue'),
                meta: {
                    name: '数据迁移',
                    icon: 'mdi:transfer',
                    roles: PermissionDataTransferSet,
                    hidden: true,
                },
                redirect: {
                    name: '@data-transfer/template-download',
                },
                children: [
                    {
                        path: 'template-download',
                        name: '@data-transfer/template-download',
                        component: () => import('./data-transfer/template-download/index.vue'),
                        meta: {
                            name: '模板下载',
                            icon: 'mdi:download',
                            roles: PermissionDataTransferSet,
                        },
                    },
                ],
            },
            {
                path: '/router-password',
                name: '@router-password',
                component: () => import('./router-password/index.vue'),
                meta: {
                    name: '前置机密码',
                    icon: 'oui:app-search-profiler',
                    roles: PermissionRouterPassword,
                    hidden: true,
                },
            },
            PrintRoute[0],
            {
                path: '/scheduling',
                name: '@scheduling',
                component: () => import('./scheduling/index.vue'),
                meta: {
                    name: '实施排班',
                    icon: 'carbon:calendar',
                    roles: PermissionAfterSalesView,
                    hidden: true,
                },
            },
            {
                path: 'sichuan-key',
                name: '@sichuan-key',
                component: () => import('./sichuan-key/index.vue'),
                meta: {
                    name: '四川公钥',
                    icon: 'mdi:key-outline',
                    roles: PermissionSichuanKey,
                    hidden: true,
                },
            },
        ],
    },
] as RouteRecordRaw[];
