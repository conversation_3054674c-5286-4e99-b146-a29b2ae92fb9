<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import {
    dockingTypeOptions,
    ORDER_TYPE_ENUM,
    orderTypeOptions,
} from '../model';
import { validateMobilePhone } from '@abc-oa/utils';
import { CorpAPI } from '@/api/corp-api';
import { SupportTicketAPI } from '@/api/support-ticket-api';
import { ElMessage } from 'element-plus';

const props = defineProps({
    visible: {
        type: Boolean,
        default: false,
    },
    occupyDialogData: {
        type: Object,
        default: () => ({}),
    },
    calendarDate: {
        type: Object,
        default: () => ({}),
    },
});
const emit = defineEmits(['update:visible', 'refresh']);
const showVisible = computed({
    get: () => props.visible,
    set: (val) => {
        emit('update:visible', val);
    },
});
const formData = ref<any>({
    clinicId: '',
    editionSnapshot: '',
    hisTypeSnapshot: undefined,
    sellerId: '',
    sellerMobile: '',
    subType: undefined,
    title: '',
    type: undefined,
});
/**
 * @description: 关闭弹窗
 * @date: 2024-07-26 10:12:32
 * @author: Horace
 * @return
 */
function handleDialogClose() {
    showVisible.value = false;
    formData.value = {};
    sellerInfo.value = '';
}
const organList = ref<any[]>([]);
const loading = ref(false);
async function fetchEmployeeList(value: string) {
    const mobile = value?.trim();
    if (!mobile) {
        return;
    }
    if (!validateMobilePhone(mobile)) {
        return;
    }
    loading.value = true;
    let res: any = {};
    try {
        res = await CorpAPI.getUserByMobileUsingGET(mobile);
    } catch (e: any) {
        ElMessage.error(e.message || e);
    } finally {
        loading.value = false;
    }
    if (res) {
        organList.value = res?.clinicList || [];
    }
}
function handleTaskChange() {
    const clinic = organList.value.find((item) => item.id === formData.value.clinicId);
    formData.value.editionSnapshot = clinic?.editionId;
    formData.value.hisTypeSnapshot = clinic?.hisType;
    sellerInfo.value = clinic?.sellerMobile ? `${clinic.sellerName || ''}(${clinic.sellerMobile})` : '';
    formData.value.title = clinic?.name;
}
const sellerInfo = ref('');
watch(() => sellerInfo.value, (newVal: string) => {
    if (newVal?.includes('(')) {
        const splits = newVal.split('(');

        formData.value.sellerMobile = splits[splits.length - 1].replace(')', '');
    } else {
        formData.value.sellerMobile = newVal;
    }
});
async function handleSubmitClick() {
    let res: any = {};
    if (!formData.value.clinicId) {
        ElMessage.error('请选择对接门店');
        return;
    }
    if (!formData.value.sellerMobile) {
        ElMessage.error('请输入销售手机号');
        return;
    }
    loading.value = true;
    try {
        res = await SupportTicketAPI.createSupportTicketUsingPOST(formData.value);
    } catch (e: any) {
        ElMessage.error(e.message || e);
    } finally {
        loading.value = false;
    }
    if (!res || !res.code) {
        ElMessage.error('新增待安排失败');
        return;
    }
    if (res?.code === 200) {
        ElMessage.success('新增待安排成功');
        handleDialogClose();
    }
}
function handleTypeChange() {
    if (formData.value.type !== ORDER_TYPE_ENUM.INCREASE) {
        formData.value.subType = undefined;
    }
}
</script>
<template>
    <el-dialog
        v-model="showVisible"
        :width="600"
        center
        title="新增待安排"
        custom-class="create-dialog"
        :before-close="handleDialogClose"
    >
        <el-form
            ref="ruleFormRef"
            :model="formData"
            class="create-dialog-form"
        >
            <el-form-item label="对接门店" prop="auditLevels">
                <el-select
                    v-model="formData.clinicId"
                    placeholder="门店成员手机号搜索"
                    filterable
                    clearable
                    remote
                    style="width: 180px;"
                    placement="bottom-end"
                    :teleported="false"
                    :popper-append-to-body="false"
                    :remote-method="fetchEmployeeList"
                    @change="handleTaskChange"
                >
                    <el-option
                        v-for="task in organList"
                        :key="task.id"
                        :value="task.id"
                        :label="task.name"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="对应销售" prop="auditLevels">
                <el-input
                    v-model="sellerInfo"
                    style="width: 180px;"
                    placeholder="请输入销售手机号"
                ></el-input>
            </el-form-item>
            <el-form-item label="订单类型" prop="auditLevels">
                <el-radio-group v-model="formData.type" class="flex-column" @change="handleTypeChange">
                    <el-radio v-for="level in orderTypeOptions" :key="level.value" :label="level.value">{{ level.label }}</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="对接类型" prop="auditLevels">
                <el-radio-group v-model="formData.subType" :disabled="formData.type !== ORDER_TYPE_ENUM.INCREASE" class="flex-column">
                    <el-radio v-for="level in dockingTypeOptions" :key="level.value" :label="level.value">{{ level.label }}</el-radio>
                </el-radio-group>
            </el-form-item>
        </el-form>
        <el-divider style="width: 100%; margin: 0;"></el-divider>
        <template #footer>
            <el-button type="primary" :loading="loading" @click="handleSubmitClick">确认</el-button>
        </template>
    </el-dialog>
</template>
<style lang="scss" scoped>
.create-dialog {
    .create-dialog-form {
        padding: 20px;

        :deep(.el-form-item__content) {
            width: 100%;

            .el-radio-group.flex-column,
            .el-checkbox-group.flex-column {
                flex-direction: row;
                flex-wrap: wrap;
            }

            .el-select-dropdown__item {
                height: auto;
            }
        }
    }
}
</style>
