<script lang="ts" setup>
import { computed, onMounted, ref, watch } from 'vue';
import { SupportTicketAPI } from '@/api/support-ticket-api';
import { ElMessage } from 'element-plus';
import { formatDate } from '@/common/utils.ts';
import _ from 'lodash';
import OccupyDialog from './components/occupy-dialog.vue';
import { FROM_STATUS, formatStatus } from './model';
import CreateDialog from './components/create-dialog.vue';
import { useUserStore } from '@/store/user';
import LayoutPageElement from '@/layout/components/page-container/layout-page-element.vue';
import { PermissionAfterSalesEdit } from '@/views/implementation-tool/permission.ts';

const currentYear: any = computed({
    get: () => `${calendarDate.value.getFullYear()}年`,
    set: (year: string) => {
        calendarDate.value = new Date(new Date(calendarDate.value).setFullYear(parseInt(year)));
    },
});
const editable = computed(() => {
    const userStore = useUserStore();
    const roles = userStore.roles;
    return PermissionAfterSalesEdit.some((role: string) => roles.includes(role));
});
const currentYearCount: number = new Date().getFullYear();
const currentMonth: any = computed({
    get: () => `${calendarDate.value.getMonth() + 1}月`,
    set: (month: string) => {
        calendarDate.value = new Date(new Date(calendarDate.value).setMonth(parseInt(month) - 1));
    },
});
const calendarDate = ref<any>(new Date());

const engineerShiftScheduling = ref<any[]>([]);

/**
 * @description: 获取排班列表
 * @date: 2024-07-22 10:57:07
 * @author: Horace
 * @return
 */
async function fetchSchedulingList() {
    let res: any = {};
    loading.value = true;
    try {
        res = await SupportTicketAPI.listDashboardScheduleAppointmentUsingGET(formatDate(calendarDate.value, 'YYYY-MM-DD'));
    } catch (e: any) {
        ElMessage.error(e.message || e);
    } finally {
        loading.value = false;
    }
    engineerShiftScheduling.value = res.rows;

    init();
}
watch(calendarDate, () => {
    fetchSchedulingList();
}, {
    deep: true,
});
const activeEngineer = ref(0);
const isDisabledChangeTab = ref(true);
/**
 * @description: 处理Tab切换前，拦截默认处理
 * @date: 2024-07-22 10:58:09
 * @author: Horace
 * @return
 */
function handleTabChangeBefore() {
    return !isDisabledChangeTab.value;
}
/**
 * @description: 处理Tab点击
 * @date: 2024-07-22 10:58:27
 * @author: Horace
 * @return
 */
function handleTabClick() {
    isDisabledChangeTab.value = true;
}
/**
 * @description: 处理Tab切换
 * @date: 2024-07-22 10:58:59
 * @author: Horace
 * @return
 */
function handleTabChange() {
    isDisabledChangeTab.value = true;
}
/**
 * @description: 辅助函数，将时间字符串转换为分钟数
 * @date: 2024-07-22 10:59:13
 * @author: Horace
 * @param {string} time 时间字符串
 * @return
 */
function timeToMinutes(time: string) {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
}

/**
 * @description: 辅助函数，将分钟数转换为时间字符串
 * @date: 2024-07-22 10:59:28
 * @author: Horace
 * @param {number} minutes 分钟数
 * @return
 */
function minutesToTime(minutes: number) {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
}
/**
 * @description: 生成时间段
 * @date: 2024-07-22 10:59:48
 * @author: Horace
 * @param {string} startTime 开始时间
 * @param {string} endTime 结束时间
 * @param {number} interval 时间间隔
 * @return
 */
function generateTimeSlots(startTime: string, endTime: string, interval: number) {
    const slots = [];
    let start = timeToMinutes(startTime);
    const end = timeToMinutes(endTime);
    while (start < end) {
        const endSlot = start + interval;
        slots.push({ start: minutesToTime(start), end: minutesToTime(endSlot) });
        start = endSlot;
    }
    return slots;
}
/**
 * @description: 生成排班表
 * @date: 2024-07-22 11:01:55
 * @author: Horace
 * @param {any} engineer 工程师对象
 * @return
 */
function generateSchedule(engineer: any) {
    const schedule: any = {};
    timeSlots.value.forEach((slot: any) => {
        schedule[`${slot.start}-${slot.end}`] = {
            work: false,
            occupy: false,
            appointment: false,
            message: '',
            task: {},
            time: `${slot.start}-${slot.end}`,
        };
    });

    engineer.scheduleList.forEach((scheduleItem: any) => {
        const start = timeToMinutes(formatDate(scheduleItem.workBeginTime, 'HH:mm'));
        const end = timeToMinutes(formatDate(scheduleItem.workEndTime, 'HH:mm'));
        timeSlots.value.forEach(slot => {
            const slotStart = timeToMinutes(slot.start);
            const slotEnd = timeToMinutes(slot.end);
            if (slotStart >= start && slotEnd <= end) {
                schedule[`${slot.start}-${slot.end}`].work = true;
            }
        });
    });

    engineer.taskList.forEach((task: any) => {
        task.appointmentList.forEach((appointment: any) => {
            const start = timeToMinutes(formatDate(appointment.appointmentBeginTime, 'HH:mm'));
            const end = timeToMinutes(formatDate(appointment.appointmentEndTime, 'HH:mm'));
            const taskItems = task.items?.length > 1 ? `${task.items?.[0].name}等${ task.items.length }项` : task.items?.[0].name || '';
            timeSlots.value.forEach(slot => {
                const slotStart = timeToMinutes(slot.start);
                const slotEnd = timeToMinutes(slot.end);
                if (slotStart >= start && slotEnd <= end) {
                    schedule[`${slot.start}-${slot.end}`].occupy = task.implementerType === 2;
                    schedule[`${slot.start}-${slot.end}`].appointment = task.implementerType === 1;
                    schedule[`${slot.start}-${slot.end}`].task = task;
                    schedule[`${slot.start}-${slot.end}`].message = task.implementerType === 2 ? '已占用' : taskItems;
                }
            });
        });
    });

    return schedule;
}
/**
 * @description: 初始化
 * @date: 2024-07-22 11:02:22
 * @author: Horace
 * @return
 */
function init() {
    engineerShiftScheduling.value?.forEach((engineer: any) => {
        engineer.schedule = generateSchedule(engineer) || {};
    });
    activeEngineer.value = engineerShiftScheduling.value?.[0]?.implementerUser?.id;
}
const timeSlots = ref<any[]>([]);

const workStartTime = ref('09:00');
const workEndTime = ref('18:00');
const timeInterval = ref(60);

onMounted(async () => {
    timeSlots.value = generateTimeSlots(workStartTime.value, workEndTime.value, timeInterval.value);
    await fetchSchedulingList();
});

const occupyDialogVisible = ref(false);
const occupyDialogData = ref<any>({});
function handleOccupyDialogOpen(schedule: any, timeSlot?: any) {
    occupyDialogData.value = { schedule, timeSlot };
    occupyDialogVisible.value = true;
}

const query = ref({
    keyword: '',
});
const taskList = ref<any[]>([]);

const loading = ref(false);
async function fetchTaskListByKeyword(value: string) {
    if (!value) {
        return [];
    }

    let res: any = {};
    try {
        res = await SupportTicketAPI.listAppointUsingGET(value);
    } catch (e: any) {
        ElMessage.error(e.message || e);
    }
    taskList.value = res.rows;
}
const debounceFetchTaskList = _.debounce(fetchTaskListByKeyword, 500);
function handleTaskChange(value: any) {
    if (!value) {
        return;
    }
    const task = taskList.value.find(item => item.id === value) || {};
    const [day, time] = formatTaskTime(task)?.split(' ') || [];
    const taskItems = task.items?.length > 1 ? `${task.items?.[0].name}等${ task.items.length }项` : task.items?.[0].name || '';
    const timeSlot = {
        work: true,
        occupy: task.implementerType === 2,
        appointment: task.implementerType === 1,
        message: task.implementerType === 2 ? '已占用' : taskItems,
        task,
        time,
    };
    handleOccupyDialogOpen({ scheduleDate: day, implementerUser: task.implementerUser }, timeSlot);
}
function formatTaskTime(task: any) {
    const appointmentList = task.appointmentList;
    if (!appointmentList?.length) {
        return '';
    }

    const { beginTime, endTime } = appointmentList.reduce((acc: any, item: any) => {
        const beginTimestamp = Date.parse(item.appointmentBeginTime);
        const endTimestamp = Date.parse(item.appointmentEndTime);
        return {
            beginTime: beginTimestamp < acc.beginTime ? item.appointmentBeginTime : acc.beginTime,
            endTime: endTimestamp > acc.endTime ? item.appointmentEndTime : acc.endTime,
        };
    }, { beginTime: appointmentList[0].appointmentBeginTime, endTime: appointmentList[0].appointmentEndTime });

    const day = formatDate(beginTime, 'YYYY-MM-DD');
    return `${day} ${formatDate(beginTime, 'HH:mm')} - ${formatDate(endTime, 'HH:mm')}`;
}
function formatTaskItems(task: any) {
    const itemsLength = task.items?.length;
    if (!itemsLength) {
        return '';
    }
    return `${task.items[0].name || ''}${itemsLength > 1 ? '等' : ''}${itemsLength}项`;
}

/**
 * @description: 刷新
 * @date: 2024-07-26 10:24:56
 * @author: Horace
 * @return
 */
function handleSubmitRefresh() {
    fetchSchedulingList();
    taskList.value = [];
    query.value.keyword = '';
}
const createDialogVisible = ref(false);
/**
 * @description: 新增待安排
 * @date: 2024-07-26 10:29:06
 * @author: Horace
 * @return
 */
function handleCreateDialogOpen() {
    createDialogVisible.value = true;
}
</script>
<template>
    <layout-page-element class="scheduling-wrapper" :background="false">
        <div class="schedule-calendar-left">
            <div class="scheduling-calendar-wrapper">
                <el-calendar ref="calendar" v-model="calendarDate">
                    <template #header>
                        <div>
                            <el-select v-model="currentYear" style="width: 7em; margin-right: 14px;">
                                <el-option
                                    v-for="year in currentYearCount + 10"
                                    :key="year"
                                    :label="year + '年'"
                                    :value="year + '年'"
                                ></el-option>
                            </el-select>
                            <el-select v-model="currentMonth" style="width: 6em;">
                                <el-option
                                    v-for="month in 12"
                                    :key="month"
                                    :label="month + '月'"
                                    :value="month + '月'"
                                ></el-option>
                            </el-select>
                        </div>
                    </template>
                </el-calendar>
            </div>
            <div class="engineer-shift-scheduling">
                <div class="engineer-shift-scheduling-title">工程师排班</div>
                <el-card v-for="schedule in engineerShiftScheduling" :key="schedule.implementerUser.userId" class="engineer-shift-card">
                    <div class="engineer-shift-content">
                        <span>{{ `${schedule.implementerUser.name}   ${ schedule.totalAppointmentHours || 0 } / ${ schedule.totalWorkHours || 0 }` }}</span>
                        <el-button
                            type="primary"
                            :disabled="!!schedule.totalAppointmentHours || !editable"
                            round
                            @click="handleOccupyDialogOpen(schedule)"
                        >
                            占
                        </el-button>
                    </div>
                </el-card>
            </div>
        </div>

        <el-divider direction="vertical" />

        <div class="schedule-calendar-right">
            <div class="task-list-search-wrapper">
                <el-button
                    v-if="editable"
                    type="primary"
                    style="margin-right: 12px;"
                    @click="handleCreateDialogOpen"
                >
                    新增待安排
                </el-button>
                <el-select
                    v-model="query.keyword"
                    placeholder="门店成员手机号搜索"
                    filterable
                    clearable
                    remote
                    placement="bottom-end"
                    :teleported="false"
                    :popper-append-to-body="false"
                    :remote-method="debounceFetchTaskList"
                    @change="handleTaskChange"
                >
                    <el-option
                        v-for="task in taskList"
                        :key="task.id"
                        :value="task.id"
                        :label="task.clinicName"
                    >
                        <div class="flex-between">
                            <div class="clinic-info">
                                <p>{{ task.clinicName || '' }}</p>
                                <p>{{ formatTaskTime(task) }}</p>
                            </div>
                            <div class="task-items">
                                {{ formatTaskItems(task) }}
                            </div>
                            <el-tag :type="task.status === FROM_STATUS.CONFIRMED ? '' : 'info'" effect="dark">
                                {{ formatStatus(task.status) }}
                            </el-tag>
                        </div>
                    </el-option>
                </el-select>
            </div>
            <div class="schedule-project-wrapper">
                <div class="schedule-time-slot-wrapper">
                    <div v-for="timeSlot in timeSlots" :key="timeSlot.start" class="schedule-time-slot">
                        <span class="schedule-time-slot-start">{{ timeSlot.start }}</span>
                        <span class="schedule-time-slot-end">{{ timeSlot.end }}</span>
                    </div>
                </div>
                <el-tabs
                    v-if="!loading"
                    v-model="activeEngineer"
                    stretch
                    class="engineer-tabs"
                    :before-leave="handleTabChangeBefore"
                    @tab-click="handleTabClick"
                    @tab-change="handleTabChange"
                >
                    <el-tab-pane
                        v-for="(engineer) in engineerShiftScheduling"
                        :key="`${engineer.scheduleDate}-${engineer.implementerUser?.id}-${engineer.taskList?.length}`"
                        :name="engineer.implementerUser?.id"
                    >
                        <template #label>
                            <div class="engineer-tab-title">
                                <span>{{ engineer.implementerUser?.name }}</span>
                                <div class="engineer-project-wrapper">
                                    <div
                                        v-for="(project, key) in engineer.schedule"
                                        :key="`${project.task?.id || engineer.scheduleDate}-${key}`"
                                        class="engineer-project-item"
                                        :class="{
                                            'is-not-work': !project.work,
                                            'is-occupy': project.occupy,
                                            'is-booked': project.appointment
                                        }"
                                        @click="handleOccupyDialogOpen(engineer, project)"
                                    >
                                        <span>{{ project.work ? project.message || '' : '未排班' }}</span>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>
    </layout-page-element>
    <occupy-dialog
        v-model:visible="occupyDialogVisible"
        :occupy-dialog-data="occupyDialogData"
        :calendar-date="calendarDate"
        @refresh="handleSubmitRefresh"
    ></occupy-dialog>
    <create-dialog
        v-model:visible="createDialogVisible"
        @refresh="handleSubmitRefresh"
    ></create-dialog>
</template>
<style lang="scss">
@use "@/style/mixins/mixins.scss" as mixins;

.scheduling-wrapper {
    height: 100%;
    width: 100%;
    padding: 8px;
    display: flex;

    .el-tabs__active-bar {
        display: none;
    }

    .el-tabs__header,
    .el-tabs__nav-wrap,
    .el-tabs__nav-scroll {
        height: 100%;
        overflow: unset !important;
        overflow-x: auto !important;
    }

    .el-tabs__item {
        padding: 0;
    }

    .el-tabs__item + .el-tabs__item {
        .engineer-project-item {
            border-left: none !important;
        }
    }

    .engineer-project-item {
        width: 100%;

        span {
            padding: 0 8px;
            line-height: 1.2;
            white-space: pre-wrap;

            @include mixins.text-ellipsis(3);
        }
    }

    .el-tabs__nav-wrap::after {
        display: none;
    }

    .schedule-calendar-left {
        min-width: 300px;
        max-width: 300px;

        .scheduling-calendar-wrapper {
            box-shadow: rgba(0, 0, 0, .16) 0 1px 4px;

            .el-calendar-table {
                td {
                    border: none;

                    &.is-selected {
                        background: none;

                        .el-calendar-day {
                            border-radius: 50%;
                            background-color: var(--el-calendar-selected-bg-color);
                        }
                    }
                }

                .el-calendar-day {
                    height: calc((300px - 8px - 40px) / 7);
                    padding: 0;
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    &:hover {
                        border-radius: 50%;
                    }
                }
            }
        }

        .engineer-shift-scheduling {
            height: calc(100% - 460px);
            overflow-y: auto;
            padding: 16px;

            .engineer-shift-scheduling-title {
                font-size: 14px;
                color: var(--el-text-color-regular);
                margin-bottom: 8px;
            }

            .engineer-shift-card {
                margin-bottom: 8px;

                :deep(.el-card__body) {
                    padding: 12px;
                }

                .engineer-shift-content {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    line-height: 32px;
                }
            }
        }
    }

    .el-divider {
        height: 100%;
    }

    .el-divider--vertical {
        margin: 0 28px;
    }

    .schedule-calendar-right {
        position: relative;
        background: #fff;
        min-width: calc(100% - 400px - 56px);
        max-width: calc(100% - 400px - 56px);
        padding-top: 120px;

        .task-list-search-wrapper {
            position: absolute;
            top: 0;
            min-width: 100%;
            text-align: right;
            padding: 32px;

            .el-select-dropdown__item {
                height: auto;
            }

            .clinic-info {
                text-align: left;
            }

            .clinic-info,
            .task-items {
                margin-right: 24px;
            }
        }

        .schedule-project-wrapper {
            flex: 1;
            display: flex;

            .engineer-tabs {
                margin-top: -42px;
                padding-right: 32px;
                min-height: 100%;
                max-width: calc(100% - 100px);
                flex: 1;

                :deep(.el-tabs__nav) {
                    padding-bottom: 0;
                }

                :deep(.el-tabs__item) {
                    padding: 0;
                }

                :deep(.el-tabs__item.is-active) {
                    .engineer-tab-title {
                        & > span {
                            border-bottom-color: var(--el-color-primary);
                        }
                    }
                }

                .engineer-tab-title {
                    position: relative;
                    padding: 12px 8px;
                    min-width: 100px;
                    text-align: center;

                    & > span {
                        padding-bottom: 12px;
                        border-bottom: 2px solid transparent;
                    }

                    .engineer-project-wrapper {
                        position: absolute;
                        left: 0;
                        top: 48px;
                        width: 100%;

                        .engineer-project-item {
                            height: calc((100vh - 240px) / 9);
                            border: 1px solid #e0e5ee;
                            display: flex;
                            justify-content: center;
                            align-items: center;

                            &:not(.is-not-work):hover {
                                border-color: #5199f8;
                                border-top: 1px solid #5199f8;
                                border-left: 1px solid #5199f8 !important;
                            }

                            &.is-not-work {
                                background: #f5f7fb;
                                color: #aab4bf;
                            }

                            &.is-booked {
                                background: #5199f8;
                                color: #fff;
                            }

                            &.is-occupy {
                                background: #f5f7fb;
                                color: #000;
                            }
                        }

                        .engineer-project-item + .engineer-project-item {
                            border-top: none;
                        }
                    }
                }

                :deep(.el-tabs__header) {
                    overflow: unset;
                    overflow-x: auto;
                    height: 100%;
                }

                :deep(.el-tabs__nav-wrap) {
                    overflow: unset;
                }

                :deep(.el-tabs__item) {
                    align-items: flex-start;
                    padding-right: 0;
                }

                :deep(.el-tabs__item) + :deep(.el-tabs__item) {
                    padding-left: 0;

                    .engineer-project-item {
                        border-left: none;
                    }
                }
            }

            .schedule-time-slot-wrapper {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                height: 98%;
                width: 100px;
                //padding-top: 48px;

                .schedule-time-slot {
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    min-height: calc((100vh - 240px) / 9);

                    & > span {
                        width: 100%;
                        text-align: right;
                        padding-right: 4px;
                    }

                    .schedule-time-slot-end {
                        margin-bottom: -12px;
                    }
                }

                .schedule-time-slot + .schedule-time-slot {
                    .schedule-time-slot-start {
                        opacity: 0;
                    }
                }
            }
        }
    }
}

.occupy-dialog {
    p {
        line-height: 24px;
    }
}
</style>
