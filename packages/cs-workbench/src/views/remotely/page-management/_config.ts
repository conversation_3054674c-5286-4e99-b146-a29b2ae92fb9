export enum DeviceStatusOptions {
    OFFLINE = 0, // 离线
    ONLINE = 1, // 在线
}
export enum DeviceConnectStatusOptions {
    NOT_CONNECTED = 0, // 未连接
    CONNECTING = 1, // 连接中
    CONNECTING_FAILED = 2, // 连接失败
    PENDING = 3, // 等待授权
    PASS = 4, // 授权通过
    REJECT = 5, // 拒绝授权
}

export const DeviceStatusTagOptions: any = {
    [DeviceStatusOptions.OFFLINE]: {
        label: '离线',
        type: 'info',
    },
    [DeviceStatusOptions.ONLINE]: {
        label: '在线',
        type: 'success',
    },
};
export const DeviceConnectStatusTagOptions: any = {
    [DeviceConnectStatusOptions.PENDING]: {
        label: '待授权',
        type: '',
    },
    [DeviceConnectStatusOptions.CONNECTING]: {
        label: '连接中',
        type: 'warning',
    },
    [DeviceConnectStatusOptions.NOT_CONNECTED]: {
        label: '未连接',
        type: 'info',
    },
    [DeviceConnectStatusOptions.CONNECTING_FAILED]: {
        label: '连接失败',
        type: 'danger',
    },
    [DeviceConnectStatusOptions.PASS]: {
        label: '用户同意授权',
        type: 'success',
    },
    [DeviceConnectStatusOptions.REJECT]: {
        label: '用户拒绝授权',
        type: 'danger',
    },
};
