// @ts-ignore
import { ElMessage, ElMessageBox } from 'element-plus';
import { DeviceConnectStatusOptions } from '@/views/remotely/page-management/_config.ts';
import { nextTick, ref } from 'vue';
import { ElectronApi } from '@/api/electron-api.ts';
import { RemotelyApi } from '@/api/remotely-api.ts';
import { useDebounceFn } from '@vueuse/core';
import UpdateRemotelyStatusDto = AbcAPI.UpdateRemotelyStatusDto;
import FindRemotelyDeviceStatusVo = AbcAPI.FindRemotelyDeviceStatusVo;
import { CustomerList } from '@/views/remotely/page-management/model.ts';

const customerList = ref<CustomerList[]>([]);
// 当前正在远程操作的客户设备信息
const currentItems = ref(new Map());
/**
 * @description: 申请远程连接点击
 * @date: 2022-11-11 10:48:58
 * @author: Horace
 * @param item 申请的诊所信息
 * @param clinicName 申请的诊所名称
 * @return
 */
const handleRemotelyClick = async (item: any, clinicName?: string) => {
    ElMessageBox.confirm(
        `确定申请远程操作诊所:  ${clinicName || item.clinicName || ''}的设备 ?`,
        '注意',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            closeOnClickModal: false,
            type: 'warning',
        },
    )
                    .then(async () => {
                        item.deviceStatus = DeviceConnectStatusOptions.PENDING;
                        !currentItems.value.has(item.identity) && currentItems.value.set(item.identity, item);
                        let res: any = {};
                        try {
                            res = await RemotelyApi.postApiLowCodeRemotelyApply(item);
                        } catch (e: any) {
                            ElMessage.error(e.message || e);
                        }
                        if (res && res.errorCode === 200) {
                            item.id = item.id || res.result?.id || '';
                            startCheckRemotelyStatus(item);
                        }
                    })
                    .catch(() => {
                        ElMessage({
                            type: 'info',
                            message: '操作取消',
                        });
                    });
};

/**
 * @description: 取消申请远程连接点击
 * @date: 2022-11-11 10:50:30
 * @author: Horace
 * @param item 申请的诊所信息
 * @return
 */
const handleRemotelyCancelClick = async (item: any) => {
    const data: UpdateRemotelyStatusDto = {
        currentDeviceId: item.id || '',
        chainId: item.chainId,
        clinicId: item.clinicId,
        deviceCode: item.identity,
        address: '',
        session: '',
        deviceStatus: DeviceConnectStatusOptions.NOT_CONNECTED,
    };
    let res: any = {};
    try {
        res = await RemotelyApi.postApiLowCodeRemotelyApplyCancel(item);
    } catch (e: any) {
        ElMessage.error(e.message || e);
    }
    if (res && res.errorCode === 200) {
        clearCheckRemotelyStatusTimer(item);
        await debounceUpdateStatus(data);
    }
};

/**
 * @description: 开始连接远程
 * @date: 2022-11-11 10:45:38
 * @author: Horace
 * @param currentDeviceId 当前设备id
 * @param chainId 当前诊所连锁id
 * @param clinicId 当前诊所id
 * @param identity 当前设备code
 * @param address 远程地址
 * @param session 会话
 * @return
 */
const remotelyStartConnect = async (
    currentDeviceId: string,
    chainId: string,
    clinicId: string,
    identity: string,
    address: string,
    session:string,
) => {
    const data: UpdateRemotelyStatusDto = {
        currentDeviceId,
        chainId,
        clinicId,
        deviceCode: identity,
        address,
        session,
        deviceStatus: DeviceConnectStatusOptions.NOT_CONNECTED,
    };
    let res: any = {};
    try {
        res = await ElectronApi.connectDesktopSession(
            address,
            session,
            '远程连接',
        );
    } catch (e: any) {
        console.log(e);
        ElMessage.error(e);
        await debounceUpdateStatus({ ...data, deviceStatus: DeviceConnectStatusOptions.CONNECTING_FAILED });
        return;
    }
    data.deviceStatus = res.success ? DeviceConnectStatusOptions.CONNECTING : DeviceConnectStatusOptions.CONNECTING_FAILED;
    await setListenRemotelyConnect(data);
};

// 是否监听远程状态
const isOnListenRemotelyStatus = ref(false);
const remotelyMaps = ref(new Map());
const isRefresh = ref(false);
const needStatusPending = ref(false);
const elMessageBox = ref<any>(null);
/**
 * @description: 监听会话变更
 * @date: 2022-11-22 20:43:23
 * @author: Horace
 * @param data <UpdateRemotelyStatusDto> 会话变更数据
 * @return
 */
const setListenRemotelyConnect = async (data: UpdateRemotelyStatusDto) => {
    remotelyMaps.value.set(data.address, data);
    if (isOnListenRemotelyStatus.value) {
        return;
    }
    // @ts-ignore
    if (!window.electron || !window.electron.slsdk) {
        return { errorCode: 400, msg: '未读取到终端' };
    }
    // @ts-ignore
    const slsdk = window.electron.slsdk;
    isOnListenRemotelyStatus.value = true;
    // @ts-ignore
    slsdk.on('remote', (address: string, event: any) => {
        const currentRemotelyItem: any = remotelyMaps.value.get(address);
        if (!currentRemotelyItem) {
            return;
        }
        const sessionEventMap = new Map([
            [slsdk.SLRemoteEvent.SLREMOTE_EVENT_ONCONNECT, '连接成功'],
            [slsdk.SLRemoteEvent.SLREMOTE_EVENT_ONDISCONNECT, '断开连接'],
            [slsdk.SLRemoteEvent.SLREMOTE_EVENT_ONDISCONNECT_FOR_FULL, '断开连接(因为连接数满了)'],
        ]);
        const deviceItem = customerList.value.find(
            (item) => item.identity === currentRemotelyItem.deviceCode && item.clinicId === currentRemotelyItem.clinicId,
        );
        currentRemotelyItem.deviceStatus = event === 0 ? DeviceConnectStatusOptions.CONNECTING : DeviceConnectStatusOptions.NOT_CONNECTED;
        deviceItem && (deviceItem.deviceStatus = currentRemotelyItem.deviceStatus);
        console.log('remoteListen', address, event, deviceItem, remotelyMaps.value.get(address));
        ElMessage[event === 0 ? 'success' : 'error'](
            `设备${deviceItem?.identity || ''}${sessionEventMap.get(event)}` || '监听会话失败！',
        );
        updateRemotelyDeviceStatus(currentRemotelyItem);
    });
};
function deviceRemotelyStatus(deviceCode: string, clinicId: string) {
    return customerList.value.find((item) => item.identity === deviceCode && item.clinicId === clinicId)?.deviceStatus;
}
/**
 * @description: 修改远程连接状态
 * @date: 2022-11-22 20:04:13
 * @author: Horace
 * @param data <UpdateRemotelyStatusDto> 会话变更数据
 * @return
 */
const updateRemotelyDeviceStatus = async (data: UpdateRemotelyStatusDto) => {
    let res: any = {};
    try {
        res = await RemotelyApi.putApiLowCodeRemotelyStatus(data);
    } catch (e: any) {
        ElMessage.error(e.message || e);
    }
    if (res && res.code === 200) {
        isRefresh.value = true;
    }
};
const debounceUpdateStatus = useDebounceFn(updateRemotelyDeviceStatus, 600);

let checkRemotelyStatusTimer: any = new Map();
const timerStartTime: any = new Map();
/**
 * @description: 开始重复读取请求申请连接状态
 * @date: 2022-11-11 10:49:37
 * @author: Horace
 * @param item <any> 申请诊所信息
 * @return
 */
const startCheckRemotelyStatus = (item: any) => {
    clearCheckRemotelyStatusTimer(item, false);
    if (!timerStartTime.has(item.identity)) {
        timerStartTime.set(item.identity, new Date());
    } else if (+timerStartTime.get(item.identity) + 120000 < +new Date()) {
        statusPendingEnd();
        handleRemotelyCancelClick(item);
        ElMessage.error('当前等待时间已经超过两分钟，对方仍未响应');
        return;
    }
    nextTick(() => {
        statusPendingStart(item);
    });
    let res: FindRemotelyDeviceStatusVo;
    const timer = setTimeout(async () => {
        try {
            res = await RemotelyApi.getApiLowCodeRemotelyStatus(
                item.id || '',
                item.chainId,
                item.clinicId,
                item.identity,
            );
        } catch (e: any) {
            console.warn('queryRemotelyStatus err', e);
            statusPendingEnd();
            clearCheckRemotelyStatusTimer(item);
        }

        const currentItem = currentItems.value.get(item.identity);
        if ([
            DeviceConnectStatusOptions.CONNECTING,
            DeviceConnectStatusOptions.CONNECTING_FAILED,
            DeviceConnectStatusOptions.REJECT,
        ].includes(res.deviceStatus)) {
            currentItem && (currentItem.deviceStatus = res.deviceStatus);
            item.deviceStatus = res.deviceStatus;
        }
        // 对方拒绝远程申请，清除定时，修改状态
        if (res.deviceStatus === DeviceConnectStatusOptions.REJECT) {
            clearCheckRemotelyStatusTimer(item);
            statusPendingEnd();
            ElMessage.error('用户拒绝授权');
            currentItem && (currentItem.rejectStatus = res.deviceStatus);
            item.rejectStatus = res.deviceStatus;
            const data: UpdateRemotelyStatusDto = {
                currentDeviceId: currentItem.id || '',
                chainId: currentItem.chainId,
                clinicId: currentItem.clinicId,
                deviceCode: currentItem.identity,
                address: '',
                session: '',
                deviceStatus: DeviceConnectStatusOptions.NOT_CONNECTED,
            };
            debounceUpdateStatus(data);
            return;
        }
        // 对方同意远程申请
        if (res.deviceStatus === DeviceConnectStatusOptions.PASS) {
            const currentItem = currentItems.value.get(item.identity);
            currentItem && (currentItem.deviceStatus = res.deviceStatus);
            item.deviceStatus = res.deviceStatus;
            statusPendingEnd();
            await remotelyStartConnect(
                item.id || res.id || '',
                item.chainId,
                item.clinicId,
                item.identity,
                res.address,
                res.session,
            );
            clearCheckRemotelyStatusTimer(item);
            return null;
        }

        startCheckRemotelyStatus({ ...item, id: item.id || res.id });
    }, 3000);
    checkRemotelyStatusTimer.set(item.identity, timer);
};

/** 清除定时
 * @description:
 * @date: 2022-11-11 10:50:10
 * @author: Horace
 * @param item <any> 申请诊所信息
 * @param clearTime <boolean> 是否清除定时
 * @return
 */
const clearCheckRemotelyStatusTimer = (item?: any, clearTime = true) => {
    if (!item) {
        clearTime && timerStartTime.clear();
        const timers: any[] = Array.from(checkRemotelyStatusTimer.values()) || [];
        timers?.forEach((timer: any) => {
            clearTimeout(timer);
        });
        return;
    }
    clearTime && timerStartTime.delete(item.identity);

    if (checkRemotelyStatusTimer.has(item.identity)) {
        const timer = checkRemotelyStatusTimer.get(item.identity);
        clearTimeout(timer);
        checkRemotelyStatusTimer.delete(item.identity);
    }
};

const statusPendingStart = (item: any) => {
    if (needStatusPending.value && !elMessageBox.value) {
        elMessageBox.value = ElMessageBox.confirm(
            `正在等待诊所:  ${ item.clinicName || ''}响应，请稍候！`,
            '',
            {
                showConfirmButton: false,
                cancelButtonText: '取消申请',
                closeOnClickModal: false,
                type: 'warning',
            },
        ).catch(() => {
            handleRemotelyCancelClick(item);
        });
    }
};
const statusPendingEnd = () => {
    elMessageBox.value = null;
    ElMessageBox.close();
};

export function useRemotelyHook() {
    return {
        isRefresh,
        customerList,
        needStatusPending,
        deviceRemotelyStatus,
        handleRemotelyClick,
        handleRemotelyCancelClick,
        remotelyStartConnect,
        setListenRemotelyConnect,
        debounceUpdateStatus,
        startCheckRemotelyStatus,
        clearCheckRemotelyStatusTimer,
        remotelyMaps,
    };
}
