<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { useDebounceFn } from '@vueuse/core';
import { ElMessage } from 'element-plus';
import { RemotelyApi } from '@/api/remotely-api.ts';
import dayjs from 'dayjs';
import LayoutPageElement from '@/layout/components/page-container/layout-page-element.vue';
import { OaOrganSelect } from '@abc-oa/components';
import { formatDate } from '@/common/utils.ts';
import { DeviceConnectStatusTagOptions } from '@/views/remotely/page-management/_config.ts';

const dateRange = ref<[Date, Date]>([
    dayjs().subtract(7, 'day').toDate(),
    dayjs().toDate(),
]);
const shortcuts = [
    {
        text: '今天',
        value: () => {
            const date = dayjs();
            const s = date.startOf('day').toDate();
            const e = date.endOf('day').toDate();
            return [s, e];
        },
    },
    {
        text: '昨天',
        value: () => {
            const date = dayjs().subtract(1, 'day');
            const s = date.startOf('day').toDate();
            const e = date.endOf('day').toDate();
            return [s, e];
        },
    },
    {
        text: '本周',
        value: () => {
            const date = dayjs();
            const s = date.startOf('week').add(1, 'day').toDate();
            const e = date.endOf('week').add(1, 'day').toDate();
            return [s, e];
        },
    },
    {
        text: '上周',
        value: () => {
            const date = dayjs().subtract(1, 'week');
            const s = date.startOf('week').add(1, 'day').toDate();
            const e = date.endOf('week').add(1, 'day').toDate();
            return [s, e];
        },
    },
    {
        text: '本月',
        value: () => {
            const date = dayjs();
            const s = date.startOf('month').toDate();
            const e = date.endOf('month').toDate();
            return [s, e];
        },
    },
    {
        text: '上月',
        value: () => {
            const date = dayjs().subtract(1, 'month');
            const s = date.startOf('month').toDate();
            const e = date.endOf('month').toDate();
            return [s, e];
        },
    },
    {
        text: '今年',
        value: () => {
            const date = dayjs();
            const s = date.startOf('year').toDate();
            const e = date.endOf('year').toDate();
            return [s, e];
        },
    },
    {
        text: '去年',
        value: () => {
            const date = dayjs().subtract(1, 'year');
            const s = date.startOf('year').toDate();
            const e = date.endOf('year').toDate();
            return [s, e];
        },
    },
];

const topDevices = ref<any[]>([]);
const topClinics = ref<any[]>([]);

onMounted(() => {
    getTopDevices();
    getTopClinics();
    getRemotelyRecords();
});

const query = ref<any>({
    deviceCode: '',
    clinicId: '',
    pageSize: 10,
    pageNum: 1,
});

const recordList = ref([]);
const total = ref(0);

const getTopDevices = async () => {
    let res: any = [];
    try {
        res = await RemotelyApi.getApiLowCodeRemotelyTopDevices(
            formatDate(dateRange.value[0], 'YYYY-MM-DD 00:00:00'),
            formatDate(dateRange.value[1], 'YYYY-MM-DD 23:59:59'),
        );
    } catch (e: any) {
        ElMessage.error(e.message || e);
    }
    topDevices.value = res || [];
};

const getTopClinics = async () => {
    let res: any = [];
    try {
        res = await RemotelyApi.getApiLowCodeRemotelyTopClinics(
            formatDate(dateRange.value[0], 'YYYY-MM-DD 00:00:00'),
            formatDate(dateRange.value[1], 'YYYY-MM-DD 23:59:59'),
        );
    } catch (e: any) {
        ElMessage.error(e.message || e);
    }
    topClinics.value = res || [];
};

const getRemotelyRecords = async () => {
    if (!query.value.clinicId && !query.value.deviceCode) {
        return;
    }
    let res: any = [];
    try {
        res = await RemotelyApi.getApiLowCodeRemotelyRecord(
            query.value.pageSize,
            (query.value.pageNum - 1) * query.value.pageSize,
            query.value.deviceCode,
            query.value.clinicId,
        );
    } catch (e: any) {
        ElMessage.error(e.message || e);
    }
    recordList.value = res.rows || [];
    total.value = res.total || 0;
};

const handleCurrentChange = (val: number) => {
    query.value.pageNum = val;
    getRemotelyRecords();
};

const handleSizeChange = (val: number) => {
    query.value.pageSize = val;
    query.value.pageNum = 1;
    getRemotelyRecords();
};

const onSearch = () => {
    query.value.pageNum = 1;
    getRemotelyRecords();
};

const debounceSearch = useDebounceFn(onSearch, 600);

const onDateRangeChange = () => {
    getTopDevices();
    getTopClinics();
};
</script>
<template>
    <div class="remotely-record-wrapper">
        <layout-page-element :background="false" class="flex-column">
            <!-- 时间范围选择 -->
            <el-card class="header-date-range mb-4">
                <el-date-picker
                    v-model="dateRange"
                    :clearable="false"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :shortcuts="shortcuts"
                    @change="onDateRangeChange"
                />
            </el-card>

            <!-- TOP10展示区域 -->
            <div class="top10-container mb-4">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-card class="top-devices">
                            <template #header>
                                <div class="card-header">
                                    <span>设备码 TOP10</span>
                                </div>
                            </template>
                            <ul class="top-list">
                                <li
                                    v-for="(device, index) in topDevices"
                                    :key="device.deviceCode"
                                    :class="['top-item', index < 3 ? `top-${index + 1}` : '']"
                                >
                                    <span class="rank">{{ index + 1 }}</span>
                                    <span class="code">
                                        {{ device.deviceCode }}
                                        <span class="tips">{{ device.clinicName || '' }}
                                            <el-tooltip v-if="index === 0" content="当前设备最近登录的诊所">
                                                <oa-icon :size="12" icon="solar:question-circle-outline"></oa-icon>
                                            </el-tooltip>
                                        </span>
                                    </span>
                                    <span class="count">{{ device.recordCount }}次</span>
                                </li>
                            </ul>
                        </el-card>
                    </el-col>
                    <el-col :span="12">
                        <el-card class="top-clinics">
                            <template #header>
                                <div class="card-header">
                                    <span>诊所 TOP10</span>
                                </div>
                            </template>
                            <ul class="top-list">
                                <li
                                    v-for="(clinic, index) in topClinics"
                                    :key="clinic.clinicId"
                                    :class="['top-item', index < 3 ? `top-${index + 1}` : '']"
                                >
                                    <span class="rank">{{ index + 1 }}</span>
                                    <span class="name">
                                        {{ clinic.clinicName || '未登录设备' }}
                                        <span class="tips">
                                            {{ clinic.deviceCode }}
                                            <el-tooltip v-if="index === 0" content="当前诊所最近远程的设备">
                                                <oa-icon :size="12" icon="solar:question-circle-outline"></oa-icon>
                                            </el-tooltip>
                                        </span>
                                    </span>
                                    <span class="count">{{ clinic.recordCount }}次</span>
                                </li>
                            </ul>
                        </el-card>
                    </el-col>
                </el-row>
            </div>

            <!-- 搜索工具栏 -->
            <el-card class="header-toolbar">
                <el-space wrap :size="12">
                    <oa-organ-select
                        v-model="query.clinicId"
                        placeholder="请选择门店"
                        clearable
                        @change="debounceSearch"
                    />
                    <el-input
                        v-model="query.deviceCode"
                        placeholder="请输入诊所设备码"
                        clearable
                        @blur="debounceSearch"
                        @keydown.enter="debounceSearch"
                    />
                </el-space>
            </el-card>
            <!-- 远程记录列表 -->

            <el-table :data="recordList" style="width: 100%; flex: 1;" max-height="100%">
                <el-table-column prop="clinicName" label="门店" />
                <el-table-column prop="clinicId" label="门店ID" />
                <el-table-column prop="deviceCode" label="设备码" />
                <el-table-column prop="connectStartTime" label="链接时间">
                    <template #default="{ row }">
                        {{ formatDate(row.connectStartTime, 'YYYY-MM-DD HH:mm:ss') }}
                    </template>
                </el-table-column>
                <el-table-column prop="connectEndTime" label="断开时间">
                    <template #default="{ row }">
                        {{ formatDate(row.connectEndTime, 'YYYY-MM-DD HH:mm:ss') }}
                    </template>
                </el-table-column>
                <el-table-column prop="created" label="创建时间">
                    <template #default="{ row }">
                        {{ formatDate(row.created, 'YYYY-MM-DD HH:mm:ss') }}
                    </template>
                </el-table-column>
                <el-table-column prop="content" label="连接状态">
                    <template #default="{ row }">
                        <el-tag
                            :type="DeviceConnectStatusTagOptions[row.deviceStatus]?.type"
                        >
                            {{ DeviceConnectStatusTagOptions[row.deviceStatus]?.label }}
                        </el-tag>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                v-model:currentPage="query.pageNum"
                v-model:page-size="query.pageSize"
                :page-sizes="[10, 20, 30, 40]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </layout-page-element>
    </div>
</template>

<style lang="scss">
.remotely-record-wrapper {
    height: 100%;

    .el-pagination {
        margin-top: 16px;
        justify-content: flex-end;
    }

    .mb-4 {
        margin-bottom: 16px;
    }

    .top10-container,
    .top10-container > .el-row {
        min-height: 20vh;
    }

    .top-clinics,
    .top-devices {
        height: 100%;
    }

    .top-list {
        list-style: none;
        padding: 0;
        margin: 0;

        .top-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            font-size: 14px;

            .rank {
                width: 24px;
                text-align: center;
                margin-right: 12px;
            }

            .tips {
                display: inline-flex;
                align-items: center;
                font-size: 12px;
                color: #ccc;
                gap: 4px;
            }

            .code,
            .name {
                flex: 1;
                margin-right: 12px;
            }

            .count {
                color: #909399;
            }

            &.top-1 {
                font-size: 16px;
                font-weight: bold;
                color: #f56c6c;
            }

            &.top-2 {
                font-size: 15px;
                font-weight: 500;
                color: #e6a23c;
            }

            &.top-3 {
                font-size: 14px;
                font-weight: 500;
                color: #67c23a;
            }
        }
    }

    .remotely-record-header-wrapper {
        height: auto !important;
        padding: 0;
    }

    .remotely-record-wrapper-main {
        padding: 0;
        margin-top: 16px;
    }

    .layout-element-section {
        height: 100%;
    }

    .el-table__inner-wrapper {
        height: 100%;

        .el-table__body-wrapper {
            overflow-y: auto;
        }
    }
}
</style>
