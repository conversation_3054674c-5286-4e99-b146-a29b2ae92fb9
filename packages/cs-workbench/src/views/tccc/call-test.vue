<script setup>
import { ref, onMounted } from 'vue';
import Tabs from './components/Tabs.vue';

const arrowImg = 'https://tccc.qcloud.com/assets/arrow.png';
const activeId = ref(0);
const items = [{ label: '电话客服' }, { label: '在线客服' }, { label: '音频视频客服' }];
const seat = ref('');
const status = ref('');
const number = ref('');
const loading = ref(false);
const isError = ref(false);
const errorField = ref('');

const statusMap = {
    offline: '已下线',
    disconnect: '网络断开，重连中',
    free: '空闲中',
    busy: '忙碌中',
    rest: '小休中',
    countdown: '话后倒计时',
    arrange: '话后整理中',
    notReady: '示忙中',
};

const errorFieldMap = {
    'InvalidParameterValue.InstanceNotExist': 'sdkAppId',
    'InvalidParameterValue.AccountNotExist': 'userId',
    'AuthFailure.SignatureFailure': 'secretKey或secretId',
    'AuthFailure.SecretIdNotFound': 'secretId',
};

const injectTCCC = ({ token, sdkAppId, userId, sdkUrl }) => {
    const scriptDom = document.createElement('script');
    scriptDom.setAttribute('crossorigin', 'anonymous');
    scriptDom.dataset.token = token;
    scriptDom.dataset.sdkAppId = sdkAppId;
    scriptDom.dataset.userid = userId;
    scriptDom.src = sdkUrl;
    document.body.appendChild(scriptDom);
    scriptDom.addEventListener('load', () => {
        window.tccc.on('ready', () => {
            const statusVal = window.tccc.Agent.getStatus();
            status.value = statusVal;
            seat.value = userId;
        });
        setInterval(() => {
            const statusVal = window.tccc.Agent.getStatus();
            status.value = statusVal;
        }, 200);
    });
};

onMounted(async () => {
    try {
        const res = await fetch('https://683oqhey-89xb18x5-yb6aik7oj9o8.ac4-preview.marscode.dev/loginTccc');
        const data = await res.json();
        if (data.code) {
            if (data.type) {
                isError.value = true;
                errorField.value = errorFieldMap[data.code];
            } else {
                isError.value = true;
                if (errorFieldMap[data.code]) {
                    errorField.value = errorFieldMap[data.code];
                } else {
                    alert(data.errMsg);
                }
                return;
            }
        }
        injectTCCC({
            token: data.token,
            userId: data.userId,
            sdkUrl: data.sdkUrl,
            sdkAppId: data.sdkAppId,
        });
    } catch (error) {
        console.error(`获取Token失败：${error.message}`);
    }
});

const handleCallout = async () => {
    if (loading.value) {
        return;
    }
    loading.value = true;
    try {
        await window.tccc.Call.startOutboundCall({ phoneNumber: number.value });
    } catch (error) {
        console.error(`呼出失败：${error.message}`);
    } finally {
        loading.value = false;
    }
};
</script>

<template>
    <div class="container">
        <div class="globalErrorContainer">
            <section class="section">
                <div class="subTitle">座席状态</div>
                <div>账号：{{ seat || '加载中...' }}</div>
                <div>状态：{{ statusMap[status] || '加载中...' }}</div>
            </section>
            <section class="section">
                <el-input v-model="number" size="large" placeholder="请输入电话号码" />
                <el-button type="primary" size="large" @click="handleCallout">拨打</el-button>
            </section>
        </div>
    </div>
</template>

<style>
.container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.container a {
    color: #10a37f;
}

.subTitle {
    font-size: 20px;
    font-weight: 400;
    text-align: left;
    color: #333;
}

.section {
    width: 500px;
    margin: 15px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.globalErrorContainer {
    flex-direction: column;
    justify-content: flex-start;
}

</style>
