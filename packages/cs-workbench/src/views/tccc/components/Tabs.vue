<template>
    <div class="tabs">
        <div
            v-for="(item, idx) in items"
            :key="idx"
            :class="idx === activeId ? 'tab active' : 'tab'"
            @click="emit('active', idx)"
        >
            {{ item.label }}
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    items: Array,
    activeId: Number,
});
const emit = defineEmits(['active']);

</script>

<style scoped>
.tabs {
    display: flex;
    background-color: #ecf9f6;
    border-radius: 20px;
}

.tabs .tab {
    text-align: center;
    cursor: pointer;
    width: 180px;
    height: 36px;
    border-radius: 20px;
    line-height: 36px;
    background-color: #ecf9f6;
}

.tabs .tab.active {
    color: #005641;
    background-color: #a9e0d3;
}
</style>
