import { reactive, ref, type Ref } from 'vue';
import { QiyukfAPI } from '@/api/qiyukf-api.ts';
import { ElMessageBox } from 'element-plus';
import { useDraggable } from '@/views/tccc/hooks/useDraggable.ts';

const DOM_ID = 'CONTAINER-CC-TOOLBAR';

interface QiyuInstance {
    status: Ref<string>;
    statusMap: Ref<{ [key: string]: string }>;
    isLoading: Ref<boolean>;
    isCalling: Ref<boolean>;
    callout: (number: string, chainId: string, clinicId: string) => Promise<void>;
    releaseResource: () => Promise<void>;
}

let instance: QiyuInstance;

export function useQiyu() {
    if (instance) {
        return instance;
    }

    useDraggable(DOM_ID);

    const accountId = ref('');
    const status = ref('');
    const statusMap = ref({});
    const isLoading = ref(false);
    const isCalling = ref(false);
    const currentCallingInfo = reactive({
        number: '',
    });

    const injectSdk = async ({ sdkUrl, onSeatOnline }) => new Promise((resolve, reject) => {
        const sdk = document.createElement('script');
        sdk.async = true;
        sdk.src = sdkUrl;
        sdk.onload = () => {
            window.qiConnect.on({
                cookieAccessibleChanged: (data) => {
                    console.log(data.result ? '此时可以获取cookie' : '没有获取cookie权限');
                },
                onload: () => {
                    console.log('呼叫工具条onload！');
                },
                initSuccess: () => {
                    console.log('呼叫工具条initSuccess！');
                    resolve();
                },
                session: (session) => {
                    console.log('呼叫工具条session', session);
                },
                statusChanged: (data) => {
                    const _status = data?.[0];
                    console.log('呼叫工具条statusChanged', _status);
                    status.value = _status;
                    if (_status === '1') {
                        onSeatOnline?.();
                    }
                },
                error: (error) => {
                    console.error('呼叫工具条error', error);
                    destroySdk();
                    reject(error);
                },
            });
        };
        sdk.onerror = (error) => {
            console.error('呼叫工具条sdk加载失败', error);
            reject(error);
        };
        document.body.appendChild(sdk);
    });

    const prepareSdk = async ({ chainId, clinicId, mobile }) => {
        isLoading.value = true;
        const res = await QiyukfAPI.querySdkUrlUsingGET(chainId, clinicId, mobile);
        let waitSeat = {
            promise: null,
            resolve: null,
            reject: null,
        };
        waitSeat.promise = new Promise((resolve, reject) => {
            waitSeat.resolve = resolve;
            waitSeat.reject = reject;
        });

        const { sdk_url: sdkUrl, accountId: _accountId } = res;
        accountId.value = _accountId;
        await injectSdk({
            sdkUrl,
            onSeatOnline: () => {
                waitSeat.resolve();
            },
        });
        await initSeat();
        await waitSeat.promise;
        isLoading.value = false;
    };

    const initSeat = () => {
        const { statusOptions, setStatus } = window.qiConnect;
        statusMap.value = statusOptions.reduce((acc, cur) => {
            acc[cur.value] = cur.label;
            return acc;
        }, {});
        setStatus([1]);
    };

    const requestCall = async (number) => {
        let callPromise = new Promise((resolve, reject) => {
            window.qiConnect.on({
                sessionClose: async (data) => {
                    console.log('呼叫工具条sessionClose', data);
                    try {
                        await window.qiConnect.overProcess({});
                    } catch (e) {
                        console.warn('overProcess error', e);
                    } finally {
                        resolve();
                    }
                },
                overProcess: (data) => {
                    console.log('呼叫工具条on overProcess', data);
                },
            });
        });

        await window.qiConnect.call({ number });
        await callPromise;
    };

    const destroySdk = async () => {
        await window.qiConnect.logoff();
        await window.qiConnect.clean();
    };

    const releaseResource = async () => {
        if (accountId.value) {
            // await QiyukfAPI.releaseUsingPUT(accountId.value);
            accountId.value = '';
        }
    };

    const preventRefresh = () => {
        let closeWindow = false;
        function start() {
            window.onbeforeunload = (e) => {
                if (!window.electron) {
                    return '';
                }
                if (closeWindow) return;
                e.returnValue = false;
                ElMessageBox.confirm('即将离开页面，是否确认？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }).then(() => {
                    releaseResource();
                    closeWindow = true;
                    window.location.reload();
                }).catch(() => {
                });
            };
        }

        function cancel() {
            closeWindow = false;
            window.onbeforeunload = null;
        }

        start();
        return {
            cancel,
        };
    };

    const callout = async (number = '', chainId = '', clinicId = '') => {
        if (!number) {
            ElMessageBox.alert('请输入号码');
            return;
        }
        if (!chainId || !clinicId) {
            ElMessageBox.alert('请先选择门店');
            return;
        }
        isCalling.value = true;
        currentCallingInfo.number = number;
        const { cancel: cancelPreventRefresh } = preventRefresh();
        try {
            await prepareSdk({ chainId, clinicId, mobile: number });
            await requestCall(number);
            await destroySdk();
        } catch (e) {
            ElMessageBox.alert(e.message);
        } finally {
            isCalling.value = false;
            currentCallingInfo.number = '';
            status.value = '';
            // 释放资源
            await releaseResource();
            cancelPreventRefresh();
        }
    };

    const isCallingNumber = (number) => isCalling.value && currentCallingInfo.number === number;

    instance = {
        status,
        statusMap,
        isLoading,
        isCalling,
        isCallingNumber,
        callout,
        releaseResource,
    };

    return instance;
}
