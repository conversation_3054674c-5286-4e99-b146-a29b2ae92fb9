<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { debounce } from 'lodash';
import { ElMessage } from 'element-plus';
import { formatHisTypeName } from '@abc-oa/utils';
import LayoutPageContainer from '@/layout/components/page-container/layout-page-container.vue';
import LayoutPageMain from '@/layout/components/page-container/layout-page-main.vue';
import LayoutPageElement from '@/layout/components/page-container/layout-page-element.vue';
import { VirtualMobileAPI } from '@/api/virtual-mobile-api.ts';
import DialogAccountsCreateView from '@/views/virtual-account/components/dialog-accounts-create-view.vue';

const route = useRoute();

const keyword = ref('');
const tableData = ref([]);
const currentPage = ref(1);
const pageSize = ref(15);
const totalRecords = ref(0);
const loading = ref(false);
const operateType = ref('create');
const selectedRow = ref(null);
const showDialog = ref(false);

const fetchData = async () => {
    loading.value = true;
    try {
        const offset = (currentPage.value - 1) * pageSize.value;
        let response;
        response = await VirtualMobileAPI.listVirtualMobileUsingGET(pageSize.value, offset, keyword.value);
        if (response && response.rows && response.rows.length) {
            response.rows = response.rows.map((v: any) => {
                v.admins = handleAdmins(v.admins);
                return v;
            });
        }
        tableData.value = response.rows || [];
        totalRecords.value = response.total;
    } catch (error) {
        ElMessage.error(error.message || error);
        console.error('Error fetching phone records:', error);
    } finally {
        loading.value = false;
    }
};

const handlePageChange = (page: number) => {
    currentPage.value = page;
    fetchData();
};

const debouncedFetchData = debounce(fetchData, 300);

const openDialog = (row: any, type: string) => {
    operateType.value = type;
    selectedRow.value = row;
    showDialog.value = true;
};

// 兼容管理员数据返回
const handleAdmins = (admins: any) => {
    if (!admins || admins.length === 0) return [];
    return admins.filter((admin: any) => !!admin);
};

onMounted(() => {
    if (route.query.clinicId) {
        keyword.value = route.query.clinicId as string;
    }
    fetchData();
});

</script>

<template>
    <layout-page-container>
        <layout-page-main>
            <template #header>
                <layout-page-element>
                    <el-space>
                        <el-input
                            v-model="keyword"
                            class="search-input"
                            style="width: 230px;"
                            placeholder="门店名称/门店ID/手机号/openID"
                            clearable
                            @input="debouncedFetchData"
                        />
                        <el-button type="primary" @click="debouncedFetchData">查询</el-button>
                    </el-space>
                </layout-page-element>
            </template>
            <layout-page-element>
                <el-table
                    v-loading="loading"
                    :data="tableData"
                    style="width: 100%;"
                    border
                >
                    <el-table-column label="门店">
                        <template #default="{ row }">
                            <span>{{ row.organ?.name || '-' }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="地区" width="100px">
                        <template #default="{ row }">
                            <span>{{ row.organ?.addressProvinceName + row.organ?.addressCityName || '-' }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="产品" width="100px">
                        <template #default="{ row }">
                            <span>{{ formatHisTypeName(row.organ?.hisType, false) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="版本" width="100px">
                        <template #default="{ row }">
                            <span>{{ row.currentEdition?.name }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="管理员" width="200px">
                        <template #default="{ row }">
                            <span v-if="row.admins.length === 0">-</span>
                            <span v-else-if="row.admins.length === 1">{{ `${row.admins[0].name}/${row.admins[0].mobile}` }}</span>
                            <el-tooltip
                                v-else
                                effect="dark"
                                placement="right-start"
                            >
                                <template #content>
                                    <div v-for="admin in row.admins" :key="admin.employeeId">{{ `${admin.name}/${admin.mobile}` }}</div>
                                </template>
                                <span>{{ `${row.admins[0].name}/${row.admins[0].mobile}...` }}</span>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                    <el-table-column label="账号数" width="100px">
                        <template #default="{ row }">
                            <span v-if="row.currentEdition">
                                {{ `${row.currentEdition?.currentEmployeeCount}/${row.currentEdition?.maxEmployeeCount}` }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="recordUrl" label="已绑定虚拟账号" width="140px">
                        <template #default="{ row }">
                            <span>{{ row.virtualMobileItems?.length || 0 }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="操作"
                        width="310px"
                        fixed="right"
                    >
                        <template #default="{ row }">
                            <el-button :disabled="!row.currentEdition" @click="openDialog(row, 'view')">
                                已绑定虚拟账号
                            </el-button>
                            <el-button type="primary" :disabled="!row.currentEdition" @click="openDialog(row, 'create')">
                                创建虚拟账号
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                    :current-page="currentPage"
                    :page-size="pageSize"
                    layout="total, prev, pager, next"
                    :total="totalRecords"
                    @current-change="handlePageChange"
                />
            </layout-page-element>
        </layout-page-main>
        <dialog-accounts-create-view
            v-if="showDialog"
            v-model="showDialog"
            :operate-type="operateType"
            :selected-row="selectedRow"
            @refresh="fetchData"
        />
    </layout-page-container>
</template>

<style lang="scss">
.page-table {
    flex-direction: column;
    gap: 16px;
}
</style>
