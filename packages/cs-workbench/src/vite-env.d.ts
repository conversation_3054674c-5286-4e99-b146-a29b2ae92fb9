/// <reference types="vite/client" />
declare module 'lodash'

interface ImportMetaEnv {
    VITE_APP_PERMISSION_FOLLOW_UP: string
    VITE_APP_PERMISSION_CALLOUT_HISTORY: string
    VITE_APP_HIS_DOMAIN: string
    // 沟通记录查看权限
    VITE_APP_PERMISSION_SALES_AFTER_VIEW: string
    // 沟通记录编辑权限
    VITE_APP_PERMISSION_SALES_AFTER_EDIT: string
    // 沟通记录审核权限
    VITE_APP_PERMISSION_AFTER_SALES_AUDIT: string
    // 远程记录查看权限
    VITE_APP_PERMISSION_REMOTELY_RECORD_VIEW: string
    // 四川公钥
    VITE_APP_PERMISSION_SICHUAN_KEY: string
}

interface ImportMeta {
    readonly env: ImportMetaEnv
}

declare global {
    interface Window {
        electronFlag?: boolean;
        electron?: {
            remote?: {
                app?: {
                    openVoiceRecorder?: () => Promise<{ success: boolean; error?: string }>;
                };
                shell?: {
                    openExternal?: (url: string) => void;
                };
            };
            ipcRenderer?: any;
        };
        require?: any;
    }
}
