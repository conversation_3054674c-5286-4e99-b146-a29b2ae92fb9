import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import Iconify from '@tomjs/vite-plugin-iconify';
import { resolve } from 'path';
import * as AbcBuildTool from 'abc-fed-build-tool';

const AbcViteOssPlugin = AbcBuildTool.AbcViteOSSPlugin;

const tagSuffix = process.env.BUILD_TAG ? `/${process.env.BUILD_TAG}` : '';
const { bucket, region, secretId, secretKey, url } = AbcBuildTool.OSS.getOSSInfo(process.env.BUILD_ENV || 'dev', `cs${tagSuffix}`);

const pathResolve = (dir: string) => resolve(__dirname, '.', dir);
const alias = {
    '@': pathResolve('./src/'),
    assets: pathResolve('./src/assets'),
    '@social': pathResolve('../shared/social/src/'),
    '@shared/utils': pathResolve('../shared/utils/'),
};

const remoteSDKOSSInfo = AbcBuildTool.OSS.getOSSInfo(process.env.BUILD_ENV || 'dev', 'abc-remote-assist');

const getRemoteSDKUrl = function () {
    return 'https:' + remoteSDKOSSInfo.url + 'remote-assist.js';
};

interface HtmlPluginOptions {
    injectData: Record<string, any>
}

const htmlPlugin = (options: HtmlPluginOptions) => {
    const injectData = options.injectData || {};
    return {
        name: 'html-transform',
        transformIndexHtml(html: string) {
            return html.replace(/<%=(.*?)%>/g, (match, p1) => injectData[p1]);
        },
    };
};

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
    base: mode && mode !== 'loc' ? url : '/',
    envDir: pathResolve('./env'),
    plugins: [
        vue(),
        Iconify(),
        htmlPlugin({
            injectData: {
                remoteSDKUrl: getRemoteSDKUrl(),
            },
        }),
        mode && mode !== 'loc' ? AbcViteOssPlugin({
            prefix: `cs${tagSuffix}`,
            ossType: 'ali',
            Bucket: bucket,
            Region: region,
            SecretId: secretId,
            SecretKey: secretKey,
        }) : null,
    ],
    resolve: {
        alias,
    },
    server: {
        proxy: {
            '/api/low-code': {
                target: 'https://dev-oa.abczs.cn',
                // target: 'http://localhost:3070',
                changeOrigin: true,
                // headers: {
                //     'user-mobile': '13980727980',
                //     'user-id': '10007',
                // },
                bypass(req, res, proxyOptions) {
                    req.headers.origin = 'https://dev-oa.abczs.cn';
                },
            },
            '/api': {
                target: 'https://dev-oa.abczs.cn',
                changeOrigin: true,
                bypass(req, res, proxyOptions) {
                    req.headers.origin = 'https://dev-oa.abczs.cn';
                },
                configure: (proxy, options) => {
                    proxy.on('proxyRes', (proxyRes, req, res) => {
                        if (proxyRes.headers['set-cookie']) {
                            proxyRes.headers['set-cookie'] = proxyRes.headers['set-cookie'].map(item => {
                                console.log('cookie:', item, req.headers.host);
                                // 去掉 cookie 中的 Secure 和 SameSite配置，注意 SameSite 的不同取值，可能是 None 可能是 Lax
                                item = item
                                                .replace(/; Secure/g, '')
                                                .replace(/; SameSite=None/g, '')
                                                .replace(/; SameSite=Lax/g, '');
                                console.log('cookie:', item);
                                return item;
                            });
                        }
                    });
                },
            },
        },
    },
}));
