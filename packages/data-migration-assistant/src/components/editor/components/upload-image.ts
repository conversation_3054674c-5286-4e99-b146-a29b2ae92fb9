import { useFormat } from '@/composables/date';
import OSSUtil from '@/utils/oss';

/**
 * @description: 上传base64类型图片至阿里云并返回url
 * @date: 2023-12-02 17:33:18
 * @author: <PERSON>
 * @param src: 图片的链接
 * @return
 */
export const handleImageSrc = async (src: any, moduleName: string, rootDir: string): Promise<string> => {
    if (src) {
        const fileName = `${moduleName}-${getBase64Hash(src)}-${useFormat(new Date(), 'YYYY-MM-DD HH:mm:ss')}.png`;

        const bufferData = dataURItoBlob(src);
        const file = new File([bufferData], fileName, { type: 'image/png' });
        try {
            const { url } = await OSSUtil.upload({
                bucket: import.meta.env.VITE_APP_OSS_BUCKET,
                region: import.meta.env.VITE_APP_OSS_REGION,
                rootDir,
            }, file);
            console.log({ url });
            return url;
        } catch (e) {
            console.error('file 上传失败', e);
            return src;
        }
    }
    return src;
};

/**
 * @description: base64转blob
 * @date: 2023-12-02 17:34:31
 * @author: Horace
 * @param dataURI: base64
 * @return
 */
const dataURItoBlob = (dataURI: string) => {
    const byteString = atob(dataURI.split(',')[1]);
    const mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0];
    const ab = new ArrayBuffer(byteString.length);
    const ia = new Uint8Array(ab);

    for (let i = 0; i < byteString.length; i++) {
        ia[i] = byteString.charCodeAt(i);
    }

    return new Blob([ab], { type: mimeString });
};

/**
 * @description:生成基于 base64 的哈希值
 * @date: 2023-12-02 17:27:46
 * @author: Horace
 * @param base64String: base64
 * @return
 */
const getBase64Hash = (base64String: string) => {
    let hash = 0;
    for (let i = 0; i < base64String.length; i++) {
        hash = (hash << 5) - hash + base64String.charCodeAt(i);
        hash |= 0; // Convert to 32bit integer
    }
    return hash.toString(16);
};