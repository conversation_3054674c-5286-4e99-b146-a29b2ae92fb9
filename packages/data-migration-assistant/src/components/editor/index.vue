<script lang="ts" setup>
import { useEditor, EditorContent, BubbleMenu } from '@tiptap/vue-3';
import StarterKit from '@tiptap/starter-kit';
import { nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue';
import EditorHeader from './components/editor-header.vue';
import { Typography } from '@tiptap/extension-typography';
import { Underline } from '@tiptap/extension-underline';
import { Subscript } from '@tiptap/extension-subscript';
import { Superscript } from '@tiptap/extension-superscript';
import { TextAlign } from '@tiptap/extension-text-align';
import { Color } from '@tiptap/extension-color';
import { TextStyle } from '@tiptap/extension-text-style';
import { Highlight } from '@tiptap/extension-highlight';
import { Link } from '@tiptap/extension-link';
import { FontFamily } from '@tiptap/extension-font-family';
// import { Image } from '@tiptap/extension-image';
import { CustomImageExtension } from '@/components/editor/components/custom-image';
import { NodeSelection } from '@tiptap/pm/state';
import { applyDevTools } from 'prosemirror-dev-toolkit';
import { CustomImageWrapperExtension } from '@/components/editor/components/custom-image-wrapper';
import { CustomVideoWrapperExtension } from '@/components/editor/components/custom-video-wrapper';
import { CustomVideoExtension } from '@/components/editor/components/custom-video';
import { CustomFileExtension } from '@/components/editor/components/custom-files';

const props = defineProps({
    modelValue: {
        type: String,
        default: '',
    },
    moduleName: {
        type: String,
        required: true,
    },
    rootDir: {
        type: String,
        required: true,
    },
});
const emit = defineEmits(['update:modelValue']);
// 记录当前光标位置
let cursorPos = ref<any>(null);
const isInitLoad = ref<boolean>(true);
watch(() => props.modelValue, (value) => {
    if (isInitLoad.value) {
        const isSame = editor.value.getHTML() === value;
        if (isSame) {
            return;
        }
        editor.value.commands.setContent(value, false);
        isInitLoad.value = false;
        if (cursorPos.value) {
            editor.value.commands.setTextSelection(cursorPos.value);
        }
    }
});
const options: Record<string, string> = {
    blockquote: 'abc-editor__blockquote',
    bulletList: 'abc-editor__bulletList',
    codeBlock: 'abc-editor__code-block',
    document: 'abc-editor__document',
    hardBreak: 'abc-editor__hard-break',
    heading: 'abc-editor__heading',
    horizontalRule: 'abc-editor__divider',
    listItem: 'abc-editor__list-item',
    orderedList: 'abc-editor__ordered-list',
    paragraph: 'abc-editor__paragraph',
    text: 'abc-editor__text',
    bold: 'abc-editor__bold',
    code: 'abc-editor__code',
    italic: 'abc-editor__italic',
    strike: 'abc-editor__strike',
    dropcursor: 'abc-editor__dropcursor',
    gapcursor: 'abc-editor__gapcursor',
    history: 'abc-editor__history',
};
const starterKitOptions: Record<string, any> = {};
Object.keys(options).forEach((key: string) => {
    starterKitOptions[key] = {
        HTMLAttributes: {
            class: options[key],
        },
    };
});
const editor: any = ref<any>(useEditor({
    content: props.modelValue,
    extensions: [
        StarterKit.configure({
            ...starterKitOptions,
        }),
        Typography,
        Underline.configure({
            HTMLAttributes: {
                class: 'abc-editor__underline',
            },
        }),
        Subscript.configure({
            HTMLAttributes: {
                class: 'abc-editor__subscript',
            },
        }),
        Superscript.configure({
            HTMLAttributes: {
                class: 'abc-editor__superscript',
            },
        }),
        TextStyle.configure({
            HTMLAttributes: {
                class: 'abc-editor__text-style',
            },
        }),
        FontFamily,
        TextAlign.configure({
            types: ['heading', 'paragraph'],
        }),
        // Image.configure({
        //     allowBase64: true,
        // }),
        Color.configure({
            types: ['textStyle'],
        }),
        Highlight.configure({
            multicolor: true,
            HTMLAttributes: {
                class: 'abc-editor__highlight',
            },
        }),
        Link.configure({
            openOnClick: true,
            autolink: false,
            HTMLAttributes: {
                class: 'abc-editor__link',
            },
        }),
        CustomImageExtension.configure({
            allowBase64: true,
        }),
        CustomImageWrapperExtension,
        CustomVideoExtension.configure({
            moduleName: props.moduleName,
            rootDir: props.rootDir,
        }),
        CustomVideoWrapperExtension,
        CustomFileExtension,
    ],
    onCreate: async () => {
        console.log('handleCreate');
        editor.value && applyDevTools(editor.value.view);
    },
    onUpdate: async () => {
        console.log('handleValueUpdate');
        emit('update:modelValue', editor.value.getHTML());
        cursorPos.value = editor.value.state.selection;
    },
}));

const shouldShow = () => {
    const { state } = editor.value?.view;
    const { from, to } = state.selection;
    const selectionContent = state.doc.slice(from, to).content;
    return selectionContent.firstChild?.type.name === 'image';
};
onBeforeUnmount(() => {
    editor.value && editor.value.destroy();
});
const customWidth = ref<string>();
const setImageWidth = (width: string) => {
    const { state, dispatch } = editor.value?.view;
    const { from, to } = state.selection;

    try {
        const node = state.doc.nodeAt(from);
        if (node?.type.name === 'image') {
            const nodeAttrs = { ...node.attrs, width }; // 添加宽度属性
            const newNode = node.type.create(nodeAttrs);
            dispatch(state.tr.replaceSelectionWith(newNode));
        }
    } catch (e) {
        console.log(e);
    }
};

const updateCustomWidth = () => {
    setImageWidth(customWidth.value + 'px');
    customWidth.value = '';
};
</script>
<template>
    <div class="abc-editor-wrapper">
        <editor-header v-if="editor" :editor="editor"></editor-header>
        <editor-content class="abc-editor-content" :editor="editor" />
        <bubble-menu
            v-if="editor"
            class="abc-editor__bubble-menu"
            plugin-key="customer-image-menu"
            :update-delay="300"
            :editor="editor"
            :should-show="shouldShow"
        >
            <el-button @click.s="setImageWidth('30%')">30%</el-button>
            <el-button @click="setImageWidth('50%')">50%</el-button>
            <el-button @click="setImageWidth('80%')">80%</el-button>
            <el-button @click="setImageWidth('100%')">100%</el-button>
            <el-input
                v-model="customWidth"
                placeholder="自定义宽度，单位px"
                @blur="updateCustomWidth"
                @keyup.enter="updateCustomWidth"
            />
        </bubble-menu>
    </div>
</template>
<style lang="scss">
@use './index.scss';

.abc-editor-wrapper {
    background-color: #fff;
    border: 3px solid #0d0d0d;
    border-radius: 8px;
    color: #0d0d0d;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;

    .tiptap.ProseMirror {
        height: 100%;
        overflow-y: auto;
    }

    .abc-editor-content {
        flex: 1 1 auto;
        border-left: 1px solid var(--el-border-color);
        border-right: 1px solid var(--el-border-color);
        width: 90%;
        margin: 0 auto;
        overflow-x: hidden;
        overflow-y: auto;
        padding: 8px;
        -webkit-overflow-scrolling: touch;
    }

    .abc-editor__bubble-menu {
        background: #9c9d9f;
        padding: 8px;

        .el-input {
            padding-top: 12px;
        }
    }

    .abc-editor__custom-image-wrapper {
        .abc-editor__custom-image:active {
            box-shadow: rgba(0, 0, 0, .1) 0 4px 12px;
        }
    }
}
</style>
