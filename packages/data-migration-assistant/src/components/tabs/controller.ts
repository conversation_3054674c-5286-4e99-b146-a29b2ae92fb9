/*
 * <AUTHOR>
 * @DateTime 2024-11-12 16:14:17
 */
import { computed, ref } from 'vue';

export const useLayoutTabsHook = () => {
    const activeName = ref('');
    const tabList = ref<any []>([]);

    // 列表展示
    const list = computed(() => tabList.value.map((item: any) => ({
        value: item.value,
        label: item.label,
        count: item.count || '',
    })));
    // 组件
    const component = computed(() => {
        const target = tabList.value.find((item: any) => item.value === activeName.value);
        return target?.component || null;
    });

    /**
     * 更新搜索关键词
     * <AUTHOR>
     * @date 2024-11-12
     * @param {String} k
     */
    const updateActiveName = (k: string) => {
        activeName.value = k;
    };
    /**
     * @description: 更新快捷列表
     * @date: 2024-11-04 11:24:02
     * @author: Horace
     * @param {QuickList[]} list 快捷列表
     * @return
    */
    function updateTabList(list: any) {
        tabList.value = list;
    }
    return {
        list,
        component,
        updateActiveName,
        updateTabList,
    };
};
