<script setup>
import { computed } from 'vue';
import { useThemeConfigStore } from '@/store/theme-config';
import SubMenu from './sub-menu.vue';
import { useRoute } from 'vue-router';
import { useMenuStore } from '@/store/menu';

const route = useRoute();
const themeConfig = useThemeConfigStore();
const menuStore = useMenuStore();

const menus = computed(() => menuStore.menus);

// 当前激活的菜单
const activeMenu = computed(() => {
    const currentRoute = route.matched?.[1];
    return currentRoute ? currentRoute.name : '';
});

const layoutAsideClass = computed(() => {
    let classes = [];
    if (themeConfig.isCollapse) {
        classes.push('layout-aside__close');
    } else {
        classes.push('layout-aside__open');
    }
    return classes;
});

</script>

<template>
    <el-aside class="layout-aside" :class="layoutAsideClass">
        <div class="layout-aside__brand">
            <img class="brand-img" src="/logo.png">
            <span class="brand-name">数据迁移</span>
        </div>
        <div class="layout-aside__divider"></div>
        <el-menu
            :collapse="themeConfig.isCollapse"
            class="layout-aside__menu"
            :default-active="activeMenu"
        >
            <sub-menu
                v-for="menu in menus"
                :menu="menu"
            >
            </sub-menu>
        </el-menu>
    </el-aside>
</template>

<style scoped></style>
