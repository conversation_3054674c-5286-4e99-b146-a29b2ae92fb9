<script setup lang="ts">
import { useThemeConfigStore } from '@/store/theme-config';
import { computed } from 'vue';
import { useUserStore } from '@/store/user';
import { useRoute } from 'vue-router';

const route = useRoute();
const breadRoutes = computed(() => route.matched.map(r => ({
    label: r.meta.name,
    route: {
        name: r.name,
    },
})));

const userStore = useUserStore();
const userInfo = userStore.userInfo;
const themeConfig = useThemeConfigStore();
const isCollapse = computed(() => themeConfig.isCollapse);

function handleMenuToggle() {
    themeConfig.setCollapse(!isCollapse.value);
}

async function handleDropdownCommand(command: string) {
    if (command === 'logout') {
        await userStore.logout();
    }
}

</script>
<template>
    <el-row class="layout-header-wrapper" align="middle">
        <el-icon class="layout-header__menu-toggle" @click="handleMenuToggle">
            <expand v-if="isCollapse" />
            <fold v-else />
        </el-icon>
        <el-breadcrumb>
            <el-breadcrumb-item v-for="breadRoute in breadRoutes" :to="breadRoute.route">
                {{ breadRoute.label }}
            </el-breadcrumb-item>
        </el-breadcrumb>
        <div class="layout-header__right">
            <el-dropdown trigger="click" @command="handleDropdownCommand">
                <div class="layout-header__user-info">
                    <el-avatar size="small" :src="userInfo.thumbAvatar"></el-avatar>
                    <span class="user-name">{{ userInfo.name }}</span>
                    <el-icon class="el-icon--right">
                        <arrow-down />
                    </el-icon>
                </div>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item command="logout">退出登录</el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
        </div>
    </el-row>
</template>

<style scoped>

</style>
