<script lang="ts" setup>
import GridMenu from '@/components/grid-menu.vue';
import useNavigator from '@/composables/navigator';

const { isEntryPage, subMenus, toMenu } = useNavigator();
</script>
<template>
    <div class="layout-page-wrapper">
        <grid-menu v-if="isEntryPage && subMenus?.length" :menus="subMenus" @click="toMenu"></grid-menu>
        <router-view></router-view>
    </div>
</template>

<style>

</style>
