<script setup lang="ts">
import { computed } from 'vue';
import { LayoutPageContainerPreset } from '@/layout/components/page-container/constants.ts';

const props = defineProps({
    preset: {
        type: String,
        default: LayoutPageContainerPreset.Default,
        validator: (value: string) => Object.values(LayoutPageContainerPreset).includes(value),
    },
});

const computedClass = computed(() => ({
    'layout-page-container': true,
    [`layout-page-container--${props.preset}`]: true,
}));
</script>

<template>
    <div :class="computedClass">
        <slot name="sidebar"></slot>
        <slot></slot>
    </div>
</template>

<style lang="scss">
@use "@/style/mixins/mixins.scss" as mixins;

.layout-page-container {
    height: 100%;
    width: 100%;
    background: var(--oa-white);
    border-radius: var(--oa-border-radius-default);
    overflow: hidden;

    @include mixins.flexLayout(space-between);

    .layout-page-sidebar {
        width: 340px;
    }

    .layout-page-main {
        flex: 1;
        overflow: hidden;
    }

    .layout-page-sidebar + .layout-page-main {
        border-left: var(--oa-pharmacy-border);
        border-radius: 0 var(--oa-border-radius-default) var(--oa-border-radius-default) 0;

        .layout-page-container__header {
            border-radius: 0 var(--oa-border-radius-default) 0 0;
        }
    }

    &--setting {
        .layout-page-sidebar {
            width: 240px;
        }
    }
}
</style>
