<script setup>
import { computed } from 'vue';

const props = defineProps({
    background: {
        type: Boolean,
        default: true,
    },
});

const className = computed(() => ({
    'layout-element-section': true,
    'layout-element-section--background': props.background,
}));

</script>
<template>
    <div :class="className">
        <slot></slot>
    </div>
</template>

<style lang="scss">
.layout-element-section {
    padding: var(--oa-page-element-padding);
    width: 100%;
    min-height: 100%;

    &--background {
        background-color: var(--oa-white);
    }
}
</style>
