<script setup lang="ts">
import { ref, PropType } from 'vue';
import { Plus, Search } from '@element-plus/icons-vue';
import LayoutPageSidebar from '@/layout/components/page-container/layout-page-sidebar.vue';
import LayoutPageElement from '@/layout/components/page-container/layout-page-element.vue';

const emit = defineEmits([
    'search',
    'create',
    'item-click',
]);
const props = defineProps({
    header: {
        type: Boolean,
        default: true,
    },
    toolbar: {
        type: Boolean,
        default: true,
    },
    quickListTotal: {
        type: Number,
        default: 0,
    },
    quickList: {
        type: Array as PropType<any[]>,
        default: () => [],
    },
    searchPlaceholder: {
        type: String,
        default: '搜索',
    },
    needCreate: {
        type: Boolean,
        default: true,
    },
});

// 快捷列表搜索关键字
const keyword = ref<string>('');

function handleKeywordSearch() {
    emit('search', keyword.value);
}
function handleKeywordCreate() {
    emit('create');
}
function handleQuickListItemClick(item: any) {
    emit('item-click', item);
}
</script>

<template>
    <layout-page-sidebar class="layout-quick-list-sidebar">
        <template v-if="props.header" #header>
            <layout-page-element class="layout-quick-list-sidebar__header">
                <slot name="header">
                    <el-space>
                        <el-input
                            v-model="keyword"
                            class="layout-quick-list-sidebar__header-field"
                            :placeholder="searchPlaceholder"
                            :prefix-icon="Search"
                            @input="handleKeywordSearch"
                        ></el-input>
                        <el-button
                            v-if="needCreate"
                            type="success"
                            class="layout-quick-list-sidebar__header-search-button"
                            :icon="Plus"
                            @click="handleKeywordCreate"
                        >
                            新增
                        </el-button>
                    </el-space>
                </slot>
            </layout-page-element>
        </template>
        <template #main>
            <div class="layout-quick-list-sidebar__menu">
                <slot name="menu">
                    <div v-if="props.toolbar" class="layout-quick-list-sidebar__menu-toolbar">
                        <slot name="toolbar">
                            <span>共 {{ props.quickListTotal }}</span>
                        </slot>
                    </div>
                    <div class="layout-quick-list-sidebar__menu-list">
                        <slot name="list">
                            <template v-if="props.quickList?.length">
                                <template v-for="item in props.quickList" :key="item.value">
                                    <div
                                        class="layout-quick-list-sidebar__menu-list-item"
                                        :class="{'is-active': item.isActive}"
                                        @click="handleQuickListItemClick(item)"
                                    >
                                        <slot name="list-item" :item="item">
                                            <span class="name">{{ item.clinicName }}</span>
                                            <span class="time">{{ item.lastModifiedWording }}</span>
                                        </slot>
                                    </div>
                                </template>
                            </template>
                            <template v-else>
                                <div class="layout-quick-list-sidebar__menu-empty">
                                    <el-empty description="无相关数据" />
                                </div>
                            </template>
                        </slot>
                    </div>
                </slot>
            </div>
        </template>
    </layout-page-sidebar>
</template>

<style lang="scss">
@use "@/style/mixins/mixins.scss" as mixins;

.layout-quick-list-sidebar {
    &__header {
        height: var(--oa-pharmacy-header-height);

        .el-space {
            width: 100%;
            justify-content: space-between;

            &__item:first-child {
                flex: 1;
            }

            &__item:last-child {
                margin-right: 0 !important;
            }
        }

        &-search-button {
            width: 70px;
        }
    }

    &__menu {
        width: 100%;
        flex: 1;
        height: 0;

        @include mixins.flexLayout(flex-start, flex-start, column);

        &-toolbar {
            width: 100%;
            padding: 8px 20px;
            line-height: 22px;
            color: var(--oa-text-color-2);
            border-bottom: var(--oa-pharmacy-border);
        }

        &-list {
            width: 100%;
            flex: 1;
            overflow-y: scroll;
            padding: 8px 0 8px 10px;

            &::-webkit-scrollbar {
                height: 12px;
                width: 10px;
                display: block;
            }

            &::-webkit-scrollbar-thumb {
                background: #e6eaed;
                background-clip: content-box;
                border: 1px solid rgba(0, 0, 0, 0);
                border-radius: 6px;
                cursor: pointer;
            }

            &::-webkit-scrollbar-thumb:hover {
                background: #dee2e6;
                background-clip: content-box;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                cursor: pointer;
            }

            &::-webkit-scrollbar-thumb {
                visibility: hidden;
            }

            &:hover::-webkit-scrollbar-thumb {
                visibility: visible;
            }

            &::-webkit-scrollbar-track,
            &:hover::-webkit-scrollbar-track {
                background: rgba(0, 0, 0, 0);
                opacity: 0;
            }
        }

        &-empty {
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px 0;
        }

        &-list-item {
            --oa-card-border-radius: var(--oa-border-radius-default);
            --oa-card-padding: var(--oa-padding-14);

            border-radius: var(--oa-card-border-radius);
            background-color: var(--oa-white);
            padding: var(--oa-padding-14);
            cursor: pointer;

            @include mixins.flexLayout(flex-start, center);

            .name {
                flex: 1;
            }

            .time {
                color: var(--oa-text-color-2);
                font-size: 12px;
                flex-shrink: 0;
            }

            &:hover {
                background: var(--oa-gray-4);
            }

            & + & {
                margin-top: 8px;
            }

            &.is-active {
                background: var(--oa-primary-color);
                color: var(--oa-white);

                .time {
                    color: var(--oa-white);
                }
            }
        }
    }
}
</style>
