import { createApp } from 'vue';
import router from '@/router/index';
import { createPinia } from 'pinia';
import ElementPlus, { ElMessage } from 'element-plus';
import zhCn from 'element-plus/es/locale/lang/zh-cn';
import 'element-plus/dist/index.css';
import * as ElIconModules from '@element-plus/icons-vue';
import '@/style/index.scss';
import App from './App.vue';
import { ApiService, cloudFunction } from '@abc-oa/common';
import { OaIcon } from '@abc-oa/components';

const app = createApp(App);
const netWorkErrCallback = () => {
    ElMessage({
        message: '你的网络有问题，请检查网络设置！',
        type: 'error',
        duration: 3 * 1000,
    });
};
ApiService.init({
    baseURL: import.meta.env.VITE_APP_BASE_API,
    timeout: 60000,
    netWorkErrCallback,
    useParamsSerializer: true,
});

for (const iconName in ElIconModules) {
    if (Reflect.has(ElIconModules, iconName)) {
        // @ts-ignore
        const item = ElIconModules[iconName];
        app.component(iconName, item);
    }
}
app.component('OaIcon', OaIcon);

// 提供路由器实例，供共享组件使用
app.provide('router', router);

app.use(router)
                .use(createPinia())
                .use(ElementPlus, {
                    locale: zhCn,
                })
                .mount('#app');

console.log('构建环境:', import.meta.env.VITE_APP_BUILD_ENV);
console.log('时间:', import.meta.env.VITE_APP_BUILD_TIME);
console.log('basdasdawd1a撒打算大', cloudFunction);
