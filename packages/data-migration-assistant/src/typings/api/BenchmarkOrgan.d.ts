declare namespace  AbcAPI {
    
    type BenchmarkOrgan = {    
        //创建人
        createdBy:string    
        
        created:any    
        //最后修改人
        lastModifiedBy:string    
        
        lastModified:any    
        
        id:number    
        //绑定的连锁id
        chainId:string    
        //绑定的门店id
        clinicId:string    
        //诊所详细地址
        addressDetail:string    
        //诊所经纬度
        addressGeo:string    
        //推荐诊所头图
        displayPicture:string    
        //推荐诊所理由
        recommendRemark:string    
        //标杆客户类型（0：自动设置，1：手动设置）
        benchmarkType:number    
        //营收类型（0：营收低，1：营收高）
        incomeType:number    
        //是否删除（0：正常，1：删除）
        isDeleted:number    
    }
}
