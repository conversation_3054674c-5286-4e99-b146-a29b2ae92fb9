declare namespace  AbcAPI {
    
    type FindClinicDetailVo = {    
        //门店连锁ID
        chainId:string    
        //门店连锁名称
        chainName:string    
        //门店ID
        clinicId:string    
        //门店名称
        clinicName:string    
        //诊所类型
        hisType:number    
        //门店类型
        nodeTypeName:string    
        //诊所版本
        editionId:string    
        //用户ID
        employeeId:string    
        //用户
        employeeName:string    
        //是否关注微信
        employeeSubscribe:number    
        //微信昵称
        employeeWechatName:string    
        //openId
        employeeOpenId:string    
        //电话
        employeeMobile:string    
        //门店分区
        regionId:string    
        //是否为管理员
        roleId:number    
        
        editionBeginDate:any    
        
        editionEndDate:any    
        //员工数量
        employeeCount:number    
        //是否为试用版
        isTrial:number    
        //公众号appId
        mpAppId:string    
        //环境
        organEnv:string    
        //企业微信CorpId
        qwCorpId:string    
        //企业微信CorpName
        qwCorpName:string    
        //销售名称
        sellerName:string    
        //小程序appId
        weAppId:string    
    }
}
