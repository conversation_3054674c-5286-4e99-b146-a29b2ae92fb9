<script setup lang="ts">
import { ref } from 'vue';
import { Picture } from '@element-plus/icons-vue';
import LayoutPageMain from '@/layout/components/page-container/layout-page-main.vue';
import LayoutPageElement from '@/layout/components/page-container/layout-page-element.vue';

const currentRows = window.history.state.rows ? JSON.parse(window.history.state.rows) : '';
console.log('currentRows', currentRows);

// 加载状态
const exporting = ref(false);

// 执行导出操作
const handleExport = async () => {
    console.log('导出');
};
</script>
<template>
    <layout-page-main>
        <div style="height: 100%;">
            <el-scrollbar>
                <layout-page-element :background="false">
                    <div class="competitive-product-export-detail">
                        <div class="form-container">
                            <div class="form-item">
                                <div class="form-label">竞品名称</div>
                                <div class="form-content">
                                    {{ currentRows.name }}
                                </div>
                            </div>
                            
                            <div class="form-item">
                                <div class="form-label">竞品icon</div>
                                <div class="form-content">
                                    <div class="icon-wrapper">
                                        <el-image 
                                            :src="currentRows.iconUrl"
                                            style="width: 120px; height: 120px;"
                                        >
                                            <template #placeholder>
                                                <div class="image-placeholder">
                                                    <el-icon><Picture /></el-icon>
                                                </div>
                                            </template>
                                        </el-image>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-item">
                                <div class="form-label">操作文档</div>
                                <div class="form-content">
                                    <div class="document-list">
                                        <div v-for="(doc, index) in currentRows?.exportOperateDoc" :key="index" class="document-item">
                                            <el-link type="primary" :underline="false" :href="doc.url">{{ doc.name }}</el-link>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-item">
                                <div class="form-label"></div>
                                <div class="form-content">
                                    <el-button
                                        type="primary"
                                        :loading="exporting"
                                        disabled
                                        @click="handleExport"
                                    >
                                        执行导出
                                    </el-button>
                                </div>
                            </div>
                        </div>
                    </div>
                </layout-page-element>
            </el-scrollbar>
        </div>
    </layout-page-main>
</template>

<style lang="scss" scoped>
.competitive-product-export-detail {
    padding: 16px;

    .form-container {
        width: 100%;
        max-width: 600px;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        padding: 20px;
        background-color: #fff;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .05);
    }

    .form-item {
        display: flex;
        margin-bottom: 16px;
        align-items: baseline;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .form-label {
        width: 80px;
        font-size: 14px;
        color: #606266;
        line-height: 32px;
    }

    .form-content {
        flex: 1;
    }

    .icon-wrapper {
        border: 1px dashed #d9d9d9;
        border-radius: 4px;
        width: 120px;
        height: 120px;
        display: flex;
        justify-content: center;
        align-items: center;
        overflow: hidden;
    }

    .image-placeholder {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        background-color: #f5f7fa;

        .el-icon {
            font-size: 28px;
            color: #c0c4cc;
        }
    }

    .document-list {
        display: flex;
        flex-direction: column;

        .document-item {
            margin-bottom: 8px;

            &:last-child {
                margin-bottom: 0;
            }
        }
    }
}
</style>
