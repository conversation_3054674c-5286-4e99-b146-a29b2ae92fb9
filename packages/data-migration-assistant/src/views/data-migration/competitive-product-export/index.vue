<script setup lang="ts">
import { defineAsyncComponent, onUnmounted, ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';

import LayoutPageContainer from '@/layout/components/page-container/layout-page-container.vue';
import { useCompetitiveProductListHook } from '@/views/data-migration/hook/competitive-product-list';
import { ElMessage } from 'element-plus';
import { Picture as IconPicture } from '@element-plus/icons-vue';
import { DataMigrationRouteKey } from '@/views/data-migration/route.ts';

const LayoutQuickListSidebar = defineAsyncComponent(() => import('@/layout/components/quick-list-wrapper/layout-quick-list-sidebar.vue'));
const AddCompetiveProductExportTaskDialog = defineAsyncComponent(
    () => import('@/views/data-migration/components/add-competitive-product-export-task-dialog/index.vue'),
);

const showAddCompetiveProductExportTaskDialog = ref(false);
const router = useRouter();
// 使用竞品列表hook
const {
    listShow,
    listTotal,
    loading,
    updateKeyword,
    setSelectedItem,
    fetchCompetitiveProductList,
    destroy,
} = useCompetitiveProductListHook();

// 组件挂载时获取竞品列表数据
onMounted(() => {
    fetchCompetitiveProductList();
});

// 组件卸载时清理资源
onUnmounted(() => {
    destroy();
});

// 处理创建新任务
const handleCreate = () => {
    showAddCompetiveProductExportTaskDialog.value = true;
};

// 处理竞品列表项点击
const handleItemClick = async (item: any) => {
    setSelectedItem(item.id);
    await router.push({
        name: DataMigrationRouteKey.ExportDetail,
        params: {
            id: item.id,
        },
        state: {
            rows: JSON.stringify(item.competitorInfo), 
        },
    });
};

</script>
<template>
    <layout-page-container>
        <template #sidebar>
            <layout-quick-list-sidebar
                :need-create="true"
                :loading="loading"
                :quick-list="listShow"
                :quick-list-total="listTotal"
                search-placeholder="竞品名称"
                @search="updateKeyword"
                @create="handleCreate"
                @item-click="handleItemClick"
            >
                <template #toolbar>
                    <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                        <span>共 {{ listTotal }}</span>
                    </div>
                </template>
                <template #search-extra>
                </template>
                <template #list-item="{item}">
                    <div class="follow-up-list-item">
                        <div class="line1">
                            <oa-icon icon="qlementine-icons:task-16" :color="item.isActive ? 'white':'#287ef1'"></oa-icon>
                            <span class="follow-up-list-item-task-name">{{ item.taskName }}</span>
                        </div>
                        <div class="line2">
                            <span class="follow-up-list-item-created" :style="{ color: item.isActive ? 'white' : '' }">{{ item.created }}</span>
                        </div>
                    </div>
                </template>
            </layout-quick-list-sidebar>
        </template>
        <router-view></router-view>
        <AddCompetiveProductExportTaskDialog
            v-if="showAddCompetiveProductExportTaskDialog"
            v-model="showAddCompetiveProductExportTaskDialog"
            @submit="fetchCompetitiveProductList"
        ></AddCompetiveProductExportTaskDialog>
    </layout-page-container>
</template>

<style lang="scss">
@use "@/style/mixins/mixins.scss" as mixins;

.follow-up-list-item {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 4px;

    .line1 {
        display: flex;
        align-items: center;
        gap: 4px;

        .follow-up-list-item-remark-sign {
            max-width: 24px;
            margin-right: 8px;
        }

        .follow-up-list-item-task-name {
            flex: 1;
        }
    }

    .line2 {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .follow-up-list-item-created {
            color: #999;
            margin-left: 18px;
        }
    }
}
</style>
