<template>
    <div class="patient-member-import-container">
        <div class="operation-bar">
            <el-button type="primary" @click="handleOpenImportDialog(TaskTypeEnum.PatientMemberImport)">上传患者会员表格</el-button>
        </div>
        
        <el-table
            v-loading="loading"
            :data="tableData"
            style="width: 100%;"
            border
        >
            <el-table-column prop="created" label="导入时间" />
            <el-table-column prop="lastModifiedByName" label="操作人员" />
            <el-table-column prop="type" label="操作类型">
                <template #default="{row}">
                    {{ row.type === TaskTypeEnum.PatientMemberImport ? '患者会员导入' : '未知' }}
                </template>
            </el-table-column>
            <el-table-column prop="dataType" label="数据类型" />
            <el-table-column prop="dataCount" label="数据条数" />
            <el-table-column prop="status" label="导入状态">
                <template #default="{row}">
                    <el-tag
                        :type="getStatusType(row.status)"
                    >
                        <div class="tag-status-loading">
                            {{ formatImportStatusEnum(row.status, row.type) }}
                            <el-icon
                                v-if="[ImportStatusEnum.Parsing, ImportStatusEnum.Processing].includes(row.status)"
                                class="is-loading"
                                color="var(--el-color-primary)"
                                :size="26"
                            >
                                <loading-icon />
                            </el-icon>
                        </div>
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="操作">
                <template #default="{row}">
                    <el-button
                        v-if="row.status !== ImportStatusEnum.Cancel"
                        type="primary"
                        link
                        @click="handleViewDetail(row)"
                    >
                        详情
                    </el-button>
                    <el-button
                        v-if="row.status !== ImportStatusEnum.Processing && row.status !== ImportStatusEnum.Completed && row.status !== ImportStatusEnum.Cancel"
                        type="primary"
                        link
                        @click="handleCancel(row)"
                    >
                        取消
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <div v-if="totalRecords > 0" class="pagination-container">
            <el-pagination
                :current-page="currentPage"
                :page-size="pageSize"
                layout="total, prev, pager, next"
                :total="totalRecords"
                @current-change="handlePageChange"
            />
        </div>

        <el-empty v-if="tableData.length === 0 && !loading" description="暂无数据" />

        <!-- 添加门店对话框 -->
        <DataImportDialog
            v-if="showImportDialog"
            v-model="showImportDialog"
            :task-type="currentTaskType"
            @submit="addTaskCallback"
        />
        <UpdateErrorExcelDialog
            v-if="showUpdeteErrorExcelDialog"
            v-model="showUpdeteErrorExcelDialog"
            :task-type="currentTaskType"
            :task-id="currentTaskId"
            @refresh="fetchTableData"
        />
        <SuccessDataTableDialog
            v-if="showSuccessDataTableDialog"
            v-model="showSuccessDataTableDialog"
            :task-type="currentTaskType"
            :task-id="currentTaskId"
        />
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, defineAsyncComponent, watch, h } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { TaskAPI } from '@/api/task-api';
import { TaskTypeEnum, ImportStatusEnum, formatImportStatusEnum } from '@/views/data-migration/constant';
import { useLayoutQuickListHook } from '../hook/quick-list.ts';
import { useRoute } from 'vue-router';
import { DataMigrationRouteKey } from '@/views/data-migration/route.ts';
import { useTaskDialog } from '../hook/use-task-dialog.ts';
import { Loading as LoadingIcon } from '@element-plus/icons-vue';

const DataImportDialog = defineAsyncComponent(() => import('@/views/data-migration/components/import-dialog/index.vue'));
const UpdateErrorExcelDialog = defineAsyncComponent(() => import('@/views/data-migration/components/update-error-excel-dialog/index.vue'));
const SuccessDataTableDialog = defineAsyncComponent(() => import('@/views/data-migration/components/success-data-table-dialog/index.vue'));

const { taskId, updateTaskId } = useTaskDialog();

const {
    parseUniqueKey,
} = useLayoutQuickListHook();

// 加载状态
const loading = ref(false);

// 当前任务类型
const currentTaskType = ref<TaskTypeEnum>(TaskTypeEnum.PatientMemberImport);

// 表格数据
const tableData = ref<any[]>([]);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const totalRecords = ref(0);

// 添加门店对话框
const showImportDialog = ref(false);
const showUpdeteErrorExcelDialog = ref(false);
const showSuccessDataTableDialog = ref(false);
const currentTaskId = ref('');
const route = useRoute();

// 定时器引用
let refreshTimer: number | null = null;

// 初始化数据
onMounted(() => {
    if (taskId.value) {
        openTaskDialog(taskId.value);
        updateTaskId('');
    }
    fetchTableData();
});

// 组件卸载时清除定时器
onUnmounted(() => {
    if (refreshTimer !== null) {
        clearTimeout(refreshTimer);
        refreshTimer = null;
    }
});

/**
 * 检查是否有非终态的任务，如果有则启动自动刷新
 */
const checkAndStartAutoRefresh = () => {
    // 清除可能存在的定时器
    if (refreshTimer !== null) {
        clearTimeout(refreshTimer);
        refreshTimer = null;
    }
    
    // 检查是否存在非终态任务（不是 Cancel、Failed 或 Completed）
    const hasNonTerminalTask = tableData.value.some(row => ![
        ImportStatusEnum.MappingFailed,
        ImportStatusEnum.Cancel,
        ImportStatusEnum.Failed,
        ImportStatusEnum.Completed,
        ImportStatusEnum.ParseFailed,
        ImportStatusEnum.ParseSuccess].includes(row.status));
    // 如果有非终态任务，启动定时器
    if (hasNonTerminalTask) {
        console.log('存在进行中的任务，10秒后自动刷新');
        refreshTimer = window.setTimeout(() => {
            // 自动刷新时不需要loading效果
            fetchTableData(false);
        }, 10000);
    }
};

// 获取表格数据
/**
 * @description: 获取表格数据
 * @param {boolean} isNeedLoading - 是否需要显示loading效果，默认为true
 * @return {Promise<void>}
 */
const fetchTableData = async (isNeedLoading = true) => {
    const limit = pageSize.value;
    const { clinicId } = parseUniqueKey(route.params.id as string);
    const offset = pageSize.value * (currentPage.value - 1);
    try {
        // 根据isNeedLoading参数决定是否显示loading效果
        if (isNeedLoading) {
            loading.value = true;
        }
        const res: any = await TaskAPI.listTasksUsingGET(limit, offset, clinicId, undefined, TaskTypeEnum.PatientMemberImport, undefined);
        tableData.value = res.rows;
        totalRecords.value = res.total;
            
        // 检查是否需要启动自动刷新
        checkAndStartAutoRefresh();
    } catch (err: any) {
        ElMessage.error(err.message || err);
    } finally {
        // 只有开启了loading才需要关闭
        if (isNeedLoading) {
            loading.value = false;
        }
    }
};

const openTaskDialog = async (taskId: string) => {
    try {
        loading.value = true;
        const res: any = await TaskAPI.getTaskUsingGET(taskId);
        if (res.id) handleViewDetail(res);
    } catch (err: any) {
        ElMessage.error(err.message || err);
    } finally {
        loading.value = false;
    }
};

// 添加任务回调
const addTaskCallback = (id: string) => {
    fetchTableData();
    currentTaskId.value = id;
    showUpdeteErrorExcelDialog.value = true;
};

// 处理页码变化
const handlePageChange = (page: number) => {
    currentPage.value = page;
    fetchTableData();
};

// 打开添加对话框
const handleOpenImportDialog = (type: TaskTypeEnum) => {
    currentTaskType.value = type;
    showImportDialog.value = true;
};

// 查看详情
const handleViewDetail = (row: any) => {
    if (row.errDetail) {
        ElMessageBox({
            title: '导入失败详情',
            message: h('div', { class: 'import-success-details' }, [
                h('div', { class: 'detail-item' }, [
                    h('span', { class: 'detail-label' }, '导入时间：'),
                    h('span', { class: 'detail-value' }, row.created),
                ]),
                h('div', { class: 'detail-item' }, [
                    h('span', { class: 'detail-label' }, '导入类型：'),
                    h('span', { class: 'detail-value' }, row.dataType),
                ]),
                h('div', { class: 'detail-item' }, [
                    h('span', { class: 'detail-label' }, '导入数量：'),
                    h('span', { class: 'detail-value' }, row.dataCount),
                ]),
                h('div', { class: 'detail-item' }, [
                    h('span', { class: 'detail-label' }, '错误详情：'),
                    h('span', { class: 'detail-value' }, row.errDetail),
                ]),
            ]),
            showCancelButton: false,
            showConfirmButton: false,
            beforeClose: (action, instance, done) => {
                done();
            },
        });
        return;
    }
    if (row.status === ImportStatusEnum.Completed) {
        currentTaskId.value = row.id;
        currentTaskType.value = row.type;
        showSuccessDataTableDialog.value = true;
    } else {
        currentTaskId.value = row.id;
        currentTaskType.value = row.type;
        showUpdeteErrorExcelDialog.value = true;
    }
};

/**
 * @description: 取消任务
 * @Date: 2025-05-19 18:27:49
 * @author: yaoyongpeng
 * @param {*} row
 * @return {*}
 */
const handleCancel = (row: any) => {
    if (!row.id) return;
    ElMessageBox.confirm(
        '确定取消本次导入任务吗?',
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        },
    )
                    .then(async () => {
                        try {
                            const res: any = await TaskAPI.cancelTaskUsingPUT(row.id);
                            if (res.id) {
                                ElMessage.success('取消成功！');
                                fetchTableData();
                            }
                        } catch (err: any) {
                            ElMessage.error(err.message || err);
                        }
                    })
                    .catch(() => {});
};

// 获取状态对应的标签类型
const getStatusType = (status: number) => {
    switch (status) {
        case ImportStatusEnum.Processing: // 导入中
            return 'primary';
        case ImportStatusEnum.Mapping: // 映射中
            return 'info';
        case ImportStatusEnum.MappingFailed: // 表头映射待确认
            return 'success';
        case ImportStatusEnum.Parsing: // 解析中
            return 'info';
        case ImportStatusEnum.ParseSuccess: // 解析成功
            return 'success';
        case ImportStatusEnum.ParseFailed: // 解析失败
            return 'danger';
        case ImportStatusEnum.Completed: // 完成
            return 'success';
        case ImportStatusEnum.Failed: // 失败
            return 'danger';
        default:
            return 'info';
    }
};

watch(() => route.params.id, async (id) => {
    // 判断是否是当前路由
    if (route.matched?.findIndex((item) => item.name === DataMigrationRouteKey.Main) === -1) {
        return;
    }
    fetchTableData();
}, {
    deep: true,
});
</script>

<style lang="scss" scoped>
.tag-status-loading {
    display: flex;
    align-items: center;
    gap: 2px;
}

.patient-member-import-container {
    .operation-bar {
        margin-bottom: 20px;
        display: flex;
        justify-content: flex-start;
    }

    .pagination-container {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;
    }
}
</style>

<style>
/* 弹窗内容样式 */
.import-success-details {
    padding: 10px 0;
}

.detail-item {
    margin-bottom: 15px;
    line-height: 24px;
    display: flex;
}

.detail-label {
    color: #909399;
    width: 100px;
    text-align: right;
    padding-right: 12px;
}

.detail-value {
    color: #303133;
    flex: 1;
}
</style>