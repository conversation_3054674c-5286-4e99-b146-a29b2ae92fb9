# 环境变量

## mode
Vite 通过 `--mode` 来设置模式，模式会影响到环境变量的值。

## 环境变量

通过 `.env` 文件进行环境变量的配置，详细介绍参考 [Vite 环境变量](https://cn.vite.dev/guide/env-and-mode#env-files)。

`./env` 目录下的文件会被自动加载到 `import.meta.env` 中，可以通过 `import.meta.env` 来访问环境变量。

`.env` 为基础配置，其余文件为对应环境的配置：`.env.loc` 为本地配置，`.env.dev` 为开发环境配置，`.env.test` 为测试环境配置，`.env.prod` 为生产环境配置

### 优先级
各个环境的配置优先级 > 基础配置，基于此特性，应该将通用配置放在 `.env` 中，与环境相关的配置放在对应的环境文件中，进行覆盖

例如，针对权限相关的配置，可以在 `.env` 中设置默认值，然后在 `.env.prod` 中进行覆盖
