/*
 * <AUTHOR> 
 * @DateTime 2025-09-03 18:21:30 
 */
import { isProd } from '@/common/env';
import { fetchPack } from '@abc-oa/common';
import AbcResponse from '@/common/AbcResponse';

export default class BaseApi {
    static fetchPack(options: any) {
        if (
            isProd
            && window.isGodMode !== true
            && options.isUnableRequest
        ) {
            // 正式环境，禁止请求
            return AbcResponse.error('正式环境，禁止请求');
        }
        return fetchPack(options);
    }
}
