/*
 * <AUTHOR> 
 * @DateTime 2025-09-08 19:40:32 
 */
import BaseApi from './base-api';

export default class CorpApi extends BaseApi {
    /**
     * 获取oss token
     * <AUTHOR>
     * @date 2025-10-15
     * @returns {Promise<AbcResponse>}
     */
    static getOssToken() {
        return this.fetchPack({
            url: '/api/management/corp/oss-token',
            method: 'GET',
        });
    }
}