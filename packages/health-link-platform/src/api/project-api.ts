/*
 * <AUTHOR> 
 * @DateTime 2025-09-08 19:40:32 
 */
import BaseApi from './base-api';

export default class ProjectApi extends BaseApi {
    /**
     * 获取项目列表
     * <AUTHOR>
     * @date 2025-09-08
     * @param {Object} params
     * @returns {Promise<AbcResponse>}
     */
    static fetchProjectList(params: any) {
        return this.fetchPack({
            url: '/api/management/healthlink/project',
            method: 'GET',
            params,
        });
    }

    /**
     * 创建医疗互联项目
     * <AUTHOR>
     * @date 2025-09-08
     * @param {Object} data
     * @returns {Promise<AbcResponse>}
     */
    static createProject(data: any) {
        return this.fetchPack({
            url: '/api/management/healthlink/project',
            method: 'POST',
            data,
            isUnableRequest: true,
        });
    }
    
    /**
     * 更新医疗互联项目
     * <AUTHOR>
     * @date 2025-09-08
     * @param {string} id
     * @param {Object} data
     * @returns {Promise<AbcResponse>}
     */
    static updateProject(id: string, data: any) {
        return this.fetchPack({
            url: `/api/management/healthlink/project/${id}`,
            method: 'PUT',
            data,
            isUnableRequest: true,
        });
    }
    
    /**
     * 删除医疗互联项目
     * <AUTHOR>
     * @date 2025-09-08
     * @param {string} id
     * @returns {Promise<AbcResponse>}
     */
    static deleteProject(id: string) {
        return this.fetchPack({
            url: `/api/management/healthlink/project/${id}`,
            method: 'DELETE',
            isUnableRequest: true,
        });
    }
}