import { IUser } from '@/common/model/user';
import { defineStore } from 'pinia';
import { getUserInfo, logout } from 'abc-oa-auth-sdk';

export const useUserStore = defineStore('user', {
    state: () => ({
        userInfo: <IUser>{},
    }),

    getters: {
        // tags || departments 决定权限
        roles: state => (state.userInfo.tags || []).map(tag => tag.name).concat((state.userInfo.departments || []).map(department => department.name)),
    },

    actions: {
        async fetchUserInfo() {
            try {
                this.userInfo = getUserInfo();
                return this.userInfo;
            } catch (e) {
            }
            return null;
        },

        async checkLogin() {
            // 存在 userInfo，则为登录
            return !!this.userInfo.id;
        },

        async logout() {
            await logout();
        },
    },
});
