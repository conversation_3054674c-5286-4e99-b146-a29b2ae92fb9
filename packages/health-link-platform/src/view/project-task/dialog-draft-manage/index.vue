<template>
    <el-dialog
        v-model="isShowDialogDraftManage"
        title="草稿管理"
        width="760px"
        class="dialog-draft-manage"
    >
        <el-scrollbar class="list-wrapper">
            <div
                v-for="item in showDataList"
                :key="item.id"
                class="card-wrapper"
            >
                <el-descriptions
                    :title="item.name"
                    :column="3"
                >
                    <el-descriptions-item
                        label="名称"
                        :span="1"
                    >
                        {{ item.displayName }}
                    </el-descriptions-item>
                    <el-descriptions-item
                        label="版本"
                        :span="1"
                    >
                        <el-tag size="small">{{ item.version }}</el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item
                        label="最近修改时间"
                        :span="1"
                    >
                        {{ item.updateTimeWording }}
                    </el-descriptions-item>
                    <el-descriptions-item
                        label="描述"
                        :span="3"
                    >
                        {{ item.description }}
                    </el-descriptions-item>
                </el-descriptions>
                <div class="handles">
                    <el-button
                        plain
                        type="primary"
                        :icon="Edit"
                        @click="onClickSelect(item)"
                    >
                    </el-button>
                    <el-button
                        plain
                        type="danger"
                        :icon="Delete"
                        @click="onClickDelete(item)"
                    >
                    </el-button>
                </div>
            </div>
            <div
                v-if="showDataList.length === 0"
                style="margin-top: 60px;"
            >
                <el-empty description="暂无数据"></el-empty>
            </div>
        </el-scrollbar>
        <template #footer>
            <span class="dialog-footer">
                <el-button
                    @click="isShowDialogDraftManage = false"
                >
                    关闭
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import taskDraftStore from '../task-draft-store';
import * as utils from '@/common/utils';
import { Edit, Delete } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { computed } from 'vue';

const props = defineProps({
    modelValue: {
        type: Number,
        default: 0,
    },
});

const $emit = defineEmits([
    'update:modelValue', // 更新显示状态
    'select', // 选择
]);

const isShowDialogDraftManage = computed({
    get() {
        return !!props.modelValue;
    },
    set(val:boolean) {
        $emit('update:modelValue', +val);
    },
});

// 草稿数据列表
const showDataList = computed(() => {
    const dataList = taskDraftStore.draftTaskData.dataList || [];
    return dataList.map((item: any) => {
        const itemInfo = {
            ...item,
            name: item.data.name,
            displayName: item.data.displayName,
            description: item.data.description,
            version: item.data.version,
            updateTimeWording: dayjs(item.updateTime).format('YYYY-MM-DD HH:mm:ss'),
        };
        return itemInfo;
    });
});

/**
 * 当点击确认
 * <AUTHOR>
 * @date 2024-05-15
 * @param {Object} item
 */
const onClickSelect = (item: any) => {
    $emit('select', item.id);
};

/**
 * 当点击删除
 * <AUTHOR>
 * @date 2024-05-15
 * @param {Object} item
 */
const onClickDelete = async (item: any) => {
    const confirmResponse = await utils.messageConfirm('请确认是否删除草稿数据？删除后无法恢复', '提示', {
        type: 'warning',
        confirmButtonText: '删除',
        cancelButtonText: '取消',
    });
    if (confirmResponse.status === false) {
        return confirmResponse;
    }
    taskDraftStore.deleteDraftTaskData(item.id);
    ElMessage.success('删除成功');
    if (showDataList.value.length === 0) {
        isShowDialogDraftManage.value = false;
    }
};
</script>

<style lang="scss">
    .dialog-draft-manage {
        .el-dialog__body {
            padding: 0 !important;
        }

        .list-wrapper {
            padding-right: 10px;

            .card-wrapper {
                padding: 12px;
                border-radius: 4px;
                border: 1px solid #dcdfe6;
                margin-bottom: 10px;
                background-color: #f5f7fa;

                .el-descriptions__body {
                    background-color: transparent;
                }

                .handles {
                    display: flex;
                    justify-content: flex-end;
                }
            }

            .el-scrollbar__view > .card-wrapper:last-child {
                margin-bottom: 0;
            }
        }

        .el-dialog__body {
            height: 480px !important;
            padding: 8px 20px 0;
        }
    }
</style>