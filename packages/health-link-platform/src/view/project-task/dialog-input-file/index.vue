<template>
    <el-dialog
        v-model="isShowDialogInputFile"
        title="AI智能添加"
        width="820px"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        class="dialog-input-file"
    >
        <el-upload
            v-model:file-list="formData.fileList"
            drag
            multiple
            action="#"
            accept=".png,.jpg,.jpeg"
            :limit="10"
            :auto-upload="false"
            list-type="picture-card"
            :on-preview="onPreview"
        >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
                拖拽文件 或 <em>点击上传</em>
            </div>
            <template #tip>
                <div class="el-upload__tip">
                    支持jpg/png文件，且大小不超过5MB
                </div>
            </template>
        </el-upload>
        <el-image-viewer
            v-if="formData.isShowPreview"
            :url-list="urlList"
            show-progress
            :initial-index="formData.index"
            @close="formData.isShowPreview = false"
        />
        <template #footer>
            <span class="dialog-footer">
                <el-button
                    type="primary"
                    :disabled="isDisabledConfirmBtn"
                    @click="onClickConfirm"
                >
                    确定
                </el-button>
                <el-button
                    @click="isShowDialogInputFile = false"
                >
                    取消
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus';
import { computed, reactive, watch } from 'vue';

const props = defineProps({
    modelValue: {
        type: Number,
        default: 0,
    },
});

const $emit = defineEmits([
    'update:modelValue', // 更新显示状态
    'confirm', // 确认
]);

// 是否显示
const isShowDialogInputFile = computed({
    get() {
        return !!props.modelValue;
    },
    set(val:boolean) {
        $emit('update:modelValue', +val);
    },
});

// 预览列表
const urlList = computed(() => formData.fileList.map((item: any) => item.url));  

const formData = reactive({
    fileList: <any[]>[],
    index: 0,
    isShowPreview: false,
});

// 是否禁用确认按钮
const isDisabledConfirmBtn = computed(() => {
    if (formData.fileList.length === 0) {
        return true;
    }
    return false;
});

/**
 * 当预览
 * <AUTHOR>
 * @date 2025-10-13
 * @param {Object} item
 */
const onPreview = (item: any) => {
    formData.index = formData.fileList.findIndex((i: any) => i.url === item.url);
    formData.isShowPreview = true;
};

/**
 * 当文件列表变化
 * <AUTHOR>
 * @date 2025-10-13
 */
const onChangeFileList = async () => {
    const list = <any[]>[];
    formData.fileList.forEach((item: any) => {
        if (item.raw.size > 1024 * 1024 * 5) {
            ElMessage.error(`文件 ${item.name} 大小超过5MB，已过滤`);
            return;
        }
        const isExist = list.some((one: any) => one.name === item.name);
        if (isExist) {
            ElMessage.error(`文件 ${item.name} 已存在，已过滤`);
            return;
        }
        list.push(item);
    });
    if (list.length !== formData.fileList.length) {
        formData.fileList = list;
    }
};

watch([
    () => formData.fileList,
], onChangeFileList);

/**
 * 当点击确认
 * <AUTHOR>
 * @date 2024-05-15
 */
const onClickConfirm = async () => {
    const fileList = formData.fileList.map((item: any) => item.raw);
    $emit('confirm', fileList);
};
</script>

<style lang="scss">
    .dialog-input-file {
        .el-upload-list {
            width: 100%;
        }

        .el-upload--picture-card {
            width: 100%;
            height: 187px;

            .el-upload-dragger {
                border: none;
            }
        }
    }
</style>