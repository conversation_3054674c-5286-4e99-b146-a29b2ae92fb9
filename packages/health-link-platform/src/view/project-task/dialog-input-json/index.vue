<template>
    <el-dialog
        v-model="isShowDialogInputJson"
        title="快速添加"
        width="820px"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        class="dialog-input-json"
    >
        <div class="tools-rapper">
            <el-select
                v-model="editInfo.type"
                placeholder="请选择"
                style="width: 120px; margin-bottom: 12px;"
            >
                <el-option
                    v-for="item in typeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
            </el-select>
            <div class="track"></div>
            <el-button
                type="primary"
                plain
                @click="onClickCopyPrompt"
            >
                拷贝Prompt
            </el-button>
        </div>
        <el-input
            v-model="editInfo.data"
            type="textarea"
            :rows="25"
            :placeholder="placeholder"
        ></el-input>
        <template #footer>
            <span class="dialog-footer">
                <el-button
                    type="primary"
                    :disabled="isDisabledConfirmBtn"
                    @click="onClickConfirm"
                >
                    确定
                </el-button>
                <el-button
                    @click="isShowDialogInputJson = false"
                >
                    取消
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus';
import { computed, reactive } from 'vue';
import * as utils from '@/common/utils';

const props = defineProps({
    modelValue: {
        type: Number,
        default: 0,
    },
    type: {
        type: String,
        default: 'json',
    },
    prompt: {
        type: String,
        default: '',
    },
});

const $emit = defineEmits([
    'update:modelValue', // 更新显示状态
    'confirm', // 确认
]);

const isShowDialogInputJson = computed({
    get() {
        return !!props.modelValue;
    },
    set(val:boolean) {
        $emit('update:modelValue', +val);
    },
});

const typeOptions = computed(() => {
    const options = [
        { value: 'properties', label: '配置项' },
    ];
    if (props.type === 'json') {
        options.push({ value: 'json', label: 'JSON' });
    }
    if (props.type === 'xml') {
        options.push({ value: 'xml', label: 'XML' });
    }
    return options;
});

const placeholder = computed(() => {
    if (editInfo.type === 'properties') {
        const template = [
            {
                key: '属性', // 
                note: '说明', // 说明
                type: '类型，string, number, boolean, array, object', // 
                defaultValue: '默认值', // 
                precision: '精度', // 精度
                maxLength: '最大长度', // 最大长度
                enumValues: '枚举值', // 枚举值
                format: '格式', // 格式
                properties: '子属性', // 属性，
                ref: '引用，数据源的key', // 引用，数据源的key
                variables: '变量，多个时逗号分隔', // 变量，多个时逗号分隔
                template: '模板', // 模板
            },
            {
                key: 'status', // 
                note: '状态', // 说明
                type: 'number', // 
                defaultValue: '0', // 
                precision: 0, // 精度
                maxLength: 32, // 最大长度
                enumValues: [
                    { refValue: '0', value: '00', description: '未处理' },
                    { refValue: '1', value: '01', description: '处理中' },
                    { refValue: '2', value: '02', description: '处理完成' },
                ], // 枚举值
                format: '', // 格式
                properties: [], // 属性，
                ref: '$.status', // 引用，数据源的key
                variables: '', // 变量，多个时逗号分隔
                template: '', // 模板
            },
        ];
        return `请输入配置项\n${JSON.stringify(template, null, 2)}`;
    }
    if (editInfo.type === 'json') {
        const template = {
            name: '姓名',
            age: '年龄',
            birthday: '生日',
        };
        return `请输入JSON格式的入参示例，可反向生成属性表\n${JSON.stringify(template, null, 2)}`;
    }
    if (editInfo.type === 'xml') {
        const template = `<?xml version="1.0" encoding="utf-16" standalone="yes" ?>
<root>
    <name>姓名</name>
    <age>年龄</age>
    <birthday>生日</birthday>
</root>
        `;
        return `请输入XML格式的入参示例，可反向生成属性表\n${template}`;
    }
    return '请输入配置项';
});

const isDisabledConfirmBtn = computed(() => !editInfo.data);

const editInfo = reactive({
    type: 'properties',
    data: '',
});

/**
 * 当点击拷贝Prompt
 * <AUTHOR>
 * @date 2025-10-17
 */
const onClickCopyPrompt = () => {
    utils.copy(props.prompt);
    ElMessage.success('拷贝成功');
};

/**
 * 当点击确认
 * <AUTHOR>
 * @date 2024-05-15
 */
const onClickConfirm = async () => {
    if (editInfo.type === 'properties') {
        const data = utils.parseJson(editInfo.data);
        if (!data) {
            return ElMessage.error('解析失败，json格式错误');
        }
        if (!Array.isArray(data)) {
            return ElMessage.error('解析失败，配置项必须为数组');
        }
        const isOk = data.every((item: any) => (
            typeof item === 'object'
            && item !== null
            && Object.keys(item).length > 0
            && !!item.key
        ));
        if (!isOk) {
            return ElMessage.error('解析失败，配置项必须为对象，且必须包含key属性');
        }
        $emit('confirm', {
            type: 'properties',
            data,
            isAiCreate: !!data[0]?.id, // 是否ai生成配置
        });
    } else if (editInfo.type === 'json') {
        const data = utils.parseJson(editInfo.data);
        if (!data) {
            return ElMessage.error('解析失败，json格式错误');
        }
        const confirmResponse = await utils.messageConfirm('是否将属性的值设为说明', '提示', {
            type: 'warning',
            confirmButtonText: '是',
            cancelButtonText: '否',
        });
        $emit('confirm', {
            type: 'json',
            data,
            isSetNote: confirmResponse.status, // 是否将属性的值设为说明
        });
    } else if (editInfo.type === 'xml') {
        const data = utils.xml2json(editInfo.data);
        if (!data) {
            return ElMessage.error('解析失败，xml格式错误');
        }
        $emit('confirm', {
            type: 'xml',
            data: editInfo.data,
        });
    } else {
        //
    }
};
</script>

<style lang="scss">
    .dialog-input-json {
        .tools-rapper {
            display: flex;
            align-items: center;

            .track {
                flex: 1;
            }
        }
    }
</style>
