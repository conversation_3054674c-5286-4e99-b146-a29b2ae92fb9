<template>
    <el-dialog
        v-model="isShowDialogInputMock"
        title="测试数据"
        width="820px"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        class="dialog-input-mock"
    >
        <div class="tools-rapper">
            <div class="track"></div>
            <el-button
                type="primary"
                plain
                @click="onClickFormat"
            >
                格式化
            </el-button>
        </div>
        <el-input
            v-model="editInfo.data"
            type="textarea"
            :rows="25"
            placeholder="请输入json格式的测试数据"
        ></el-input>
        <template #footer>
            <span class="dialog-footer">
                <el-button
                    type="primary"
                    :disabled="isDisabledConfirmBtn"
                    @click="onClickConfirm"
                >
                    确定
                </el-button>
                <el-button
                    type="danger"
                    plain
                    @click="onClickClear"
                >
                    清除
                </el-button>
                <el-button
                    @click="isShowDialogInputMock = false"
                >
                    取消
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus';
import { computed, reactive } from 'vue';
import * as utils from '@/common/utils';

const props = defineProps({
    modelValue: {
        type: Number,
        default: 0,
    },
    mockData: {
        type: String,
        default: '',
    },
});

const $emit = defineEmits([
    'update:modelValue', // 更新显示状态
    'confirm', // 确认
]);

const isShowDialogInputMock = computed({
    get() {
        return !!props.modelValue;
    },
    set(val:boolean) {
        $emit('update:modelValue', +val);
    },
});

const isDisabledConfirmBtn = computed(() => !editInfo.data);

const editInfo = reactive({
    data: props.mockData,
});

/**
 * 当点击格式化
 * <AUTHOR>
 * @date 2024-05-15
 */
const onClickFormat = () => {
    const data = utils.parseJson(editInfo.data);
    if (!data) {
        return ElMessage.error('解析失败，json格式错误');
    }
    editInfo.data = JSON.stringify(data, null, 4);
};

/**
 * 当点击确认
 * <AUTHOR>
 * @date 2024-05-15
 */
const onClickConfirm = () => {
    const data = utils.parseJson(editInfo.data);
    if (!data) {
        return ElMessage.error('解析失败，json格式错误');
    }
    $emit('confirm', {
        mockData: JSON.stringify(data),
    });
};

/**
 * 当点击清空
 * <AUTHOR>
 * @date 2025-10-21
 */
const onClickClear = () => {
    $emit('confirm', {
        mockData: '',
    });
};
</script>

<style lang="scss">
    .dialog-input-mock {
        .tools-rapper {
            display: flex;
            align-items: center;
            margin-bottom: 12px;

            .track {
                flex: 1;
            }
        }
    }
</style>
