/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import prompt from './prompt';
import ProjectTaskApi from '@/api/project-task-api';
import serialTimestamp from '@/common/serial-timestamp';
import * as utils from '@/common/utils';
import * as sqlFormatter from 'sql-formatter';
import { ref, reactive, computed } from 'vue';

import createLoadingModel from '@/model/loading';
import createDialogModel from '@/model/dialog';
import AbcResponse from '@/common/AbcResponse';

export const createProjectCreateController = (props: any) => {
    const dialogModelSelectDevice = createDialogModel();
    const loadingModelAiIntelligentAdd = createLoadingModel();

    // 表单数据
    const formData = reactive({
        name: '',
        displayName: '',
        description: '',
        version: '1.0.0',
        isValid: 0, // 是否启用，0-禁用，1-启用
        initialReportTime: {
            type: '', // 时间类型：fix-固定时间，relativeToOpenTime-相对开启时间
            startTime: '', // 开始时间（固定时间模式使用，格式：yyyy-MM-dd）
            relativeTimeInDays: '', // 相对时间天数（相对时间模式使用）
        },
        reportFreq: {
            type: '', // 频率类型：poll-轮询，t1-T1频率
            intervalInSeconds: '', // 间隔时间（秒为单位）
            intervalUnit: 's', // 间隔时间单位
        },
        reportAllowTimeRange: <any[]>[], // 允许上报时间范围
        keys: <string []>[], // 配置的key
        protocol: 'custom', // 协议：http-HTTP，https-HTTPS，custom-自定义
        hostname: '', // 主机名
        port: 80, // 端口
        path: '', // 路径
        method: 'post', // 方法，get/post/put/delete
        timeout: 30, // 超时时间，单位：秒
    });

    // 任务ID
    const taskId = ref('');

    /**
     * 设置任务ID
     * <AUTHOR>
     * @date 2025-09-18
     * @param {string} id
     */
    const setTaskId = (id: string) => {
        taskId.value = id;
    };

    // 是否是创建
    const isCreate = computed(() => !taskId.value);

    // 是否是更新
    const isUpdate = computed(() => taskId.value);

    // 当前草稿ID
    const currentDraftId = ref('');

    // 设置当前草稿ID
    const setCurrentDraftId = (id: string) => {
        currentDraftId.value = id;
    };

    // 删除当前草稿ID
    const delCurrentDraftId = () => {
        currentDraftId.value = '';
    };

    // 可配置的key
    const keyOptions = Object.freeze([
        { value: 'protocol', label: 'Protocol' },
        { value: 'hostname', label: 'Hostname' },
        { value: 'port', label: 'Port' },
        { value: 'path', label: 'Path' },
        { value: 'method', label: 'Method' },
        { value: 'timeout', label: 'Timeout' },
    ]);

    // 需要展示的字段
    const isShowKey = computed(() => {
        const info = {
            protocol: formData.keys.includes('protocol'),
            hostname: formData.keys.includes('hostname'),
            port: formData.keys.includes('port'),
            path: formData.keys.includes('path'),
            method: formData.keys.includes('method'),
            timeout: formData.keys.includes('timeout'),
        };
        if (
            info.protocol
            && formData.protocol !== 'http'
            && formData.protocol !== 'https'
        ) {
            // 非http和https协议，不展示相关字段
            info.hostname = false;
            info.port = false;
            info.path = false;
            info.method = false;
            info.timeout = false;
        }
        return info;
    });

    let cachePostData: any = null;

    /**
     * 设置缓存的表单数据
     * <AUTHOR>
     * @date 2025-09-08
     * @param {any} postData:any
     * @returns {any}
     */
    const setCachePostData = (postData: any) => {
        cachePostData = postData;
    };

    // 是否禁用下一步按钮
    const isDisabledNextBtn = computed(() => {
        if (
            formData.name === ''
            || formData.displayName === ''
            || formData.description === ''
        ) {
            return true;
        }
        if (formData.initialReportTime.type === 'fix' && formData.initialReportTime.startTime === '') {
            return true;
        }
        if (formData.initialReportTime.type === 'relativeToOpenTime' && formData.initialReportTime.relativeTimeInDays === '') {
            return true;
        }
        if (formData.reportFreq.type === 'poll' && formData.reportFreq.intervalInSeconds === '') {
            return true;
        }
        const isDisabledReportFunc = formData.keys.some((key: string) => !formData[key]);
        if (isDisabledReportFunc) {
            return true;
        }
        return false;
    });

    // 数据源配置
    const dataSourceConfig = reactive({
        currentType: 1, // 当前数据源类型，1-数据源，2-基础配置
        dataSources: <any[]> [], // 数据源列表
        sourceName: <any[]> [], // 选中的数据源名称
        keyword: '', // 搜索关键词
        filter: {
            filterEnable: 0, // 是否参与测试
            variables: <string[]> [], // 变量
            ftlTemplate: '', // ftl模版
            note: '', // 说明
        },
        mockData: '', // 模拟数据
    });

    // 模拟数据
    const dialogInputMockModel = createDialogModel();

    // 数据源名称
    const dataSourceName = computed(() => dataSourceConfig.sourceName[0]?.[0] || '');

    // 数据源勾选的依赖项
    const dataSourceDependency = computed(() => {
        const dependency: string[] = [];
        dataSourceConfig.sourceName.forEach((item: string[]) => {
            if (item.length > 1) {
                dependency.push(item[1]);
            }
        });
        return dependency;
    });

    // 表格key，防止数据与视图不一致
    const tableKey = computed(() => `${dataSourceName.value}-${dataSourceDependency.value.join('_')}-${dataSourceConfig.keyword}`);

    // 数据源选项
    const dataSourceOptions = computed(() => {
        const options = dataSourceConfig.dataSources
                        .filter((item: any) => item.sourceName !== 'config')
                        .map((item: any) => {
                            const itemInfo = {
                                value: item.sourceName,
                                label: item.displayName,
                                disabled: false,
                                children: (item.optionalProperties || []).map((child: any) => ({
                                    value: child.name,
                                    label: child.description,
                                })),
                            };
                            if (itemInfo.children.length === 0) {
                                delete itemInfo.children;
                            }
                            if (dataSourceName.value && item.sourceName !== dataSourceName.value) {
                                itemInfo.disabled = true;
                            }
                            return itemInfo;
                        });
        return options;
    });

    /**
     * 设置originRef
     * <AUTHOR>
     * @date 2025-10-13
     * @param {Array} properties:any
     * @param {String} parentKey:string
     * @returns {Array}
     */
    const setOriginRef = (properties: any, parentKey: string) => (properties || [])
                    .map((item: any) => {
                        const itemInfo = {
                            ...item,
                            typeWording: (() => {
                                if (
                                    item.type === 'array'
                                    && item.elementType
                                ) {
                                    return `${item.type}<${item.elementType}>`;
                                }
                                return item.type;
                            })(),
                            maxLengthWording: (() => {
                                if (!item.maxLength) {
                                    return '-';
                                }
                                return `${item.maxLength}位`;
                            })(),
                            nullableTag: (() => {
                                const info = {
                                    type: '',
                                    text: '',
                                };
                                if (item.nullable === false) {
                                    info.type = 'info';
                                    info.text = '否';
                                }
                                if (item.nullable === true) {
                                    info.type = 'primary';
                                    info.text = '是';
                                }
                                return info;
                            })(),
                            enumTag: (() => {
                                const info = {
                                    type: '',
                                    text: '',
                                    disabled: true,
                                    tableData: [],
                                };
                                const enumValues = item.enumValues || [];
                                if (enumValues.length === 0) {
                                    info.type = 'info';
                                    info.text = '否';
                                }
                                if (enumValues.length > 0) {
                                    info.type = 'primary';
                                    info.text = '是';
                                    info.disabled = false;
                                    info.tableData = enumValues;
                                }
                                return info;
                            })(),
                            formatWording: (() => {
                                if (!item.format) {
                                    return '-';
                                }
                                return item.format;
                            })(),
                            exampleWording: (() => {
                                if (!item.example) {
                                    return '-';
                                }
                                return item.example;
                            })(),
                            ref: (() => {
                                let ref = `${parentKey}.${item.name}`;
                                if (item.type === 'array') {
                                    ref = `${ref}[*]`;
                                }
                                return ref;
                            })(),
                            extendData: {},
                        };
                        const properties = setOriginRef(item.properties, itemInfo.ref);
                        if (properties.length > 0) {
                            itemInfo.properties = properties;
                        }
                        return itemInfo;
                    })
                    .filter((item: any) => !!item);

    // 基础信息
    const dataSourceBaseInfo = computed(() => {
        const target = dataSourceConfig.dataSources.find((item: any) => item.sourceName === 'config');
        if (!target) {
            return [];
        }
        return setOriginRef(target.properties, '$config');
    });

    // 数据源信息
    const dataSourceInfo = computed(() => {
        const target = dataSourceConfig.dataSources.find((item: any) => item.sourceName === dataSourceName.value);
        if (!target) {
            return [];
        }
        return setOriginRef(target.properties, '$');
    });

    // 来源数据表格数据
    const dataSourceTableData = computed(() => {
        const tableData = dataSourceConfig.currentType === 2 ? dataSourceBaseInfo.value : dataSourceInfo.value;
        const getDataList = (properties: any) => (properties || [])
                        .map((item: any) => {
                            if (
                                item.require
                                && !dataSourceDependency.value.includes(item.require)
                            ) {
                                // 该属性是个依赖项，但是有没勾选，因此不展示
                                return null;
                            }
                            if (dataSourceConfig.keyword) {
                                const keywordLower = dataSourceConfig.keyword.toLowerCase();
                                if (
                                    !item.name.toLowerCase().includes(keywordLower)
                                    && !item.description.toLowerCase().includes(keywordLower)
                                ) {
                                    return null;
                                }
                            }
                            const properties = getDataList(item.properties);
                            if (properties.length > 0) {
                                item.properties = properties;
                            }
                            return item;
                        }).filter((item: any) => !!item);
        return getDataList(tableData);
    });

    // 来源数据mockData
    const sourceDataMockData = computed(() => {
        const target: any = dataSourceConfig.dataSources.find((item: any) => item.sourceName === dataSourceName.value);
        if (!target) {
            return {};
        }
        const getObjectData = (properties: any) => {
            const data = (properties || []).reduce((pre: any, cur: any) => {
                if (
                    cur.require
                    && !dataSourceDependency.value.includes(cur.require)
                ) {
                    // 该属性是个依赖项，但是有没勾选，因此不展示
                    return pre;
                }
                if (cur.type === 'object') {
                    pre[cur.name] = getObjectData(cur.properties);
                } else if (cur.type === 'array') {
                    pre[cur.name] = [
                        // eslint-disable-next-line @typescript-eslint/no-unused-vars
                        getObjectData(cur.properties),
                    ];
                } else {
                    pre[cur.name] = (() => {
                        let value = cur.example;
                        if (cur.type === 'number') {
                            // 转换为数字
                            value = Number(value);
                        }
                        return value;
                    })();
                }
                return pre;
            }, {});
            return data;
        };
        return getObjectData(target.properties);
    });

    /**
     * 根据ref查找数据源
     * <AUTHOR>
     * @date 2025-09-18
     * @param {string} ref
     * @returns {Object}
     */
    const findDataSourceByRef = (ref: string) => {
        if (!ref) {
            return null;
        }
        const getObjectData = (properties: any) => {
            for (let i = 0; i < properties.length; i++) {
                const item = properties[i];
                if (item.ref === ref) {
                    return item;
                }
                if (
                    (item.type === 'object' || item.type === 'array')
                    && item.properties
                    && item.properties.length > 0
                ) {
                    const result: any = getObjectData(item.properties);
                    if (result) {
                        return result;
                    }
                }
            }
        };
        return getObjectData(dataSourceTableData.value);
    };

    // 加载模型 - 转换配置
    const loadingModelTransform = createLoadingModel();

    // 转换配置
    const transformConfig = reactive({
        targetFormat: 'json', // 目标格式
        properties: <any[]>[],
        editingId: '',
        resultId: '',
        resultType: 'json',
        resultData: '',
        resultWarn: '',
        selectRow: <any> null, // 当前选中的行，用于ai添加子项
        aiThinking: '',
    });

    // 警告信息
    const warnings = computed(() => transformConfig.resultWarn.split('\n'));

    // 目标格式选项
    const targetFormatOptions = Object.freeze([
        {
            value: 'json',
            label: 'JSON',
        },
        {
            value: 'xml',
            label: 'XML',
        },
        {
            value: 'sql',
            label: 'SQL',
        },
    ]);

    // 文件输入框
    const dialogInputFileModel = createDialogModel();

    // JSON输入框
    const dialogInputJsonModel = createDialogModel();

    // ai思考过程
    const dialogAiThinkingModel = createDialogModel();

    // 类型选项
    const typeOptions = Object.freeze([
        { value: 'string', label: '字符串' },
        { value: 'number', label: '数值' },
        { value: 'boolean', label: '布尔' },
        { value: 'date', label: '日期' },
        { value: 'object', label: '对象' },
        { value: 'array', label: '数组' },
    ]);

    // prompt
    const transformAiPrompt = computed(() => {
        const simpleProperties = createSimpleProperties(dataSourceInfo.value);
        return prompt.replace(/{{DATA_B}}/g, JSON.stringify(simpleProperties));
    });

    /**
     * 创建转换配置字段
     * <AUTHOR>
     * @date 2025-09-12
     * @param {Object} options
     * @returns {Object}
     */
    const createProperty = (options?: any) => {
        const layer = options?.layer || 1; // 层级，默认 1层
        const config = options?.config || null; // 配置
        const parent = options?.parent || null; // 父级
        const property = {
            id: serialTimestamp.create(),
            key: '', // key
            note: '', // 说明
            type: 'string', // 类型，string, number, boolean, array, object
            defaultValue: '', // 默认值
            precision: 0, // 精度
            maxLength: 32, // 最大长度
            enumValues: <any[]>[], // 枚举值
            format: '', // 格式
            properties: <any[]>[], // 属性，
            ref: '', // 引用，数据源的key
            variables: <any[]>[], // 变量，多个时逗号分隔
            templateType: '', // 模板类型
            template: '', // 模板
            extend: {
                required: 1, // 是否必填
                originRef: '', // 原始引用
            },
            extendData: {
                layer,
                redWitdh: layer * 16 + 4,
            }, // 扩展数据
        };
        if (config) {
            property.key = config.key || '';
            property.note = config.note || '';
            property.type = config.type || 'string';
            property.defaultValue = config.defaultValue || '';
            property.precision = (() => {
                const value = parseInt(config.precision);
                if (Number.isNaN(value)) {
                    return 0;
                }
                return value;
            })();
            property.maxLength = (() => {
                const value = parseInt(config.maxLength);
                if (Number.isNaN(value)) {
                    return 32;
                }
                return value;
            })();
            if (config.enumValues) {
                property.enumValues = utils.cloneDeep(config.enumValues);
            }
            property.format = config.format || '';
            // 需要递归处理，因此不深拷贝
            // property.properties = utils.cloneDeep(config.properties);
            property.ref = config.ref || '';
            if (config.variables) {
                property.variables = utils.cloneDeep(config.variables);
            }
            property.templateType = config.templateType === 'ftl' ? 'ftl' : '';
            property.template = config.template || '';
            property.extend = {
                originRef: config.extend?.originRef || '',
                required: Number(config.extend?.required) === 0 ? 0 : 1,
            };

            // 对老数据的兼容
            if (!property.extend.originRef) {
                const ref = config.ref || '';
                if (
                    ref.startsWith('$.')
                    || ref.startsWith('$config.')
                ) {
                    property.extend.originRef = ref;
                } else if (
                    ref.startsWith('.')
                    && parent
                    && parent.templateType === ''
                    && parent.ref
                    && parent.ref.startsWith('$')
                ) {
                    if (parent.type === 'object') {
                        property.extend.originRef = `${parent.ref}${ref}`;
                    }
                    if (parent.type === 'array') {
                        property.extend.originRef = `${parent.ref}[*]${ref}`;
                    }
                }
            }
        }
        return property;
    };

    /**
     * 查找转换配置字段
     * <AUTHOR>
     * @date 2025-09-15
     * @param {string} id
     * @returns {Object}
     */
    const findProperty = (id: string, info?: any) => {
        if (!info) {
            info = {
                parent: transformConfig,
                properties: transformConfig.properties,
                isBase: true,
                target: null,
            };
        }
        for (let i = 0; i < info.properties.length; i++) {
            const item = info.properties[i] as any;
            if (item.id === id) {
                info.target = item;
                return info;
            }
            if (
                (item.type === 'object' || item.type === 'array')
                && item.properties
                && item.properties.length > 0
            ) {
                const info = {
                    parent: item,
                    properties: item.properties,
                    isBase: false,
                    target: null,
                };
                const result: any = findProperty(id, info);
                if (result) {
                    return result;
                }
            }
        }
        return null;
    };

    /**
     * 根据json创建属性列表
     * <AUTHOR>
     * @date 2025-09-16
     * @param {any} json:any
     * @param {Object} params:是否将属性的值设为说明
     * @returns {Array}
     */
    const createProperiesByJson = (json: any, params?: any) => {
        const layer = params?.layer || 1;
        const isSetNote = params?.isSetNote || false;
        const getProperties = (obj: any, layer: number) => {
            const options = {
                layer, // 层级
            };
            const properies = Object.keys(obj).reduce((list: any, key: string) => {
                const property = createProperty(options);
                const value = obj[key];
                property.key = key;
                property.type = (() => {
                    if (Array.isArray(value)) {
                        return 'array';
                    }
                    if (utils.isPlainObject(obj[key])) {
                        return 'object';
                    }
                    if (utils.isNumber(obj[key])) {
                        return 'number';
                    }
                    if (utils.isString(obj[key])) {
                        return 'string';
                    }
                    if (utils.isBoolean(obj[key])) {
                        return 'boolean';
                    }
                    return 'string';
                })();
                if (property.type === 'object') {
                    property.properties = getProperties(value, layer + 1);
                }
                if (property.type === 'array' && utils.isObject(value[0])) {
                    property.properties = getProperties(value[0], layer + 1);
                }
                if (
                    isSetNote
                    && utils.isString(value)
                ) {
                    property.note = value;
                }
                list.push(property);
                return list;
            }, []);
            return properies;
        };
        return getProperties(json, layer);
    };
    /**
     * 根据xml创建属性列表
     * <AUTHOR>
     * @date 2025-09-16
     * @param {any} xml:any
     * @param {Object} params:是否将属性的值设为说明
     * @returns {Array}
     */
    const createProperiesByXml = (xml: any, params?: any) => {
        const json = utils.xml2json(xml);
        const properties = createProperiesByJson(json, params);
        if (
            json['?xml']?.$
            && properties[0]?.key === '?xml'
            && properties[0]?.type === 'object'
            && properties[0]?.properties?.[0]?.key === '$'
        ) {
            const baseInfo = json['?xml']?.$;
            const baseProperty = properties[0].properties[0].properties;
            properties[0].note = '声明';
            properties[0].properties[0].note = '属性';
            baseProperty.forEach((item: any) => {
                if (baseInfo[item.key]) {
                    item.defaultValue = baseInfo[item.key];
                }
                if (item.key === 'version') {
                    item.note = '版本';
                }
                if (item.key === 'encoding') {
                    item.note = '编码；utf-8为UTF-8编码，utf-16为UTF-16编码，gbk为GBK编码';
                }
                if (item.key === 'standalone') {
                    item.note = '是否独立；no为依赖，yes为独立';
                }
            });
        }
        if (
            properties[1]?.key === 'root'
            && properties[1]?.type === 'object'
            
        ) {
            properties[1].note = '根节点';
        }
        return properties;
    };

    /**
     * 创建JSON转换数据
     * <AUTHOR>
     * @date 2025-09-15
     * @returns {Object}
     */
    const createJsonTransformData = () => {
        const postData = {
            filterEnable: dataSourceConfig.filter.filterEnable, // 是否启用过滤
            inputJson: dataSourceConfig.mockData || JSON.stringify(sourceDataMockData.value), // 来源数据json字符串
            template: {
                targetFormat: transformConfig.targetFormat, // 目标格式
                transformTemplate: {
                    filter: dataSourceConfig.filter, // 过滤条件
                    properties: transformConfig.properties.filter((item: any) => !!item.key),
                },
            },
        };
        return postData;
    };

    /**
     * 请求数据转换测试
     * <AUTHOR>
     * @date 2025-09-15
     * @param {Object} data
     * @returns {Promise<AbcResponse>}
     */
    const requestJsonTransformer = async (data: any) => {
        const requestResponse = await ProjectTaskApi.requestJsonTransformer(data);
        if (requestResponse.status === false) {
            return requestResponse;
        }
        return requestResponse;
    };

    /**
     * 处理转换数据格式
     * <AUTHOR>
     * @date 2025-09-17
     * @param {string} data:json
     * @param {string} format:string
     * @returns {string}
     */
    const handleTransformDataFormat = (data: string, format: string) => {
        if (format === 'json') {
            if (!data) {
                return '{}';
            }
            const json = JSON.parse(data);
            return JSON.stringify(json, null, 2); // 格式化json, 2个空格缩进
        }
        if (format === 'xml') {
            const json = utils.xml2json(data);
            return utils.json2xml(json);
        }
        if (format === 'sql') {
            const sqlList = JSON.parse(data) || [];
            return sqlList.map((sql: string) => sqlFormatter.format(sql)).join('\n\n');
        }
        return data;
    };

    /**
     * 创建XML模版数据
     * <AUTHOR>
     * @date 2025-09-17
     * @returns {Array}
     */
    const createXmlTemplateProperties = () => {
        const xmlTemplate = `
            <?xml version="1.0" encoding="utf-16" standalone="yes" ?>
            <root>
                <input>xx</input>
            </root>
        `;
        return createProperiesByXml(xmlTemplate);
    };

    /**
     * 创建SQL模版数据
     * <AUTHOR>
     * @date 2025-09-17
     * @returns {Array}
     */
    const createSqlTemplateProperties = () => {
        const json = {
            sqlType: 'mysql', // sql类型
            tableName: 'xxx', // 表名
            action: 'insert', // insert | update | delete
            ref: '', // 引用，数据源的key
            where: {
                id: '', // where条件
            },
            values: {
                name: '', // 值
            },
        };
        const properties = createProperiesByJson(json);
        properties.forEach((item: any) => {
            if (item.key === 'sqlType') {
                item.defaultValue = json.sqlType;
                item.note = '数据库类型；mysql | oracle';
            }
            if (item.key === 'tableName') {
                item.defaultValue = json.tableName;
                item.note = '表名';
            }
            if (item.key === 'action') {
                item.defaultValue = json.action;
                item.note = '操作；insert | update | delete';
            }
            if (item.key === 'ref') {
                item.defaultValue = json.ref;
                item.note = '引用，数据源的key，非必填';
            }
            if (item.key === 'where') {
                item.note = 'where条件';
            }
            if (item.key === 'values') {
                item.note = 'values';
            }
        });
        return properties;
    };

    /**
     * 创建简单数据源属性
     * <AUTHOR>
     * @date 2025-10-13
     * @param {Array} properties
     * @returns {Array}
     */
    const createSimpleProperties = (properties: any) => properties.map((property: any) => {
        const info = [
            property.name,
            property.description,
            (() => {
                if (property.type === 'string') {
                    return '';
                }
                if (property.type === 'number') {
                    return 'n';
                }
                if (property.type === 'boolean') {
                    return 'b';
                }
                if (property.type === 'date') {
                    return 'd';
                }
                if (property.type === 'array') {
                    return 'a';
                }
                if (property.type === 'object') {
                    return 'o';
                }
            })(),
            property.example || '',
        ];
        const enumValues = (property.enumValues || []).map((item: any) => [item.value, item.description]);
        const isEnumValues = enumValues.length !== 0;
    
        const properties = (() => {
            if (
                property.properties
                    && property.properties.length !== 0
                    && (property.type === 'object' || property.type === 'array')
            ) {
                return createSimpleProperties(property.properties);
            }
            return [];
        })();
        const isProperties = properties.length !== 0;
    
        if (isEnumValues && !isProperties) {
            info.push(enumValues);
        }
        if (!isEnumValues && isProperties) {
            info.push([], properties);
        }
        if (isEnumValues && isProperties) {
            info.push(enumValues, properties);
        }
        return info;
    });

    /**
     * 创建依赖数据列表
     * <AUTHOR>
     * @date 2025-10-13
     * @param {Array} properties
     * @returns {Array}
     */
    const createDependencyDataList = (properties: any): any[] => {
        const dependencyDataList: any[] = [];
        properties.forEach((item: any) => {
            if (
                item.require
                && !dependencyDataList.includes(item.require)
            ) {
                dependencyDataList.push({
                    ref: item.ref,
                    required: item.require,
                });
            }
            if (
                item.properties
                && item.properties.length !== 0
                && (item.type === 'object' || item.type === 'array')
            ) {
                const subDependencyDataList = createDependencyDataList(item.properties);
                dependencyDataList.push(...subDependencyDataList);
            }
        });
        return dependencyDataList;
    };

    /**
     * 检查引用依赖
     * <AUTHOR>
     * @date 2025-10-13
     * @param {Object} config
     */
    const updateRefDependency = (config: any, dependencyDataList: any[]) => {
        const refList = [
            config.extend?.originRef,
            ...config.variables,
        ];
        for (let i = 0; i < refList.length; i++) {
            const ref = refList[i];
            if (!ref) {
                continue;
            }
            for (let j = 0; j < dependencyDataList.length; j++) {
                const item = dependencyDataList[j];
                if (!ref.startsWith(item.ref)) {
                    continue;
                }
                if (dataSourceDependency.value.includes(item.required)) {
                    continue;
                }
                dataSourceConfig.sourceName.push([
                    dataSourceName.value,
                    item.required,
                ]);
            }
        }
    };

    /**
     * 插入属性
     * <AUTHOR>
     * @date 2025-10-13
     * @param {Object} config
     */
    const handlePropertyInsert = (config: any) => {
        console.log('handlePropertyInsert config => ', config);
        const properties = (() => {
            if (transformConfig.selectRow) {
                return transformConfig.selectRow?.properties;
            }
            return transformConfig.properties;
        })();
        const layer = (() => {
            if (transformConfig.selectRow) {
                return transformConfig.selectRow.extendData?.layer + 1;
            }
            return 1;
        })();
        const options = {
            layer,
            config,
        };
        const property = createProperty(options);
        properties.push(property);
    };

    /**
     * 初始化数据
     * <AUTHOR>
     * @date 2025-09-08
     * @param {Object} taskData
     */
    const initData = (taskData: any) => {
        // 初始化表单数据 - 基础信息
        formData.name = taskData.name || '';
        formData.displayName = taskData.displayName || '';
        formData.description = taskData.description || '';
        formData.version = taskData.version || '1.0.0';
        formData.isValid = taskData.isValid || 0;

        const {
            initialReportTime,
            reportFreq,
            reportAllowTimeRange,
        } = taskData.reportTimePlan || {};
        if (initialReportTime) {
            formData.initialReportTime.type = initialReportTime.type;
            if (formData.initialReportTime.type === 'fix') {
                formData.initialReportTime.startTime = initialReportTime.startTime;
            }
            if (formData.initialReportTime.type === 'relativeToOpenTime') {
                formData.initialReportTime.relativeTimeInDays = initialReportTime.relativeTimeInDays;
            }
        }
        if (reportFreq) {
            formData.reportFreq.type = reportFreq.type;
            if (formData.reportFreq.type === 'poll') {
                if (reportFreq.intervalInSeconds % 3600 === 0) {
                    formData.reportFreq.intervalInSeconds = (reportFreq.intervalInSeconds / 3600).toString();
                    formData.reportFreq.intervalUnit = 'h';
                } else if (reportFreq.intervalInSeconds % 60 === 0) {
                    formData.reportFreq.intervalInSeconds = (reportFreq.intervalInSeconds / 60).toString();
                    formData.reportFreq.intervalUnit = 'm';
                } else {
                    formData.reportFreq.intervalInSeconds = reportFreq.intervalInSeconds.toString();
                    formData.reportFreq.intervalUnit = 's';
                }
            }
        }
        if (reportAllowTimeRange) {
            reportAllowTimeRange.forEach((item: any) => {
                const timeItem = [item.startTime, item.endTime];
                formData.reportAllowTimeRange.push(timeItem);
            });
        }

        if (taskData.reportFunc) {
            formData.keys = Object.keys(taskData.reportFunc);
            formData.protocol = taskData.reportFunc.protocol || 'custom';
            formData.hostname = taskData.reportFunc.hostname || '';
            formData.port = taskData.reportFunc.port || 80;
            formData.path = taskData.reportFunc.path || '';
            formData.method = taskData.reportFunc.method || 'post';
            formData.timeout = taskData.reportFunc.timeout || 30;
        }

        // 初始化数据源配置
        const sourceItem = taskData.dataSource[0];
        if (sourceItem) {
            const sourceName = [
                [sourceItem.dataSourceName],
                ...(sourceItem.optionalFields || []).map((optionalField: string) => ([
                    sourceItem.dataSourceName,
                    optionalField,
                ])),
            ];
            dataSourceConfig.sourceName = sourceName;
        }

        const {
            targetFormat,
            transformTemplate,
        } = taskData.dataTransformTemplate || {};

        if (transformTemplate?.filter) {
            Object.assign(dataSourceConfig.filter, {
                filterEnable: transformTemplate.filter.filterEnable || 0, // 是否参与测试
                variables: transformTemplate.filter.variables || [], // 变量
                ftlTemplate: transformTemplate.filter.ftlTemplate || '', // ftl模版
                note: transformTemplate.filter.note || '', // 说明
            });
        }

        // 初始化转换配置
        transformConfig.targetFormat = targetFormat || 'json'; // 目标格式
        transformConfig.properties = (() => {
            const properties = transformTemplate?.properties || [];
            if (properties.length === 0) {
                return [];
            }
            return handleProperties(properties);
        })();
    };

    /**
     * 处理属性
     * <AUTHOR>
     * @date 2025-09-24
     * @param {any} properties:any[]
     * @param {any} params:any
     * @returns {any}
     */
    const handleProperties = (properties: any[], params?: any) => {
        const layer = params?.layer || 1;
        return properties.map((item: any) => {
            const itemNew = createProperty({
                layer,
                config: item,
                parent: params?.parent,
            });
            if (
                item.properties
                && item.properties.length !== 0
            ) {
                itemNew.properties = handleProperties(item.properties, {
                    layer: layer + 1,
                    parent: item,
                });
            }
            return itemNew;
        });
    };

    /**
     * 初始化reportAllowTimeRange
     * <AUTHOR>
     * @date 2025-09-22
     */
    const initReportAllowTimeRange = () => {
        while (formData.reportAllowTimeRange.length < 4) {
            formData.reportAllowTimeRange.push(null);
        }
    };

    // 提交loading
    const loadingModelSubmit = createLoadingModel();

    // 保存loading
    const loadingModelSave = createLoadingModel();

    // 是否禁用提交按钮
    const isDisabledSubmitBtn = computed(() => {
        if (
            formData.name === ''
            || formData.displayName === ''
            || formData.description === ''
        ) {
            return true;
        }
        return !currentDraftId.value;
    });

    /**
     * 创建提交数据
     * <AUTHOR>
     * @date 2025-09-08
     * @returns {Object}
     */
    const createPostData = () => {
        const postData = {
            projectId: props.projectId,
            name: formData.name, // 项目key
            displayName: formData.displayName, // 项目名称
            description: formData.description, // 项目描述
            version: formData.version, // 项目版本
            isValid: formData.isValid, // 是否启用，0-禁用，1-启用
            reportTimePlan: {
                initialReportTime: (() => {
                    const config = {
                        type: formData.initialReportTime.type, // 时间类型：fix-固定时间，relativeToOpenTime-相对开启时间
                        startTime: '', // 开始时间（固定时间模式使用，格式：yyyy-MM-dd）
                        relativeTimeInDays: '', // 相对时间天数（相对时间模式使用）
                    };
                    if (config.type === 'fix') {
                        config.startTime = formData.initialReportTime.startTime;
                    }
                    if (config.type === 'relativeToOpenTime') {
                        config.relativeTimeInDays = parseInt(formData.initialReportTime.relativeTimeInDays).toString();
                    }
                    return config;
                })(),
                reportAllowTimeRange: (() => {
                    const list: any[] = [];
                    formData.reportAllowTimeRange.forEach((item: any) => {
                        if (
                            !item
                            || item.length !== 2
                            || !item[0]
                            || !item[1]
                        ) {
                            return;
                        }
                        const itemInfo = {
                            startTime: item[0].slice(0, 5), // 开始时间（格式：HH:mm:ss）
                            endTime: item[1].slice(0, 5), // 结束时间（格式：HH:mm:ss）
                        };
                        const exist = list.some((item: any) => item.startTime === itemInfo.startTime && item.endTime === itemInfo.endTime);
                        if (exist) {
                            return;
                        }
                        list.push(itemInfo);
                    });
                    return list;
                })(),
                reportFreq: (() => {
                    const config: any = {
                        type: formData.reportFreq.type, // 频率类型：poll-轮询，t1-T1频率
                        intervalInSeconds: '', // 间隔时间（秒为单位）
                    };
                    if (config.type === 'poll') {
                        config.intervalInSeconds = (() => {
                            const intervalInSeconds = parseInt(formData.reportFreq.intervalInSeconds);
                            if (formData.reportFreq.intervalUnit === 's') {
                                return intervalInSeconds;
                            }
                            if (formData.reportFreq.intervalUnit === 'm') {
                                return intervalInSeconds * 60;
                            }
                            if (formData.reportFreq.intervalUnit === 'h') {
                                return intervalInSeconds * 60 * 60;
                            }
                            return intervalInSeconds;
                        })();
                    }
                    return config;
                })(),
            },
            dataSource: (() => {
                const sourceItem = dataSourceConfig.sourceName[0];
                if (!sourceItem) {
                    return [];
                }
                const item = {
                    dataSourceName: sourceItem[0],
                    optionalFields: dataSourceConfig.sourceName.slice(1).map((item: string[]) => item[1]),
                };
                return [item];
            })(),
            dataTransformTemplate: {
                targetFormat: transformConfig.targetFormat,
                transformTemplate: {
                    filter: dataSourceConfig.filter, // 过滤配置
                    properties: transformConfig.properties,
                },
            },
            reportFunc: formData.keys.reduce((info: any, key: string) => {
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                info[key] = formData[key];
                return info;
            }, {}),
            extendData: null, // 扩展数据
        };
        return utils.cloneDeep(postData);
    };

    /**
     * 检查是否可以保存草稿
     * <AUTHOR>
     * @date 2025-09-16
     * @param {any} postData:any
     * @returns {boolean}
     */
    const checkIsEnableSaveDraft = (postData: any) => {
        if (
            !postData
            || !postData.name
            || !postData.displayName
            || !postData.description
            || !postData.version
        ) {
            // 关键信息为空时，不保存草稿
            return false;
        }
        if (
            isUpdate.value
            && isUpdatedFormData.value === false
        ) {
            // 修改任务，如果此时没有更新数据，不创建草稿
            return false;
        }
        return true;
    };

    // 是否更新了表单数据
    const isUpdatedFormData = computed(() => {
        const postData = createPostData();
        return !utils.isEqual(postData, cachePostData);
    });

    /**
     * 请求创建
     * <AUTHOR>
     * @date 2023-11-14
     * @returns {Promise<AbcResponse>}
     */
    const requestSubmit = async () => {
        const postData = createPostData();
        let requestResponse = null;
        if (isCreate.value) {
            requestResponse = await ProjectTaskApi.createProjectTask(postData);
        } else {
            requestResponse = await ProjectTaskApi.updateProjectTask(taskId.value, postData);
        }
        if (requestResponse.status === false) {
            return requestResponse;
        }
        return requestResponse;
    };

    /**
     * 请求数据源配置
     * <AUTHOR>
     * @date 2025-09-11
     * @returns {Promise<AbcResponse>}
     */
    const requestDataSourceConfig = async () => {
        const requestResponse = await ProjectTaskApi.fetchDataSourceConfig();
        if (requestResponse.status === false) {
            return requestResponse;
        }
        return requestResponse;
    };

    /**
     * 上传文件 - 多个
     * <AUTHOR>
     * @date 2025-10-15
     * @param {any} fileList:File[]
     * @returns {Promise<AbcResponse>}
     */
    const requestUploadFiles = async (fileList: File[]) => {
        const urls = [];
        for (let index = 0; index < fileList.length; index++) {
            const file = fileList[index];
            const uploadResponse = await utils.uploadFile(file);
            if (uploadResponse.status === false) {
                return uploadResponse;
            }
            urls.push(uploadResponse.data.url);
        }
        return AbcResponse.success({
            urls, // 上传成功后的url列表
        });
    };

    /**
     * 智能添加
     * <AUTHOR>
     * @date 2025-10-13
     * @param {any} urlList:string[]
     * @returns {Promise<AbcResponse>}
     */
    const requestAiIntelligentAdd = async (params: any) => {
        const postData = {
            data: {
                dataSource: {
                    value: JSON.stringify(params.simpleProperties),
                    valueType: 'string',
                },
            },
            imageUrls: params.urls,
        };
        let lastIndex = 0;
        const flag = '==========以下为输出结果==========';
        const ids: string[] = [];
        const dependencyDataList = createDependencyDataList(dataSourceInfo.value);
        const requestResponse = await ProjectTaskApi.fetchAiIntelligentAdd(postData, (data: any) => {
            transformConfig.aiThinking = data;
            const flagIndex = data.indexOf(flag);
            if (flagIndex === -1) {
                return;
            }
            const dataList = (() => {
                const curIndex = data.lastIndexOf('}');
                if (curIndex !== lastIndex) {
                    lastIndex = curIndex;
                    const sIndex = data.indexOf('[', flagIndex);
                    const output = data.slice(sIndex, curIndex + 1) + ']';
                    const result = utils.jsonParseSafety(output);
                    if (result && Array.isArray(result)) {
                        return result;
                    }
                }
                return [];
            })();
            dataList.forEach((item: any) => {
                if (ids.includes(item.id)) {
                    return;
                }
                ids.push(item.id);
                updateRefDependency(item, dependencyDataList);
                handlePropertyInsert(item);
            });
        });
        if (requestResponse.status === false) {
            return requestResponse;
        }
        return requestResponse;
    };

    return {
        dialogModelSelectDevice,
        loadingModelAiIntelligentAdd,
        formData,
        taskId,
        setTaskId,
        currentDraftId,
        setCurrentDraftId,
        delCurrentDraftId,
        keyOptions,
        isShowKey,
        cachePostData,
        setCachePostData,
        isUpdatedFormData,
        isDisabledNextBtn,
        
        tableKey,
        dialogInputMockModel,
        dataSourceConfig,
        dataSourceOptions,
        dataSourceInfo,
        dataSourceTableData,
        findDataSourceByRef,
        createSimpleProperties,
        
        loadingModelTransform,
        transformConfig,
        warnings,
        targetFormatOptions,
        dialogInputFileModel,
        dialogInputJsonModel,
        dialogAiThinkingModel,
        typeOptions,
        transformAiPrompt,
        createProperty,
        findProperty,
        createProperiesByJson,
        createProperiesByXml,
        createJsonTransformData,
        requestJsonTransformer,
        handleTransformDataFormat,
        createXmlTemplateProperties,
        createSqlTemplateProperties,

        loadingModelSubmit,
        loadingModelSave,
        createPostData,
        checkIsEnableSaveDraft,
        isDisabledSubmitBtn,
        initData,
        createDependencyDataList,
        updateRefDependency,
        handlePropertyInsert,
        handleProperties,
        initReportAllowTimeRange,
        requestSubmit,
        requestDataSourceConfig,
        requestUploadFiles,
        requestAiIntelligentAdd,
    };
};