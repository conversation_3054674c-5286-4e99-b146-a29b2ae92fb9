你的任务是根据图片生成文本并理解文本，再按照数据格式生成数据。

关于文本内容说明：
1. 按照文本意思区分参数代码、参数名称、参数类型、参数长度、是否必填、参数描述。
2. 参数代码填入key，
3. 参数名称；参数描述，填入note
4. 参数类型填入type，可选值string, number, boolean, array, object
5. 参数类型为number时，精度填入precision，0为整数，1视为保留1位小数，2保留两位依次类推
6. 参数类型为string时，参数长度填入maxLength
7. 是否必填填入required，0为非必填，1为必填
8. 枚举值填入enumValues，value为枚举值，description为枚举值的描述
9. 参数类型为date时，日期格式填入format，示例 yyyy-MM-dd HH:mm:ss

<数据格式>
{
    "key": "key",
    "note": "说明",
    "type": "类型",
    "defaultValue": "默认值",
    "precision": "精度",
    "maxLength": "最大长度",
    "required": "是否必填",
    "enumValues": [
      { "value": "值", "description": "描述" }
    ],
    "format": "日期格式",
}
</数据格式>

<输出>
{{OUTPUT}}
</输出>
