<template>
    <el-popover
        :visible="visible"
        :width="580"
        placement="left"
        trigger="click"
        popper-style="box-shadow: rgb(14 18 22 / 35%) 0px 10px 38px -10px, rgb(14 18 22 / 20%) 0px 10px 20px -15px; padding: 20px;"
    >
        <template #reference>
            <el-button
                size="small"
                :color="validateInfos.error ? 'rgb(248, 227, 197)' : ''"
                :icon="Edit"
                @click="emit('switch')"
            />
        </template>
        <template #default>
            <el-form
                :model="props.config"
                label-width="80px"
                status-icon
                class="view-popover-form"
            >
                <el-tooltip
                    content="ESC可关闭"
                    placement="top"
                >
                    <el-icon
                        class="close-wrapper"
                        size="20px"
                        @click="emit('switch')"
                    >
                        <Close />
                    </el-icon>
                </el-tooltip>
                <el-form-item
                    v-if="validateInfos.infos.key.show"
                    label="key"
                    prop="key"
                >
                    <el-input
                        v-model="config.key"
                        maxlength="200"
                        style="width: 420px;"
                    />
                    <el-tooltip
                        v-if="validateInfos.infos.key.error"
                        :content="validateInfos.infos.key.content"
                        placement="top"
                    >
                        <el-icon
                            size="20px"
                            color="#E6A23C"
                            class="icon-warning"
                        >
                            <WarningFilled />
                        </el-icon>
                    </el-tooltip>
                </el-form-item>
                <el-form-item
                    v-if="validateInfos.infos.type.show"
                    label="类型"
                    prop="type"
                >
                    <el-select
                        v-model="config.type"
                        placeholder="请选择类型"
                        style="width: 160px;"
                        :teleported="false"
                    >
                        <el-option
                            v-for="item in typeOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                    <el-tooltip
                        v-if="validateInfos.infos.type.error"
                        :content="validateInfos.infos.type.content"
                        placement="top"
                    >
                        <el-icon
                            size="20px"
                            color="#E6A23C"
                            class="icon-warning"
                        >
                            <WarningFilled />
                        </el-icon>
                    </el-tooltip>
                </el-form-item>
                <el-form-item
                    v-if="validateInfos.infos.templateType.show"
                    label="取值方式"
                    prop="templateType"
                >
                    <el-radio-group v-model="config.templateType">
                        <el-radio label="">ref</el-radio>
                        <el-radio label="ftl">ftl</el-radio>
                    </el-radio-group>
                    <el-link
                        v-if="config.templateType === 'ftl'"
                        href="http://freemarker.foofun.cn/toc.html"
                        target="_blank"
                        type="primary"
                        style="margin-left: 24px;"
                    >
                        FreeMarker 中文官方参考手册
                    </el-link>
                    <el-tooltip
                        v-if="validateInfos.infos.templateType.error"
                        :content="validateInfos.infos.templateType.content"
                        placement="top"
                    >
                        <el-icon
                            size="20px"
                            color="#E6A23C"
                            class="icon-warning"
                        >
                            <WarningFilled />
                        </el-icon>
                    </el-tooltip>
                </el-form-item>
                <el-form-item
                    v-if="validateInfos.infos.ref.show"
                    label="ref"
                    prop="ref"
                >
                    <el-input
                        v-model="props.config.ref"
                        maxlength="100"
                        style="width: 420px;"
                        placeholder="$.key1.key2"
                    />
                    <el-tooltip
                        v-if="validateInfos.infos.ref.error"
                        :content="validateInfos.infos.ref.content"
                        placement="top"
                    >
                        <el-icon
                            size="20px"
                            color="#E6A23C"
                            class="icon-warning"
                        >
                            <WarningFilled />
                        </el-icon>
                    </el-tooltip>
                </el-form-item>
                <el-form-item
                    v-if="validateInfos.infos.variables.show"
                    label="入参"
                    prop="variables"
                >   
                    <el-input
                        v-model="formData.argument"
                        style="width: 420px;"
                        class="input-tag__variables"
                        clearable
                        placeholder="请输入入参，回车添加"
                        @keydown.enter.prevent="onClickEnterTag"
                    />
                    <el-tooltip
                        v-if="validateInfos.infos.variables.error"
                        :content="validateInfos.infos.variables.content"
                        placement="top"
                    >
                        <el-icon
                            size="20px"
                            color="#E6A23C"
                            class="icon-warning"
                        >
                            <WarningFilled />
                        </el-icon>
                    </el-tooltip>
                    <div class="args-wrapper">
                        <el-tag
                            v-for="item in config.variables"
                            :key="item"
                            type="primary"
                            closable
                            @click="onClickTag(item)"
                            @close="onClickCloseTag(item)"
                        >
                            {{ item }}
                        </el-tag>
                    </div>
                </el-form-item>
                <el-form-item
                    v-if="validateInfos.infos.template.show"
                    label="模版"
                    prop="template"
                >
                    <el-input
                        v-model="config.template"
                        type="textarea"
                        :rows="5"
                        :autosize="false"
                        style="width: 420px;"
                        placeholder="请输入ftl模版，变量为：it、it1、it2、it3、it4"
                    />
                    <el-tooltip
                        v-if="validateInfos.infos.template.error"
                        :content="validateInfos.infos.template.content"
                        placement="top"
                    >
                        <el-icon
                            size="20px"
                            color="#E6A23C"
                            class="icon-warning"
                        >
                            <WarningFilled />
                        </el-icon>
                    </el-tooltip>
                </el-form-item>
                <el-form-item
                    v-if="validateInfos.infos.key.show"
                    label="映射path"
                    prop="originRef"
                >
                    <el-input
                        v-model="config.extend.originRef"
                        maxlength="200"
                        disabled
                        style="width: 420px;"
                    />
                    <div class="clear-btn">
                        <el-button
                            type="primary"
                            link
                            @click="onClearOriginRef"
                        >
                            清除
                        </el-button>
                    </div>
                </el-form-item>
                <el-form-item
                    v-if="validateInfos.infos.precision.show"
                    label="精度"
                    prop="precision"
                >
                    <el-input-number
                        v-model="config.precision"
                        :min="0"
                        :max="6"
                        style="width: 160px;"
                    />
                    <el-tooltip
                        v-if="validateInfos.infos.precision.error"
                        :content="validateInfos.infos.precision.content"
                        placement="top"
                    >
                        <el-icon
                            size="20px"
                            color="#E6A23C"
                            class="icon-warning"
                        >
                            <WarningFilled />
                        </el-icon>
                    </el-tooltip>
                </el-form-item>
                <el-form-item
                    v-if="validateInfos.infos.maxLength.show"
                    label="最大长度"
                    prop="maxLength"
                >
                    <el-input-number
                        v-model="config.maxLength"
                        :min="1"
                        :max="1000000"
                        style="width: 160px;"
                    />
                    <el-tooltip
                        v-if="validateInfos.infos.maxLength.error"
                        :content="validateInfos.infos.maxLength.content"
                        placement="top"
                    >
                        <el-icon
                            size="20px"
                            color="#E6A23C"
                            class="icon-warning"
                        >
                            <WarningFilled />
                        </el-icon>
                    </el-tooltip>
                </el-form-item>
                <el-form-item
                    v-if="validateInfos.infos.format.show"
                    label="日期格式"
                    prop="format"
                >
                    <el-autocomplete
                        v-model="config.format"
                        :fetch-suggestions="fetchFormatSuggestions"
                        clearable
                        style="width: 420px;"
                    />
                    <el-tooltip
                        v-if="validateInfos.infos.format.error"
                        :content="validateInfos.infos.format.content"
                        placement="top"
                    >
                        <el-icon
                            size="20px"
                            color="#E6A23C"
                            class="icon-warning"
                        >
                            <WarningFilled />
                        </el-icon>
                    </el-tooltip>
                </el-form-item>
                <el-form-item
                    v-if="validateInfos.infos.enumValues.show"
                    label="枚举列表"
                    prop="enumValues"
                >
                    <el-table
                        :data="formData.enumValues"
                        border
                        
                        :cell-style="{
                            padding: '4px 0',
                        }"
                        :max-height="280"
                        style="width: 420px;"
                        row-key="refValue"
                    >
                        <el-table-column
                            prop="refValue"
                            label="值"
                            min-width="68"
                            show-overflow-tooltip
                        />
                        <el-table-column
                            prop="refLabel"
                            label="说明"
                            min-width="100"
                        />
                        <el-table-column
                            prop="value"
                            label="值"
                            min-width="68"
                        >
                            <template #default="{ row }">
                                <el-input v-model="row.value" />
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="description"
                            label="说明"
                            min-width="100"
                        >
                            <template #default="{ row }">
                                <el-input v-model="row.description" />
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-tooltip
                        v-if="validateInfos.infos.enumValues.error"
                        :content="validateInfos.infos.enumValues.content"
                        placement="top"
                    >
                        <el-icon
                            size="20px"
                            color="#E6A23C"
                            class="icon-warning"
                        >
                            <WarningFilled />
                        </el-icon>
                    </el-tooltip>
                </el-form-item>
                <el-form-item
                    v-if="validateInfos.infos.required.show"
                    label="是否必填"
                    prop="required"
                >
                    <el-checkbox
                        v-model="config.extend.required"
                        label=""
                        :true-value="1"
                        :false-value="0"
                    />
                </el-form-item>
                <el-form-item
                    v-if="validateInfos.infos.defaultValue.show"
                    label="默认值"
                    prop="defaultValue"
                >
                    <el-input
                        v-model="config.defaultValue"
                        maxlength="100"
                        style="width: 420px;"
                        placeholder="必传参数时应填写"
                    />
                    <el-tooltip
                        v-if="validateInfos.infos.defaultValue.error"
                        :content="validateInfos.infos.defaultValue.content"
                        placement="top"
                    >
                        <el-icon
                            size="20px"
                            color="#E6A23C"
                            class="icon-warning"
                        >
                            <WarningFilled />
                        </el-icon>
                    </el-tooltip>
                </el-form-item>
                <el-form-item
                    label="说明"
                    prop="note"
                >
                    <el-input
                        v-model="config.note"
                        maxlength="500"
                        type="textarea"
                        :rows="3"
                        :autosize="false"
                        style="width: 420px;"
                    />
                    <el-tooltip
                        v-if="validateInfos.infos.note.error"
                        :content="validateInfos.infos.note.content"
                        placement="top"
                    >
                        <el-icon
                            size="20px"
                            color="#E6A23C"
                            class="icon-warning"
                        >
                            <WarningFilled />
                        </el-icon>
                    </el-tooltip>
                </el-form-item>
            </el-form>
        </template>
    </el-popover>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus';
import { Edit } from '@element-plus/icons-vue';
import { createViewPopoverConfigController } from './controller';
import { computed, reactive, watch, onMounted, onDeactivated } from 'vue';

const props = defineProps({
    visible: {
        type: Boolean,
        required: true,
    },
    targetFormat: {
        type: String,
        required: true,
    },
    config: {
        type: Object,
        required: true,
    },
    typeOptions: {
        type: Array,
        required: true,
    },
    findDataSourceByRef: {
        type: Function,
        required: true,
    },
});

const emit = defineEmits([
    'switch',
    'update',
]);

const {
    createValidateInfos,
} = createViewPopoverConfigController();

const formData = reactive({
    enumValues: <any []>[],
    argument: '',
});

let watchRef: any = null;
let watchEnumValues: any = null;

onMounted(() => {
    initFormData();
    updateEnumValues();

    watchRef = watch(
        [
            () => props.config.extend.originRef,
        ],
        () => {
            initFormData();
        },
    );
    watchEnumValues = watch(
        formData,
        () => {
            updateEnumValues();
        },
        {
            deep: true,
        },
    );
});

onDeactivated(() => {
    if (watchRef) {
        watchRef();
        watchRef = null;
    }
    if (watchEnumValues) {
        watchEnumValues();
        watchEnumValues = null;
    }
});

watch([
    () => props.config.key,
    () => props.config.type,
], () => {
    if (
        props.targetFormat === 'xml'
        && props.config.key === '$'
        && props.config.type === 'object'
        && props.config.note === ''
    ) {
        // 让xml的$对象不用每次都设置说明，默认：属性
        props.config.note = '属性';
    }
}, {
    deep: true,
    immediate: true,
});

/**
 * 初始化表单数据
 * <AUTHOR>
 * @date 2025-09-18
 */
const initFormData = () => {
    const refTarget = (() => {
        const originRef = props.config.extend?.originRef || '';
        if (!originRef) {
            return null;
        }
        return props.findDataSourceByRef(originRef);
    })();
    
    // 枚举列表初始化
    if (
        !refTarget
        || !refTarget.enumValues
        || refTarget.enumValues.length === 0
    ) {
        formData.enumValues = [];
        return;
    }
    // 枚举列表
    formData.enumValues = refTarget.enumValues.map((item: any) => {
        const target = props.config.enumValues.find((one: any) => one.refValue === item.value);
        return {
            refValue: item.value,
            refLabel: item.description,
            value: target?.value || '',
            description: target?.description || '',
        };
    });
};

/**
 * 更新枚举列表
 * <AUTHOR>
 * @date 2025-09-29
 */
const updateEnumValues = () => {
    props.config.enumValues = (formData.enumValues || []).map((item: any) => {
        const itemInfo = {
            refValue: item.refValue,
            // refLabel: item.refLabel,
            value: item.value,
            description: item.description,
        };
        return itemInfo;
    });
};

// 验证信息
const validateInfos = computed(() => createValidateInfos(props.config, props.targetFormat));

/**
 * 查询日期格式建议
 * <AUTHOR>
 * @date 2025-09-15
 * @param {String} queryString
 * @param {Function} cb
 * @returns {Array}
 */
const fetchFormatSuggestions = (_: string, cb: any) => {
    const suggestions = [
        { value: 'yyyy-MM-dd' },
        { value: 'yyyy-MM-dd HH:mm:ss' },
    ];
    if (props.targetFormat === 'sql') {
        // sql时，可以转成日期对象
        suggestions.unshift({ value: 'DATE' });
    }
    cb(suggestions);
};

/**
 * 清除映射路径
 * <AUTHOR>
 * @date 2025-09-29
 */
const onClearOriginRef = () => {
    props.config.extend.originRef = '';
    emit('update');
};

/**
 * 添加变量
 * <AUTHOR>
 * @date 2025-09-29
 */
const onClickEnterTag = () => {
    const tag = formData.argument;
    if (!tag) {
        return;
    }
    if (props.config.variables.includes(tag)) {
        ElMessage.error('变量已存在');
        return;
    }
    props.config.variables.push(tag);
    formData.argument = '';
};

/**
 * 点击标签
 * <AUTHOR>
 * @date 2025-09-25
 * @param {string} tag
 */
const onClickTag = (tag: string) => {
    formData.argument = tag;
};

/**
 * 关闭标签
 * <AUTHOR>
 * @date 2025-09-25
 * @param {string} tag
 */
const onClickCloseTag = (tag: string) => {
    props.config.variables = props.config.variables.filter((item: string) => item !== tag);
};
</script>

<style lang="scss">
.view-popover-form {
    position: relative;
    padding: 8px 0;

    .close-wrapper {
        position: absolute;
        top: -10px;
        right: -10px;
        cursor: pointer;
        color: rgb(177, 179, 184);

        &:hover {
            color: #409eff;
        }
    }

    .el-form-item__content {
        align-items: flex-start;

        .icon-warning {
            margin: 6px 0 0 10px !important;
        }

        .clear-btn {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 32px;
            height: 32px;
            margin-left: 4px;
        }
    }

    .args-wrapper {
        width: 100%;
        display: flex;
        flex-wrap: wrap;

        .el-tag {
            margin-top: 8px;
            margin-right: 8px;
            cursor: pointer;
        }
    }
}
</style>
