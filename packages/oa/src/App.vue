<script setup lang='ts'>
// This starter template is using Vue 3 <script setup> SFCs
// Check out https://v3.vuejs.org/api/sfc-script-setup.html#sfc-script-setup
import { OaFloatNav } from '@abc-oa/components';
import { useFloatNavStore } from '@/store/float-nav';

// 使用浮动导航状态管理
const floatNavStore = useFloatNavStore();
</script>

<template>
    <router-view></router-view>
    <!-- 根据 store 状态决定是否显示浮动导航 -->
    <OaFloatNav v-if="floatNavStore.isVisible" />
</template>
