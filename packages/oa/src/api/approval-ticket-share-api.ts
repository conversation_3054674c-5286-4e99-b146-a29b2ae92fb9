import BaseAPI from "./share-base-api";

/**
* 分享单据
*/
export class ApprovalTicketShareAPI extends BaseAPI{

    
    /**
    * 查询审批单详情-增购账号
    * @param {string} id - id    
    */
    static getApprovalTicketViewBuyAccountUsingGET_1 (
        id:string,
        ) {
    
    return this.get<AbcAPI.ApprovalTicketBuyAccountView>(`/api-share/management/approval/ticket/buy-account/${id}`);
    
    }

    
    /**
    * 查询审批单详情-检验设备采购
    * @param {string} id - id    
    */
    static createApprovalTicketBuyExaminationDeviceUsingGET (
        id:string,
        ) {
    
    return this.get<AbcAPI.ApprovalTicketBuyExaminationDeviceView>(`/api-share/management/approval/ticket/buy-examination-device/${id}`);
    
    }

    
    /**
    * 查询审批单详情-新建子店
    * @param {string} id - id    
    */
    static getApprovalTicketViewCreateClinicUsingGET_1 (
        id:string,
        ) {
    
    return this.get<AbcAPI.ApprovalTicketCreateClinicView>(`/api-share/management/approval/ticket/create-clinic/${id}`);
    
    }

    
    /**
    * 查询审批单详情-新建云检门店
    * @param {string} id - id    
    */
    static getApprovalTicketViewCreateCloudExamUsingGET_1 (
        id:string,
        ) {
    
    return this.get<AbcAPI.ApprovalTicketCreateCloudExamView>(`/api-share/management/approval/ticket/create-cloud-exam/${id}`);
    
    }

    
    /**
    * 查询审批单详情-申请试用
    * @param {string} id - id    
    */
    static getApprovalTicketViewCreateTrialClinicUsingGET_1 (
        id:string,
        ) {
    
    return this.get<AbcAPI.ApprovalTicketCreateTrialClinicView>(`/api-share/management/approval/ticket/create-trial-clinic/${id}`);
    
    }

    
    /**
    * 查询审批单详情-降级
    * @param {string} id - id    
    */
    static getApprovalTicketViewDowngradeClinicUsingGET_1 (
        id:string,
        ) {
    
    return this.get<AbcAPI.ApprovalTicketRenewClinicView>(`/api-share/management/approval/ticket/downgrade-clinic/${id}`);
    
    }

    
    /**
    * 查询审批单详情-微商城
    * @param {number} buyMethod - buyMethod    
    * @param {string} id - id    
    */
    static getApprovalTicketViewMicroMartPrescriptionUsingGET_1 (
        buyMethod:number,
        id:string,
        ) {
    
    
    return this.get<AbcAPI.ApprovalTicketBuyMicroMartView>(`/api-share/management/approval/ticket/micro-mart/${id}`, {
        params: {
                buyMethod,
                }
    });
    
    
    }

    
    /**
    * 查询审批单详情-其他收费
    * @param {string} id - id    
    */
    static getApprovalTicketViewOtherChargeUsingGET_1 (
        id:string,
        ) {
    
    return this.get<AbcAPI.ApprovalTicketOtherChargeView>(`/api-share/management/approval/ticket/other-charge/${id}`);
    
    }

    
    /**
    * 查询审批单详情-防治方
    * @param {string} id - id    
    */
    static getApprovalTicketViewPreventionPrescriptionUsingGET_1 (
        id:string,
        ) {
    
    return this.get<AbcAPI.ApprovalTicketPreventionPrescriptionView>(`/api-share/management/approval/ticket/prevention-prescription/${id}`);
    
    }

    
    /**
    * 查询审批单详情-续费
    * @param {string} id - id    
    */
    static getApprovalTicketViewRenewClinicUsingGET_2 (
        id:string,
        ) {
    
    return this.get<AbcAPI.ApprovalTicketRenewClinicView>(`/api-share/management/approval/ticket/renew-clinic/${id}`);
    
    }

    
    /**
    * 查询审批单详情-试用门店转正
    * @param {string} id - id    
    */
    static getApprovalTicketViewTransTrialClinicUsingGET (
        id:string,
        ) {
    
    return this.get<AbcAPI.ApprovalTicketRenewClinicView>(`/api-share/management/approval/ticket/trans-trial-clinic/${id}`);
    
    }

    
    /**
    * 查询审批单详情-升级
    * @param {string} id - id    
    */
    static getApprovalTicketViewUpgradeClinicUsingGET_1 (
        id:string,
        ) {
    
    return this.get<AbcAPI.ApprovalTicketRenewClinicView>(`/api-share/management/approval/ticket/upgrade-clinic/${id}`);
    
    }

    
    /**
    * 查询审批单详情-企微管家
    * @param {number} buyMethod - buyMethod    
    * @param {string} id - id    
    */
    static getApprovalTicketViewEnterpriseWechatPrescriptionUsingGET_1 (
        buyMethod:number,
        id:string,
        ) {
    
    
    return this.get<AbcAPI.ApprovalTicketBuyEnterpriseWechatView>(`/api-share/management/approval/ticket/wechat/${id}`, {
        params: {
                buyMethod,
                }
    });
    
    
    }

    
    /**
    * OA订单查询ABC收款账户
    * @param {string} id - 审批单ID    
    * @param {number} payMode - 支付方式    
    */
    static getApprovalTicketOwnAccountUsingGET (
        id:string,
        payMode:number,
        ) {
    
    
    return this.get<AbcAPI.QueryOwnAccountRsp>(`/api-share/management/approval/ticket/${id}/own-account`, {
        params: {
                payMode,
                }
    });
    
    
    }

    
    /**
    * 发起审批单支付
    * @param {string} id - id    
    * @param {AbcAPI.ApprovalTicketPayReq} reqBody - reqBody    
    */
    static payApprovalTicketUsingPUT_1 (
        id:string,
        reqBody:AbcAPI.ApprovalTicketPayReq,
        ) {
    
    
    return this.put<AbcAPI.ApprovalTicketPayRsp>(
        `/api-share/management/approval/ticket/${id}/pay`,
        reqBody
    );
    
    
    }

    
    /**
    * 查询审批单支付状态
    * @param {string} id - id    
    */
    static queryApprovalTicketPayStatusUsingGET_1 (
        id:string,
        ) {
    
    return this.get<AbcAPI.ApprovalTicketPayStatusRsp>(`/api-share/management/approval/ticket/${id}/pay-status`);
    
    }

}