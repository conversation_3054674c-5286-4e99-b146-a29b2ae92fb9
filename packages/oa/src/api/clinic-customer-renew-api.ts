import ShareBaseAP<PERSON> from '@/api/share-base-api';

/**
* Clinic Customer Renew Controller
*/
export class ClinicCustomerRenewAPI extends ShareBaseAPI {
    /**
    * 创建订单
    * @param {string} chainId - chainId
    * @param {string} clinicId - clinicId
    * @param {string} employeeId - employeeId
    * @param {AbcAPI.ClinicEditionOrderCreateReq} reqBody - reqBody
    */
    // eslint-disable-next-line camelcase
    static createClinicEditionOrderUsingPOST_1(
        chainId:string,
        clinicId:string,
        employeeId:string,
        reqBody:AbcAPI.ClinicEditionOrderCreateReq,
    ) {
        return this.post<AbcAPI.ClinicCustomerEditionOrderView>(
            `/api-share/management/clinics/edition/order?chainId=${chainId}&clinicId=${clinicId}&employeeId=${employeeId}`, reqBody,
        );
    }

    /**
    * 订单算费
    * @param {string} chainId - chainId
    * @param {string} clinicId - clinicId
    * @param {string} employeeId - employeeId
    * @param {AbcAPI.ClinicEditionOrderCalculateReq} reqBody - reqBody
    */
    // eslint-disable-next-line camelcase
    static calculateClinicEditionOrderUsingPOST_1(
        chainId:string,
        clinicId:string,
        employeeId:string,
        reqBody:AbcAPI.ClinicEditionOrderCalculateReq,
    ) {
        return this.post<AbcAPI.ClinicCustomerEditionOrderCalculateRsp>(
            `/api-share/management/clinics/edition/order/calculate?chainId=${chainId}&clinicId=${clinicId}&employeeId=${employeeId}`,
            reqBody,
        );
    }

    /**
    * 获取待支付订单
    * @param {string} chainId - chainId
    * @param {string} clinicId - clinicId
    */
    static getWaitingPaidClinicEditionOrderUsingGET(
        chainId:string,
        clinicId:string,
    ) {
        return this.get<AbcAPI.ClinicCustomerEditionOrderView>(
            `/api-share/management/clinics/edition/order/waiting-by-clinic?chainId=${chainId}&clinicId=${clinicId}`,
        );
    }

    /**
    * 获取订单信息
    * @param {string} chainId - chainId
    * @param {string} employeeId - employeeId
    * @param {string} orderId - orderId
    */
    // eslint-disable-next-line camelcase
    static getClinicEditionOrderUsingGET_1(
        chainId:string,
        employeeId:string,
        orderId:string,
    ) {
        return this.get<AbcAPI.ClinicCustomerEditionOrderView>(
            `/api-share/management/clinics/edition/order/${orderId}?chainId=${chainId}&employeeId=${employeeId}`,
        );
    }

    /**
    * 支付订单
    * @param {string} employeeId - employeeId
    * @param {string} orderId - orderId
    * @param {AbcAPI.QWPayForClinicEditionOrderReq} reqBody - reqBody
    */
    // eslint-disable-next-line camelcase
    static payClinicEditionOrderUsingPUT_1(
        employeeId:string,
        clinicId:string,
        orderId:string,
        reqBody:AbcAPI.QWPayForClinicEditionOrderReq,
    ) {
        return this.put<AbcAPI.QWPayForClinicEditionOrderRsp>(
            `/api-share/management/clinics/edition/order/${orderId}/pay?employeeId=${employeeId}&clinicId=${clinicId}`, reqBody,
        );
    }

    /**
    * 获取订单状态
    * @param {string} orderId - orderId
    */
    // eslint-disable-next-line camelcase
    static queryClinicEditionOrderStatusUsingGET_1(
        orderId:string,
        clinicId:string,
    ) {
        return this.get<AbcAPI.QueryPayOrderStatusRsp>(`/api-share/management/clinics/edition/order/${orderId}/status?clinicId=${clinicId}`);
    }

    /**
    * 客户微信code登录
    * @param {AbcAPI.CustomerLoginByCodeReq} reqBody - reqBody
    */
    static customerEditionOrderClosePut(
        chainId:string,
        clinicId:string,
        employeeId:string,
        orderId: string,
    ) {
        return this.put<any>(
            `/api-share/management/clinics/edition/order/${orderId}/close?chainId=${chainId}&clinicId=${clinicId}&employeeId=${employeeId}`,
        );
    }
}
