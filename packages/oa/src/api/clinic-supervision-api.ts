import BaseAPI from './base-api';

/**
* Clinic Supervision Controller
*/
export class ClinicSupervisionAPI extends BaseAPI {
    /**
    * oa根据门店导出追进销存明细列表
    * @param {AbcAPI.StatTracCodeByClinicDetailExportReq} req - req    
    */
    static statTracCodeByClinicDetailOaExportUsingPOST(
        req:AbcAPI.StatTracCodeByClinicDetailExportReq,
    ) {
        return this.post<AbcAPI.BaseOaSuccessRsp>(
            '/api/management/supervision/stat/stat-trac-code-by-clinic-detail-oa/export',
            req,
        );
    }
    
    /**
    * OA按日期进销存数据统计
    * @param {string} beginDate - beginDate    
    * @param {string} chainId - chainId    
    * @param {string} clinicId - clinicId    
    * @param {string} endDate - endDate    
    */
    static getShebaoStockStatOaUsingGET(
        beginDate:string,
        chainId:string,
        clinicId:string,
        endDate:string,
    ) {
        return this.get<AbcAPI.ClinicShebaoStockStatView>('/api/management/supervision/stock/stat/get-shebao-stock-stat-oa', {
            params: {
                beginDate,
                chainId,
                clinicId,
                endDate,
            },
        });
    }
}