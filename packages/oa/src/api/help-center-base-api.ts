import { service } from '@/utils/help-center-request';

export default class HelpCenterBaseAPI {
    protected static get get() {
        return service.get;
    }

    protected static get post() {
        return service.post;
    }

    protected static get put() {
        return service.put;
    }

    protected static get del() {
        return service.delete;
    }

    protected static get head() {
        return service.delete;
    }

    protected static get options() {
        return service.options;
    }

    protected static get patch() {
        return service.patch;
    }
}
