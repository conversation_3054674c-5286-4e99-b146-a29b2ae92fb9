import BaseAPI from './base-api';

/**
* 
*/
export class Inspection<PERSON>pi extends BaseAPI {
    /**
    * 获取检查检验分类树
    * @param {number} type - 检查检验类型    
    * @param {string} isClean - 是否干净    
    */
    static getApiWarehouseInspectionCategory(
        type:number,
        isClean?:string,
    ) {
        return this.get<AbcAPI.InspectionCategory>('/api/warehouse/inspection/category', {
            params: {
                type,
                isClean,
            },
        });
    }
    
    /**
    * 新建检查检验分类
    * @param {AbcAPI.CreatedInspectionCategoryDto} createdInspectionCategoryDto -     
    */
    static postApiWarehouseInspectionCategory(
        createdInspectionCategoryDto:AbcAPI.CreatedInspectionCategoryDto,
    ) {
        return this.post<AbcAPI.InspectionCategory>('/api/warehouse/inspection/category', createdInspectionCategoryDto);
    }
    
    /**
    * 编辑检查检验分类
    * @param {AbcAPI.CreatedInspectionCategoryDto} createdInspectionCategoryDto -     
    * @param {string} id -     
    */
    static putApiWarehouseInspectionCategoryById(
        createdInspectionCategoryDto:AbcAPI.CreatedInspectionCategoryDto,
        id:string,
    ) {
        return this.put<AbcAPI.InspectionCategory>(`/api/warehouse/inspection/category/${id}`, createdInspectionCategoryDto);
    }
    
    /**
    * 删除检查检验分类
    * @param {string} id -     
    */
    static deleteApiWarehouseInspectionCategoryById(
        id:string,
    ) {
        return this.del<AbcAPI.InspectionCategory>(`/api/warehouse/inspection/category/${id}`);
    }
    
    /**
    * 获取检查检验标题列表
    * @param {string} type -     
    */
    static getApiWarehouseInspectionTagList(
        type:string,
    ) {
        return this.get<AbcAPI.InspectionCategory>('/api/warehouse/inspection/tag/list', {
            params: {
                type,
            },
        });
    }
    
    /**
    * 新建检查检验标题
    * @param {AbcAPI.CreatedInspectionTagDto} createdInspectionTagDto -     
    */
    static postApiWarehouseInspectionTag(
        createdInspectionTagDto:AbcAPI.CreatedInspectionTagDto,
    ) {
        return this.post<AbcAPI.InspectionTag>('/api/warehouse/inspection/tag', createdInspectionTagDto);
    }
    
    /**
    * 编辑检查检验标题
    * @param {AbcAPI.CreatedInspectionTagDto} createdInspectionTagDto -     
    */
    static putApiWarehouseInspectionTag(
        createdInspectionTagDto:AbcAPI.CreatedInspectionTagDto,
    ) {
        return this.put<AbcAPI.InspectionTag>('/api/warehouse/inspection/tag', createdInspectionTagDto);
    }
    
    /**
    * 删除检查检验标题
    * @param {AbcAPI.CreatedInspectionTagDto} createdInspectionTagDto -     
    */
    static deleteApiWarehouseInspectionTag(
        createdInspectionTagDto:AbcAPI.CreatedInspectionTagDto,
    ) {
        return this.del<AbcAPI.InspectionTag>('/api/warehouse/inspection/tag', { data: createdInspectionTagDto });
    }
    
    /**
    * 获取检查检验
    * @param {string} id -     
    */
    static getApiWarehouseInspectionById(
        id:string,
    ) {
        return this.get<AbcAPI.InspectionCategory>(`/api/warehouse/inspection/${id}`);
    }
    
    /**
    * 编辑检查检验
    * @param {AbcAPI.CreatedInspectionCategoryDto} createdInspectionCategoryDto -     
    * @param {string} id -     
    */
    static putApiWarehouseInspectionById(
        createdInspectionCategoryDto:AbcAPI.CreatedInspectionCategoryDto,
        id:string,
    ) {
        return this.put<AbcAPI.InspectionCategory>(`/api/warehouse/inspection/${id}`, createdInspectionCategoryDto);
    }
    
    /**
    * 删除检查检验
    * @param {string} id -     
    */
    static deleteApiWarehouseInspectionById(
        id:string,
    ) {
        return this.del<AbcAPI.InspectionCategory>(`/api/warehouse/inspection/${id}`);
    }
    
    /**
    * 新建检查检验
    * @param {AbcAPI.CreatedInspectionDto} createdInspectionDto -     
    */
    static postApiWarehouseInspection(
        createdInspectionDto:AbcAPI.CreatedInspectionDto,
    ) {
        return this.post<AbcAPI.InspectionCategory>('/api/warehouse/inspection', createdInspectionDto);
    }
    
    /**
    * 编辑检查检验详情
    * @param {AbcAPI.CreatedInspectionDto} createdInspectionDto -     
    * @param {string} id -     
    */
    static putApiWarehouseInspectionDetailById(
        createdInspectionDto:AbcAPI.CreatedInspectionDto,
        id:string,
    ) {
        return this.put<AbcAPI.InspectionCategory>(`/api/warehouse/inspection/detail/${id}`, createdInspectionDto);
    }
}