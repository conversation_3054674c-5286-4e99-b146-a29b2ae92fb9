import BaseAPI from './base-api';

/**
* 实施工单
*/
export class SupportTicketAPI extends BaseAPI {
    /**
    * 更换实施人可选实施人列表
    * @param {string} formId - formId    
    */
    static listAvailableImplementerUsingGET(
        formId:string,
    ) {
        return this.get<AbcAPI.AbcListPageCorpUserBasicView>(`/api/management/support/appoint/change-implementer/available-implementer/${formId}`);
    }
    
    /**
    * 待实施客户数量-七天日历看板
    * @param {string} date - 日期yyyy-MM-dd    
    */
    static sevenDaysCalendarCountToImplementedUsingGET(
        date:string,
    ) {
        return this.get<AbcAPI.CalendarCountView>('/api/management/support/calendar/seven-days/to-implemented', {
            params: {
                date,
            },
        });
    }
    
    /**
    * 待实施客户数量-日历看板
    * @param {number} month - month    
    * @param {number} year - year    
    */
    static calendarCountToImplementedUsingGET(
        month:number,
        year:number,
    ) {
        return this.get<AbcAPI.CalendarCountView>('/api/management/support/calendar/to-implemented', {
            params: {
                month,
                year,
            },
        });
    }
    
    /**
    * 门店活跃度详情
    * @param {string} clinicId - clinicId    
    */
    static getClinicActiveDetailUsingGET(
        clinicId:string,
    ) {
        return this.get<AbcAPI.ClinicActiveDetailView>(`/api/management/support/clinic/active/${clinicId}`);
    }
    
    /**
    * 获取预约时间段
    * @param {string} clinicId - clinicId    
    * @param {string} supportSheetId - supportSheetId    
    */
    static calcAppointTimeUsingGET(
        clinicId:string,
        supportSheetId:string,
    ) {
        return this.get<AbcAPI.CalculateAppointTimeRsp>(`/api/management/support/clinic/${clinicId}/sheet/${supportSheetId}/appoint-time`);
    }
    
    /**
    * 创建实施工单
    * @param {AbcAPI.CreateSupportTicketReq} reqBody - reqBody    
    */
    static createSupportTicketUsingPOST(
        reqBody:AbcAPI.CreateSupportTicketReq,
    ) {
        return this.post<AbcAPI.CreateSupportTicketRsp>(
            '/api/management/support/create-ticket',
            reqBody,
        );
    }
    
    /**
    * 客户资料详情
    * @param {string} clinicId - clinicId 客户id    
    */
    static getCustomerDetailUsingGET(
        clinicId:string,
    ) {
        return this.get<AbcAPI.CustomerDetailView>('/api/management/support/customer/detail', {
            params: {
                clinicId,
            },
        });
    }
    
    /**
    * 客户确认页
    * @param {string} formId - formId    
    * @param {string} sheetId - sheetId    
    */
    static showConfirmPageUsingGET(
        formId?:string,
        sheetId?:string,
    ) {
        return this.get<AbcAPI.CustomerConfirmView>('/api/management/support/customer/form/show-confirm', {
            params: {
                formId,
                sheetId,
            },
        });
    }
    
    /**
    * OA实施排期看板列表
    * @param {string} date - date    
    */
    static listDashboardScheduleAppointmentUsingGET(
        date:string,
    ) {
        return this.get<AbcAPI.AbcListPageDashboardAppointmentView>('/api/management/support/dashboard/schedule-appointment', {
            params: {
                date,
            },
        });
    }
    
    /**
    * 新增跟进记录
    * @param {AbcAPI.SupportClinicVisitRecordReq} req - req    
    */
    static addFollowUpRecordUsingPOST(
        req:AbcAPI.SupportClinicVisitRecordReq,
    ) {
        return this.post<AbcAPI.SupportClinicVisitRecordView>(
            '/api/management/support/follow-up/record',
            req,
        );
    }
    
    /**
    * 预约列表
    * @param {string} keyword - keyword    
    */
    static listAppointUsingGET(
        keyword:string,
    ) {
        return this.get<AbcAPI.AbcListPageSupportTicketFormView>('/api/management/support/form/appoint', {
            params: {
                keyword,
            },
        });
    }
    
    /**
    * OA看板预约
    * @param {AbcAPI.DashboardAppointSupportReq} req - req    
    */
    static appointForDashboardUsingPUT(
        req:AbcAPI.DashboardAppointSupportReq,
    ) {
        return this.put<AbcAPI.AppointSupportView>(
            '/api/management/support/form/appoint',
            req,
        );
    }
    
    /**
    * 保存预约安排
    * @param {AbcAPI.SupportTicketChangeImplementerReq} req - req    
    */
    static saveAppointUsingPOST(
        req:AbcAPI.SupportTicketChangeImplementerReq,
    ) {
        return this.post<AbcAPI.SupportTicketFormView>(
            '/api/management/support/form/appoint/save',
            req,
        );
    }
    
    /**
    * 客户-预约/完成确认
    * @param {AbcAPI.SupportTicketFormConfirmReq} req - req    
    */
    static CustomerFormConfirmUsingPUT(
        req:AbcAPI.SupportTicketFormConfirmReq,
    ) {
        return this.put<AbcAPI.SupportCustomerConfirmView>(
            '/api/management/support/form/customer/confirm',
            req,
        );
    }
    
    /**
    * 客户评价
    * @param {AbcAPI.SupportTicketFormEvaluateReq} req - req    
    * @param {string} sheetId - sheetId    
    */
    static evaluateUsingPUT(
        req:AbcAPI.SupportTicketFormEvaluateReq,
        sheetId:string,
    ) {
        return this.put<AbcAPI.SupportTicketSheetView>(
        `/api/management/support/form/evaluate/${sheetId}`,
        req,
        );
    }
    
    /**
    * 撤销安排
    * @param {string} formId - formId    
    */
    static revokeUsingPUT(
        formId:string,
    ) {
        return this.put<AbcAPI.SupportTicketFormView>(`/api/management/support/form/revoke/${formId}`);
    }
    
    /**
    * 工单选择实施项目
    * @param {AbcAPI.ChooseSupportItemReq} req - req    
    */
    static chooseItemUsingPOST(
        req:AbcAPI.ChooseSupportItemReq,
    ) {
        return this.post<AbcAPI.SupportItemChooseListView>(
            '/api/management/support/item/choose',
            req,
        );
    }
    
    /**
    * 实施工单实施项目列表
    * @param {string} supportSheetId - supportSheetId    
    */
    static itemChooseListUsingGET(
        supportSheetId:string,
    ) {
        return this.get<AbcAPI.SupportItemChooseListView>('/api/management/support/item/choose/list', {
            params: {
                supportSheetId,
            },
        });
    }
    
    /**
    * 实施人-实施项目确认/取消/重约
    * @param {AbcAPI.SupportTicketItemConfirmReq} req - req    
    */
    static implementerConfirmUsingPUT(
        req:AbcAPI.SupportTicketItemConfirmReq,
    ) {
        return this.put<AbcAPI.SupportTicketItemView>(
            '/api/management/support/item/confirm',
            req,
        );
    }
    
    /**
    * 实施项目详情
    * @param {string} itemId - itemId    
    */
    static getItemUsingGET(
        itemId:string,
    ) {
        return this.get<AbcAPI.SupportTicketItemView>(`/api/management/support/item/detail/${itemId}`);
    }
    
    /**
    * 完善实施项目信息
    * @param {AbcAPI.RefineSupportTicketItemReq} req - req    
    */
    static refineItemInfoUsingPOST(
        req:AbcAPI.RefineSupportTicketItemReq,
    ) {
        return this.post<AbcAPI.SupportTicketItemView>(
            '/api/management/support/item/refine',
            req,
        );
    }
    
    /**
    * 实施工单列表
    * @param {number} limit - 每页条数    
    * @param {string} keyword - 搜索关键词    
    * @param {string} lastId - 最后一条记录的id    
    * @param {number} status - 状态 0:待安排 10:待实施 20:已实施    
    */
    static pageListUsingGET(
        limit:number,
        keyword?:string,
        lastId?:string,
        status?:number,
    ) {
        return this.get<AbcAPI.AbcListPageSupportTicketSheetListView>('/api/management/support/page-list', {
            params: {
                keyword,
                lastId,
                limit,
                status,
            },
        });
    }
    
    /**
    * 查询实施某日排班及预约信息
    * @param {string} date - date    
    * @param {string} implementerId - implementerId    
    */
    static getImplementerScheduleAppointmentUsingGET(
        date:string,
        implementerId:string,
    ) {
        return this.get<AbcAPI.GetImplementerAppointmentRsp>('/api/management/support/schedule/appointment', {
            params: {
                date,
                implementerId,
            },
        });
    }
    
    /**
    * 预约
    * @param {AbcAPI.AppointSupportReq} req - req    
    */
    static appointUsingPUT(
        req:AbcAPI.AppointSupportReq,
    ) {
        return this.put<AbcAPI.AppointSupportView>(
            '/api/management/support/sheet/appoint',
            req,
        );
    }
    
    /**
    * 工单详情
    * @param {string} sheetId - sheetId    
    */
    static getSheetUsingGET(
        sheetId:string,
    ) {
        return this.get<AbcAPI.SupportTicketSheetView>(`/api/management/support/sheet/${sheetId}`);
    }
    
    /**
    * 查询实施工单实施时间
    * @param {string} sheetId - sheetId    
    */
    static getSheetImplementTimeUsingGET(
        sheetId:string,
    ) {
        return this.get<AbcAPI.SupportTicketSheetImplementTimeView>(`/api/management/support/sheet/${sheetId}/implement-time`);
    }
    
    /**
    * 待激活/已激活客户列表
    * @param {number} limit - 每页条数    
    * @param {number} status - 状态 300:待激活 400:已激活    
    * @param {string} lastId - 最后一条记录的id    
    */
    static toActivatedPageListUsingGET(
        limit:number,
        status:number,
        lastId?:string,
    ) {
        return this.get<AbcAPI.AbcListPageSupportClinicImplementerView>('/api/management/support/to-activated/page-list', {
            params: {
                lastId,
                limit,
                status,
            },
        });
    }
}