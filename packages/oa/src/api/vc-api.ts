import BaseAPI from './base-api';

/**
* 商家中心接口
*/
export class VcAPI extends BaseAPI {
    /**
    * pageListBisOrgans
    * @param {number} limit - limit    
    * @param {number} offset - offset    
    * @param {number} organType - organType    
    */
    static pageListBisOrgansUsingGET(
        limit?:number,
        offset?:number,
        organType?:number,
    ) {
        return this.get<AbcAPI.AbcListPageVendorListVO>('/api/management/vc/vendors/organs', {
            params: {
                limit,
                offset,
                organType,
            },
        });
    }
}