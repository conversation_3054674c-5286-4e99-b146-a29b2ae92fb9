/**
 * 通用的 Response 结构
 */
export class AbcResponse {
    status: boolean;

    message: string;

    data: any;

    private constructor(status: boolean, message: string, data: any) {
        this.status = status;
        this.message = message;
        this.data = data;
    }

    static success(data?: any) {
        return new AbcResponse(true, 'success', data);
    }

    static error(message: string, data?: any) {
        return new AbcResponse(false, message, data);
    }
}
