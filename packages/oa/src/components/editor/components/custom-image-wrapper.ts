import { mergeAttributes, Node } from '@tiptap/core';

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        imageWrapper: {
            /**
             * Add an imageWrapper
             */
            setImageWrapper: (options: { class?: string, width?: string, style?: string }) => ReturnType,
        }
    }
}
export interface ImageWrapperOptions {
    HTMLAttributes: Record<string, any>,
}
export const CustomImageWrapperExtension = Node.create<ImageWrapperOptions>({
    name: 'imageWrapper',

    addAttributes() {
        return {
            width: {
                default: null,
            },
            class: {
                default: null,
            },
            images: {
                default: null,
            },
            style: {
                default: null,
            },
        };
    },

    addOptions() {
        return {
            HTMLAttributes: {},
        };
    },

    content() {
        return 'image*';
    },

    group: 'block',

    parseHTML(): any {
        return [
            {
                tag: 'div',
                getAttrs: (dom: HTMLElement) => dom.className.includes('abc-editor__custom-image-wrapper'),
            },
        ];
    },

    renderHTML({ HTMLAttributes, node }): any {
        const justifyContentClass = node.childCount > 1 ? 'flex-around' : 'flex-center';
        // 用正则替换HTMLAttributes.class中的flex-around以及flex-center为justifyContentClass,如果两个都不存在则添加justifyContentClass
        if (HTMLAttributes.class?.includes('flex-around') || HTMLAttributes.class?.includes('flex-center')) {
            HTMLAttributes.class = HTMLAttributes.class?.replace(/flex-around|flex-center/g, justifyContentClass);
        } else {
            HTMLAttributes.class = `${HTMLAttributes.class} ${justifyContentClass}`;
        }
        if (!HTMLAttributes.class?.includes('abc-editor__custom-image-wrapper')) {
            HTMLAttributes.class = `${HTMLAttributes.class} abc-editor__custom-image-wrapper`;
        }
        HTMLAttributes.class += ` data-${Math.random()}`;

        const wrapperAttributes = mergeAttributes(
            HTMLAttributes,
            this.options.HTMLAttributes,
        );
        return ['div', wrapperAttributes, 0];
    },

    addCommands() {
        return {
            setImageWrapper: options => ({ commands }) => commands.insertContent({
                type: this.name,
                attrs: options,
            }),
        };
    },
});
