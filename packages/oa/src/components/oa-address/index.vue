<script lang="ts" setup>
import { computed, inject, reactive, Ref, watch } from 'vue';
import { clone } from '@/utils/utils';

const areaTree = inject('areaTree') as Ref<any>;
const props = defineProps(
    {
        modelValue: {
            type: Object,
            required: true,
        },
        placeholder: {
            type: String,
            default: '请选择',
        },
        size: {
            type: String,
            default: 'default',
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        showDistrict: {
            type: Boolean,
            default: false,
        },
        multiple: {
            type: Boolean,
            default: true,
        },
    },
);

const emits = defineEmits([
    'update:modelValue',
    'change',
]);

const currentValue = computed({
    get: () => clone(props.modelValue),
    set: (v) => {
        emits('update:modelValue', v);
    },
});

let cascaderProps = reactive({
    options: <any[]>[],
    props: {
        multiple: props.multiple,
    },
});
watch(() => areaTree.value, (value: any) => {
    if (!value || value.length === 0) {
        return;
    }
    cascaderProps.options = transAddressData();
}, {
    immediate: true,
    deep: true,
});
function transAddressData(): any[] {
    const address: any = areaTree.value;
    return address.map((province: any) => {
        const cities = province.children.map((city: any) => ({
            value: city.value,
            label: city.label,
            children: props.showDistrict ? city.children.map((district: any) => ({
                value: district.value,
                label: district.label,
            })) : [],
        }));
        return {
            value: province.value,
            label: province.label,
            children: cities,
        };
    });
}
function changeHandler(val: any) {
    emits('change', val);
}
</script>

<template>
    <el-cascader
        v-model="currentValue"
        :placeholder="placeholder"
        :size="size"
        :disabled="disabled"
        collapse-tags
        :props="cascaderProps.props"
        :options="cascaderProps.options"
        @change="changeHandler"
    >
    </el-cascader>
</template>
