<script lang="ts" setup>
import { computed, PropType, provide } from 'vue';

const props = defineProps({
    labelWidth: {
        type: String,
    },
    size: {
        type: String as PropType<'large' | 'normal' | 'small'>,
        default: '',
    },
    border: {
        type: Boolean,
        default: true,
    },
    verticalAlign: {
        type: String as PropType<'top'|'bottom'|'center'>,
        default: 'center',
    },
    // cell 高度
    cellHeight: {
        type: String,
        default: '',
    },
});

const computedClass = computed(() => {
    const classList = ['oa-cell-group-wrapper', `oa-cell-group-wrapper--${props.size}`];
    if (!props.border) {
        classList.push('oa-cell-group-wrapper--no-border');
    }
    return classList;
});

provide('label-width', props.labelWidth);
provide('size', props.size);
provide('verticalAlign', props.verticalAlign);
provide('cellHeight', props.cellHeight);

</script>
<template>
    <div :class="computedClass">
        <slot></slot>
    </div>
</template>

<style lang="scss">
    .oa-cell-group-wrapper {
        .oa-cell-wrapper {
            border-bottom: var(--oa-border);

            &:last-child {
                border-bottom: none;
            }
        }

        &--no-border {
            .oa-cell-wrapper {
                border-bottom: none;
            }
        }
    }
</style>
