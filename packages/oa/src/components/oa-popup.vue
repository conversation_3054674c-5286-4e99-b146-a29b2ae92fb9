<script setup lang="ts">
const props = defineProps({
    title: {
        type: String,
        default: '',
    },
});
</script>
<template>
    <van-popup class="oa-popup-wrapper" round safe-area-inset-bottom>
        <div class="oa-popup__title-wrapper">
            <slot name="title">{{ title }}</slot>
        </div>
        <div class="oa-popup__content-wrapper">
            <slot></slot>
        </div>
    </van-popup>
</template>

<style lang="scss">
    .oa-popup-wrapper {
        .oa-popup__title-wrapper {
            height: 50px;
            border-bottom: 1px solid var(--oa-gray-3);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--oa-font-size-16);
            font-weight: 500;
        }

        --van-popup-round-border-radius: var(--oa-border-radius-default);
        --van-popup-close-icon-size: var(--oa-font-size-16);
        --van-popup-close-icon-color: #000;
    }
</style>
