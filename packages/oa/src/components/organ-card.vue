<script setup lang="ts">
import {
    getClinicTypeDisplayName,
    getHisTypeDisplayName,
    HisTypeClinic,
    isChainSubClinic,
    NodeTypeClinic,
} from '@/utils/clinic';
import { concatAddress } from '@/utils/format';
import { computed } from 'vue';

const props = defineProps({
    organ: {
        type: Object,
        required: true,
    },
    showAdministrator: {
        type: Boolean,
        default: true,
    },
});

const displayOrgan = computed(() => {
    const organ = props.organ;
    return {
        name: organ.shortName || organ.name,
        address: concatAddress(organ),
        adminName: organ.adminName,
        adminMobile: organ.adminMobile,
        clinicTypeName: getClinicTypeDisplayName(<NodeTypeClinic>organ),
        hisTypeName: getHisTypeDisplayName(<HisTypeClinic>organ),
        chainName: isChainSubClinic(<any>organ) ? organ.chainName : '',
    };
});
</script>
<template>
    <div class="organ-card-wrapper">
        <div class="title">
            {{ displayOrgan.name }}
            <van-tag class="clinic-type" type="primary">{{ displayOrgan.clinicTypeName }}</van-tag>
            <van-tag class="his-type">{{ displayOrgan.hisTypeName }}</van-tag>
        </div>
        <div v-if="displayOrgan.chainName" class="chain-name">连锁：{{ displayOrgan.chainName }}</div>
        <div class="address">{{ displayOrgan.address }}</div>
        <div v-if="showAdministrator" class="administrator">
            管理员：{{ displayOrgan.adminName }}
            {{ displayOrgan.adminMobile }}
        </div>
    </div>
</template>

<style lang="scss">
.organ-card-wrapper {
    padding: 10px 16px;

    .title {
        display: flex;
        align-items: center;
        font-weight: bold;
        font-size: var(--oa-font-size-16);

        .clinic-type {
            margin-left: 4px;
        }

        .his-type {
            margin-left: 4px;
            background: var(--oa-success-color);
        }
    }

    .chain-name {
        font-weight: bold;
    }
}
</style>
