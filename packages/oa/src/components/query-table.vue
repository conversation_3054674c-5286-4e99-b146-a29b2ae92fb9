<script setup lang="ts">

import { PageParams } from '@/common/model/page-params';
import OaList from '@/components/oa-list.vue';
import { isFn } from '@/utils/utils';
import { Form } from '@/vendor/x-form/core/form';
import { computed, nextTick, reactive, ref, watch } from 'vue';
import { useThemeConfigStore } from '@/store/theme-config';
import { ReportActiveClinicsFunction, ReportNewClinicsFunction } from '@/vendor/cloud-function';
import { H5Form, PcForm } from '@/vendor/x-form';
import KvCard from '@/components/kv-card.vue';
import { OaCard } from '@abc-oa/components';
import OaCellGroup from '@/components/oa-cell-group.vue';
import OaCell from '@/components/oa-cell.vue';
import { CloudScriptExecutor } from '@/vendor/cloud-script';
import { useDebounceFn } from '@vueuse/core';

const themeConfig = useThemeConfigStore();

const props = defineProps({
    querySchema: {
        type: Object,
        required: true,
    },
    tableSchema: {
        type: Object,
        required: true,
    },
    useKvCard: {
        type: Boolean,
        default: true,
    },
});
const emit = defineEmits(['prepared', 'actionClick', 'formChange']);
const vmData = reactive({
    form: <Form><unknown>null,
    tableData: <any[]>[],
    page: 1,
    pageSize: 10,
    totalCount: 0,
    isTableLoading: false,
});

const tableColumns = computed(() => {
    if (props.tableSchema.columns && props.tableSchema.columns.length) {
        return props.tableSchema.columns;
    }
    let columns: any[] = [];
    if (vmData.tableData.length) {
        const tableData = vmData.tableData[0];
        columns = Object.keys(tableData).map(prop => ({
            prop,
            name: prop,
        }));
    }
    return columns;
});

// 数据源请求接口
async function queryTableData(formData: any) {
    const { tableSchema } = props;
    if (tableSchema.visiblePagination) {
        const offset = ((vmData.page - 1) * vmData.pageSize || 0).toString();
        const limit = vmData.pageSize.toString();
        formData = { ...formData, offset, limit };
    }
    if (isFn(tableSchema.dataSource)) {
        vmData.isTableLoading = true;
        vmData.form.actions.search && (vmData.form.actions.search.loading = true);
        let response: any = {};
        try {
            response = await tableSchema.dataSource(formData);
        } catch (e: any) {
            console.error(e.message || e);
        }
        vmData.tableData = response.rows || [];
        vmData.totalCount = response.total || 0;
        vmData.form.actions.search && (vmData.form.actions.search.loading = false);
        vmData.isTableLoading = false;
        return;
    }
    if (tableSchema.dataSource.type === 'cloud-function') {
        vmData.isTableLoading = true;

        if (tableSchema.dataSource.func === ReportActiveClinicsFunction.NAME) {
            const response = await ReportActiveClinicsFunction.exec(formData);
            if (!response.status) {
                return;
            }
            vmData.tableData = (response.data.rows || []).map((item: any) => ({
                name: item.shortName || item.clinicName,
                count: item.count,
            }));
            vmData.totalCount = response.data.total || 0;
        } else if (tableSchema.dataSource.func === ReportNewClinicsFunction.NAME) {
            const response = await ReportNewClinicsFunction.exec(formData);
            if (!response.status) {
                return;
            }
            vmData.tableData = (response.data.rows || []).map((item: any) => ({
                name: item.shortName || item.clinicName,
                ...item,
            }));
            vmData.totalCount = response.data.total || 0;
        }

        vmData.isTableLoading = false;
    } else if (tableSchema.dataSource.type === 'cloud-script') {
        // 脚本类型
        vmData.isTableLoading = true;
        const response = await CloudScriptExecutor.execute(tableSchema.dataSource.func, formData);
        if (!response.status) {
            return;
        }
        if (tableSchema.dataSource.result.type === 'list') {
            vmData.tableData = response.data.rows || [];
            vmData.totalCount = response.data.total || 0;
        }
        vmData.isTableLoading = false;
    }
    // 检查触发列表加载
    if (themeConfig.isMobile) {
        await nextTick();
        paginationListRef.value?.check();
    }
}

const debounceQuery = useDebounceFn(queryTableData, props.tableSchema.delay || 100);

const resetPageQuery = async (formData: any) => {
    if (vmData.page !== 1) {
        vmData.page = 1;
    } else {
        await debounceQuery(formData);
    }
};
defineExpose({ debounceQuery, resetPageQuery });
// 查询条件准备就绪
async function handleQueryPrepared(form: Form) {
    vmData.form = form;
    emit('prepared', form);
    if (props.tableSchema.requestWhenQueryPrepared) {
        await resetPageQuery(form.formData);
    }
}

/**
 * 查询条件改变触发
 * @param formData
 */
async function handleQueryChange({ formData, property }: any) {
    if (props.tableSchema.requestWhenQueryChanged) {
        await resetPageQuery(formData);
    }
    emit('formChange', { formData, property });
}

watch([() => vmData.page, () => vmData.pageSize], (newVal) => {
    debounceQuery(vmData.form.formData);
});
/**
 * 触发 action
 * @param action
 * @param formData
 */
async function handleAction({ action, formData }: any) {
    if (action.type === 'query') {
        await resetPageQuery(formData);
    } else {
        emit('actionClick', action, formData);
    }
}

const paginationListRef = ref();

async function paginationListProvider(pageParams: PageParams) {
    let filterList = vmData.tableData;
    if (props.tableSchema.search?.enable && pageParams.keyword) {
        filterList = filterList.filter(item => item[props.tableSchema.search.field]?.includes(pageParams.keyword));
    }
    const nextPageData = filterList.slice(pageParams.offset, pageParams.offset + pageParams.limit);
    return {
        rows: nextPageData,
        limit: pageParams.limit,
        offset: pageParams.offset,
        total: filterList.length,
    };
}

</script>
<template>
    <h5-form
        v-if="themeConfig.isMobile"
        :schema="querySchema"
        @action="handleAction"
        @prepared="handleQueryPrepared"
        @change="handleQueryChange"
    ></h5-form>
    <oa-card v-else>
        <pc-form
            :schema="querySchema"
            @action="handleAction"
            @prepared="handleQueryPrepared"
            @change="handleQueryChange"
        >
        </pc-form>
    </oa-card>

    <div v-if="themeConfig.isMobile" class="query-table__h5-wrapper">
        <van-skeleton :row="6" :loading="vmData.isTableLoading">
            <van-empty v-if="!vmData.tableData.length" description="暂无数据" image="search"></van-empty>
            <template v-else>
                <div v-if="tableSchema.visibleTotal" class="table-total-info">
                    <span class="total-count">总数: {{ vmData.totalCount }}</span>
                </div>
                <oa-list
                    ref="paginationListRef"
                    :immediate-check="true"
                    :data-provider="paginationListProvider"
                    :page-size="15"
                    :searchable="tableSchema.search?.enable"
                >
                    <template #default="{item}">
                        <kv-card
                            v-if="useKvCard"
                            class="query-table-item"
                            :info="item"
                            :columns="tableSchema.columns"
                        ></kv-card>
                        <oa-card v-else class="query-table-item">
                            <oa-cell-group label-width="70px" cell-height="40px">
                                <oa-cell v-for="column in tableSchema.columns" :label="column.name" class="query-table-item-customize">
                                    <span v-if="!column.slot">{{ item[column.prop] }}</span>
                                    <slot
                                        v-else
                                        :name="column.slot"
                                        :column="column"
                                        :row="item"
                                    ></slot>
                                </oa-cell>
                            </oa-cell-group>
                        </oa-card>
                    </template>
                </oa-list>
            </template>
        </van-skeleton>
    </div>
    <div v-else class="query-table__pc-wrapper">
        <div v-if="tableSchema.visibleTotal && vmData.tableData.length" class="table-total-info">
            <span class="total-count"> 总数: {{ vmData.totalCount }}  </span>
        </div>
        <el-table v-loading="vmData.isTableLoading" :data="vmData.tableData" v-bind="$attrs">
            <el-table-column
                v-for="column in tableColumns"
                :key="column.prop"
                :prop="column.prop"
                :label="column.name"
                :align="column.align"
                :min-width="column.minWidth"
                :width="column.width"
                :header-align="column.headerAlign"
                :fixed="column.fixed"
            >
                <template #default="scope">
                    <span v-if="!column.slot">{{ scope.row[column.prop] }}</span>
                    <slot
                        v-else
                        :name="column.slot"
                        :column="column"
                        :row="scope.row"
                    ></slot>
                </template>
            </el-table-column>
            <template v-if="tableSchema.visiblePagination" #append>
                <el-pagination
                    v-model:current-page="vmData.page"
                    v-model:page-size="vmData.pageSize"
                    class="deployment-pagination"
                    background
                    layout="prev, pager, next, sizes, total "
                    :total="vmData.totalCount"
                    @size-change="debounceQuery(vmData.form.formData)"
                >
                    <template #total>
                        <span>共 {{ vmData.totalCount }} 条</span>
                    </template>
                </el-pagination>
            </template>
        </el-table>
    </div>
</template>

<style lang="scss">
.query-table-item {
    margin-bottom: 4px;
}

.query-table__h5-wrapper {
    margin-top: 12px;
}

.query-table__pc-wrapper {
    margin-top: 12px;
}

.table-total-info {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;

    .total-count {
        text-align: center;
        padding: 0 16px;
        color: var(--oa-text-color);
        font-weight: bold;
        font-family: var(--oa-price-integer-font-family);
    }

    &::after,
    &::before {
        content: '';
        display: flex;
        flex: 1;
        justify-content: space-between;
        background: var(--el-border-color-base);
        height: 1px;
    }
}
</style>
