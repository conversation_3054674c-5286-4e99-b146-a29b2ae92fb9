<script lang="ts" setup>
import { computed, nextTick, PropType, ref, watch } from 'vue';
import { elPlusIcon, elSettingIcon } from './svg-icons';
import _ from 'lodash';
import { ElMessage } from 'element-plus';

interface OptionProps {
    label: string,
    value: string
}
const props = defineProps({
    modelValue: {
        type: [Array, String, Number],
        default: () => [],
    },
    options: {
        type: Array as PropType<any>,
        default: () => [],
    },
    optionProps: {
        type: Object as PropType<OptionProps>,
        default: () => ({
            label: 'label',
            value: 'value',
        }),
    },
    multiple: {
        type: Boolean,
        default: true,
    },
});
const emit = defineEmits(['update:modelValue', 'update:options', 'changeOption', 'deletedOption']);
const refSelect = ref<any>();
const sourceOptions = ref<any[]>([]);
const isEdit = ref(false);
const selectOptions = ref<any[]>([]);
const selectValue: any = computed({
    get() {
        return props.modelValue;
    },
    set(newVal) {
        emit('update:modelValue', newVal);
    },
});
watch(() => props.options, (newVal: any[]) => {
    sourceOptions.value = newVal;
    selectOptions.value = _.cloneDeep(newVal);
}, { immediate: true });
/**
 * 点击设置切换到操作下拉框模式
 */
const onClickSetting = () => {
    isEdit.value = true;
    VisibleChange(true);
};
const optionOperateType = ref('');
/**
 * 点击新增按钮
 */
const onclickAdd = async () => {
    const newOption = {
        [props.optionProps.label]: '',
        [props.optionProps.value]: new Date().getTime(),
        isEdit: true,
    };
    optionOperateType.value = 'add';
    selectOptions.value = [newOption, ...sourceOptions.value];
    await nextTick();
    const selectPopperWrapper = document.querySelector('.select-operate-wrapper .el-select-dropdown__wrap');
    if (selectPopperWrapper) {
        selectPopperWrapper.scrollTop = 0;
    }
};
/**
 * 点击修改按钮
 * @param item 修改的选项
 */
const toEdit = (item: any) => {
    optionOperateType.value = 'edit';
    item.sourceLabel = item[props.optionProps.label];
    item.isEdit = true;
};
/**
 * 点击删除按钮
 * @param item 删除的选项
 */
const toDelete = (item: any) => {
    emit('deletedOption', item);
};
/**
 * 点击确定按钮
 * @param item 确定的选项
 */
const toSubmit = (item: any) => {
    if (sourceOptions.value.find((option: any) => option[props.optionProps.label] === item[props.optionProps.label])) {
        ElMessage.error('当前选项已存在，请修改！');
        return false;
    }

    emit('changeOption', item, optionOperateType.value);
};
/**
 * 点击取消按钮
 * @param item 取消的选项
 */
const toCancel = (item: any) => {
    item.isEdit = false;
    item[props.optionProps.label] = item.sourceLabel;
    if (optionOperateType.value === 'add') {
        selectOptions.value = sourceOptions.value;
    }
    optionOperateType.value = '';
};
/**
 * 添加下拉栏操作按钮
 * @param visible
 * @constructor
 */
const VisibleChange = (visible: boolean) => {
    if (visible) {
        let bkpRef = refSelect.value;
        let popper = bkpRef.popperPaneRef;
        if (!Array.from(popper.children).some((v: any) => v.className.includes('select-operate-bottom'))) {
            const el: any = document.createElement('div');
            if (isEdit.value) {
                el.className = 'select-operate-bottom select-operate-bottom-add';
                el.innerHTML = `${elPlusIcon}<span>新增</span>`;
            } else {
                el.className = 'select-operate-bottom select-operate-bottom-setting';
                el.innerHTML = `${elSettingIcon}<span>设置</span>`;
            }
            popper.appendChild(el);
            el.onclick = () => {
                isEdit.value ? onclickAdd() : onClickSetting();
            };
        } else {
            Array.from(popper.children).forEach((v: any) => {
                v.className.includes('select-operate-bottom') && popper.removeChild(v);
            });
            VisibleChange(true);
        }
    } else {
        isEdit.value = false;
        selectOptions.value = sourceOptions.value;
        optionOperateType.value = '';
    }
};
const getChecked = (item: any): boolean => selectValue.value?.includes(item[props.optionProps.value]);
</script>
<template>
    <el-select
        ref="refSelect"
        v-model="selectValue"
        :multiple="multiple"
        collapse-tags
        collapse-tags-tooltip
        :fit-input-width="false"
        :value-key="props.optionProps.value"
        popper-class="select-operate-wrapper"
        @visible-change="VisibleChange"
    >
        <el-option
            v-for="item in selectOptions"
            :key="item[props.optionProps.value]"
            :label="item[props.optionProps.label]"
            :value="item[props.optionProps.value]"
        >
            <span v-if="!isEdit" class="custom-select-label-wrapper" @click="item.checked = !item.checked">
                <el-checkbox
                    v-if="multiple"
                    :model-value="getChecked(item)"
                    class="custom-select-option-checked"
                    @click="item.checked = !item.checked"
                ></el-checkbox>
                <span>{{ item[props.optionProps.label] }}</span>
            </span>
            <div v-else-if="!item.isEdit" class="custom-select-option">
                <span class="custom-select-label-wrapper" @click="item.checked = !item.checked">
                    <el-checkbox
                        v-if="multiple"
                        v-model="item.checked"
                        class="custom-select-option-checked"
                        @click="item.checked = !item.checked"
                    ></el-checkbox>
                    <span>{{ item[props.optionProps.label] }}</span>
                </span>
                <el-space class="select-operate-btn hover-show">
                    <span class="select-operate-btn-edit" @click.stop.prevent="toEdit(item)">修改</span>
                    <span class="select-operate-btn-deleted" @click.stop.prevent="toDelete(item)">删除</span>
                </el-space>
            </div>
            <div v-else class="custom-select-option">
                <el-input v-model="item[props.optionProps.label]" class="custom-select-option-input" @click.stop.prevent></el-input>
                <el-space class="select-operate-btn">
                    <span class="select-operate-btn-submit" @click.stop.prevent="toSubmit(item)">确定</span>
                    <span class="select-operate-btn-cancel" @click.stop.prevent="toCancel(item)">取消</span>
                </el-space>
            </div>
        </el-option>
    </el-select>
</template>
<style lang="scss">
.select-operate-wrapper {
    .el-select-dropdown.is-multiple .el-select-dropdown__item.selected::after {
        display: none;
    }

    .custom-select-option:hover .hover-show {
        display: inline-flex;
    }

    .custom-select-label-wrapper {
        display: flex;
        align-items: center;
    }

    .custom-select-option {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 100%;

        .hover-show {
            display: none;
        }

        &-input {
            margin-right: 8px;
        }

        &-checked {
            margin-right: 4px;
        }

        .select-operate-btn {
            &-submit,
            &-edit {
                color: #409eff;
            }

            &-deleted {
                color: #f56c6c;
            }
        }
    }

    .el-select-dropdown__item {
        padding-right: 4px;
    }
}

.select-operate-bottom {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    height: 42px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    cursor: pointer;
    padding: 4px 8px 0 0;
    border-top: 1px solid rgb(240 242 245);
}

.select-operate-bottom-add {
    justify-content: flex-start;
    padding: 4px 0 0 8px;
    color: #409eff;
}

</style>