import { useMenuStore } from '@/store/menu';
import { computed } from 'vue';
import { RouteRecordRaw, useRoute, useRouter } from 'vue-router';

export default function useNavigator() {
    const router = useRouter();
    const route = useRoute();
    function toMenu(menu: RouteRecordRaw) {
        router.push({
            name: menu.name,
        });
    }

    const menuStore = useMenuStore();

    function findCurrentMenu(menus: Array<RouteRecordRaw>): RouteRecordRaw | null {
        for (let menu of menus) {
            if (menu.name === route.name) {
                return menu;
            }
            if (menu.children) {
                let m = findCurrentMenu(menu.children);
                if (m) {
                    return m;
                }
            }
        }
        return null;
    }

    const subMenus = computed(() => (
        findCurrentMenu(menuStore.menus)?.children ?? []
    ).filter(menu => !menu.meta?.hidden));
    const isEntryPage = computed(() => route.meta.isEntry);
    return {
        isEntryPage,
        subMenus,
        toMenu,
    };
}
