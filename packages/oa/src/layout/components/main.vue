<script lang="ts" setup>
import { computed } from 'vue';
import { useThemeConfigStore } from '@/store/theme-config';

const themeConfig = useThemeConfigStore();
const layoutPageClass = computed(() => {
    const classes = ['layout-tabbar-page-wrapper'];
    if (themeConfig.isMobile) return classes;
    if (themeConfig.isCollapse) {
        classes.push('layout-tabbar-page__sidebar--close');
    } else {
        classes.push('layout-tabbar-page__sidebar--open');
    }
    return classes;
});
</script>
<template>
    <div :class="layoutPageClass">
        <router-view></router-view>
    </div>
</template>

<style scoped>

</style>
