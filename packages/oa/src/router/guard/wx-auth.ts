import { useShareUserStore } from '@/share-store/user';
import { isWx } from '@/utils/ua';
import { Router } from 'vue-router';

export default function (router: Router) {
    return router.beforeEach(async (to, from, next) => {
        const userStore = useShareUserStore();

        if (to.name === '@share/auth-callback') {
            return userStore.handleWxAuthCallback(to, from, next);
        }

        // 不需要鉴权，或者不在微信内
        if (!isWx || to.meta.skipAuth) {
            next();
            return;
        }
        const isLogin = await userStore.checkLogin();
        if (!isLogin) {
            // 没有 登录
            userStore.login(to.fullPath);
        } else {
            // 存在 token
            next();
        }
    });
}
