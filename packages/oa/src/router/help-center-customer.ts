import HelpCenterLayout from '@/layout/help-center-customer/layout.vue';
import QuestionList from '@/views/help-center-customer/question-list/index.vue';
import QuestionDetail from '@/views/help-center-customer/question-list/components/question-detail.vue';
import BlankPage from '@/views/help-center-customer/blank/index.vue';
import { createRouter, createWebHistory } from 'vue-router';

const staticRoutes = [
    {
        path: '/',
        name: '@help-center-customer',
        component: HelpCenterLayout,
        redirect: {
            name: '@blank',
        },
        children: [
            {
                path: '/blank',
                name: '@blank',
                component: BlankPage,
            },
            {
                path: 'question-list/:id',
                name: '@question-list',
                component: QuestionList,
            },
            {
                path: 'question-detail/:id',
                name: '@question-detail',
                component: QuestionDetail,
            },
        ],
    },
];
const router = createRouter({
    history: createWebHistory('/help-center-customer'),
    routes: [...staticRoutes],
});
export default router;
