import wxAuth from '@/router/guard/wx-auth';
import { createRouter, createWebHistory } from 'vue-router';
import Layout from '@/layout/layout-share.vue';
import OrderDetail from '@/views/order/detail/share.vue';
import OrderMobileRenewal from '@/views/order/mobile-renewal/index.vue';
import OrderMobileRenewalDetail from '@/views/order/mobile-renewal/components/share.vue';
import AirPharmacyOrderDetail from '@/views/air-pharmacy/detail/share.vue';
import Lottery from '@/views/lottery/lottery.vue';

// 静态路由
const staticRoutes = [
    {
        path: '/share',
        name: 'layout',
        component: Layout,
        meta: {
            name: '首页',
        },
        children: [
            {
                path: 'order-detail/:id',
                name: '@share/order-detail',
                component: OrderDetail,
                props: true,
                meta: {
                    name: '订单',
                },
                beforeEnter() {
                    window.document.title = 'ABC产品订单';
                },
            },
            {
                path: 'air-pharmacy/order-detail/:id',
                name: '@share/air-pharmacy/order-detail',
                component: AirPharmacyOrderDetail,
                props: true,
                meta: {
                    name: '订单',
                },
                beforeEnter() {
                    window.document.title = 'ABC空中药房订单';
                },
            },
            {
                path: 'lottery',
                name: '@share/lottery',
                component: Lottery,
                meta: {
                    name: '年会抽奖',
                    skipAuth: true,
                },
            },
            {
                path: 'auth-callback',
                name: '@share/auth-callback',
                meta: {
                    skipAuth: true,
                },
            },
            {
                path: 'order-mobile-renewal',
                name: '@share/order-mobile-renewal',
                component: OrderMobileRenewal,
                meta: {
                    name: '续费',
                },
                beforeEnter() {
                    window.document.title = 'ABC数字医疗云续费';
                },
            },
            {
                path: 'order-mobile-renewal-detail/:id',
                name: '@share/order-mobile-renewal-detail',
                component: OrderMobileRenewalDetail,
                props: true,
                meta: {
                    name: '订单',
                },
                beforeEnter() {
                    window.document.title = 'ABC产品订单';
                },
            },
        ],
    },
];

const router = createRouter({
    history: createWebHistory(),
    // @ts-ignore
    routes: [...staticRoutes],
});

// 前置处理
wxAuth(router);

export default router;
