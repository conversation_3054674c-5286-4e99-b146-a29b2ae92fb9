import { CorpAPI } from '@/api/corp-api';
import UserAP<PERSON> from '@/api/user';
import { AbcResponse } from '@/common/AbcResponse';
import { IUser } from '@/common/model/user';
import router from '@/router';
import { defineStore } from 'pinia';

export const useUserStore = defineStore('user', {
    state: () => ({
        userInfo: <IUser>{},
    }),

    getters: {
        // tags || departments 决定权限
        roles: state => (state.userInfo.tags || []).map(tag => tag.name).concat((state.userInfo.departments || []).map(department => department.name)),
    },

    actions: {
        async fetchUserInfo() {
            try {
                this.userInfo = await CorpAPI.getCorpMeUsingGET();
                return this.userInfo;
            } catch (e) {
            }
            return null;
        },

        async checkLogin() {
            // 存在 userInfo，则为登录
            if (this.userInfo.id) {
                return true;
            }

            const userInfo = await this.fetchUserInfo();
            return userInfo && userInfo.id;
        },

        async loginByCode(code: string) {
            try {
                return await UserAPI.loginByCode(code);
            } catch (e: any) {
                return AbcResponse.error('登录失败', e.message || e);
            }
        },

        async logout() {
            await UserAPI.logout();
            await router.replace({
                name: '@login',
            });
        },
    },
});
