* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    outline: none !important;
}

html,
body,
#app {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    font-family: var(--oa-base-font-family);
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -webkit-tap-highlight-color: transparent;
    background-color: var(--oa-background-color);
    font-size: var(--oa-font-size-14);
    overflow: hidden;
    position: relative;
    color: var(--oa-text-color);
}

::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
    color: transparent;
}

[v-clock] {
    display: none;
}

// 布局样式
.layout-h5-container {
    width: 100%;
    height: 100%;
    overflow: auto;

    .layout-page-wrapper {
        padding: var(--oa-page-padding);
    }

    .layout-tabbar-page-wrapper {
        padding: var(--oa-page-padding) var(--oa-page-padding) 0;

        .layout-tabbar-page__content {
            height: calc(100vh - 58px);
            overflow: auto;
        }

        .layout-tabbar-page__tabbar {
            padding-bottom: 8px;
        }
    }
}

.layout-pc-container {
    width: 100%;
    height: 100%;

    .layout-aside {
        background-color: var(--el-menu-bg-color);
        height: 100vh;
        transition: width .3s;

        &.layout-aside__open {
            width: 220px;
        }

        &.layout-aside__close {
            width: 64px;
        }

        // 移动端适配
        &.layout-aside-mobile {
            position: fixed;
            top: 0;
            width: 220px;
            z-index: 99999;
            left: -220px;
            transition: all .3s ease-out;

            &.layout-aside__open {
                left: 0;
            }

            &.layout-aside__close {
                left: -220px;
            }
        }

        .layout-aside__menu {
            height: 100%;

            &:not(.el-menu--collapse) {
                width: 220px;
                min-height: 400px;
            }
        }
    }

    .layout-header-wrapper {
        height: 50px;

        .layout-header__menu-toggle {
            margin-right: 12px;
            font-size: 18px;
            cursor: pointer;
        }

        .layout-header__right {
            margin-left: auto;

            .layout-header__user-info {
                display: flex;
                align-items: center;
                margin-left: auto;

                .user-name {
                    margin-left: 6px;
                }
            }
        }
    }

    .layout-tabbar-page-wrapper {
        &.layout-tabbar-page__sidebar--open {
            .layout-tabbar-page__tabbar,
            .h5-form__action-wrapper,
            .pc-form__action-wrapper {
                transition: all .3s ease-out;
                width: calc(100% - 220px);
                left: 220px;
            }
        }

        &.layout-tabbar-page__sidebar--close {
            .layout-tabbar-page__tabbar,
            .h5-form__action-wrapper,
            .pc-form__action-wrapper {
                transition: all .3s ease-out;
                width: calc(100% - 64px);
                left: 64px;
            }
        }
    }
}

.jv-container .jv-code {
    padding: 0 !important;
}
