@import "./media/var";
// 侧边栏
.el-aside {
    //transition: width .3s;
}

.el-overlay-dialog {
    display: flex;
    align-items: center;

    .el-dialog {
        margin: 0 auto;
    }
}

// 顶部菜单
.el-header {
    height: 48px;
}

// 菜单
.el-menu {
    --el-menu-border-color: transparent;
}

// 卡片
.el-card {
    --el-card-padding: var(--oa-padding-12);

    border: none !important;
}

.el-avatar {
    --el-avatar-text-font-size: 20px;
}

.van-cell {
    --van-cell-horizontal-padding: var(--oa-padding-12);
}

.van-cell__value {
    flex: 2 !important;
}

.el-message {
    min-width: 100px;
}

/* 页面宽度小于576px
------------------------------- */
@media screen and (max-width: $xs) {
    .el-header {
        --el-header-height: 48px;
    }

    .el-main {
        --el-main-padding: var(--oa-padding-12);
    }
}
