declare namespace  AbcAPI {
    
    type ApprovalTicketClinicEditionOrderCalculateRsp = {    
        
        accountDeductionFee:number    
        
        accountOrderFeeInfo?:OrderFeeInfo    
        
        adjustmentPrice:number    
        
        availableEditionIds?:Array<any>    
        
        availablePromotions?:Array<Promotion>    
        //合作诊所账号数
        basicCooperateClinicCount:number    
        
        beginDate:string    
        
        clinic?:BindOrganView    
        //增购药诊互通账号费用详情
        cooperationAccountOrderFeeInfo?:OrderFeeInfo    
        
        deductionFee:number    
        
        discountPrice:number    
        
        editionDeductionFee:number    
        
        editionId:string    
        
        editionOrderFeeInfo?:OrderFeeInfo    
        //版本总费用
        editionOrderTotalPrice:number    
        
        endDate:string    
        
        giftDays:number    
        
        id:string    
        
        maxAdjustmentFee:number    
        
        maxBeginDate:string    
        
        maxEndDate:string    
        
        minReceivableFee:number    
        //最低升级年数：仅升级返回该字段
        minUpgradeYears:number    
        
        prePaidFee:number    
        
        receivableFee:number    
        
        sopRenewRelateEditionOrders?:Array<QWClinicEditionOrderAbstract>    
        //实施项抵扣金额
        supportDeductionFee:number    
        //实施项目费用
        supportItems?:Array<SupportItemCalculateRspSupportItem>    
        
        totalPrice:number    
        
        type:number    
        
        unit:string    
        
        unitCount:number    
        
        unitPrice:number    
    }
}
