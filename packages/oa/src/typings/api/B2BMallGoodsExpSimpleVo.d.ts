declare namespace  AbcAPI {
    
    type B2BMallGoodsExpSimpleVo = {    
        
        approvedCode:string    
        
        barCode:string    
        
        cadn:string    
        
        categoryId:string    
        
        categoryName:string    
        
        goodsId:string    
        
        id:string    
        
        manufacturer:string    
        
        maxSalesCount:number    
        
        minSalesCount:number    
        
        newUnitPrice:number    
        
        oldUnitPrice:number    
        
        salesUnit:string    
        
        salesUnitPrice:number    
        
        shortId:string    
        
        skuGoodsId:string    
        
        skuGoodsName:string    
        
        skuStockCount:number    
        
        specification:string    
        
        spuGoodsId:string    
        
        status:number    
        
        tradeName:string    
        
        transRatio:number    
        
        vendorName:string    
    }
}
