declare namespace  AbcAPI {
    
    type ClinicCustomerEditionOrderView = {
        
        abcCreator?:AbcEmployeeView    
        
        adjustmentPrice:number    
        
        attachments?:Array<ClinicEditionAttachment>    
        
        beginDate:string    
        
        bindChain?:ClinicCustomerBindOrganView
        
        bindClinic?:ClinicCustomerBindOrganView
        
        created:string    
        
        customer?:ClinicCustomerEmployeeView
        
        deductionFee:number    
        
        discountPrice:number    
        
        editionId:string    
        
        editionName:string    
        
        endDate:string    
        
        expiredTime:string    
        
        giftDays:number    
        
        id:string    
        
        minReceivableFee:number    
        
        organType:number    
        
        paidFee:number    
        
        paidTime:string    
        
        payMode:number    
        
        payOrderId:string    
        
        promotions?:Array<Promotion>    
        
        receivableFee:number    
        
        remarks:string    
        
        seller?:AbcEmployeeView    
        
        source:number    
        
        status:number    
        
        totalPrice:number    
        
        type:number    
        
        unit:string    
        
        unitCount:number    
        
        unitPrice:number    
    }
}
