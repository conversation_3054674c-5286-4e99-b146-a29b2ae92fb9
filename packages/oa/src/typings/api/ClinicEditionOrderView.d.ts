declare namespace  AbcAPI {
    
    type ClinicEditionOrderView = {    
        
        abcCreator?:AbcEmployee    
        
        adjustmentPrice:number    
        
        attachments?:Array<Attachment>    
        
        beginDate:string    
        
        bindChain?:BindOrganView    
        
        bindClinic?:BindOrganView    
        
        created:string    
        
        customer?:EmployeeA    
        
        deductionFee:number    
        
        discountPrice:number    
        
        editionId:string    
        
        editionName:string    
        
        endDate:string    
        
        expiredTime:string    
        
        giftDays:number    
        
        id:string    
        
        organType:number    
        
        paidFee:number    
        
        paidTime:string    
        
        payMode:number    
        
        payOrderId:string    
        
        promotions?:Array<Promotion>    
        
        receivableFee:number    
        
        remarks:string    
        
        seller?:AbcEmployee    
        
        source:number    
        
        status:number    
        
        totalPrice:number    
        
        type:number    
        
        unit:string    
        
        unitCount:number    
        
        unitPrice:number    
    }
}
