declare namespace  AbcAPI {
    
    /**
     * 诊所开票申请摘要视图
     */
    type ClinicInvoiceApplyBriefView = {    
        //创建时间
        created:string    
        //申请单ID
        id:string    
        //发票代码
        invoiceCodeNum:string    
        //开票信息
        invoiceConfigSnap?:ClinicInvoiceConfigView    
        //发票号码
        invoiceNo:string    
        //开票总金额
        invoiceTotalPrice:number    
        //发票类型 1：普通发票，2：增值税专用发票
        invoiceType:number    
        //发票链接
        invoiceUrl:string    
        //原因
        reason:string    
        //状态 0:待开票 10:开票中 20:已完成 30:开票失败
        status:number    
    }
}
