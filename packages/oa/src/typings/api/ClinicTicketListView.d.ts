declare namespace  AbcAPI {
    
    type ClinicTicketListView = {    
        //连锁id
        chainId:string    
        //外部联系人ID
        clientId:string    
        //门店id
        clinicId:string    
        //反馈门店名称
        clinicName:string    
        //工单号
        code:string    
        //会话ID
        conversationId:string    
        //创建人
        createByName:string    
        //创建时间
        created:string    
        //创建人名
        createdByName:string    
        //处理人id
        dealerId:string    
        //处理人名字
        dealerName:string    
        //问题描述
        description:string    
        //反馈客户id
        employeeId:string    
        //反馈客户名字
        employeeName:string    
        //反馈时间
        feedbackTime:string    
        //跟进人id
        followerId:string    
        //跟进人名字
        followerName:string    
        //来源-0：1v1客服，1：外部群，2：客户于系统提单，3：内部人员提单，90其他{@linkClinicTicket.FromWay}
        fromWay:number    
        //是否存在评论
        haveReply:number    
        //产品
        hisType:number    
        //工单id
        id:string    
        //是否是草稿 0否 1是
        isDraft:number    
        //是否紧急
        isEmergency:number    
        //结果
        reason:string    
        //备注
        remark:string    
        //群聊名称
        roomName:string    
        //客服ID
        servicerId:string    
        //状态: 0处理中 5已采纳 10已完成 11已拒绝 {@linkClinicTicket.Status}
        status:number    
        //标签
        tags?:Array<ClinicTicketTagView>    
        //tapd 处理人的id
        tapdDealerId:string    
        //tapd单链接
        tapdLick:string    
        //标题
        title:string    
        //工单类型 0bug 1需求 2实施 4支持(已废弃) 5商务 6专网
        type:number    
    }
}
