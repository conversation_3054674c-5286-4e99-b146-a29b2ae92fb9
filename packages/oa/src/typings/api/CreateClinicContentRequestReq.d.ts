declare namespace  AbcAPI {
    
    type CreateClinicContentRequestReq = {    
        
        addressRegion?:AddressRegion    
        //折扣金额
        adjustmentPrice?:number    
        
        admin?:Employee    
        //生效开始时间 (yyyy-MM-dd)
        beginDate?:string    
        //连锁id
        chainId?:string    
        //连锁名称
        chainName?:string    
        //crm 机构id
        crmOrganId?:string    
        //crm 机构名称
        crmOrganName?:string    
        //购买店版本id (基础版 10, 专业版 20, 旗舰版 30, 大客户版 40)
        editionId?:string    
        //生效结束时间 (yyyy-MM-dd)
        endDate?:string    
        //类型 (诊所管家 0, 口腔管家 1)
        hisType?:number    
        
        name?:string    
        //支付对象 10楚天云 0ABC
        payAccount?:number    
        //应收费用
        receivableFee?:number    
        //收款账户id (字节流 1, 字节星球 2)
        receiveAccountId?:number    
        
        referrerCode?:string    
        
        regionId?:string    
        //备注
        remark?:string    
        //成单分享
        share?:string    
        
        supportItems?:Array<SupportItemCalculateReqSupportItem>    
        //合计费用
        totalPrice?:number    
        //单位数量 (N年)
        unitCount?:number    
    }
}
