declare namespace  AbcAPI {
    
    type CreateStockOutOrderReq = {    
        //出库单备注
        comment?:string    
        //出库门店Id
        fromOrganId?:string    
        //退货出库退的入库单
        inOrderId?:number    
        //修改出库单的时候有用
        lastModifiedDate?:string    
        //出库的详细信息
        list?:Array<CreateStockOutOrderItemReq>    
        //出库哪个药房的出库【只入库有用】
        pharmacyNo?:number    
        
        reqMd5Key?:string    
        //出库领用门店 部门Id 领料出库有用
        toOrganId?:string    
        //领料出库的领用人 领料出库有用
        toUserId?:string    
        //出库类型 1领料出库 2报损出库 3 退货出库 ,4外部api领料出库
        type?:number    
    }
}
