declare namespace  AbcAPI {
    
    type CreateTrialClinicContentRequest = {    
        
        addressRegion?:AddressRegion    
        
        admin?:Employee    
        //生效开始时间 (yyyy-MM-dd)
        beginDate:string    
        //连锁id
        chainId:string    
        //连锁名称
        chainName:string    
        //crm 机构id
        crmOrganId:string    
        //crm 机构名称，前端可以不传
        crmOrganName:string    
        //购买店版本id (基础版 10, 专业版 20, 旗舰版 30, 大客户版 40)
        editionId:string    
        //生效结束时间 (yyyy-MM-dd)
        endDate:string    
        //类型 (诊所管家 0, 口腔管家 1, 眼视光管家2)
        hisType:number    
        
        name:string    
        //支付对象 10楚天云 0ABC
        payAccount:number    
        
        regionId:string    
        //备注
        remark:string    
        
        source:string    
    }
}
