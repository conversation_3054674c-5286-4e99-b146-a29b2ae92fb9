declare namespace  AbcAPI {
    
    type CreatedCategoryDto = {    
        //分类名称
        category?:string
        //父级分类id
        parentCategoryId?:number
        //his系统分类moduleId
        categoryModuleId?:number
        //分类排序
        sort?:number
        //是否隐藏（0：不隐藏，1：隐藏）
        isHide?:number
        //是否是叶子结点（1：是，0：不是）
        isLeaf?:number
        name:string
        //中西医疾病枚举。0 西医，1 中医
        type?:number
    }
}
