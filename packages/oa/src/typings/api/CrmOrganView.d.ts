declare namespace  AbcAPI {
    
    type CrmOrganView = {    
        //门店ID
        abcOrganId:string    
        //完成激活人员Id
        activeEmployeeId:number    
        //完成激活人员姓名
        activeEmployeeName:string    
        //活跃分
        activePredictionScore:number    
        //完成激活时间
        activeTime:string    
        //更多联系电话
        additionalContactPhone:string    
        //市行政编码
        addressCityId:string    
        //市名称
        addressCityName:string    
        //企业公司详细地址
        addressDetail:string    
        //区行政编码
        addressDistrictId:string    
        //区名称
        addressDistrictName:string    
        //省份行政编码
        addressProvinceId:string    
        //省份名称
        addressProvinceName:string    
        //附件
        attachment?:Array<Attachment>    
        //门店活跃度信息
        clinicActiveActionStatFlat?:ClinicActiveStatFlatView    
        //云检首次签约日期
        cloudExamFirstBindTime:string    
        //线索信息
        clue?:CrmClueView    
        //线索ID
        clueId:string    
        //联系电话
        contactPhone:string    
        //法人
        corporation:string    
        //录入时间
        created:string    
        //创建人
        createdBy:number    
        //客服信息
        csInfo?:CsInfoView    
        //现有系统id
        curSystemId:number    
        //现有系统名字
        curSystemName:string    
        //售后SOP客户生命周期 100待新装，200已新装 300待激活 400已激活 500待促活 600活跃中
        customerLifeCycle:number    
        //距离：单位是米
        distance:string    
        //成立日期
        establishmentDate:string    
        //首购订单审批单
        firstPurchaseApprovalTicket?:TicketInfoView    
        //首购日期 (yyyyMMdd)
        firstPurchaseDate:string    
        //门店新购实施工单信息
        firstPurchaseSupportTicket?:SupportTicketStat    
        //机构id
        id:string    
        //是否是开城城市 0:否 1:是
        isMainOfflineSaleCity:number    
        //是否是新店
        isNew:number    
        //是否开通医保
        isOpenShebao:number    
        
        isProtect:number    
        //是否是标杆客户
        isSightcing:number    
        //修改人
        lastModifiedBy:number    
        //纬度
        latitude:string    
        //证照资质类型 0:医疗机构 1:非医疗机构 2:个人
        licenseType:number    
        //经度
        longitude:string    
        //机构名称
        name:string    
        //新装实施人员Id
        newImplementedEmployeeId:number    
        //新装实施人员姓名
        newImplementedEmployeeName:string    
        //新装实施时间
        newImplementedTime:string    
        //无工商信息认证审批单
        noBusinessApprovalTicket?:TicketInfoView    
        //责任人id
        ownerId:number    
        //责任人名称
        ownerName:string    
        //责任人类型：0销售人员，1组织部门
        ownerType:number    
        //一级机构类型
        primaryType:number    
        //一级机构类型
        primaryTypeDesc:string    
        
        protectEndDate:string    
        //注册资本
        registeredCapital:number    
        //续费意向 99:待联系 0:未联系上 1:续费待回款 2:考虑中 3:不愿续费 4:关店停业
        renewIntention:number    
        //续费订单审批
        renewalApprovalTicket?:TicketInfoView    
        //0未接触， 10销售中， 20已成单
        saleStatus:number    
        //二级机构类型
        secondaryType:number    
        //二级机构类型
        secondaryTypeDesc:string    
        //验证状态：0未验证1已验证
        validStatus:number    
        //验证类型：0工商信息认证 1无工商认证 2个人客户
        validType:number    
    }
}
