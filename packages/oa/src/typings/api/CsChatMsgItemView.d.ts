declare namespace  AbcAPI {
    
    type CsChatMsgItemView = {    
        //会话id
        chatId:number    
        //会话名称
        chatName:string    
        //id
        id:number    
        //如果是非文本类型，该字段为不同类型的json格式
        msgDetail:string    
        //媒体数据。如果是非文本类型，该字段不为空
        msgMedia?:MsgMedia    
        //消息发送时间戳
        msgTime:string    
        //消息类型，如text/image/voice/video等
        msgType:string    
        //标识撤回的原消息的企业微信msgid
        preMsgId:number    
        //消息发送方
        sender?:CsChatMemberView    
        //消息序号
        seq:number    
    }
}
