declare namespace  AbcAPI {
    
    type DataTransferRecord = {    
        
        id:number    
        //迁移类型（0：表格导入，1：竞品导入，2：ABC门店导入, 3：数据清理）
        type:number    
        //绑定的连锁id
        chainId:string    
        //绑定的门店id
        clinicId:string    
        //迁移数据详情ID
        transferDataId:number    
        //任务状态（0：任务未创建，1：创建成功，2：任务处理中，3：任务已完成，4：任务已拒绝，5：任务已取消）
        status:number    
        //tapd单ID
        tapdId:string    
        //任务执行人
        executor:string    
        //拒绝原因
        rejectReason:string    
        //是否删除
        isDeleted:number    
        //创建人
        createdBy:string    
        
        created:any    
        //最后修改人
        lastModifiedBy:string    
        
        lastModified:any    
    }
}
