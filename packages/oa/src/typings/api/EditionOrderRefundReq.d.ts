declare namespace AbcAPI {
    interface EditionOrderRefundReq {
        /**
         * 门店ID
         */
        clinicId: string;

        /**
         * 订单号
         */
        orderId: string;

        /**
         * 退款类型
         * 1 首购
         * 2 续费（续费、升级、降级、sop）
         * 3 优惠劵差价
         * 4 其他原因：如销售失误，多输入了金额
         */
        refundType: number;

        /**
         * 是否全额退款
         * 0 否
         * 1 是
         */
        isFullRefund: number;

        /**
         * 退款金额
         */
        refundFee: number;

        /**
         * 退款年限，退款xx年
         * 单位：年
         */
        refundYear?: number;

        /**
         * refundType 为3的时候 必填
         * 使用的优惠劵ID集合
         */
        couponIds?: string[];
    }

    /**
     * 退款类型枚举
     */
    const enum RefundType {
        /** 首购 */
        FIRST_PURCHASE = 1,
        /** 续费（续费、升级、降级、sop） */
        RENEW = 2,
        /** 优惠劵差价 */
        COUPON_DIFF = 3,
        /** 其他原因：如销售失误，多输入了金额 */
        OTHER = 4
    }
}
