declare namespace  AbcAPI {
    
    type GoodsBatchInfo = {    
        
        availablePackageCount?:number    
        
        availablePieceCount?:number    
        
        batchId?:number    
        
        batchNo?:string    
        
        clinicId?:string    
        
        dispGoodsCount?:string    
        
        dispLockingGoodsCount?:string    
        
        dispOutGoodsCount?:string    
        
        dispProhibitGoodsCount?:string    
        
        dispStockGoodsCount?:string    
        
        expiryDate?:string    
        
        lockingPackageCount?:number    
        
        lockingPieceCount?:number    
        
        packageCostPrice?:number    
        
        packageCount?:number    
        
        pharmacyNo?:number    
        
        pharmacyType?:number    
        
        pieceCount?:number    
        
        prohibitPackageCount?:number    
        
        prohibitPieceCount?:number    
        
        stockList?:Array<GoodsStockInfo>    
        
        stockPackageCount?:number    
        
        stockPieceCount?:number    
        
        supplierId?:string    
        
        supplierName?:string    
    }
}
