declare namespace  AbcAPI {
    
    type GoodsModifyPriceItem = {    
        //修改进价的GoodsId
        goodsId?:string    
        //回射KeyId，返回修改后售价
        keyId?:string    
        //最近供应商名字 改价时的快照
        lastSupplierName?:string    
        //成本价
        packageCostPrice?:number    
        //修改进价大单位价格
        updatePackagePrice?:ModifyPriceItem    
        //修改进价的小单位价格
        updatePiecePrice?:ModifyPriceItem    
    }
}
