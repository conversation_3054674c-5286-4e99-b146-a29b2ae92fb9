declare namespace  AbcAPI {
    
    type GoodsRedisCacheRes = {    
        
        atc:string    
        
        barCode:string    
        
        bizExtensions?:JsonNode    
        
        bizRelevantId:string    
        
        cMSpec:string    
        
        certificateName:string    
        
        certificateNo:string    
        
        combineType:number    
        
        componentContentNum:number    
        
        componentContentUnit:string    
        
        createdDate:string    
        
        createdUserId:string    
        
        customTypeId:number    
        
        customTypeName:string    
        
        defaultInOutTax:number    
        
        deviceInfo?:GoodsBindDeviceInfo    
        
        disable:number    
        
        dismounting:number    
        
        domainId:number    
        
        enName:string    
        
        extendSpec:string    
        
        goodsVersion:number    
        
        id:string    
        
        inTaxRat:number    
        
        inorderConfig:number    
        
        isSell:number    
        
        lastModifiedDate:string    
        
        lastModifiedUserId:string    
        
        manufacturer:string    
        
        manufacturerFull:string    
        
        manufacturerId:string    
        
        materialSpec:string    
        
        medicineCadn:string    
        
        medicineDosageForm:string    
        
        medicineDosageNum:number    
        
        medicineDosageUnit:string    
        
        medicineNmpn:string    
        
        name:string    
        
        needExecutive:number    
        
        organId:string    
        
        origin:string    
        
        outTaxRat:number    
        
        packageCostPrice:number    
        
        packageCount:number    
        
        packagePrice:number    
        
        packageUnit:string    
        
        pieceCount:number    
        
        pieceNum:number    
        
        piecePrice:number    
        
        pieceUnit:string    
        
        position:string    
        
        py:string    
        
        remark:string    
        
        sellConfig:number    
        
        shortId:string    
        
        specType:number    
        
        status:number    
        
        subType:number    
        
        type:number    
        
        typeId:number    
    }
}
