declare namespace  AbcAPI {
    
    type GoodsSnapV3 = {    
        
        atc:string    
        
        barCode:string    
        
        bizExtensions?:JsonNode    
        
        bizRelevantId:string    
        
        cMSpec:string    
        
        certificateName:string    
        
        certificateNo:string    
        
        chainComposePackagePrice:number    
        
        chainComposePiecePrice:number    
        
        chainDisable:number    
        
        chainId:string    
        
        chainPackageCostPrice:number    
        
        chainPackagePrice:number    
        
        chainPiecePrice:number    
        
        chainV2DisableStatus:number    
        
        children?:Array<GoodsSnapV3>    
        
        clinicId:string    
        
        combineType:number    
        
        componentContentNum:number    
        
        componentContentUnit:string    
        
        composePackageCount:number    
        
        composePackagePrice:number    
        
        composePieceCount:number    
        
        composePiecePrice:number    
        
        composePrice:number    
        
        composeSort:number    
        
        composeUseDismounting:number    
        
        createdDate:string    
        
        createdUserId:string    
        
        customTypeId:number    
        
        customTypeName:string    
        
        defaultInOutTax:number    
        
        deviceInfo?:GoodsBindDeviceInfo    
        
        disable:number    
        
        disableSell:number    
        
        dismounting:number    
        
        displayName:string    
        
        displaySpec:string    
        
        domainId:number    
        
        enName:string    
        
        extendSpec:string    
        
        goodsId:string    
        
        goodsVersion:number    
        
        id:string    
        
        inTaxRat:number    
        
        inorderConfig:number    
        
        isSell:number    
        
        lastModifiedDate:string    
        
        lastModifiedUserId:string    
        
        manufacturer:string    
        
        manufacturerFull:string    
        
        materialSpec:string    
        
        medicineCadn:string    
        
        medicineDosageForm:string    
        
        medicineDosageNum:number    
        
        medicineDosageUnit:string    
        
        medicineNmpn:string    
        
        name:string    
        
        needExecutive:number    
        
        organId:string    
        
        origin:string    
        
        outTaxRat:number    
        
        packageCostPrice:number    
        
        packagePrice:number    
        
        packageUnit:string    
        
        pieceNum:number    
        
        piecePrice:number    
        
        pieceUnit:string    
        
        position:string    
        
        py:string    
        
        remark:string    
        
        sellConfig:number    
        
        shebaoPayMode:number    
        
        shortId:string    
        
        smartDispense:number    
        
        smartDispenseMachineNo:number    
        
        specType:number    
        
        standardName:string    
        
        standardUnit:string    
        
        status:number    
        
        subType:number    
        
        type:number    
        
        typeId:number    
        
        v2DisableStatus:number    
    }
}
