declare namespace  AbcAPI {
    
    type GoodsStockCheckBatchView = {    
        //盘点批次的批次ID, 如果没指定批次盘点为null
        batchId:string    
        //盘点批次的批次No
        batchNo:string    
        //【批次】盘前账面数量 大
        beforePackageCount:number    
        //【批次】盘前账面数量 小
        beforePieceCount:number    
        //盘点商品的GoodsId
        goodsId:string    
        //数据库记录Id
        id:string    
        //成本价 每个盘点批次的成本，会跟着盘点单的成本变，每次拉每次算
        packageCostPrice:number    
        //盘后账面数量 大
        packageCount:number    
        //盘点变更数量 大
        packageCountChange:number    
        //盘后账面数量 小
        pieceCount:number    
        //盘点变更数量 小
        pieceCountChange:number    
        
        pieceNum:number    
        //盘点批次对应的goodsStock记录的id
        stockId:number    
        //盘点总成本的变更量
        totalCostPriceChange:number    
        //盘点总售价的变更量
        totalPriceChange:number    
    }
}
