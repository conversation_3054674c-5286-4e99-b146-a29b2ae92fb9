declare namespace  AbcAPI {
    
    type GoodsStockCheckImportParseBatch = {    
        //如果是按批次盘点这个字段非空
        batchId:string    
        
        batchNo:string    
        //盘点前大包装单位
        beforePackageCount:number    
        //盘点前小包装单位
        beforePieceCount:number    
        
        expiryDate:string    
        //盘点的goodsId
        goodsId:string    
        
        inDate:string    
        
        isBlank:boolean    
        //实际要盘点成的大包装单位数量
        packageCount:number    
        
        packageCountChange:number    
        //实际要盘点成的小包装单位数量
        pieceCount:number    
        
        pieceCountChange:number    
    }
}
