declare namespace  AbcAPI {
    
    type GoodsStockInDraftView = {    
        //生产批号
        batchNo:string    
        //连锁id
        chainId:string    
        //诊所id
        clinicId:string    
        //效期
        expiryDate:string    
        //商品id
        goodsId:string    
        //主键id
        id:number    
        //入库草稿单id
        orderDraftId:number    
        //生产日期
        productionDate:string    
        //数量
        useCount:number    
        //成本总价
        useTotalCostPrice:number    
        //单位
        useUnit:string    
        //成本单价
        useUnitCostPrice:number    
    }
}
