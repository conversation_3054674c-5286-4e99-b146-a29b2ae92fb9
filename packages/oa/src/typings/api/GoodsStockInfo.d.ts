declare namespace  AbcAPI {
    
    type GoodsStockInfo = {    
        
        availablePackageCount?:number    
        
        availablePieceCount?:number    
        
        batchId?:number    
        
        created?:string    
        
        dispGoodsCount?:string    
        
        dispLockingGoodsCount?:string    
        
        dispOutGoodsCount?:string    
        
        dispProhibitGoodsCount?:string    
        
        dispStockGoodsCount?:string    
        
        from?:number    
        
        lockingPackageCount?:number    
        
        lockingPieceCount?:number    
        
        packageCount?:number    
        
        pharmacyNo?:number    
        
        pieceCount?:number    
        
        prohibitPackageCount?:number    
        
        prohibitPieceCount?:number    
        
        stockId?:number    
        
        stockInId?:number    
        
        stockPackageCount?:number    
        
        stockPieceCount?:number    
    }
}
