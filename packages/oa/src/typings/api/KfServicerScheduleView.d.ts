declare namespace  AbcAPI {
    
    type KfServicerScheduleView = {    
        //创建时间
        created:string    
        //部门id
        departmentId:number    
        //是否国家法定节日：0=正常工作日，1=国家法定节日
        isNationalHoliday:number    
        //是否双休日：0=正常工作日，1=周末双休日
        isWeekend:number    
        //上次修改时间
        lastModified:string    
        //上次修改人姓名
        lastModifiedBy:string    
        //客服帐号名称
        openKfName:string    
        //客服帐号ID
        openKfid:string    
        //值班班次码，如：A
        scheduleClass:string    
        //值班日期，如：2022-05-25
        scheduleDate:string    
        //状态：1=此条排班有效，0=此条排班无效
        scheduleStatus:number    
        //值班人员姓名,如：樊豫
        servicerName:string    
        //值班人员企微userid，如：FanYu
        servicerUserid:string    
    }
}
