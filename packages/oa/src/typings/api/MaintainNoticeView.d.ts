declare namespace  AbcAPI {
    
    type MaintainNoticeView = {    
        //故障公告：所有环境产品应用：0否1是
        applyAll:number    
        //医保文章标题
        articleTitle:string    
        //文章连接
        articleUrl:string    
        //故障、维护公告）配置url
        configUrl:string    
        //内容
        content:string    
        //主键
        id:string    
        //维护公告：预告内容
        preContent:string    
        //（故障、维护公告）重定向url
        redirectUrl:string    
        
        rules?:Array<Rule>    
        //开始展示时间
        showBeginTime:string    
        //结束展示时间
        showEndTime:string    
        //展示区域
        showRegions?:Array<ShowRegion>    
        //0初始状态；1停机中;2恢复中；3撤回4正常；
        status:number    
        //标题
        title:string    
        //类型：1故障公告；2维护公告；3医保公告
        type:number    
    }
}
