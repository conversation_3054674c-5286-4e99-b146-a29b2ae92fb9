declare namespace  AbcAPI {
    
    type MallGoodsOrderForFinishInOrderView = {    
        
        afterSalePurchaseCount:number    
        
        approvedCode:string    
        
        barCode:string    
        
        cadn:string    
        
        categoryId:string    
        
        categoryName:string    
        //Erp发货的批次信息
        erpBatchInfoList?:Array<ErpGoodsBatchInfo>    
        
        hisOrderItemId:string    
        
        mallDealPurchaseCount:number    
        
        mallPurchaseCount:number    
        
        manufacturer:string    
        
        maxSalesCount:number    
        
        minSalesCount:number    
        
        salesUnit:string    
        
        salesUnitPrice:number    
        
        shortId:string    
        
        skuGoodsId:string    
        
        skuGoodsName:string    
        
        skuStockCount:number    
        
        specification:string    
        
        spuGoodsId:string    
        
        status:number    
        
        stockInOrderStatus:number    
        
        stockInOrderStatusName:string    
        
        tradeName:string    
        
        transRatio:number    
        
        vendorName:string    
    }
}
