declare namespace  AbcAPI {
    
    type PreventionPrescriptionContentRequest = {    
        //处方截图
        attachments?:Array<any>    
        //门店id：不能为空
        clinicId?:string    
        //门店名称，前端可以不传，后台会获取
        clinicName?:string    
        //剂数：不能为空
        doseCount?:number    
        //应收费用：后台算好返回
        receivableFee?:number    
        //收款账户id (字节流 1, 字节星球 2)
        receiveAccountId?:number    
        //备注
        remark?:string    
        //供应商结算金额: 不能为空
        suppliersFee?:number    
        //收费项目 (1 饮片代煎; 2 饮片; 3 颗粒;), 不能为空
        type?:number    
    }
}
