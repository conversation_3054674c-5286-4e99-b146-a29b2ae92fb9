declare namespace  AbcAPI {
    
    type QWClinicEditionOrderAbstract = {    
        
        abcCreator?:AbcEmployee    
        
        beginDate:string    
        
        bindChainId:string    
        
        bindClinicId:string    
        
        created:string    
        
        customer?:EmployeeA    
        
        editionId:string    
        
        editionStr:string    
        
        endDate:string    
        
        feePerDay:number    
        
        id:string    
        
        isExpired:number    
        
        isLatestOrder:number    
        
        name:string    
        
        organType:number    
        
        paidTime:string    
        
        receivableFee:number    
        
        remarks:string    
        
        sellerId:string    
        
        sellerName:string    
        
        source:number    
        
        status:number    
        
        type:number    
        
        typeStr:string    
        
        unit:string    
        
        unitCount:number    
    }
}
