declare namespace  AbcAPI {
    
    type SaveOrUpdateCmsPushReq = {    
        //广告类型 0:全量展示 1:条件展示 2:迭代推送
        adType?:number    
        //推送条件
        condition?:CmsPushConditionDto    
        //排除的门店ID列表
        excludeClinicIds?:Array<any>    
        //附加的推送门店ID列表
        includeClinicIds?:Array<any>    
        //推送资源列表
        pushResList?:Array<CmsPushResourceDto>    
        //展示地区
        regions?:Array<CmsPushRegionDto>    
        //发布版本 adType 为 2:迭代推送 时有效
        releaseVersion?:string    
    }
}
