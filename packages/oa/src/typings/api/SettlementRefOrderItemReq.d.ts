declare namespace  AbcAPI {
    
    type SettlementRefOrderItemReq = {    
        //参与结算的入库单或出库单金额 含税
        amount?:number    
        //参与结算的入库单或出库单金额 不含税
        amountExcludingTax?:number    
        //参与结算的入库单或出库单 大包装单位后的累加
        count?:number    
        //数据库记录Id
        id?:number    
        //参与结算的入库单或出库单 药品品种数量
        kindCount?:number    
        
        lastModifiedDate?:string    
        //参与结算的入库单或出库单门店的信息
        orderClinic?:OrganViewReq    
        
        orderCreator?:EmployeeView    
        //参与结算的入库单或出库单 订单数据库Id
        orderId?:number    
        //参与结算的入库单或出库单出入库发生门店的id
        refOrderClinicId?:string    
        //参与结算的入库单或出库单单门店的信息
        refOrderClinicName?:string    
        //参与结算的入库单或出库单 入库单就是入库日期，出库就是出库日期
        refOrderDate?:string    
        
        refOrderId?:number    
        //参与结算的入库单或出库单 可读的订单号
        refOrderNo?:string    
        //单据的创建人名字
        refOrderUserName?:string    
        //参与结算的入库单或出库单金额 税
        tax?:number    
        //参与结算的入库单或出库单 类型 0 入库 1 出库
        type?:number    
    }
}
