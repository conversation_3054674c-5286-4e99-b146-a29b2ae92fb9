declare namespace  AbcAPI {
    
    type ShoppingPlanSimpleInfoView = {    
        //创建时间
        createdTime:string    
        //创建人
        createdUser?:EmployeeView    
        //多采购计划记录在数据库里面的ID，用于后续的请求操作
        id:string    
        //最近修改时间
        lastModifiedTime:string    
        //最近修改人
        lastModifiedUser?:EmployeeView    
        //采购计划的名字
        name:string    
        //诊所内部按0开始递增的序号，方便前端再没有id的情况下也能拉取采购计划里面的内容
        purchasePlanIdx:number    
    }
}
