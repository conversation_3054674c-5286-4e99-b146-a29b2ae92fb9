declare namespace  AbcAPI {
    
    /**
     * 实施工单表单视图
     */
    type SupportTicketSheetListView = {    
        //项目预约列表
        appointments?:Array<SupportTicketFormAppointmentView>    
        //门店id
        clinicId:string    
        //客户实施完成确认时间
        customerConfirmImplementedTime:string    
        //客户生命周期 100待新装，200已新装 300待激活 400已激活 500待促活 600活跃中
        customerLifeCycle:number    
        //版本
        editionSnapshot:string    
        //(仅用于工单搜索判定, 列表禁止使用该字段判定详情) 结合Form判定的实施工单状态: 0:待安排(全部form为待安排) 10:待实施(有form为待实施即可) 20:已实施(全部form为已实施)
        ensembleSheetStatus:number    
        //完成实施时间
        finishImplementTime:string    
        //我的已完成项目总数
        finishItemCount:number    
        //项目任务列表
        forms?:Array<SupportTicketFormBaseView>    
        //0诊所 1口腔 2眼科 10药店 100医院
        hisTypeSnapshot:number    
        //id
        id:string    
        //技术支持工程师Id
        implementer:string    
        //技术支持工程师名称
        implementerName:string    
        //我的任务总数
        itemCount:number    
        //销售Id
        sellerId:number    
        //销售名
        sellerName:string    
        //0待处理20已实施30已评价
        status:number    
        //对接类型 20企微管家 21体检系统 22微商城 23儿保
        subType:number    
        //实施工单标题(门店名称快照)
        title:string    
        //实施工单类型 0新装，1升级 2增购 90其他
        type:number    
    }
}
