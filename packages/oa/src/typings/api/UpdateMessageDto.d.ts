declare namespace  AbcAPI {
    
    type UpdateMessageDto = {    
        //留言内容
        content?:string    
        //区分有帮助和没帮助0：无帮助，1：有帮助
        type?:number    
        //帮助中心问题表id
        questionId?:number    
        //绑定的连锁id
        chainId?:string    
        //绑定的门店id
        clinicId?:string    
        //留言处理状态（0：未处理，1：已处理，2：失效）
        status?:number    
        //留言类型：（0：客户留言，1：回复留言）
        messageType?:number    
        //回复关联的留言id
        associationMessageId?:number    
        //是否隐藏
        isHide?:number    
    }
}
