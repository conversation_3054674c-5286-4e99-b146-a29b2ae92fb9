/**
 * 动态加载 script，由于本工程不兼容 IE，所以该实现没有 IE 实现
 * @param url
 * @param id
 */
export async function loadScript(url: string, id?: string) {
    return new Promise((resolve, reject) => {
        const head = document.getElementsByTagName('head')[0];
        const script = document.createElement('script');
        script.type = 'text/javascript';
        if (id) {
            script.id = id;
        }
        script.onload = function () {
            resolve(true);
        };
        script.onerror = function () {
            reject();
        };
        script.src = url;
        head.appendChild(script);
    });
}

/**
 * 根据 id 移除 script
 * @param id
 */
export async function unloadScript(id: string) {
    const script = document.querySelector(`#${id}`);
    script?.parentNode?.removeChild(script);
}

export function copy(value: string, cb?: Function) {
    // 针对搜狗输入法等特殊情况，优先使用现代Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(value).then(() => {
            if (cb && Object.prototype.toString.call(cb) === '[object Function]') {
                cb();
            }
        }).catch(() => {
            // Clipboard API失败时使用传统方法作为备选
            useExecCommand(value, cb);
        });
    } else {
        // 非安全上下文使用传统方法
        useExecCommand(value, cb);
    }
}

/**
 * 使用传统document.execCommand方式复制文本
 * @param value 要复制的文本
 * @param cb 复制成功后的回调函数
 */
function useExecCommand(value: string, cb?: Function) {
    try {
        // 创建textarea元素
        const textarea = document.createElement('textarea');
        // 设置只读防止iOS唤起键盘
        textarea.readOnly = true;
        // 移出可视区域但保持可访问
        textarea.style.position = 'fixed';
        textarea.style.top = '0';
        textarea.style.left = '-9999px';
        textarea.style.opacity = '0';
        textarea.style.zIndex = '-1';
        // 避免搜狗输入法等特殊情况干扰
        textarea.style.userSelect = 'text';
        textarea.style.webkitUserSelect = 'text';
        // 设置要复制的文本
        textarea.value = value;
        
        document.body.appendChild(textarea);
        
        // 选择文本
        textarea.focus();
        textarea.select();
        // 确保在移动设备上也能选中
        textarea.setSelectionRange(0, value.length);
        
        // 尝试复制
        const successful = document.execCommand('copy');
        document.body.removeChild(textarea);
        
        if (successful && cb && Object.prototype.toString.call(cb) === '[object Function]') {
            cb();
        }
    } catch (err) {
        console.error('复制操作失败:', err);
    }
}
