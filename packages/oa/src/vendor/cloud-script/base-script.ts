import { AbcResponse } from '@/common/AbcResponse';
import { ScriptApi } from '@/api/script-api';

export class BaseScript {
    static NAME: string;

    public static async exec(params?: any): Promise<AbcResponse> {
        try {
            const { result } = await ScriptApi.postApiLowCodeScriptExec({
                name: this.NAME,
                params,
            });
            if (result?.error) {
                // @ts-ignore
                return AbcResponse.error(result?.error?.message || result?.error || '执行脚本异常', result);
            }
            // @ts-ignore
            return AbcResponse.success(this.handleResponse(result.data?.data || result.data));
        } catch (e: any) {
            return AbcResponse.error(e || e.message || '执行脚本未知异常', e);
        }
    }

    public static handleResponse(data: any) {
        return data;
    }
}
