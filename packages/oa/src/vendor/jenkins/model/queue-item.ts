export default class QueueItem {
    id: number;

    executable: Executable;

    constructor(id: number, executable: Executable) {
        this.id = id;
        this.executable = executable;
    }

    static parse(schema: any) {
        const {
            id,
            executable,
        } = schema;
        return new QueueItem(id, executable);
    }
}

export interface Executable {
    _class: string;
    number: number;
    url: string;
}
