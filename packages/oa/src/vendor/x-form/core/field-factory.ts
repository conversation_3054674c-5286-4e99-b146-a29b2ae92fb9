/**
 * 字段工厂
 */
import { Field } from '@/vendor/x-form/core/field';
import { Form } from '@/vendor/x-form/core/form';
import { OptionField } from '@/vendor/x-form/core/option-field';

export default class FieldFactory {
    static OptionField = ['OrganPicker', 'CrmOrganPicker', 'Select', 'EmployeePicker', 'OrganEmployeePicker'];

    static create(fieldSchema: any, form: Form) {
        if (this.isOptionField(fieldSchema.component)) {
            return new OptionField(fieldSchema, form);
        }
        return new Field(fieldSchema, form);
    }

    static isOptionField(component: string) {
        return this.OptionField.indexOf(component) !== -1;
    }
}
