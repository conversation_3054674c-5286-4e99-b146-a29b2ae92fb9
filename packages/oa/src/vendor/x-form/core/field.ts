import { isDef, isEmptyValue, isFn } from '@/utils/utils';
import { OrganListFunction } from '@/vendor/cloud-function';
import { Form } from '@/vendor/x-form/core/form';
import { IValidateRule, IValidateState, ValidateTrigger } from '@/vendor/x-form/types/model';
import { WatchOptions } from '@/vendor/x-form/types/schema';
import { inject, watch, watchEffect } from 'vue';
// @ts-ignore
import { isEqual } from '@tool/utils';
import RoomListFunction from '@/vendor/cloud-function/functions/room-list';

export class Field {
    // 唯一标志
    identifier: string;

    property: string;

    component: string;

    componentProps: Record<string, any>;

    // 初始值
    initValue: any;

    defaultValue: any;

    defaultValueFn?: Function;

    label: string;

    labelWidth: string;

    value: any;

    type: string;

    // 额外数据包
    private bundle: null | any;

    // 当 type 为 object 时，拥有 properties
    properties?: Record<string, any>;

    // 判断是否禁用
    disableFn: Function | null;

    // 是否禁用
    disable: boolean;

    // 判断是否只读
    readonlyFn: Function | null;

    // 是否只读
    readonly: boolean;

    // 判断是否可见
    visibleFn: Function;

    // 是否可见（由 visibleFn 计算得出）
    visible: boolean;

    // 是否需要 required
    required: boolean;

    flatten: boolean;

    dataSource: any;

    hasDataSource: boolean;

    loading: boolean;

    // select的 options
    selectOptions: Array<any>;

    // 校验规则数组
    rules: Array<IValidateRule>;

    // 校验状态
    validateState: IValidateState;

    minDate?: Date;

    maxDate?: Date;

    minDateFn?: Function;

    maxDateFn?: Function;

    watch?: WatchOptions;

    form: Form;

    // pc端布局
    colSpan?: number;

    // labelPosition为left时布局
    labelColSpan?: number;

    valueColSpan?: number;

    // 禁用小时段
    disabledHours?: Function;

    // 禁用分钟段
    disabledMinutes?: Function;

    // 禁用秒段
    disabledSeconds?: Function;

    disableTimeFn?: Function;

    // input图标
    prefixIcon?: string;

    private isFirstInit: Boolean = true;

    constructor(schema: any, form: Form) {
        this.identifier = schema.property;
        this.property = schema.property;
        this.component = schema.component;
        this.componentProps = schema.componentProps;
        this.defaultValueFn = schema.defaultValue;
        this.label = schema.label;
        this.labelWidth = schema.labelWidth;
        this.type = schema.type;
        this.colSpan = schema.colSpan;
        this.labelColSpan = schema.labelColSpan;
        this.valueColSpan = schema.valueColSpan;
        this.disableTimeFn = schema.disableTimeFn;
        this.properties = schema.properties;
        this.prefixIcon = schema.prefixIcon;
        if (isFn(schema.disable)) {
            this.disableFn = schema.disable;
            this.disable = false;
        } else {
            this.disableFn = null;
            this.disable = schema.disable || false;
        }
        if (isFn(schema.readonly)) {
            this.readonlyFn = schema.readonly;
            this.readonly = false;
        } else {
            this.readonlyFn = null;
            this.readonly = isDef(schema.readonly) ? Boolean(schema.readonly) : form.readonly;
        }
        this.visibleFn = schema.visible;
        this.visible = true;
        this.flatten = schema.flatten || false;
        this.selectOptions = schema.selectOptions || [];
        this.rules = schema.rules || [];
        this.required = this.rules.some(rule => rule.required);
        this.validateState = {
            error: false,
            message: '',
        };

        // 解析 DatePicker
        if ((this.component === 'DatePicker' || this.component === 'DateTimePickerRange') && this.componentProps) {
            this.minDateFn = this.componentProps.minDate;
            if (!isFn(this.minDateFn)) {
                this.minDate = this.componentProps.minDate;
            }
            this.maxDateFn = this.componentProps.maxDate;
            if (!isFn(this.maxDateFn)) {
                this.maxDate = this.componentProps.maxDate;
            }
            if (!isFn(this.disableTimeFn)) {
                this.disabledHours = this.componentProps.disabledHours || (() => []);
                this.disabledMinutes = this.componentProps.disabledMinutes || (() => []);
                this.disabledSeconds = this.componentProps.disabledSeconds || (() => []);
            }
        }

        this.watch = schema.watch;

        this.dataSource = schema.dataSource;
        this.hasDataSource = !!schema.dataSource;
        this.loading = false;

        this.form = form;
        this.initValue = this.form.getFieldValue(this.property);
    }

    get modelValue() {
        return this.form.getFieldValue(this.property);
    }

    set modelValue(val) {
        this.onFieldValueChange(val, this.modelValue);
    }

    /**
     * 注册 Effects
     */
    registerEffects() {
        if (isFn(this.visibleFn)) {
            watchEffect(() => {
                this.visible = this.visibleFn(this.form.formData, this, this.form);
            });
        }
        if (isFn(this.disableFn)) {
            watchEffect(() => {
                this.disable = this.disableFn!(this.form.formData, this, this.form);
            });
        }
        if (isFn(this.readonlyFn)) {
            watchEffect(() => {
                this.readonly = this.readonlyFn!(this.form.formData, this, this.form);
            });
        }
        if (isFn(this.defaultValueFn)) {
            watchEffect(() => {
                const newDefaultValue = this.defaultValueFn?.(this.form.formData, this, this.form);
                // 第一次初始化，优先使用 initValue，再使用默认值
                if (this.isFirstInit) {
                    let initValue = newDefaultValue;
                    if (!this.isEmptyInitValue()) {
                        initValue = this.initValue;
                    }
                    this.defaultValue = initValue;
                    this.modelValue = initValue;
                    this.isFirstInit = false;
                    return;
                }
                // 后续调用，说明是响应式产生的默认值变化，重新对 modelValue 赋值
                if (!isEqual(newDefaultValue, this.defaultValue)) {
                    this.defaultValue = newDefaultValue;
                    this.modelValue = newDefaultValue;
                }
            });
        }

        if (isFn(this.minDateFn)) {
            watchEffect(() => {
                this.minDate = this.minDateFn?.(this.form.formData, this, this.form);
            });
        }

        if (isFn(this.maxDateFn)) {
            watchEffect(() => {
                this.maxDate = this.maxDateFn?.(this.form.formData, this, this.form);
            });
        }

        if (isFn(this.disableTimeFn)) {
            watchEffect(() => {
                this.disabledHours = () => this.disableTimeFn?.(this.form.formData, this, this.form)?.disabledHours?.();
                this.disabledMinutes = (hour: number) => this.disableTimeFn?.(this.form.formData, this, this.form)?.disabledMinutes?.(hour);
                this.disabledSeconds = (hour: number, minute: number) => this.disableTimeFn?.(this.form.formData,
                    this, this.form)?.disabledSeconds?.(hour, minute);
            });
        }
    }

    /**
     * 判断初始值是否为空
     */
    isEmptyInitValue() {
        if (this.type === 'object') {
            if (!this.initValue) {
                return true;
            }
            return Object.keys(this.initValue).every((key) => isEmptyValue(this.initValue[key]));
        }
        return isEmptyValue(this.initValue);
    }

    registerWatch() {
        if (this.watch) {
            const sources = this.watch.sources.map(source => () => this.form.formData[source]);
            watch(sources, (newValues, oldValues) => {
                this.watch!.handler(newValues, oldValues, this);
            }, {
                immediate: this.watch.immediate !== false,
            });
        }
    }

    validateWithTrigger(trigger: ValidateTrigger) {
    }

    /**
     * 触发校验
     */
    validate() {
        if (this.visible && this.rules.length) {
            // 先重置校验状态
            this.resetValidateState();
            for (let rule of this.rules) {
                if (rule.required && isEmptyValue(this.modelValue)) {
                    this.updateValidateState(true, this.getValidateMessage(this.modelValue, rule, this, this.form));
                    return false;
                }
                if (isFn(rule.validator) && !rule.validator!(this.modelValue, rule, this, this.form)) {
                    this.updateValidateState(true, this.getValidateMessage(this.modelValue, rule, this, this.form));
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 获取校验失败的 message
     * @param value
     * @param rule
     */
    private getValidateMessage(value: any, rule: IValidateRule, field: Field, form: Form) {
        const { message } = rule;
        if (isFn(message)) {
            // @ts-ignore
            return message(value, rule, field, form);
        }
        return message || '请输入值';
    }

    private updateValidateState(error: boolean, message: string) {
        this.validateState.error = error;
        this.validateState.message = message;
    }

    /**
     * 重置校验状态
     * @private
     */
    private resetValidateState() {
        this.updateValidateState(false, '');
    }

    /**
     * 字段值发生改变
     * @param newVal 新值
     * @param oldVal 旧值
     */
    onFieldValueChange(newVal: any, oldVal: any) {
        this.form.onFieldValueChange(this, newVal, oldVal);
        this.resetValidateState();
    }

    async requestDataSource(params?: any) {
        if (this.hasDataSource) {
            const dataSource = this.dataSource;
            switch (dataSource.type) {
                case 'cloud-function':
                    this.loading = true;
                    console.log(`${this.identifier} exec remote func: ${dataSource.func}`, params);
                    const cloudFuncMap = new Map([
                        [OrganListFunction.NAME, OrganListFunction],
                        [RoomListFunction.NAME, RoomListFunction],
                    ]);
                    if (cloudFuncMap.has(dataSource.func)) {
                        const func: any = cloudFuncMap.get(dataSource.func);
                        const response = await func.exec({ keyword: params });
                        console.log(`${dataSource.func} Response：`, response);
                        if (response.status === false) {
                            return [];
                        }
                        const { rows = [] } = response.data;
                        const options = rows.map((row: any) => ({
                            label: row.shortName || row.roomName || row.name,
                            value: row.id || row.roomId,
                            ...row,
                        }));
                        if (this.component === 'Select') {
                            this.selectOptions = options;
                        }
                    }
                    this.loading = false;
                    break;
                default:
                    console.warn('unknown dataSource type:', dataSource.type);
                    break;
            }
        }
    }

    setBundle(bundle: any) {
        this.bundle = bundle;
    }

    getBundle() {
        return this.bundle;
    }

    onCreate() {
        console.log(`Field.onCreate[${this.property}]`);
        this.registerEffects();
    }

    onMounted() {
        console.log(`Field.onMounted[${this.property}]`);
        this.registerWatch();
    }

    onUnmounted() {
        console.log(`Field.onUnmounted[${this.property}]`);
    }
}

export function useField() {
    return <Field>inject('field');
}
