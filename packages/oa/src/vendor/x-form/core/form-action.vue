<!-- 字段管理组件 -->
<script setup lang="ts">
import { useForm } from '@/vendor/x-form/core/form';
import { provide, reactive } from 'vue';

const props = defineProps({
    schema: {
        type: Object,
        required: true,
    }, // 字段信息
});

const form = useForm();
const action = reactive(form.addAction(props.schema));
provide('action', action);
</script>

<template>
    <slot></slot>
</template>

<style scoped>

</style>
