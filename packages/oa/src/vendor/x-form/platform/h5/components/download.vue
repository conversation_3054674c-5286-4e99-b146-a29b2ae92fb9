<script setup lang="ts">
import { useField } from '@/vendor/x-form/core/field';
import H5Field from './h5-field.vue';

const field = useField();

</script>
<template>
    <h5-field>
        <a
            class="h5-download-button el-link el-link--primary is-underline"
            download
            :href="field.componentProps?.downloadUrl || ''"
            target="_blank"
        >{{ field.componentProps?.downloadBtn || '下载' }}</a>
    </h5-field>
</template>

<style lang="scss">
.h5-download-button {
    padding: 0;
}
</style>
