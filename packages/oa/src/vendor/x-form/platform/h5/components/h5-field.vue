<script setup lang="ts">
import { useField } from '@/vendor/x-form/core/field';
import { useForm } from '@/vendor/x-form/core/form';
import { computed } from 'vue';

const field = useField();
const form = useForm();

const isLabelTop = computed(() => form.labelPosition === 'top');
const isRequired = computed(() => field.required);
const isDisabled = computed(() => field.disable);
const isReadonly = computed(() => field.readonly);

const labelClass = computed(() => {
    let classArr = ['h5-field-wrapper'];
    if (isLabelTop.value) {
        classArr.push('label-top');
    }
    if (isRequired.value) {
        classArr.push('is-required');
    }
    if (isDisabled.value) {
        classArr.push('is-disabled');
    }
    if (isReadonly.value) {
        classArr.push('is-readonly');
    }
    return classArr;
});
</script>

<template>
    <el-row :class="labelClass">
        <el-col class="h5-field__label-wrapper" :span="field.labelColSpan">
            <div class="h5-field__label">
                <slot name="label" :field="field">
                    <span v-if="field.componentProps?.showLabelTooltip" class="h5-field__label-tooltip-wrapper">
                        {{ field.label }}
                        <el-tooltip popper-class="h5-field__label-tooltip">
                            <template #content>
                                <span v-html="field.componentProps.tooltipContent"></span>
                            </template>
                            <el-icon><QuestionFilled /></el-icon>
                        </el-tooltip>
                    </span>
                    <span v-else>{{ field.label }}</span>
                </slot>
            </div>
            <div
                v-if="field.validateState.error && isLabelTop"
                class="h5-field__error-wrapper"
            >
                <van-icon name="warning" />{{ field.validateState.message }}
            </div>
        </el-col>
        <!--默认插槽，表单内容-->
        <el-col class="h5-field__value-wrapper" :span="field.valueColSpan">
            <slot>
                {{ field.modelValue }}
            </slot>
        </el-col>

        <div
            v-if="field.componentProps?.tips"
            class="h5-field__tips"
        >
            {{ field.componentProps?.tips }}
        </div>

        <div
            v-if="field.validateState.error && !isLabelTop"
            class="h5-field__error-wrapper"
        >
            <van-icon name="warning" />{{ field.validateState.message }}
        </div>
    </el-row>
</template>

<style lang="scss">
    .h5-field-wrapper {
        display: flex;
        background: var(--oa-white);
        position: relative;
        border-bottom: 1px solid var(--oa-border-color);

        &.label-top {
            display: block;

            .h5-field__label-wrapper {
                display: flex;
                align-items: center;
                flex-shrink: 0;
                line-height: initial;
                padding-top: 6px;

                .h5-field__error-wrapper {
                    margin-left: 8px;
                }

                .h5-field__label-tooltip-wrapper {
                    display: inline-block;
                }
            }

            .h5-field__value-wrapper {
                text-align: left;
                padding: var(--oa-padding-4) var(--oa-padding-12);

                .van-cell__value {
                    text-align: left;
                }
            }
        }

        &.is-required {
            .h5-field__label::after {
                position: relative;
                top: 3px;
                left: 2px;
                content: '*';
                color: var(--oa-warning-color);
                font-size: var(--oa-font-size-16);
            }
        }

        &.is-disabled {
            .h5-field__value-wrapper {
                color: var(--oa-text-color-3);
                //background: var(--oa-gray-3);
            }
        }

        //&.is-readonly {
        //    .h5-field__value-wrapper {
        //        color: var(--oa-text-color-3);
        //        background: var(--oa-gray-3);
        //    }
        //}

        .h5-field__label-wrapper {
            flex: 1;
            line-height: 40px;
            padding-left: var(--oa-padding-12);
            color: var(--oa-text-color-2);
        }

        .h5-field__value-wrapper {
            flex: 2;
            width: 100%;
            min-height: 32px;
            line-height: 24px;
            padding: var(--oa-padding-8) var(--oa-padding-12);
            background: var(--oa-white);
            text-align: right;
            color: var(--oa-text-color);
        }

        .h5-field__error-wrapper {
            color: var(--oa-warning-color);
            font-size: var(--oa-font-size-12);
            line-height: 16px;

            .van-icon {
                margin-right: 2px;
            }
        }

        >.h5-field__error-wrapper {
            position: absolute;
        }

        .h5-field__tips {
            font-size: 12px;
            color: #dd8989;
            padding-left: 12px;
            padding-bottom: 8px;
        }
    }

    .h5-field__label-tooltip {
        max-width: 60vw;
    }
</style>
