<!--移动端适配的 multiple-select，实际上是一个 action-sheet-->
<script setup lang="ts">
import { isFn, noop } from '@/utils/utils';
import { useField } from '@/vendor/x-form/core/field';
import { SearchInstance } from 'vant';
import { computed, ref } from 'vue';
import H5Field from '@/vendor/x-form/platform/h5/components/h5-field.vue';

const field = useField();

const props = defineProps({
    modelValue: {
        type: Array,
        default: () => [],
    },
    filterable: {
        type: Boolean,
        default: false,
    },
    options: {
        type: Array,
        default: () => [],
    },
    remote: {
        type: Boolean,
        default: false,
    },
    remoteMethod: {
        type: Function,
        default: () => noop(),
    },
    className: {
        type: String,
        default: '',
    },
    placeholder: {
        type: String,
        default: '',
    },
    'on-option-click': {
        type: Function,
        default: null,
    },
});

const emit = defineEmits(['update:modelValue', 'option-click']);

// popup 可见性
const visiblePopup = ref(false);
const popupStyle = computed(() => {
    // 存在搜索，则固定高度
    if (props.filterable) {
        return {
            height: '90%',
        };
    }
    return { maxHeight: '80%', minHeight: '20%' };
});

const searchRef = ref<SearchInstance>();

// 渲染的 options
const filteredOptions = computed<Array<any>>(() => {
    if (props.filterable) {
        // 开启了远程搜索，直接返回传入的 options
        if (props.remote) {
            return props.options;
        }
        return props.options.filter((item: any) => item.label.includes(keyword.value));
    }
    return props.options;
});

// 搜索关键字
const isSearchLoading = ref(false);
const keyword = ref('');
const onSearch = async function () {
    if (props.remote && isFn(props.remoteMethod)) {
        isSearchLoading.value = true;
        await props.remoteMethod(keyword.value);
        isSearchLoading.value = false;
    }
};

// 当前选中的选项
const currentOptions = computed<any[]>(() => {
    const selectedValues = props.modelValue || [];
    return props.options.filter((item: any) => selectedValues.includes(item.value));
});

// 选项是否被选中
const isOptionSelected = (option: any) => props.modelValue?.includes(option.value);

const onOptionClick = async function (option: any) {
    const selectedValues = [...(props.modelValue || [])];
    const index = selectedValues.indexOf(option.value);
    
    if (index === -1) {
        // 添加选项
        selectedValues.push(option.value);
    } else {
        // 移除选项
        selectedValues.splice(index, 1);
    }
    
    // 更新值
    emit('update:modelValue', selectedValues);
    field.setBundle(option);
    
    // 触发 option-click 事件
    emit('option-click', option);
};

function showPopup() {
    if (field.readonly || field.disable) {
        return;
    }
    visiblePopup.value = true;
}

function handlePopupOpened() {
    searchRef.value?.focus();
}

// 确认选择
function handleConfirm() {
    visiblePopup.value = false;
}

</script>
<template>
    <h5-field :class="className" @click="showPopup">
        <van-loading
            v-if="field.loading"
            size="20px"
            type="spinner"
        ></van-loading>
        <template v-else>
            <slot name="label" :options="currentOptions">
                <template v-if="currentOptions.length">
                    {{ currentOptions.map(opt => opt.label).join('、') }}
                </template>
                <span v-else class="h5-select__placeholder">{{ placeholder || '' }}</span>
            </slot>
        </template>
    </h5-field>
    <van-popup
        v-model:show="visiblePopup"
        class="h5-select__popup"
        position="bottom"
        round
        :style="popupStyle"
        closeable
        teleport="#app"
        transition-appear
        @opened="handlePopupOpened"
    >
        <div class="h5-select__popup-header">
            <div class="title">{{ field.label }}</div>
            <div v-if="filterable" class="h5-select__search-bar-wrapper">
                <van-search
                    ref="searchRef"
                    v-model="keyword"
                    show-action
                    shape="round"
                    :placeholder="placeholder"
                    @search="onSearch"
                >
                    <template #action>
                        <div @click="onSearch">搜索</div>
                    </template>
                </van-search>
            </div>
        </div>
        <div class="h5-select__options-wrapper">
            <div v-if="isSearchLoading" class="h5-select__loading-wrapper">
                <van-loading type="spinner"></van-loading>
            </div>
            <template v-else>
                <div
                    v-for="option in filteredOptions"
                    :key="option.label"
                    class="h5-select__option-item-wrapper"
                    @click.stop="onOptionClick(option)"
                >
                    <slot name="option" :option="option">
                        <div class="h5-select__option-default">
                            <span>{{ option.label }}</span>
                            <van-icon v-if="isOptionSelected(option)" name="success" class="h5-select__option-selected" />
                        </div>
                    </slot>
                </div>
            </template>
        </div>
        <div class="h5-select__popup-footer">
            <van-button
                round
                block
                type="primary"
                @click="handleConfirm"
            >
                确定
            </van-button>
        </div>
    </van-popup>
</template>

<style lang="scss">
.h5-select__placeholder {
    color: var(--van-field-placeholder-text-color);
}

.h5-select__popup {
    display: flex;
    flex-direction: column;

    .h5-select__popup-header {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 50px;
        padding-top: 12px;

        .title {
            font-size: 16px;
            font-weight: bold;
        }

        .h5-select__search-bar-wrapper {
            width: calc(100% - 24px);
        }
    }

    .h5-select__loading-wrapper {
        margin-top: 48px;
    }

    .h5-select__options-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1;
        overflow: scroll;
        padding-bottom: 60px;

        .h5-select__option-item-wrapper {
            display: block;
            width: 100%;

            .h5-select__option-default {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 14px;
                border: none;
                line-height: 22px;
            }

            &:active {
                background-color: #f2f3f5;
            }
        }
    }

    .h5-select__popup-footer {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 12px 16px;
        background: #fff;
        box-shadow: 0 -2px 8px rgba(0, 0, 0, .06);
    }

    .h5-select__option-selected {
        color: var(--van-primary-color);
    }
}
</style>
