<script setup lang="ts">
import { useField } from '@/vendor/x-form/core/field';

const props = defineProps({
    modelValue: String,
});

const field = useField();

</script>
<template>
    <div class="h5-remark">
        {{ field.label }} {{ modelValue }}
    </div>
</template>

<style lang="scss">
.h5-remark {
    display: flex;
    justify-content: flex-end;
    padding-bottom: var(--oa-padding-12);
    color: var(--oa-text-color);
    line-height: 24px;
}
</style>
