<script setup lang="ts">
import { useField } from '@/vendor/x-form/core/field';
import H5Field from './h5-field.vue';

const props = defineProps({
    modelValue: String,
    rows: {
        type: Number,
        default: 2,
    },
    placeholder: {
        type: String,
        default: '',
    },
    maxLength: {
        type: String,
    },
});

const emit = defineEmits(['update:modelValue']);
const field = useField();

function onChange(val: string) {
    emit('update:modelValue', val);
}
</script>
<template>
    <h5-field>
        <van-field
            type="textarea"
            :model-value="modelValue"
            :readonly="field.readonly"
            :rows="rows"
            autosize
            class="h5-input-field"
            :placeholder="placeholder"
            :maxlength="maxLength"
            @update:model-value="onChange"
        ></van-field>
    </h5-field>
</template>

<style lang="scss">
.h5-input-field {
    padding: 0;
}
</style>
