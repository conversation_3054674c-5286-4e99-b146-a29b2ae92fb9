<script lang="ts" setup>
import { computed, ref } from 'vue';
import { CsSummaryAPI } from '@/api/cs-summary-api';
import ChatRecord from '@/components/chat-record/chat-record.vue';
import { PageParams } from '@/common/model/page-params';
import { ElMessage } from 'element-plus';
import { formatMsgDetail } from './schema';

const props = defineProps({
    visible: {
        type: Boolean,
        default: false,
    },
    currentRow: {
        type: Object,
        default: () => ({}),
    },
});

const emit = defineEmits(['update:visible']);

// 使用计算属性控制弹窗显示
const dialogVisible = computed({
    get() {
        return props.visible;
    },
    set(val) {
        emit('update:visible', val);
    },
});

const receptionMemberNames = computed(() => {
    let str = '';
    props.currentRow.receptionMemberNames?.forEach((item: any) => {
        str += item?.name + '、';
    });
    str = str.slice(0, -1);
    return str;
});

const loading = ref(false);

const loadMore = async (pageParams: PageParams) => {
    let res: any = {};
    try {
        res = await CsSummaryAPI.getChatDetailUsingGET(
            props.currentRow.summaryId,
            pageParams.limit,
            pageParams.offset,
        );
    } catch (e: any) {
        loading.value = false;
        ElMessage.error(e.message || e);
    }
    const tempRows = res.rows?.map(formatMsgDetail) || [];

    return {
        rows: tempRows,
        limit: pageParams.limit,
        offset: pageParams.offset,
        total: res.total || 0,
    };
};

// 关闭弹窗
const handleClose = () => {
    dialogVisible.value = false;
};
</script>

<template>
    <el-dialog
        v-model="dialogVisible"
        title="售后接待问题详情"
        width="80%"
        :before-close="handleClose"
        append-to-body
        destroy-on-close
        :close-on-press-escape="false"
    >
        <div v-loading="loading" class="dialog-content">
            <!-- 左侧详情信息 -->
            <div class="left-panel">
                <div class="detail-item">
                    <label>专属群名:</label>
                    <div class="value">{{ currentRow?.qwRoomName }}</div>
                </div>
                <div class="detail-item">
                    <label>接待人员:</label>
                    <div class="value">{{ receptionMemberNames }}</div>
                </div>
                <div class="detail-item">
                    <label>接待时间:</label>
                    <div class="value">{{ currentRow?.summaryDate }}</div>
                </div>
                <div class="detail-item">
                    <label>核心问题:</label>
                    <div class="value">{{ currentRow?.question }}</div>
                </div>
            </div>

            <!-- 右侧聊天记录 -->
            <div class="right-panel">
                <chat-record
                    height="60vh"
                    type="pagination"
                    :data-provider="loadMore"
                    :infinite-scroll-delay="300"
                />
            </div>
        </div>
    </el-dialog>
</template>

<style lang="scss" scoped>
.dialog-content {
    display: flex;
    gap: 20px;
    min-height: 60vh;
}

.left-panel {
    flex: 0 0 30%;
    padding-right: 20px;
    border-right: 1px solid #e4e7ed;
}

.right-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.detail-item {
    margin-bottom: 20px;
    display: flex;
    align-items: baseline;

    label {
        display: inline-block;
        width: 70px;
        font-weight: bold;
        color: #606266;
        margin-bottom: 8px;
        flex-shrink: 0;
    }

    .value {
        color: #303133;
    }
}
</style>