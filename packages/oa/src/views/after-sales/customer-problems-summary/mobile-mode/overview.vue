<script setup lang="ts">
import { ref, onMounted, nextTick, watch } from 'vue';
import * as echarts from 'echarts';
import { Icon as VanIcon, Toast } from 'vant';
import { CsSummaryAPI } from '@/api/cs-summary-api';
import { useGroupSessionMobile } from '../hooks/useGroupSessionMobile';

const { changeComponent } = useGroupSessionMobile();
// 接收父组件传递的props
const props = defineProps<{
    beginDate: string;
    endDate: string;
}>();

// 数据
const consultationTotal = ref(0);
const chartData = ref<any[]>([]);

// 图表实例
let chartInstance: echarts.ECharts | null = null;
const chartContainer = ref<HTMLElement>();

// 跳转到详情页
const goToCategoryList = (parentId?: string) => {
    changeComponent('CategoryList', false, { parentId });
};

/**
 * @description: 获取咨询总量
 * @Date: 2025-08-26 17:55:44
 * @author: yaoyongpeng
 * @return {*}
 */
const getConsultationTotal = async () => {
    try {
        const res: any = await CsSummaryAPI.countSummaryUsingGET(
            props.endDate,
            props.beginDate,
        );
        consultationTotal.value = res.count;
    } catch (e: any) {
        consultationTotal.value = 0;
        Toast.fail(e.message || e);
    }
};

// 获取图表数据
const getChartData = async () => {
    try {
        const topNum = 200;
    
        const res: any = await CsSummaryAPI.getChatTagTopUsingGET(
            topNum, 
            props.endDate, 
            undefined,
            props.beginDate,
        );
    
        chartData.value = res?.rows?.map((item: any) => ({
            ...item,
            name: item.tagName,
            value: item.count,
        })) || [];
    } catch (error) {
        console.error('获取图表数据失败:', error);
    }
};

// 初始化图表
const initChart = () => {
    if (!chartContainer.value) return;
  
    if (chartInstance) {
        chartInstance.dispose();
    }
  
    chartInstance = echarts.init(chartContainer.value);
  
    const option = !chartData.value || chartData.value.length === 0
        ? {
            graphic: {
                elements: [{
                    type: 'text',
                    style: {
                        text: '暂无数据',
                        font: '14px sans-serif',
                        fill: '#999',
                    },
                    left: 'center',
                    top: 'center',
                }],
            },
        }
        : {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow',
                },
            },
            grid: {
                left: '0',
                right: '10%',
                top: '3%',
                bottom: '18%',
                containLabel: true,
            },
            xAxis: {
                type: 'value',
                axisLabel: {
                    fontSize: 12,
                },
                minInterval: 1,
            },
            yAxis: {
                type: 'category',
                data: chartData.value.map(item => item.name),
                axisLabel: {
                    fontSize: 12,
                    interval: 0,
                },
                inverse: true,
            },
            series: [{
                name: '咨询数量',
                type: 'bar',
                data: chartData.value.map(item => item.value),
                itemStyle: {
                    color: '#409EFF',
                },
                label: {
                    show: true,
                    position: 'right',
                    fontSize: 10,
                    formatter: '{c}',
                },
            }],
        };
  
    chartInstance.setOption(option);
    
    // 只有在有数据时才添加点击事件
    if (chartData.value && chartData.value.length > 0) {
        // 添加点击事件
        chartInstance.on('click', (params: any) => {
            // 获取点击的数据项
            const clickedData = chartData.value[params.dataIndex];
            if (clickedData) {
                goToCategoryList(clickedData.tagId);
            }
        });
    }
};

// 加载数据
const loadData = async () => {
    await getConsultationTotal();
    await getChartData();
    nextTick(() => {
        initChart();
    });
};

// 监听日期变化，重新加载数据
watch([() => props.beginDate, () => props.endDate], () => {
    loadData();
}, { immediate: false });

onMounted(() => {
    loadData();
});
</script>

<template>
    <div>
        <!-- 咨询总量 -->
        <div class="consultation-total">
            <div class="total-label">咨询总量：{{ consultationTotal }}</div>
        </div>

        <!-- 一级分类榜 -->
        <div class="chart-section">
            <h3 class="section-title">一级分类榜</h3>
            <div ref="chartContainer" class="chart-container"></div>
        </div>
    </div>
</template>

<style scoped lang="scss">
    .consultation-total {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 16px;
        background-color: #fff;
        margin-bottom: 8px;
        position: relative;

        .total-label {
            font-size: 16px;
            font-weight: 500;
        }
    }

    .chart-section {
        background-color: #fff;
        margin-bottom: 8px;
        height: calc(100vh - 100px);
        display: flex;
        flex-direction: column;

        .section-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            padding: 16px 16px 8px;
            margin: 0;
        }

        .chart-container {
            flex: 1;
            padding: 0 16px;
            box-shadow: unset;
        }
    }
</style>