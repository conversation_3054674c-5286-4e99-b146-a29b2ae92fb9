<template>
    <div class="session-list">
        <!-- 搜索表单 -->
        <div class="search-form">
            <div class="form-row">
                <van-field
                    v-model="searchQuery.qwRoomNameKeyword"
                    placeholder="搜索群名关键词"
                    clearable
                    maxlength="50"
                />
            </div>
            
            <div class="form-row">
                <div class="area-selector" @click="openAreaPopup">
                    <div class="area-header">
                        <span v-if="selectedAreas.length === 0" class="placeholder">选择地区</span>
                        <span v-else class="selected-count">已选择 {{ selectedAreas.length }} 个地区</span>
                        <van-icon name="arrow-down" />
                    </div>
                    
                    <!-- 已选择的地区标签 -->
                    <div v-if="selectedAreas.length > 0" class="selected-areas">
                        <div 
                            v-for="(area, index) in selectedAreas" 
                            :key="index"
                            class="area-tag"
                        >
                            {{ area.provinceName }}·{{ area.cityName }}
                            <span class="remove-btn" @click.stop="removeArea(index)">×</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 地区选择弹窗 -->
            <van-popup v-model:show="showAreaPopup" round position="bottom">
                <div class="popup-header">
                    <span class="popup-title">选择地区</span>
                    <div class="header-buttons">
                        <van-button 
                            v-if="currentProvinceCities.length > 0" 
                            size="small" 
                            type="primary" 
                            plain 
                            @click="selectAllCities"
                        >
                            {{ isAllCitiesSelected ? '取消全选' : '全选' }}
                        </van-button>
                        <van-button size="small" @click="confirmAreaSelection">确定</van-button>
                    </div>
                </div>
                <van-tree-select
                    v-model:main-active-index="mainActiveIndex"
                    v-model:active-id="activeId"
                    :items="areaTreeSelectData"
                    @click-nav="handleAreaNavClick"
                    @click-item="handleAreaItemClick"
                />
            </van-popup>
            
            <div class="form-row">
                <van-button type="primary" size="small" @click="handleSearch">搜索</van-button>
                <van-button size="small" @click="handleReset">重置</van-button>
            </div>
        </div>
        
        <!-- 会话列表 -->
        <div class="session-items">
            <div 
                v-for="(item, index) in sessionList" 
                :key="index"
                class="session-item"
                @click="goToSessionDetail(item)"
            >
                <div class="session-content">
                    <div class="session-title">{{ item.question }}</div>
                    <div class="session-group">专属群：{{ item.qwRoomName }}</div>
                </div>
            </div>
        </div>

        <!-- 加载更多状态 -->
        <div v-if="loadingMore" class="loading-more">
            <van-loading size="16px">加载更多...</van-loading>
        </div>

        <!-- 没有更多数据 -->
        <div v-if="!hasMore && sessionList.length > 0" class="no-more">
            <div class="no-more-text">没有更多数据了</div>
        </div>

        <!-- 初始加载状态 -->
        <div v-if="loading" class="loading">
            <van-loading size="24px">加载中...</van-loading>
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && sessionList.length === 0" class="empty-state">
            <div class="empty-text">暂无会话数据</div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, onUnmounted, onActivated, reactive, computed } from 'vue';
import { Loading as VanLoading, Toast, Field as VanField, TreeSelect as VanTreeSelect, Button as VanButton, Popup as VanPopup, Icon as VanIcon } from 'vant';
import { useGroupSessionMobile } from '../hooks/useGroupSessionMobile';
import { CsSummaryAPI } from '@/api/cs-summary-api';
import useArea from '@/composables/area';

const { changeComponent, componentParams } = useGroupSessionMobile();
const { areaTree } = useArea();

// 接收父组件传递的props
const props = defineProps<{
    endDate: string;
    beginDate: string;
}>();

// 数据状态
const loading = ref(false);
const sessionList = ref<any[]>([]);
const pageSize = ref(10);
const offset = ref(0);
const hasMore = ref(true);
const loadingMore = ref(false);

// 搜索表单数据
const searchQuery = reactive({
    addressCityCodeList: [] as string[],
    addressProvinceCodeList: [] as string[],
    qwRoomNameKeyword: '',
});

// 地区选择器相关
const mainActiveIndex = ref(0);
const activeId = ref<string | string[]>([]);
const selectedAreas = ref<any[]>([]);
const showAreaPopup = ref(false);
// 临时存储弹窗中的选择状态
const tempActiveId = ref<string | string[]>([]);

// 当前选中省份的城市列表
const currentProvinceCities = computed(() => {
    if (areaTreeSelectData.value[mainActiveIndex.value]) {
        return areaTreeSelectData.value[mainActiveIndex.value].children || [];
    }
    return [];
});

// 检查当前省份的城市是否已全选
const isAllCitiesSelected = computed(() => {
    if (currentProvinceCities.value.length === 0) return false;
    
    const activeIds = Array.isArray(tempActiveId.value) ? tempActiveId.value : [];
    const allCityIds = currentProvinceCities.value.map((city: any) => city.id);
    
    return allCityIds.every((cityId: string) => activeIds.includes(cityId));
});

// 计算每个省份已选城市数量
const getProvinceSelectedCount = (provinceCode: string) => {
    const activeIds = Array.isArray(tempActiveId.value) ? tempActiveId.value : [];
    const province = areaTree.value.find((p: any) => p.code === provinceCode);
    if (!province) return 0;
    
    return province.children?.filter((city: any) => activeIds.includes(city.code)).length || 0;
};

// 处理地区数据，转换为 van-tree-select 需要的格式
const areaTreeSelectData = computed(() => areaTree.value.map((province: any) => {
    const selectedCount = getProvinceSelectedCount(province.code);
    
    return {
        text: province.name,
        id: province.code,
        badge: selectedCount > 0 ? selectedCount : undefined,
        children: province.children?.map((city: any) => {
            const isSelected = Array.isArray(tempActiveId.value) && tempActiveId.value.includes(city.code);
            return {
                text: city.name,
                id: city.code,
                parentId: province.code,
                dot: isSelected,
            };
        }) || [],
    };
}));

// 滚动位置记录
const scrollPosition = ref(0);

// 获取会话数据
const getSessionData = async (isLoadMore = false) => {
    if (isLoadMore) {
        loadingMore.value = true;
    } else {
        loading.value = true;
        offset.value = 0;
        sessionList.value = [];
        hasMore.value = true;
    }
    
    try {
        // 获取从上一个页面传递的分类参数
        const categoryInfo = componentParams.value;
        if (!categoryInfo?.tagId) return;
        
        const currentOffset = isLoadMore ? offset.value : 0;
        
        const res: any = await CsSummaryAPI.getChatListUsingPOST({
            endDate: props.endDate,
            startDate: props.beginDate,
            limit: pageSize.value,
            addressCityCodeList: searchQuery.addressCityCodeList,
            addressProvinceCodeList: searchQuery.addressProvinceCodeList,
            qwRoomNameKeyword: searchQuery.qwRoomNameKeyword,
            offset: currentOffset,
            tagId: categoryInfo.tagId,
        });
        
        const newData = res?.rows || [];
        
        if (isLoadMore) {
            sessionList.value = [...sessionList.value, ...newData];
        } else {
            sessionList.value = newData;
        }
        
        // 判断是否还有更多数据
        hasMore.value = newData.length === pageSize.value;
        
        // 更新偏移量
        offset.value = currentOffset + newData.length;
    } catch (error) {
        console.error('获取会话数据失败:', error);
        Toast.fail('获取会话数据失败');
    } finally {
        loading.value = false;
        loadingMore.value = false;
    }
};

// 加载更多数据
const loadMore = async () => {
    if (!hasMore.value || loadingMore.value) return;
    await getSessionData(true);
};

// 滚动监听
const handleScroll = () => {
    const scrollContainer = document.querySelector('.content-container') as HTMLElement;
    if (!scrollContainer) return;
    
    const scrollTop = scrollContainer.scrollTop;
    const scrollHeight = scrollContainer.scrollHeight;
    const clientHeight = scrollContainer.clientHeight;
    
    // 距离底部50px时触发加载更多
    if (scrollTop + clientHeight >= scrollHeight - 50) {
        loadMore();
    }
};

// 地区选择器事件处理
const handleAreaNavClick = (index: number) => {
    mainActiveIndex.value = index;
};

// 打开地区选择弹窗时初始化临时状态
const openAreaPopup = () => {
    // 初始化临时选择状态为当前已选择的城市 ID
    const currentCityIds = selectedAreas.value.map(area => area.cityCode);
    tempActiveId.value = currentCityIds;
    activeId.value = currentCityIds;
    showAreaPopup.value = true;
};

// 全选/取消全选当前省份下的所有城市
const selectAllCities = () => {
    if (currentProvinceCities.value.length === 0) return;
    
    const allCityIds = currentProvinceCities.value.map((city: any) => city.id);
    const currentActiveIds = Array.isArray(tempActiveId.value) ? [...tempActiveId.value] : [];
    
    if (isAllCitiesSelected.value) {
        // 如果已全选，则取消全选
        const newActiveIds = currentActiveIds.filter(id => !allCityIds.includes(id));
        tempActiveId.value = newActiveIds;
        activeId.value = newActiveIds;
    } else {
        // 如果未全选，则全选
        const newActiveIds = [...new Set([...currentActiveIds, ...allCityIds])];
        tempActiveId.value = newActiveIds;
        activeId.value = newActiveIds;
    }
};

const handleAreaItemClick = (item: any) => {
    // 如果点击的是城市，更新临时选择状态
    if (item.parentId) {
        const currentActiveIds = Array.isArray(tempActiveId.value) ? [...tempActiveId.value] : [];
        const itemIndex = currentActiveIds.indexOf(item.id);
        
        if (itemIndex === -1) {
            // 添加选择
            currentActiveIds.push(item.id);
        } else {
            // 取消选择
            currentActiveIds.splice(itemIndex, 1);
        }
        
        tempActiveId.value = currentActiveIds;
        activeId.value = currentActiveIds;
    }
};

// 确认地区选择
const confirmAreaSelection = () => {
    // 根据 tempActiveId 更新 selectedAreas
    const newSelectedAreas: any[] = [];
    const activeIds = Array.isArray(tempActiveId.value) ? tempActiveId.value : [];
    
    activeIds.forEach(cityId => {
        // 找到对应的省份和城市信息
        for (const province of areaTreeSelectData.value) {
            const city = province.children?.find((c: any) => c.id === cityId);
            if (city) {
                newSelectedAreas.push({
                    provinceCode: province.id,
                    provinceName: province.text,
                    cityCode: city.id,
                    cityName: city.text,
                });
                break;
            }
        }
    });
    
    selectedAreas.value = newSelectedAreas;
    updateAreaCodes();
    showAreaPopup.value = false;
};

// 更新地区代码
const updateAreaCodes = () => {
    searchQuery.addressProvinceCodeList = [...new Set(selectedAreas.value.map(area => area.provinceCode))];
    searchQuery.addressCityCodeList = [...new Set(selectedAreas.value.map(area => area.cityCode))];
};

// 移除选中的地区
const removeArea = (index: number) => {
    selectedAreas.value.splice(index, 1);
    updateAreaCodes();
};

// 搜索处理
const handleSearch = () => {
    getSessionData();
};

// 重置搜索
const handleReset = () => {
    searchQuery.qwRoomNameKeyword = '';
    searchQuery.addressCityCodeList = [];
    searchQuery.addressProvinceCodeList = [];
    selectedAreas.value = [];
    activeId.value = [];
    mainActiveIndex.value = 0;
    getSessionData();
};

// 跳转到会话详情
const goToSessionDetail = (item: any) => {
    const scrollContainer = document.querySelector('.content-container') as HTMLElement;
    scrollPosition.value = scrollContainer?.scrollTop || 0;
    changeComponent('SessionDetail', false, item);
};

// 监听日期变化，重新加载数据
watch(() => [props.endDate, props.beginDate], () => {
    getSessionData();
}, { immediate: false });

onMounted(() => {
    getSessionData();
    // 添加滚动监听
    const scrollContainer = document.querySelector('.content-container');
    if (scrollContainer) {
        scrollContainer.addEventListener('scroll', handleScroll);
    }
});

onUnmounted(() => {
    // 移除滚动监听
    const scrollContainer = document.querySelector('.content-container');
    if (scrollContainer) {
        scrollContainer.removeEventListener('scroll', handleScroll);
    }
});

// keep-alive 生命周期钩子
// 组件被激活时恢复滚动位置
onActivated(() => {
    // 使用 nextTick 确保 DOM 已更新
    setTimeout(() => {
        if (scrollPosition.value > 0) {
            const scrollContainer = document.querySelector('.content-container') as HTMLElement;
            if (scrollContainer) {
                scrollContainer.scrollTop = scrollPosition.value;
            }
        }
    }, 0);
});
</script>

<style scoped lang="scss">
.session-list {
    background-color: #f5f5f5;
    min-height: calc(100vh - 120px);

    .search-form {
        background-color: #fff;
        padding: 16px;
        margin-bottom: 8px;

        .form-row {
            margin-bottom: 12px;

            .van-field {
                border: 1px solid #dcdfe6;
                border-radius: 4px;
            }

            &:last-child {
                margin-bottom: 0;
                display: flex;
                gap: 12px;

                .van-button {
                    flex: 1;
                }
            }
        }

        .area-selector {
            padding: 12px 16px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            background-color: #fff;
            cursor: pointer;

            .area-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                width: 100%;

                .placeholder {
                    color: #c0c4cc;
                }

                .selected-count {
                    color: #666;
                    font-weight: 500;
                }
            }
        }

        .selected-areas {
            margin-top: 12px;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;

            .area-tag {
                display: flex;
                align-items: center;
                padding: 4px 8px;
                background-color: #f5f5f5;
                border: 1px solid #d9d9d9;
                border-radius: 4px;
                font-size: 12px;
                color: #666;

                .remove-btn {
                    margin-left: 4px;
                    cursor: pointer;
                    color: #909399;

                    &:hover {
                        color: #f56c6c;
                    }
                }
            }
        }
    }

    .popup-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px;
        border-bottom: 1px solid #ebedf0;

        .popup-title {
            font-size: 16px;
            font-weight: 500;
        }

        .header-buttons {
            display: flex;
            gap: 8px;
        }
    }

    .session-items {
        background-color: #fff;

        .session-item {
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background-color .2s;

            &:last-child {
                border-bottom: none;
            }

            &:active {
                background-color: #f8f8f8;
            }

            .session-content {
                .session-title {
                    font-size: 16px;
                    color: #333;
                    line-height: 1.5;
                    margin-bottom: 8px;
                    font-weight: 400;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                }

                .session-group {
                    font-size: 12px;
                    color: #999;
                    line-height: 1.4;
                }
            }
        }
    }

    .loading-more {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 16px 0;
        background-color: #fff;
        border-top: 1px solid #f0f0f0;
    }

    .no-more {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 16px 0;
        background-color: #fff;
        border-top: 1px solid #f0f0f0;

        .no-more-text {
            font-size: 12px;
            color: #999;
        }
    }

    .loading {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 40px 0;
        background-color: #fff;
    }

    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 60px 20px;
        background-color: #fff;

        .empty-text {
            font-size: 14px;
            color: #999;
            margin-top: 16px;
        }
    }
}
</style>