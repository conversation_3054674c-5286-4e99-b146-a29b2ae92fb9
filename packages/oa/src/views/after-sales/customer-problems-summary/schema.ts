/**
 * 格式化消息详情数据
 * @param item 原始消息项
 * @returns 格式化后的消息项
 */
export const formatMsgDetail = (item: any) => {
    if (item.msgDetail) {
        const msgDetail = JSON.parse(item.msgDetail);
    
        if (item.msgType === 'image') {
            item.msgImage = {
                ossUrl: item.msgMedia?.ossUrl,
            };
        } else if (item.msgType === 'voice') {
            item.msgVoice = {
                ossUrl: item.msgMedia?.ossUrl,
            };
        } else if (item.msgType === 'video') {
            item.msgVideo = {
                ossUrl: item.msgMedia?.ossUrl,
            };
        } else if (item.msgType === 'text') {
            item.msgText = {
                content: msgDetail.content,
            };
        }
    }
    
    return {
        ...item,
        senderName: item.sender.name,
    };
};