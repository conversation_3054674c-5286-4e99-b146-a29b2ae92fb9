import { useFormat } from '@/composables/date';
import { ElMessage } from 'element-plus';
import dayjs from 'dayjs';
import { ConversationApi } from '@/api/conversation-api';

export const querySchema = {
    type: 'object',
    component: 'QueryTable',
    name: '查询条件',
    actions: {
        search: {
            type: 'query',
            component: 'Button',
            loading: false,
            componentProps: {
                buttonText: '搜索',
                type: 'primary',
            },
        },
    },
    properties: {
        roomId: {
            label: '客户群名称',
            type: 'string',
            colSpan: 6,
            component: 'Select',
            componentProps: {
                placeholder: '请选择客户群名称',
                filterable: true,
                clearable: true,
            },
            selectOptions: [],
            // 数据源
            dataSource: {
                type: 'cloud-function',
                func: 'room-list',
            },
        },
        range: {
            label: '群消息时间范围',
            type: 'object',
            component: 'DateRangePicker',
            colSpan: 10,
            properties: {
                beginDate: {
                    type: 'date',
                },
                endDate: {
                    type: 'date',
                },
            },
            flatten: true,
            defaultValue: () => ({
                beginDate: useFormat(new Date(), 'YYYY-MM-DD HH:mm:ss', 'start'),
                endDate: useFormat(new Date(), 'YYYY-MM-DD HH:mm:ss', 'end'),
            }),
            componentProps: {
                startPlaceholder: '开始时间',
                endPlaceholder: '结束时间',
                type: 'datetimerange',
                format: 'YYYY-MM-DD HH:mm:ss',
                valueFormat: 'YYYY-MM-DD HH:mm:ss',
                minDate: dayjs().subtract(1, 'year').toDate(), // 最小时间
                maxDate: new Date(), // 最大时间
            },
        },
        keyword: {
            label: '消息关键词',
            type: 'string',
            colSpan: 6,
            component: 'Input',
            componentProps: {
                placeholder: '请输入沟通信息关键词',
            },
        },
    },
};
export const tableSchema = {
    // 数据源
    dataSource: async (formData: any) => {
        let res: any = {};
        try {
            res = await ConversationApi.getApiLowCodeConversationPageList(
                formData.offset,
                formData.limit,
                formData.keyword,
                formData.beginDate,
                formData.endDate,
                1,
                formData.roomId,
            );
        } catch (e: any) {
            ElMessage({
                type: 'error',
                message: e.message || e,
            });
        }
        if (res && res.rows) {
            return { rows: res.rows, total: res.total };
        }
        return [];
    },
    columns: [
        { prop: 'roomName', name: '客户群' },
        { prop: 'lastContactTime', name: '时间', slot: 'formatTime' },
        { prop: 'keywordHit', name: '关键词命中次数' },
        { prop: 'operate', slot: 'operate', name: '操作' },
    ],
    // 是否在查询条件就绪后立即请求数据
    requestWhenQueryPrepared: true,
    // 是否显示分页
    visiblePagination: true,
    // 是否在查询条件改变后请求数据
    requestWhenQueryChanged: true,
};
