import { RouteRecordRaw } from 'vue-router';

import AfterSales from './service-review/index.vue';
import GroupCommunicationArchive from './group-communication-archive/index.vue';
import Entry from './entry.vue';
import { PermissionAfterSalesView } from '@/views/after-sales/permission';

const Scheduling = () => import('@/views/after-sales/scheduling/index.vue');

export default [
    {
        path: 'after-sales',
        name: '@afterSales',
        component: Entry,
        redirect: {
            path: '/after-sales/service-review',
        },
        meta: {
            isEntry: true,
            name: '售后服务',
            icon: 'Service',
            roles: PermissionAfterSalesView,
        },
        children: [
            {
                path: 'service-review',
                name: '@afterSales/serviceReview',
                component: AfterSales,
                meta: {
                    name: '一对一服务审核',
                    icon: 'Files',
                },
            },
            {
                path: 'customer-problems-summary',
                name: '@afterSales/customerProblemsSummary',
                component: () => import('@/views/after-sales/customer-problems-summary/index.vue'),
                meta: {
                    name: '客户问题汇总',
                    icon: 'Platform',
                },
            },
            {
                path: 'group-communication-archive',
                name: '@afterSales/groupCommunicationArchive',
                component: GroupCommunicationArchive,
                meta: {
                    name: '客户群沟通存档',
                    icon: 'PhoneFilled',
                },
            },
            {
                path: 'scheduling',
                name: '@client-service/scheduling',
                component: Scheduling,
                meta: {
                    name: '实施排期看板',
                    icon: 'document-add',
                    hidden: true,
                },
            },
        ],
    },
] as RouteRecordRaw[];
