<script lang="ts" setup>

import { computed, onMounted, ref, watch } from 'vue';
import { formatDate } from '@/utils/utils';
import { useUserStore } from '@/store/user';
import { ElMessage, ElMessageBox } from 'element-plus';
import { SupportTicketAPI } from '@/api/support-ticket-api';
import { formatStatus, FROM_STATUS } from '@/views/after-sales/scheduling/model';
import { PermissionAfterSalesEdit } from '@/views/after-sales/permission';
import { SopOrderStatus } from '@/views/order/model/model';

const props = defineProps({
    visible: {
        type: Boolean,
        default: false,

    },
    occupyDialogData: {
        type: Object,
        default: () => ({}),
    },
    calendarDate: {
        type: [String, Date],
        default: '',
    },
});
const formData = ref<any>({
    reason: '',
});
const emit = defineEmits(['update:visible', 'refresh']);
const showVisible = computed({
    get: () => props.visible,
    set: (val) => {
        emit('update:visible', val);
    },
});
const editable = computed(() => {
    const userStore = useUserStore();
    const roles = userStore.roles;
    return PermissionAfterSalesEdit.some((role: string) => roles.includes(role));
});

// 对接事项
const formatDockingMatters: any = computed(() => {
    const { schedule, timeSlot } = props.occupyDialogData;
    if (timeSlot) {
        if (timeSlot?.occupy) {
            return `占用（${timeSlot.task?.items?.map((item: any) => item.name)?.join(', ')}）`;
        }

        return timeSlot.task?.items?.map((item: any) => item.name).join(', ');
    }
    // 超过3项现实等X项
    return schedule.taskList.map((task: any) => task.items.map((item: any) => item.name).join(', ')).join(', ');
});
// 对接人
const formatDockingPeople: any = computed(() => {
    const { timeSlot } = props.occupyDialogData;
    if (!timeSlot || timeSlot.occupy || (!timeSlot.task?.customerName && !timeSlot.task?.customerMobile)) {
        return '';
    }
    return `${timeSlot.task?.customerName}（${timeSlot.task?.customerMobile}）`;
});
// 对接状态
const formatDockingStatus: any = computed(() => {
    const { timeSlot } = props.occupyDialogData;
    if (!timeSlot) {
        return '';
    }

    return formatStatus(timeSlot.task?.status);
});
// 对接时间
const formatDockingTime: any = computed(() => {
    const { schedule, timeSlot } = props.occupyDialogData;
    const formattedDate = formatDate(schedule.scheduleDate, 'YYYY-MM-DD');

    if (!timeSlot) {
        const scheduleStrList: any[] = [];
        const scheduleList: any[] = [];
        schedule.scheduleList.forEach((item: any) => {
            scheduleStrList.push(`${formatDate(item.workBeginTime, 'HH:mm')}-${formatDate(item.workEndTime, 'HH:mm')}`);
            scheduleList.push({
                start: item.workBeginTime,
                end: item.workEndTime,
            });
        });
        return {
            dockStr: scheduleStrList.join('、'),
            times: scheduleList,
        };
    }

    const [start, end] = timeSlot.time?.split('-') || [];
    return {
        dockStr: `${timeSlot.time || ''}`,
        start: `${formattedDate} ${start}:00`,
        end: `${formattedDate} ${end}:00`,
    };
});
// 对接工程师
const formatDockingEngineer = computed(() => {
    if (!props.occupyDialogData.schedule?.implementerUser?.name && !props.occupyDialogData.schedule?.implementerUser?.mobile) {
        return '';
    }
    return `${props.occupyDialogData.schedule?.implementerUser?.name || ''}（${props.occupyDialogData.schedule?.implementerUser?.mobile || ''}）`;
});
const formatAppointmentUser = computed(() => `${props.occupyDialogData.timeSlot?.task?.creatorName || ''}（${
        props.occupyDialogData.timeSlot?.task?.creatorMobile || ''}）`);
const occupyLabel = computed(() => (props.occupyDialogData.timeSlot?.occupy || props.occupyDialogData.timeSlot?.appointment ? '释放' : '占用'));
const taskDisabled = computed(() => {
    const isFreed = occupyLabel.value === '释放';
    const isUnconfirmed = props.occupyDialogData.timeSlot?.task?.status === FROM_STATUS.UNCONFIRMED
        || props.occupyDialogData.timeSlot?.task?.status === FROM_STATUS.CONFIRMED;
    const isScheduled = props.occupyDialogData.timeSlot?.work;
    const isExpired = new Date(`${formatDate(props.calendarDate, 'YYYY-MM-DD')} ` + props.occupyDialogData.timeSlot?.time?.split('-')?.[1]) < new Date();

    // 无timeSlot 全占，释放且不是待实施，未排班，已过期
    return props.occupyDialogData.timeSlot && ((isFreed && !isUnconfirmed) || !isScheduled || isExpired);
});

const loading = ref(false);
/**
 * @description: 关闭弹窗
 * @date: 2024-07-26 10:12:32
 * @author: Horace
 * @return
*/
function handleDialogClose() {
    showVisible.value = false;
    formData.value.reason = '';
    dockingEngineerList.value = [];
}

/**
 * @description: 占用
 * @date: 2024-07-22 11:05:26
 * @author: Horace
 * @return
 */
async function handleOccupy() {
    const params = {
        appointReason: formData.value.reason,
        appointTimeList: formatDockingTime.value.times?.length
            ? formatDockingTime.value.times.map((item: any) => ({
                appointmentBeginTime: item.start,
                appointmentEndTime: item.end,
            }))
            : [
                {
                    appointmentBeginTime: formatDockingTime.value.start,
                    appointmentEndTime: formatDockingTime.value.end,
                },
            ],
        implementer: props.occupyDialogData.schedule?.implementerUser?.id || '',
    };
    loading.value = true;
    let res: any = {};
    try {
        res = await SupportTicketAPI.appointForDashboardUsingPUT(params);
    } catch (e: any) {
        ElMessage.error(e.message || e);
    } finally {
        loading.value = false;
    }

    if (res?.id) {
        handleDialogClose();
        emit('refresh');
        ElMessage.success('占用资源成功');
    }
}
/**
 * @description: 撤销占用
 * @date: 2024-07-22 13:59:50
 * @author: Horace
 * @param {any} timeSlot 工程师对象
 * @return
 */
async function revokeOccupy(timeSlot: any) {
    let res: any = {};
    loading.value = true;
    try {
        res = await SupportTicketAPI.revokeUsingPUT(timeSlot?.task?.id);
    } catch (e: any) {
        ElMessage.error(e.message || e);
    } finally {
        loading.value = false;
    }
    if (res.id) {
        handleDialogClose();
        emit('refresh');
        ElMessage.success('释放资源成功！');
    }
}
/**
 * @description: 弹窗提交
 * @date: 2024-07-26 10:16:51
 * @author: Horace
 * @return
*/
async function handleOccupySubmit() {
    ElMessageBox.confirm(
        occupyLabel.value === '占用' ? '占用后无法再排期此工程师的此段时间。请确认是否占用？' : '客户已确认此排期，取消后客户会收到取消通知。请确认是否取消？',
        '',
        {
            confirmButtonText: `确认${occupyLabel.value}`,
            showCancelButton: false,
        },
    ).then(() => {
        if (occupyLabel.value === '占用') {
            handleOccupy();
        } else {
            revokeOccupy(props.occupyDialogData.timeSlot);
        }
    });
}
const dockingEngineerList = ref<any[]>([]);
/**
 * @description: 获取对接工程师列表
 * @date: 2024-08-29 15:32:38
 * @author: Horace
 * @return
*/
async function fetchDockingEngineerList() {
    let res: any = {};
    loading.value = true;
    try {
        res = await SupportTicketAPI.listAvailableImplementerUsingGET(props.occupyDialogData.timeSlot.task.id);
    } catch (e: any) {
        console.log(e);
    } finally {
        loading.value = false;
    }
    dockingEngineerList.value = res.rows || [];
    isChangeDockingEngineer.value = !!res.rows?.length;
}
const query = ref<any>({
    dockingEngineerId: '',
});
const isChangeDockingEngineer = ref(false);
watch(() => props.occupyDialogData, (val) => {
    if (val?.timeSlot?.task?.id && val?.timeSlot?.appointment) {
        fetchDockingEngineerList();
    } else {
        dockingEngineerList.value = [];
    }
    query.value.dockingEngineerId = val?.schedule?.implementerUser?.id;
}, {
    immediate: true,
    deep: true,
});
async function handleChangeEngineerSubmit() {
    if (!query.value.dockingEngineerId) {
        ElMessage.error('请选择对接工程师');
        return;
    }
    let res: any = {};
    loading.value = true;
    try {
        res = await SupportTicketAPI.saveAppointUsingPOST({
            formId: props.occupyDialogData.timeSlot.task.id,
            implementer: query.value.dockingEngineerId,
        });
    } catch (e: any) {
        ElMessage.error(e.message || e);
    } finally {
        loading.value = false;
    }
    if (res.id) {
        handleDialogClose();
        emit('refresh');
        ElMessage.success('更换工程师成功');
    }
}
</script>
<template>
    <el-dialog
        v-model="showVisible"
        :width="600"
        center
        title="排期详情"
        custom-class="occupy-dialog"
        :before-close="handleDialogClose"
    >
        <p>对接门店：{{ occupyDialogData.timeSlot?.task?.clinicName || '' }}</p>
        <p>对接事项：{{ formatDockingMatters }}</p>
        <p>对接状态：{{ formatDockingStatus }}</p>
        <p>对接人：{{ formatDockingPeople }}</p>
        <p>对接日期：{{ occupyDialogData.schedule?.scheduleDate }}</p>
        <p>对接时间：{{ formatDockingTime.dockStr }}</p>
        <p>
            对接工程师：
            <span v-if="!dockingEngineerList?.length">
                {{ formatDockingEngineer }}
            </span>
            <el-select
                v-else
                v-model="query.dockingEngineerId"
                placeholder="请选择对接工程师"
            >
                <el-option
                    v-for="option in dockingEngineerList"
                    :key="option.id"
                    :label="option.name"
                    :value="option.id"
                />
            </el-select>
        </p>
        <p>安排人：{{ formatAppointmentUser }}</p>
        <p>安排时间：{{ occupyDialogData.timeSlot?.task?.created || '' }}</p>
        <el-divider></el-divider>
        <p class="flex-start">
            <span>备注：</span>
            <el-input
                v-model="formData.reason"
                style="width: 90%;"
                :disabled="taskDisabled || !editable"
                :placeholder="`请说明${occupyLabel}原因（最多100字）`"
                maxlength="100"
            ></el-input>
        </p>
        <template #footer>
            <div class="dialog-footer">
                <el-button
                    type="primary"
                    :loading="loading"
                    :disabled="taskDisabled || !editable"
                    @click="handleOccupySubmit"
                >
                    {{ occupyLabel }}
                </el-button>
                <el-button
                    v-if="isChangeDockingEngineer"
                    type="primary"
                    :loading="loading"
                    :disabled="taskDisabled || !editable || query.dockingEngineerId === occupyDialogData.schedule?.implementerUser?.id"
                    @click="handleChangeEngineerSubmit"
                >
                    保存
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>
