<script lang="ts" setup>

import { computed, inject, nextTick, ref, watch } from 'vue';
import { ClinicTicketAPI } from '@/api/clinic-ticket-api';
import { ElMessage } from 'element-plus/es';
import { Toast } from 'vant';
import ChatRecord from '@/components/chat-record/chat-record.vue';
import ReplyInput from '@/components/reply-input.vue';
import dayjs from 'dayjs';
import { useUserStore } from '@/store/user';
import { AfterSalesApi } from '@/api/after-sales-api';
import UserWorkspace = AbcAPI.UserWorkspace;
import _ from 'lodash';
import { NodeTypeFilter } from '@/vendor/x-form/types/model';
import { OrganListFunction } from '@/vendor/cloud-function';
import ClinicEmployeeListV2Function from '@/vendor/cloud-function/functions/clinic-employee-list-v2';
import {
    FROM_WAY, STATUS_LIST, PRODUCT_LIST,
    formatFromWay,
} from '@/views/after-ticket/constant';
import { formatHisTypeName } from '@/utils/format';
import { getClinicTypeDisplayName, getHisTypeDisplayName } from '@/utils/clinic';
import {
    APPID,
    formatTicketOrderType,
    TicketOrderTypeEnum,
    TicketOrderTypeOptions,
    TicketStatusEnum,
} from '@/utils/ticket';

const userStore = useUserStore();
const userInfo = userStore.userInfo;
const typeList = TicketOrderTypeOptions.filter(item => item.value !== TicketOrderTypeEnum.IMPLEMENTATION && item.value !== TicketOrderTypeEnum.SUPPORT);
const props = defineProps({
    visible: {
        type: Boolean,
        default: false,
    },
    ticketId: {
        type: String,
        default: '',
    },
    isCreatedDialog: {
        type: Boolean,
        default: true,
    },
    type: {
        type: Number,
        default: TicketOrderTypeEnum.BUG,
    },
    currentRow: {
        type: Object,
        default: () => {},
    },
});
const emit = defineEmits(['update:visible', 'refresh']);

const organList = ref<any[]>([]);
const typeOptions = ref<any[]>([]);
const dealerOptions = ref<any[]>([]);
const employeeList = ref<any[]>([]);
const isShowChat = ref(false);
const loading = ref(false);
const formRef = ref<any>(null);
const isWatchValue = ref(false);
const replyValue = ref<any>(null);
const chatInputValue = ref<any[]>([]);
const ticketData = ref<any>({
    clinicId: '',
    clinicName: '',
    employeeId: '',
    type: 0,
    hisType: '',
    title: '',
    feedbackTime: '',
    typeList: '',
    dealerId: '',
    changeType: '',
    changeDealerId: '',
});
watch(() => props.type, () => {
    ticketData.value.type = props.type;
}, {
    immediate: true,
});

const rules = computed(() => ({
    clinicId: [
        { required: true, message: '请选择反馈门店', trigger: 'change' },
    ],
    employeeId: [
        { required: true, message: '请选择反馈用户', trigger: 'change' },
    ],
    type: [
        { required: true, message: '请选择类型', trigger: 'change' },
    ],
    hisType: [
        { required: true, message: '请选择产品', trigger: 'change' },
    ],
    feedbackTime: [
        { required: true, message: '请选择反馈时间', trigger: 'change' },
    ],
    title: [
        { required: true, message: '请输入标题', trigger: 'blur' },
    ],
    typeList: [
        { required: true, message: '请选择分类', trigger: 'change' },
    ],
    dealerId: [
        { required: true, message: '请选择处理人', trigger: 'change' },
    ],
    changeType: [
        { required: ticketData.value.changeType !== '', message: '请选择类型', trigger: 'change' },
    ],
    changeDealerId: [
        { required: ticketData.value.changeType !== '', message: '请选择处理人', trigger: 'change' },
    ],
}));

const dialogVisible = computed({
    get() {
        return props.visible;
    },
    set(val) {
        emit('update:visible', val);
    },
});

const isDisabled = computed(() => props.currentRow?.clinicId !== ticketData.value?.clinicId
                                    || props.currentRow?.employeeId !== ticketData.value?.employeeId
                                    || props.currentRow?.status !== ticketData.value?.status
                                    || ticketData.value.changeType !== ''
                                    || ticketData.value.changeDealerId !== ''
                                    || chatInputValue.value.length > 0);
/**
 * @description: 查询Ticket详情
 * @date: 2023-11-24 09:39:40
 * @author: Horace
 * @param null:
 * @return
*/
const findDetailByTicketId = async () => {
    let res: any = {};
    try {
        res = await ClinicTicketAPI.getByIdUsingGET(props.ticketId);
    } catch (e: any) {
        ElMessage.error(e.message || e);
    }
    if (res.tagList) {
        const typeList = res.tagList.filter((item: any) => item.type).map((item: any) => item.id);
        res.typeList = typeList[0];
        res.tagList = res.tagList.map((item: any) => item.id);
    }
    Object.assign(ticketData.value, res);
    ticketData.value.isCreatedDialog = props.isCreatedDialog;
    fetchOrganList(ticketData.value.clinicName); // 详情回显反馈门店
    isShowChat.value = true; // 详情回显聊天记录
};
const loadMore = () => ({
    rows: ticketData.value.msgList || [],
});
/**
 * @description: 获取标签列表
 * @date: 2023-11-24 11:12:08
 * @author: Horace
 * @param null:
 * @return
*/
async function getTagList() {
    let res: any = {};
    try {
        res = await ClinicTicketAPI.tagListUsingGET();
    } catch (e: any) {
        ElMessage.error(`${e.message || e}`);
    }
    if (res && res.rows && res.rows.length) {
        const tagList = res.rows.filter((item: any) => !item.type).map((item: any) => ({
            label: item.name,
            value: item.id,
            id: item.id,
            disabled: item.id === '1',
        }));
        const typeList = res.rows.filter((item: any) => item.type).map((item: any) => ({
            label: item.name,
            value: item.id,
            id: item.id,
        }));
        typeOptions.value = typeList;
    }
}
const pagination = ref({
    page: 1,
    pageSize: 10,
    total: 0,
});
/**
 * @description: 弹窗关闭
 * @date: 2023-11-24 09:40:52
 * @author: Horace
 * @param null:
 * @return
*/
const onHandleCancel = () => {
    const wrapper = document.querySelector('.timeline-wrapper');
    if (wrapper) {
        wrapper.scrollTop = 0;
    }
    pagination.value.page = 1;
    pagination.value.total = 0;
    dialogVisible.value = false;
    ticketData.value = {};
    emit('refresh');
};
const corpList = inject<any>('corpList');
const tagName = computed(() => {
    const option = typeOptions.value.find((item) => item.value === ticketData.value.typeList);
    return option?.label || '';
});
const organInfo = computed(() => organList.value?.find((item) => item?.id === ticketData.value?.clinicId));
const curEmployeeInfo = computed(() => employeeList.value.find((item) => item.id === ticketData.value?.employeeId));
const title = computed(() => (props.isCreatedDialog ? '新建工单' : '工单详情'));
function getParams() {
    const clientId = props.isCreatedDialog ? '' : ticketData.value.clientId;
    const roomName = props.isCreatedDialog ? '' : ticketData.value.roomName;
    const openId = props.isCreatedDialog ? curEmployeeInfo.value.wechatOpenIdMp : ticketData.value.openId;
    const owner = props.isCreatedDialog ? ticketData.value.dealerId : ticketData.value.changeDealerId;
    const isChatAccount = props.isCreatedDialog ? 0 : ticketData.value.isChatAccount;
    const msgList = props.isCreatedDialog ? [] : ticketData.value.msgList;
    const qwCorpId = props.isCreatedDialog ? APPID : ticketData.value.qwCorpId;
    const conversationId = props.isCreatedDialog ? '' : ticketData.value.conversationId;
    const changeInputValue = props.isCreatedDialog ? chatInputValue.value : [...chatInputValue.value, ...ticketData.value.chatInputValueList];
    const fromWay = props.isCreatedDialog ? FROM_WAY.INSIDER : ticketData.value.fromWay;
    const type = props.isCreatedDialog ? ticketData.value.type : ticketData.value.changeType;
    const params = {
        clientId,
        clientName: curEmployeeInfo.value.name || '',
        roomName,
        mobile: curEmployeeInfo.value.mobile || '',
        employeeId: ticketData.value.employeeId || '',
        openId,
        clinicId: ticketData.value.clinicId || '',
        hisType: ticketData.value.hisType,
        edition: organInfo.value.editionName,
        question: ticketData.value.title,
        userId: userInfo.userId || '',
        qwUserId: userInfo.id?.toString() || '',
        qwUserName: userInfo.name || '',
        owner,
        dealerName: getDealerName(owner),
        tagList: ticketData.value.typeList ? [ticketData.value.typeList] : [],
        tagNameList: tagName.value ? [tagName.value] : [],
        isChatAccount,
        msgList,
        qwCorpId,
        feedbackTime: dayjs(ticketData.value.feedbackTime).format('YYYY-MM-DD HH:mm:ss'),
        conversationId,
        chatInputValue: changeInputValue,
        adminInfo: {
            name: organInfo.value.adminName,
            mobile: organInfo.value.adminMobile,
            openId: organInfo.value.adminOpenId,
        },
        type,
        fromWay,
    };
    return params;
}

async function handleOrderDelete(ticketId: string) {
    let res: any = {};
    try {
        res = await ClinicTicketAPI.deleteUsingDELETE(ticketId);
    } catch (e: any) {
        ElMessage.error(`${e.message || e}`);
    }
}

const saveOrUpdate = async () => {
    if (props.isCreatedDialog) {
        formRef.value.validate(async (valid: boolean) => {
            if (!valid) {
                return;
            }
            loading.value = true;
            const params = getParams();
            let res: any = {};
            try {
                res = await AfterSalesApi.postApiLowCodeTapd(params);
            } catch (e: any) {
                ElMessage.error(`${e.message || e}`);
            } finally {
                loading.value = false;
            }
            if (res?.ticket?.code === 200) {
                ElMessage({
                    message: '工单创建成功',
                    type: 'success',
                });
                onHandleCancel();
            }
        });
    } else if (ticketData.value.changeType === '') { //  不改变工单类型
        handleTicketUpdate(ticketData.value);
    } else {
        formRef.value.validate(async (valid: boolean) => {
            if (!valid) {
                return;
            }
            loading.value = true;
            const params = getParams();
            let res: any = {};
            try {
                res = await AfterSalesApi.postApiLowCodeTapd(params);
            } catch (e: any) {
                ElMessage.error(`${e.message || e}`);
            } finally {
                loading.value = false;
            }
            if (res?.ticket?.code === 200) {
                ElMessage({
                    message: '工单转类型成功',
                    type: 'success',
                });
                await handleOrderDelete(ticketData.value.id);
                onHandleCancel();
            }
        });
    }
};
/**
 * @description: 编辑ticket数据
 * @date: 2023-11-24 14:24:47
 * @author: Horace
 * @param formData: ticket数据
 * @return
*/
const handleTicketUpdate = async (formData: any) => {
    try {
        loading.value = true;
        let res: any = {};
        res = await ClinicTicketAPI.updateUsingPUT(props.ticketId, formData);
        if (res && res.code === 200) {
            ElMessage.success('Ticket编辑成功！');
            onHandleCancel();
        } else {
            ElMessage.error('Ticket编辑失败！');
        }
    } catch (e) {
        console.log(e);
    } finally {
        loading.value = false;
    }
};

const handleChatInputChange = (nodes: any[]) => {
    console.log('输入框的值变化', nodes);
    isWatchValue.value = false;
    chatInputValue.value = [];
    if (nodes && nodes.length) {
        nodes.forEach((node: any) => {
            console.log(node);
            let msg: AbcAPI.QwConversationMsg = {
                origin: 1,
                msgTime: dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss'),
            };
            if (node.type === 'text') {
                msg.msgType = 'text';
                msg.msgText = {
                    content: node.content.trim(),
                };
            } else if (node.type === 'image') {
                msg.msgType = node.type;
                msg.msgImage = {
                    ossUrl: node.url,
                };
            } else if (node.type === 'video') {
                msg.msgType = node.type;
                msg.msgVideo = {
                    ossUrl: node.url,
                };
            } else if (node.type === 'File') {
                msg.msgType = 'file';
                msg.msgFile = {
                    ossUrl: node.url,
                    filename: node.fileName,
                };
            } else { return; }
            chatInputValue.value.push(msg);
        });
        console.log('chatInputValue', chatInputValue.value);
    }
};
const replyInputRef = ref<any>(null);
const chatRecordRef = ref<any>(null);
const handleChatSend = async () => {
    console.log('按了 Enter 键');
    let res: any = {};
    try {
        res = await ClinicTicketAPI.saveMsgUsingPOST(ticketData.value.id, {
            msgList: chatInputValue.value,
        });
    } catch (e: any) {
        ElMessage.error(e.message || e);
    }
    console.log(res);
    if (res && res.code === 200) {
        await findDetailByTicketId();
        chatRecordRef.value?.resetPageQuery();
        replyInputRef.value?.clean();
        chatInputValue.value = [];
    }
};
watch(() => ticketData.value.clinicId, async (newVal, oldVal) => {
    if (newVal) {
        let res: any = {};
        try {
            res = await ClinicEmployeeListV2Function.exec({
                chainId: organInfo.value?.chainId || ticketData.value?.chainId,
                clinicId: ticketData.value.clinicId,
            });
        } catch (e: any) {
            console.error(e.message || e);
        }
        if (!res.status) {
            console.error('ClinicEmployeeListV2Function Response', res.message);
        }
        res.data.rows?.forEach((item: any) => {
            item.name = `${item.name} / ${item.mobile}${item.wechatNickName ? ` / ${item.wechatNickName}` : ''}`;
        });
        employeeList.value = res.data.rows;
    }
},
{
    deep: true,
});
async function fetchEmployeeList() {
    let res: any = {};
    try {
        res = await ClinicEmployeeListV2Function.exec({ chainId: organInfo.value?.chainId, clinicId: ticketData.value.clinicId });
    } catch (e: any) {
        console.error(e.message || e);
    }
    if (!res.status) {
        console.error('ClinicEmployeeListV2Function Response', res.message);
    }
    res.data.rows?.forEach((item: any) => {
        item.name = `${item.name} / ${item.mobile}${item.wechatNickName ? ` / ${item.wechatNickName}` : ''}`;
    });
    employeeList.value = res.data.rows;
}
async function fetchOrganList(keyword: string) {
    if (keyword === undefined) return;
    let res: any = {};
    const params = {
        keyword,
        nodeTypeFilter: NodeTypeFilter.NO_FILTER,
        isTrial: 0,
    };
    try {
        res = await OrganListFunction.exec(params);
    } catch (e: any) {
        console.error(e.message || e);
    }
    if (!res.status) {
        console.error('getOrganList Response', res.message);
    }
    const { rows = [] } = res.data;

    organList.value = rows.map((row: any) => ({
        label: row.shortName || row.name,
        value: row.id,
        ...row,
        clinicTypeName: getClinicTypeDisplayName(row),
        hisTypeName: getHisTypeDisplayName(row),
        chainName: row.chainName,
    }));
}
const _debounceSearch = _.debounce(fetchOrganList, 500);

const workspaceUsers = ref<any[]>([]);
/**
 * @description: 获取tapd人员信息列表
 * @date: 2023-11-21 09:54:59
 * @author: Horace
 * @param null:
 * @return
 */
async function getTapdWorkSpacesUsers() {
    let res: any = {};
    try {
        res = await AfterSalesApi.getApiLowCodeTapdWorkspaceUsers();
    } catch (e: any) {
        ElMessage.error(e.message || e);
    }
    dealerOptions.value = res?.map((item: UserWorkspace) => {
        if (props.isCreatedDialog && userInfo.name === (item.name || item.user)) {
            ticketData.value.dealerId = item.user;
        }
        return {
            label: item.name || item.user,
            value: item.user,
        };
    });
    workspaceUsers.value = res;
}
const getDealerName = (dealerId: string) => {
    const option = workspaceUsers.value.find((item: any) => item.user === dealerId);
    if (option) {
        return option.name || option.user;
    }
    return '';
};
watch(() => props.visible, async () => {
    if (props.visible) {
        ticketData.value.isCreatedDialog = props.isCreatedDialog;
        !props.isCreatedDialog && props.ticketId && await findDetailByTicketId();
        await nextTick();
        await getTagList();
        await getTapdWorkSpacesUsers();
    }
}, {
    immediate: true,
});
const disabledDate = (time: any) => time.getTime() > new Date().getTime();
const clearEmployee = () => {
    ticketData.value.employeeName = '';
};
const linkJump = () => {
    if (ticketData.value.tapdLick) {
        window.open(ticketData.value.tapdLick);
    }
};

const clinicChange = (value: any) => {
    if (value) {
        ticketData.value.clinicName = organInfo.value?.name || '';
        ticketData.value.hisType = organInfo.value?.hisType === 0 ? 0 : (organInfo.value?.hisType || '');
    } else {
        ticketData.value.employeeId = '';
        ticketData.value.employeeName = '';
        ticketData.value.clinicName = '';
        ticketData.value.hisType = '';
        employeeList.value = [];
        organList.value = [];
    }
};
</script>
<template>
    <el-dialog
        v-model="dialogVisible"
        v-loading="loading"
        width="60%"
        append-to-body
        :title="title"
        :before-close="onHandleCancel"
        :custom-class="`${ isCreatedDialog ? 'is-created' : ''} ticket-management-detail-wrapper`"
        :close-on-press-escape="false"
        destroy-on-close
        @close="onHandleCancel"
    >
        <div class="left">
            <el-form
                v-if="isCreatedDialog"
                ref="formRef"
                :rules="rules"
                :model="ticketData"
                style="width: 600px;"
                label-width="100px"
                label-position="left"
            >
                <el-row>
                    <el-col :span="11">
                        <el-form-item label-width="2px" label=" " prop="clinicId">
                            <el-select
                                v-model="ticketData.clinicId"
                                placeholder="反馈门店"
                                style="width: 100%;"
                                filterable
                                remote
                                clearable
                                :remote-method="_debounceSearch"
                                @change="clinicChange"
                            >
                                <el-option
                                    v-for="item in organList"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id"
                                >
                                    <span>{{ item?.label }}</span>
                                    <van-tag style="margin-left: 4px;" type="primary">{{ item?.clinicTypeName }}</van-tag>
                                    <van-tag style="margin-left: 4px;" color="#36b090">{{ item?.hisTypeName }}</van-tag>
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="2"></el-col>
                    <el-col :span="11">
                        <el-form-item label-width="2px" label=" " prop="employeeId">
                            <el-select
                                v-model="ticketData.employeeId"
                                placeholder="反馈用户"
                                style="width: 100%;"
                                filterable
                                clearable
                                @clear="clearEmployee"
                            >
                                <el-option
                                    v-for="item in employeeList"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="类型" prop="type">
                            <el-radio-group v-model="ticketData.type">
                                <el-radio
                                    v-for="(item, index) in typeList"
                                    :key="index"
                                    :label="item.value"
                                >
                                    {{ item.label }}
                                </el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="产品" prop="hisType">
                            <el-select
                                v-model="ticketData.hisType"
                                placeholder="请选择产品"
                                style="width: 100%;"
                                filterable
                                clearable
                                disabled
                            >
                                <el-option
                                    v-for="item in PRODUCT_LIST"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="标题" prop="title">
                            <el-input v-model="ticketData.title" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="反馈时间" prop="feedbackTime">
                            <el-date-picker
                                v-model="ticketData.feedbackTime"
                                type="datetime"
                                format="YYYY-MM-DD HH:mm:ss"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                :disabled-date="disabledDate"
                                placeholder="请选择反馈时间"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="分类" prop="typeList">
                            <el-select
                                v-model="ticketData.typeList"
                                placeholder="请选择问题分类"
                                style="width: 100%;"
                                filterable
                                clearable
                            >
                                <el-option
                                    v-for="item in typeOptions"
                                    :key="item.id"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="处理人" prop="dealerId">
                            <el-select
                                v-model="ticketData.dealerId"
                                placeholder="请选择处理人"
                                style="width: 100%;"
                                filterable
                                clearable
                            >
                                <el-option
                                    v-for="(item, index) in dealerOptions"
                                    :key="index"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="补充信息" prop="chatInputValue">
                            <reply-input
                                ref="replyInputRef"
                                :chat-input-value="chatInputValue"
                                :show-submit-btn="false"
                                placeholder="粘贴文字/截图/视频等，方便研发更高效定位问题"
                                :editor-style="{width: '100%'}"
                                :is-watch-value="isWatchValue"
                                :enter="true"
                                @change="handleChatInputChange"
                            >
                            </reply-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-form
                v-else
                ref="formRef"
                label-width="100px"
                :rules="rules"
                :model="ticketData"
            >
                <el-form-item v-if="ticketData.code" label="工单号">
                    {{ ticketData.code }}
                    <el-select
                        v-model="ticketData.status"
                        :disabled="currentRow.type !== TicketOrderTypeEnum.SUPPORT || currentRow.status !== TicketStatusEnum.PROCESSING"
                        :class="{ 'normalSelect': true, 'blueSelect': ticketData.status === TicketStatusEnum.PROCESSING }"
                    >
                        <el-option
                            v-for="item in STATUS_LIST"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-if="formatTicketOrderType(ticketData.type)" label="工单类型">
                    {{ formatTicketOrderType(ticketData.type) }}
                </el-form-item>
                <el-form-item label="反馈门店" prop="clinicId">
                    <el-select
                        v-model="ticketData.clinicId"
                        placeholder="反馈门店"
                        style="width: 100%;"
                        filterable
                        remote
                        clearable
                        :remote-method="_debounceSearch"
                        @change="clinicChange"
                    >
                        <el-option
                            v-for="item in organList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                            <span>{{ item?.label }}</span>
                            <van-tag style="margin-left: 4px;" type="primary">{{ item?.clinicTypeName }}</van-tag>
                            <van-tag style="margin-left: 4px;" color="#36b090">{{ item?.hisTypeName }}</van-tag>
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="反馈用户" prop="employeeId">
                    <el-select
                        v-model="ticketData.employeeId"
                        placeholder="反馈用户"
                        style="width: 100%;"
                        filterable
                        clearable
                        @clear="clearEmployee"
                    >
                        <el-option
                            v-for="item in employeeList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-if="ticketData.title" label="标题">
                    {{ ticketData.title || '' }}
                </el-form-item>
                <el-form-item label="产品">
                    {{ formatHisTypeName(ticketData.hisType) }}
                </el-form-item>
                <el-form-item label="来源">
                    {{ formatFromWay(ticketData.fromWay) }}
                </el-form-item>
                <el-form-item label="创建人">
                    {{ ticketData.createByName }}
                </el-form-item>
                <el-form-item v-if="ticketData.dealerName" label="处理人">
                    {{ ticketData.dealerName }}
                </el-form-item>
                <el-form-item v-if="ticketData.tapdLick" label="TAPD">
                    <el-link :underline="false" type="primary" @click="linkJump">查看对应TAPD单据</el-link>
                </el-form-item>
                <el-divider v-if="currentRow.type === TicketOrderTypeEnum.SUPPORT && currentRow.status === TicketStatusEnum.PROCESSING" />
                <el-form-item
                    v-if="currentRow.type === TicketOrderTypeEnum.SUPPORT && currentRow.status === TicketStatusEnum.PROCESSING"
                    label="转工单类型"
                    prop="changeType"
                >
                    <el-radio-group v-model="ticketData.changeType">
                        <el-radio
                            v-for="(item, index) in typeList"
                            :key="index"
                            :label="item.value"
                        >
                            {{ item.label }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item
                    v-if="currentRow.type === TicketOrderTypeEnum.SUPPORT && currentRow.status === TicketStatusEnum.PROCESSING"
                    label="处理人"
                    prop="changeDealerId"
                >
                    <el-select
                        v-model="ticketData.changeDealerId"
                        placeholder="请选择处理人"
                        style="width: 100%;"
                        filterable
                        clearable
                    >
                        <el-option
                            v-for="(item, index) in dealerOptions"
                            :key="index"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item
                    v-if="currentRow.type === TicketOrderTypeEnum.SUPPORT && currentRow.status === TicketStatusEnum.PROCESSING"
                    label="补充信息"
                    prop="chatInputValue"
                >
                    <reply-input
                        ref="replyInputRef"
                        :chat-input-value="chatInputValue"
                        :show-submit-btn="false"
                        placeholder="粘贴文字/截图/视频等，方便研发更高效定位问题"
                        :editor-style="{width: '100%'}"
                        :is-watch-value="isWatchValue"
                        :enter="true"
                        @change="handleChatInputChange"
                    >
                    </reply-input>
                </el-form-item>
            </el-form>
        </div>

        <el-divider v-if="!isCreatedDialog" direction="vertical" style="height: auto;" />

        <chat-record
            v-if="!isCreatedDialog && isShowChat"
            ref="chatRecordRef"
            height="74vh"
            :data-provider="loadMore"
            :is-paginate="false"
            :infinite-scroll-delay="300"
            @dialogCancel="onHandleCancel"
        ></chat-record>
        <template #footer>
            <div class="dialog-footer">
                <el-button
                    type="primary"
                    plain
                    :loading="loading"
                    :disabled="isCreatedDialog ? false : !isDisabled"
                    @click="saveOrUpdate"
                >
                    保存
                </el-button>
                <el-button @click="dialogVisible = false">关闭</el-button>
            </div>
        </template>
    </el-dialog>
</template>
<style lang="scss">
.ticket-management-detail-wrapper {
    .blueSelect {
        .el-input__inner {
            color: #409eff !important;
        }
    }

    .normalSelect {
        width: 90px;
        margin-left: 5px;

        .el-input__inner {
            font-weight: bold;
        }
    }

    .el-input {
        width: 100% !important;
    }

    .el-input.is-disabled .el-input__inner {
        color: #606266;
    }

    &.is-created {
        width: 600px;

        .left {
            width: 100%;
            min-width: 100%;
        }
    }

    .ticket-management-detail-title {
        .el-input__wrapper {
            border-radius: unset;
            box-shadow: var(--el-input-border-color) 0 1px 0, rgba(255, 255, 255, .25) 0 1px 0 inset;
        }
    }

    .left {
        width: 50%;
        min-width: 50%;
    }

    .el-dialog__body {
        display: flex;
        overflow-x: auto;
    }

    .left {
        flex-direction: column;
    }

    // .reply-input-wrapper {
    //     width: 100%;
    //     min-width: 100%;
    //     margin-top: 14px;
    // }

    .chat-record-wrapper {
        flex: auto;
    }
}
</style>
