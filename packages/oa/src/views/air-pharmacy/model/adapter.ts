import { ApprovalTicketAPI } from '@/api/approval-ticket-api';
import OrderSummaryCreateClinic from '@/views/air-pharmacy/components/order-summary-create-clinic.vue';

import {
    OrderType,
    ClinicOrderSummary,
} from '@/views/air-pharmacy/model/model';
import {
    formSchema as createFormSchema,
} from '@/views/air-pharmacy/model/schema';
import { ApprovalTicketShareAPI } from '@/api/approval-ticket-share-api';

export const strategyMap = {
    [OrderType.PREVENTION_PRESCRIPTION_USING]: {
        async fetch(id: string, isShare: false) {
            const rsp = isShare
                ? await ApprovalTicketShareAPI.getApprovalTicketViewPreventionPrescriptionUsingGET_1(id)
                : await ApprovalTicketAPI.getApprovalTicketViewPreventionPrescriptionUsingGET(id);
            const {
                requestContent,
                payAttachments = [],
                payModeName,
            } = rsp;

            const formSchema = {
                ...createFormSchema,
                properties: {
                    ...createFormSchema.properties,
                    receivableFee: {
                        label: '收款金额',
                        type: 'string',
                        component: 'Input',
                    },
                    attachments: {
                        label: '转账截图',
                        type: 'array',
                        component: 'Images',
                    },
                    payModeName: {
                        label: '支付方式',
                        type: 'string',
                        component: 'Input',
                    },
                },
                readonly: true,
                actions: null,
            };
            const formData = {
                clinicId: requestContent!.clinicId,
                type: requestContent!.type,
                doseCount: requestContent!.doseCount,
                suppliersFee: requestContent!.suppliersFee,
                // 应收费用
                receivableFee: requestContent!.receivableFee,
                // 收款账户id (字节流 1, 字节星球 2)
                receiveAccountId: requestContent!.receiveAccountId,
                attachments: requestContent!.attachments || [],
                remark: requestContent!.remark,
                payAttachments: payAttachments || [],
                payModeName,
            };
            return {
                formSchema,
                formData,
                orderInfo: {
                    id: rsp.id,
                    clinicName: requestContent!.clinicName,
                    status: rsp.status,
                    statusName: rsp.statusName,
                    created: rsp.created,
                    createdByName: rsp.createdByName,
                    orderType: rsp.approvalId,
                    formatOrderType: '其他-开放平台接口费',
                    formatOrderDetail: `${requestContent!.doseCount}剂`,
                    title: rsp.title,
                    currentTicketFlow: rsp.currentTicketFlow,
                    allowActions: rsp.allowActions,
                    receivableFee: requestContent!.receivableFee,
                    payAttachments: payAttachments || [],
                },
            };
        },
        convert2OrderSummaryItem(raw: AbcAPI.ApprovalTicketListItemView) {
            // @ts-ignore
            const { request = {} } = raw.content;
            const requestContent:any = request;
            return {
                orderInfo: <ClinicOrderSummary>{
                    id: raw.id,
                    title: raw.title,
                    created: raw.created,
                    createdByName: raw.createdByName,
                    statusName: raw.statusName,
                    status: raw.status,
                    clinicId: requestContent.clinicId,
                    clinicName: requestContent.clinicName,
                    doseCount: requestContent.doseCount,
                    receivableFee: raw.receivableFee,
                    // 收款账户id (字节流 1, 字节星球 2)
                    receiveAccountId: requestContent.receiveAccountId,
                    // 备注
                    remark: requestContent.remark,
                    // 供应商结算金额: 不能为空
                    suppliersFee: requestContent.suppliersFee,
                    // 收费项目 (1 饮片代煎; 2 饮片; 3 颗粒;), 不能为空
                    type: requestContent.type,
                    orderType: OrderType.PREVENTION_PRESCRIPTION_USING,
                },
                component: OrderSummaryCreateClinic,
            };
        },
        submitRouteName: '@air-pharmacy/create',
        isNeedPay: true,
    },
};

export async function getOrderDetail(id: string, orderType: OrderType, isShare = false) {
    const strategy = strategyMap[orderType];
    if (strategy) {
        // @ts-ignore
        return strategy.fetch(id, isShare);
    }
}

export function convert2OrderSummaryItem(raw: AbcAPI.ApprovalTicketListItemView) {
    const strategy = strategyMap[<OrderType>raw.approvalId];
    if (strategy) {
        return strategy.convert2OrderSummaryItem(raw);
    }
}

export function getSubmitRouteName(orderType: OrderType) {
    const strategy = strategyMap[orderType];
    if (strategy) {
        return strategy.submitRouteName;
    }
}

/**
 * 订单是否需要支付
 * @param orderType
 */
export function isOrderNeedPay(orderType: OrderType) {
    const strategy = strategyMap[orderType];
    if (strategy) {
        return strategy.isNeedPay;
    }
}
