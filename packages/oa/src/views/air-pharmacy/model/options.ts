/*
 * <AUTHOR>
 * @DateTime 2020-09-01 17:53:19
 */
// 订单范围

export const orderScopeOptions = [
    { text: '全部订单', value: 0 },
    { text: '我的订单', value: 1 },
];

// 订单状态
export const orderStatusOptions = [
    { text: '全部状态', value: null },
    { text: '待付款', value: 0 },
    { text: '已付款', value: 10 },
    { text: '已取消', value: 90 },
];

// 支付方式
export const payModeOptions = [
    { value: 100, label: '线下支付' },
    { value: 1, label: '预存款支付' },
    { value: 2, label: '微信' },
    { value: 3, label: '支付宝' },
];

// 订单类型
export const orderTypeOptions = [
    { value: 1, label: '首购' },
    { value: 2, label: '续费' },
    { value: 3, label: '升级' },
];

// 经营方式
export const businessModeOptions = [
    { value: 0, label: '普通单店' },
    { value: 1, label: '连锁总店' },
    { value: 2, label: '连锁子店' },
];
