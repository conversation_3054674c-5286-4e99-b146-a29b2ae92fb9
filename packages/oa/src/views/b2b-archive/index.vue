<script setup lang="ts">
import { querySchema, tableSchema } from './schema';
import QueryTable from '@/components/query-table.vue';
import B2bArchiveDetail from './detail/detail.vue';
import { computed, onMounted, ref } from 'vue';
import { Form } from '@/vendor/x-form/core/form';
import { useFormat } from '@/composables/date';
import { useRoute } from 'vue-router';

onMounted(async () => {
    linkToDetail();
});

let formControl: Form;

const route = useRoute();
const conversationId = computed(() => route.query?.conversationId as string || '');
/**
 * 从链接跳转到详情
 */
const linkToDetail = () => {
    if (conversationId.value) {
        const data = {
            id: conversationId.value,
        };
        onHandleView({}, data);
    }
};

/**
 * 筛选项已就绪
 * @param form 筛选项值
 */
const handlePrepared = (form: Form) => {
    formControl = form;
};

const visibleDetailDialog = ref(false);
const currentRows = ref<any>({});

/**
 * 查看详情
 * @param column
 * @param row
 */
const onHandleView = (column: any, row: any) => {
    currentRows.value = row || {};
    visibleDetailDialog.value = true;
};

const queryTableRef = ref();
/**
 * 刷新数据
 */
const refreshData = () => {
    if (!queryTableRef.value) {
        return;
    }
    queryTableRef.value.debounceQuery(formControl.formData);
};

</script>
<template>
    <div class="after-sales-wrapper">
        <query-table
            ref="queryTableRef"
            :table-schema="tableSchema"
            :query-schema="querySchema"
            @prepared="handlePrepared"
        >
            <template #operate="{column, row}">
                <el-button @click="onHandleView(column, row)">详情</el-button>
            </template>
            <template #formatTime="{column, row}">
                {{ useFormat(row[column.prop],'YYYY-MM-DD HH:mm:ss') }}
            </template>
        </query-table>
        <b2b-archive-detail
            v-model:visible="visibleDetailDialog"
            :conversation-id="currentRows.id"
            @refresh="refreshData"
        ></b2b-archive-detail>
    </div>
</template>
<style lang="scss">
.after-sales-wrapper {
    .el-col .el-form-item .el-form-item__content {
        .el-input {
            width: 80%;
        }

        .el-date-editor--daterange {
            flex: .8;
        }
    }
}
</style>
