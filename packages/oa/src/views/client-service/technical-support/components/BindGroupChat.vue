<script setup lang="ts">
import { ref, nextTick, watch } from 'vue';
import { vendorModel } from '@abc-oa/common';
import { copy } from '@/utils/dom';
import { ElMessage } from 'element-plus';
import { Dialog } from 'vant';
import { InfoFilled, OfficeBuilding, CopyDocument, Setting } from '@element-plus/icons-vue';
import { CsChatAPI } from '@/api/cs-chat-api';
import { agentJsConfig } from '@/utils/wx';
import { AbcResponse } from '@abc-oa/social';
import OaOrganSelect from '@abc-oa/components/src/oa-organ-select.vue';
import { useRoomChatHook } from '@/views/client-service/technical-support/hook/roomChat';
import { useSupportEmployeeHook } from '@/views/client-service/technical-support/hook/employee';

const {
    roomId,
    boundOrganList,
    isChatAccount,
} = useRoomChatHook();

const {
    employeeInfo,
} = useSupportEmployeeHook();

// 改用数组存储所有绑定的连锁
const loadingBoundOrgan = ref(false);
// 控制是否显示添加新连锁的选择器
const showChainSelector = ref(false);

const chainBindingPopupVisible = ref(false);

const roomName = ref('');

const organSelectRef = ref<any>(null);

const loading = ref(false);

watch(() => roomId.value, () => {
    fetchBoundOrganList();
});
/**
 * 获取当前会话绑定的门店列表
 */
async function fetchBoundOrganList() {
    if (!roomId.value) return;

    loadingBoundOrgan.value = true;
    try {
        const res: any = await CsChatAPI.getQwBindOrganListUsingGET(roomId.value as string);
        roomName.value = res?.innerName || '';
        if (res && res.organList && res.organList.length > 0) {
            // 存储所有绑定的连锁
            boundOrganList.value = res.organList || [];
        } else {
            boundOrganList.value = [];
        }
    } catch (e: any) {
        console.error('获取绑定门店列表失败', e);
        ElMessage.error(e.message || '获取绑定门店列表失败');
        boundOrganList.value = [];
    } finally {
        loadingBoundOrgan.value = false;
    }
}

/**
 * 解绑指定连锁
 * @param organId 要解绑的连锁ID
 */
async function handleUnbindOrgan(organId: string) {
    if (!organId || !roomId.value) return;

    loadingBoundOrgan.value = true;
    try {
        // 使用类型断言来解决类型不匹配问题
        await CsChatAPI.unbindQwChatOrganUsingPOST({
            qwChatId: roomId.value,
            organId,
        } as any);
        ElMessage.success('解绑成功');
        fetchBoundOrganList();
    } catch (e: any) {
        console.error('解绑门店失败', e);
        ElMessage.error(e.message || '解绑门店失败');
    } finally {
        loadingBoundOrgan.value = false;
    }
}
/**
 * 处理连锁选择器选择事件
 * 先添加企业内部人员到群聊，成功后再绑定连锁
 */
async function handleOrganSelected(organId: any, organ: any) {
    console.log('organId', organId);
    if (!organId) return;
    Dialog.confirm({
        message: `是否将门店 ${organ?.label || ''} 绑定至当前专属群？`,
        title: '提示',
        confirmButtonText: '继续',
        cancelButtonText: '取消',
    })
                    .then(() => {
                        bindOrgan(organId);
                    })
                    .catch(() => {
                        // 用户取消操作
                        console.log('用户取消了连锁选择');
                    });
}
async function bindOrgan(organId: any) {
    loading.value = true;
    try {
        const { status, data } = await getOrganServiceUserIds(organId);
        if (status && data) {
            await addUsersToEnterpriseChat(String(roomId.value), data);
        }
        await bindOrganToChat(organId);
    } catch (e: any) {
        console.error('处理连锁选择事件失败', e);
        ElMessage.error(e.message || '处理失败');
    } finally {
        loading.value = false;
        // 选择完成后关闭选择器
        showChainSelector.value = false;
    }
}
/**
 * 添加企业内部人员到企业微信群聊
 * @param chatId 企业微信群聊ID
 * @param userIds 需要添加的企业内部成员ID列表，格式为userid1;userid2;...
 */
async function addUsersToEnterpriseChat(chatId: string, userIds: string): Promise<boolean> {
    if (!chatId || !userIds) {
        return false;
    }
    try {
        const { status, message, data } = await agentJsConfig([
            'updateEnterpriseChat',
        ]);
        console.log('agentConfig', status, message, data);
        if (status === false) {
            console.error('agentConfig 失败', message, data);
        }
    } catch (e: any) {
        console.error('agentConfig 异常', e);
        return false;
    }
    return new Promise((resolve) => {
        wx.invoke('updateEnterpriseChat', {
            chatId,
            userIdsToAdd: userIds,
        }, (res: any) => {
            if (res.err_msg == 'updateEnterpriseChat:ok') {
                console.log('添加企业内部人员成功', res);
                resolve(true);
            } else if (res.err_msg?.includes('晓鹏') || res.err_msg?.includes('cancel')) {
                console.error('添加企业内部人员失败', res);
                resolve(false);
            } else {
                console.error('添加企业内部人员失败', res);
                resolve(true);
            }
        });
    });
}

/**
 * 获取连锁对应的客服人员用户ID列表
 * @param organId 连锁ID
 * @returns 客服人员用户ID，格式为userid1;userid2;...
 */
async function getOrganServiceUserIds(organId: string) {
    try {
        const res = await CsChatAPI.getCreateChatInfoUsingPOST({
            organId,
            qwChatId: roomId.value as string,
        });

        if (res && Array.isArray(res.toAddMembers) && res.toAddMembers.length > 0) {
            // 将用户ID列表格式化为分号分隔的字符串
            const userIds = res.toAddMembers.filter((item:any) => item.type === 1).map((staff: any) => staff.userId).join(';');
            return AbcResponse.success(userIds);
        }
        return AbcResponse.success('');
    } catch (e: any) {
        console.error('获取连锁客服人员失败', e);
        ElMessage.error(e.message || e);
        return AbcResponse.error('获取连锁客服人员失败');
    }
}
/**
 * 处理连锁绑定的核心逻辑
 */
async function bindOrganToChat(organId: string) {
    try {
        // 使用正确的绑定接口 bindQwChatOrganUsingPOST
        await CsChatAPI.bindQwChatOrganUsingPOST({
            qwChatId: roomId.value,
            organId,
        } as any);

        ElMessage.success('绑定成功');
        nextTick(() => {
            // 接受 oa-organ-select 导出的 setBlur 方法
            organSelectRef.value?.setBlur();
        });
        // 刷新绑定连锁列表
        await fetchBoundOrganList();
        return true;
    } catch (e: any) {
        console.error('绑定连锁失败', e);
        ElMessage.error(e.message || '绑定连锁失败');
        return false;
    }
}
/**
 * 复制会话ID，如果OrganInfo中有门店信息，也一并复制
 */
function copyRoomId() {
    if (!roomId.value) return;

    try {
        // 获取基础的会话ID文本
        let copyText = `会话ID: ${roomId.value}`;

        // 如果有门店信息，也添加到复制内容中
        if (employeeInfo.value?.clinic?.id) {
            const organInfoList = [
                { label: '门店名称', value: employeeInfo.value.clinic?.name },
                { label: '门店区域', value: employeeInfo.value.clinic?.addressAll },
                { label: '运行环境', value: employeeInfo.value.clinic?.envName },
                { label: '试用门店', value: employeeInfo.value.clinic?.isTrial ? '是' : '否' },
                { label: '医保开通', value: employeeInfo.value.clinic?.isTrial ? '已开通' : '未开通' },
                { label: '门店ID', value: employeeInfo.value.clinic?.id },
                { label: '总部ID', value: employeeInfo.value.clinic?.parentId },
                { label: '管理员',
                    value: employeeInfo.value.clinic?.admin?.name && employeeInfo.value.clinic?.admin?.mobile
                        ? `${employeeInfo.value.clinic?.admin?.name}（${employeeInfo.value.clinic?.admin?.mobile}）` : '' },
                { label: 'openId', value: employeeInfo.value.clinic?.admin?.wechatOpenIdMp },
                { label: '客户经理', value: employeeInfo.value.clinic?.sellerName },
                { label: '公众号ID', value: employeeInfo.value.clinic?.mpId },
                { label: '小程序ID', value: employeeInfo.value.clinic?.weappId },
            ];

            // 添加门店信息
            const organText = organInfoList.map(item => `${item.label}: ${item.value || ''}`).join('\n');
            copyText = `${copyText}\n\n${organText}`;
        }

        copy(copyText);
        ElMessage.success('信息已复制');
    } catch (err) {
        console.error('复制失败:', err);
        ElMessage.error('复制失败，请手动复制');
    }
}

/**
 * 复制专属群名称
 */
function copyRoomName() {
    try {
        copy(roomName.value);
        ElMessage.success('专属群名称复制成功！');
    } catch (err) {
        console.error('复制失败:', err);
        ElMessage.error('复制失败，请手动复制');
    }
}
</script>
<template>
    <div class="bind-group-chat-wrapper">
        <div
            v-if="isChatAccount"
            class="customer-header"
        >
            <div class="header-left">
                <span v-if="!boundOrganList.length" class="text-gray-400">当前专属群暂未绑定任何门店</span>
                <span v-else>当前专属群已绑定{{ boundOrganList.length }}家门店</span>
            </div>
            <div class="header-right">
                <el-button
                    type="primary"
                    style="width: 100px;"
                    class="group-setting-btn"
                    @click="chainBindingPopupVisible = true"
                >
                    <span style="margin-left: 4px;">绑定门店</span>
                </el-button>
            </div>
        </div>

        <!-- 连锁绑定弹窗 -->
        <van-popup
            v-model:show="chainBindingPopupVisible"
            class="technical-support-popup-wrapper chain-binding-popup-wrapper"
            safe-area-inset-bottom
            round
            closeable
        >
            <div class="technical-support-popup-title mb-3">
                <el-icon class="setting-title-icon"><Setting /></el-icon>
                专属群设置
            </div>
            <!-- 添加会话ID到弹窗中，显示复制图标 -->
            <div v-if="roomId" class="room-info-popup-container">
                <span class="room-info-popup-label">专属群ID:</span>
                <span class="room-info-popup cursor-pointer" @click="copyRoomId">
                    <span class="room-id-text">{{ roomId }}</span>
                    <el-icon class="copy-icon"><CopyDocument /></el-icon>
                </span>

                <span class="room-info-popup-label">专属群名称:</span>
                <span class="room-info-popup cursor-pointer" @click="copyRoomName">
                    <span class="room-id-text">{{ roomName }}</span>
                    <el-icon v-if="roomName" class="copy-icon"><CopyDocument /></el-icon>
                </span>
                <span v-if="roomName" class="room-info-popup-tips">请尽快设置专属群名称为此名称！</span>
            </div>
            <div class="chain-binding-content">
                <!-- 已绑定连锁列表 -->
                <div v-if="boundOrganList.length > 0" class="bound-organ-list">
                    <div
                        v-for="organ in boundOrganList"
                        :key="organ.id"
                        class="bound-organ-container"
                    >
                        <div class="bound-organ-icon">
                            <el-icon><OfficeBuilding /></el-icon>
                        </div>
                        <div class="bound-organ-info">
                            <div class="bound-organ-name">{{ organ.name }}</div>
                            <div class="bound-organ-address">{{ organ.address }}</div>
                        </div>
                        <el-button
                            class="unbind-button"
                            size="small"
                            type="danger"
                            :loading="loadingBoundOrgan"
                            @click="handleUnbindOrgan(organ.id)"
                        >
                            解绑
                        </el-button>
                    </div>
                </div>
                <el-empty v-else description="暂无绑定连锁" />

                <!-- 选择器组件，点击添加按钮后显示 -->
                <div class="new-chain-selector">
                    <div class="organ-select-wrapper">
                        <oa-organ-select
                            ref="organSelectRef"
                            style="width: 100%;"
                            placeholder="请选择连锁"
                            show-chain
                            show-address
                            show-administrator
                            :node-type-filter="vendorModel.NodeTypeFilter.NO_FILTER"
                            @change="handleOrganSelected"
                        >
                        </oa-organ-select>
                    </div>
                </div>
            </div>

            <!-- 底部按钮区域 -->
            <div class="technical-support-popup-footer">
                <el-button
                    round
                    type="primary"
                    :disabled="!roomName"
                    @click="copyRoomName"
                >
                    复制专属群名称
                </el-button>
            </div>
        </van-popup>
    </div>
</template>
<style lang="scss">
@use '@/style/mixins/mixins.scss' as mixins;

.bind-group-chat-wrapper {
    margin: 16px 16px 0;

    .customer-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;

        .header-left {
            font-size: var(--el-font-size-base);

            .room-id-simple {
                display: flex;
                align-items: center;

                .room-id-simple-label {
                    font-size: 13px;
                    color: #909399;
                    margin-right: 4px;
                }

                .room-id-simple-value {
                    font-size: 13px;
                    color: #303133;
                    font-weight: 500;
                    max-width: 120px;

                    @include mixins.text-no-wrap;
                }
            }
        }

        .header-right {
            margin-left: auto;
        }

        .group-setting-btn {
            border-radius: 4px;
            padding: 8px 12px;
        }
    }

    .organ-select-card {
        margin-bottom: 10px;
        background-color: white;
        border-radius: 6px;
        box-shadow: 0 1px 6px 0 rgba(0, 0, 0, .03);
        overflow: hidden;
    }

    .organ-select-header {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        border-bottom: 1px solid #f0f0f0;
    }

    .organ-select-title {
        font-weight: 500;
        font-size: 13px;
        color: #606266;
        flex: 0 0 auto;
        margin-right: 10px;
    }

    .room-id-container {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        background-color: white;
        border-radius: 6px;
        box-shadow: 0 1px 6px 0 rgba(0, 0, 0, .03);
        margin-bottom: 10px;
    }

    .room-id {
        font-size: 12px;
        color: #909399;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: flex;
        align-items: center;
    }

    .cursor-pointer {
        cursor: pointer;
    }

    .copy-icon {
        font-size: 14px;
        margin-left: 4px;
    }

    .organ-select-content {
        padding: 10px;
    }

    .bound-organ-list {
        display: flex;
        flex-direction: column;
        gap: 6px;
        box-shadow: 0 4px 6px -4px rgba(0, 0, 0, .1);
        padding-bottom: 10px;
        margin-bottom: 10px;
    }

    .bound-organ-list {
        max-height: 300px;
        overflow-y: auto;
    }

    .bound-organ-container {
        display: flex;
        align-items: center;
        background-color: #f5f7fa;
        padding: 4px;
        border-radius: 6px;
        transition: all .3s;

        &:hover {
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
        }
    }

    .add-chain-button-container {
        display: flex;
        justify-content: center;
        margin-top: 16px;
        margin-bottom: 10px;

        .add-chain-btn {
            width: 160px;
            border-radius: 20px;
        }
    }

    .new-chain-selector {
        margin-top: 10px;
        padding: 10px;
        background-color: #f9f9f9;
        border-radius: 4px;
        border: 1px solid #ebeef5;
    }

    .chain-selector-footer {
        display: flex;
        justify-content: flex-end;
        margin-top: 10px;
        gap: 10px;
    }

    .bound-organ-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        background-color: #ecf5ff;
        color: #409eff;
        border-radius: 50%;
        margin-right: 12px;
        box-shadow: 0 2px 6px rgba(0, 149, 255, .15);
    }

    .bound-organ-info {
        flex: 1;
        overflow: hidden;
    }

    .bound-organ-name {
        font-weight: 500;
        font-size: 14px;
        color: #303133;

        @include mixins.text-no-wrap;

        margin-bottom: 4px;
    }

    .bound-organ-address {
        margin-top: 2px;
        font-size: 11px;
        color: #909399;
    }

    .unbind-button {
        margin-left: 8px;
        padding: 6px 12px;
        height: auto;
        font-size: 12px;
        border-radius: 16px;
    }

    .organ-select-wrapper {
        width: 100%;
    }
}

.oa-organ-select-popper {
    .el-select-dropdown__item {
        padding: 0 16px;

        .option-content {
            margin-right: 0;

            .el-space {
                .el-space__item:last-child {
                    margin-right: 0 !important;
                }
            }
        }
    }
}

.chain-binding-popup-wrapper.technical-support-popup-wrapper {
    .technical-support-popup-title {
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;

        .setting-title-icon {
            margin-right: 8px;
            font-size: 18px;
            color: #409eff;
        }
    }

    .technical-support-popup-footer {
        display: flex;
        justify-content: center;
        padding: 10px 16px 0;
        border-top: 1px solid #ebeef5;
        background-color: #fff;
    }

    .chain-binding-content {
        padding: 16px;
    }

    .room-info-popup-container {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        background-color: #f5f7fa;
        padding: 10px 16px;
        margin-bottom: 16px;
        border-radius: 6px;

        .room-info-item {
            display: flex;
            align-items: center;
            width: 100%;
            margin-top: 8px;
        }

        .room-info-popup-tips {
            width: 100%;
            margin-top: 8px;
            font-size: 12px;
            color: var(--el-color-warning);
        }
    }

    .room-info-popup-label {
        font-size: 13px;
        color: #606266;
        margin-right: 8px;
        font-weight: 500;
        white-space: nowrap;
    }

    .room-info-popup {
        font-size: 13px;
        color: #409eff;
        display: flex;
        align-items: center;
        flex: 1;
        white-space: nowrap;
        justify-content: space-between;

        .room-id-text {
            flex: 1;
            font-size: 12px;
            color: #606266;
        }

        .copy-icon {
            margin-left: 8px;
            cursor: pointer;
            color: #909399;

            &:hover {
                color: #409eff;
            }
        }
    }
}

</style>
