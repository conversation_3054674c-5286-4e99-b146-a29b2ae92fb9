<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import _ from 'lodash';
import OaCellGroup from '@/components/oa-cell-group.vue';
import OaCell from '@/components/oa-cell.vue';
import { KfAPI } from '@/api/kf-api';
import { useUserStore } from '@/store/user';
import { AfterSalesApi } from '@/api/after-sales-api';
import { agentJsConfig } from '@/utils/wx';
import { isMobile } from '@/utils/ua';
import { Toast } from 'vant';
import { useSupportEmployeeHook } from '@/views/client-service/technical-support/hook/employee';
import { useRoomChatHook } from '@/views/client-service/technical-support/hook/roomChat';
import { useAfterSalesChatHook } from '@/views/client-service/technical-support/hook/afterSalesChat';

const recommendList = ref<any[]>([]);
const currentCustomerNews = ref<string>();
const selectChat = ref<string>();
const loading = ref(false);
const userStore = useUserStore();
const userInfo = userStore.userInfo;

const { roomId, isChatAccount } = useRoomChatHook();
const { getApiLowCodeAfterSales, getConversationDetail, getOriginBySender } = useAfterSalesChatHook();
const { externalUserId } = useSupportEmployeeHook();

const activeOpenKF = ref<any>({});

onMounted(async () => {
    await getScheduleUsingGET();
});

const APPID = import.meta.env.VITE_APP_WW_APP_ID;
const roomRecordList = ref<any[]>([]);
const conversationId = ref<string>();
/**
 * @description: 获取近10条会话记录
 * @date: 2023-11-26 09:36:49
 * @author: Horace
 * @return
 */
async function getRecordList() {
    let res: any = {};
    if (isChatAccount.value) {
        res = await AfterSalesApi.getApiLowCodeAfterSalesRoomListByLimit(
            <string>roomId.value,
            APPID,
            10,
        );
    } else if (externalUserId.value) {
        conversationId.value = await getApiLowCodeAfterSales(externalUserId.value, userInfo.userId || '');
        if (conversationId.value) {
            res = await getConversationDetail(conversationId.value);
            res = res.reverse();
        }
    }
    let rows = Array.isArray(res) ? res : res.rows || [];
    rows = rows?.filter(
        (item: any) => (item.origin === 3 || !getOriginBySender(item.sender)) && (item.msgType === 'text' || item.msgType === 'voice'),
    )?.slice(0, 10)?.map((item: any) => ({
        label: item.msgText?.content || '',
        value: item.msgText?.content || '',
        sender: item.sender,
        senderName: item.senderName,
    })) || [];
    roomRecordList.value = rows;

    selectChat.value = roomRecordList.value[0]?.value;
    !isChatAccount.value && await getGptAnswerForServicerUsingGET();
}
watch([() => externalUserId.value, roomId], (newVal) => {
    (newVal[0] || roomId.value) && getRecordList();
}, {
    immediate: true,
});
/**
 * @description: 会话记录选择变化
 * @date: 2023-11-26 10:43:51
 * @author: Horace
 * @return
 */
function handleRoomRecordChange() {
    if (isChatAccount.value) {
        return;
    }
    getGptAnswerForServicerUsingGET();
}
/**
 * @description: 获取客户首次进入会话，最后一句问题的答案
 * @date: 2023-07-18 09:40:11
 * @author: Horace
 * @return
 */
async function getCustomerFirstEnter() {
    if (!externalUserId.value || !activeOpenKF.value?.openKfid || !userInfo.userId) {
        return;
    }
    let res: any = {};
    loading.value = true;
    try {
        res = await KfAPI.getGptAnswerForCustomerFirstEnterUsingGET(
            <string>externalUserId.value,
            activeOpenKF.value?.openKfid,
            userInfo.userId,
        );
    } catch (e: any) {
        Toast.fail(e.message || e);
        return;
    } finally {
        loading.value = false;
    }
    if (res && res.answers) {
        currentCustomerNews.value = res.question;
        recommendList.value = res.answers?.slice(0, 6);
    }
}
/**
 * @description:为接待员获取用户问题的答案
 * @date: 2023-07-18 10:06:47
 * @author: Horace
 * @return
 */
async function getGptAnswerForServicerUsingGET() {
    if (loading.value) {
        return;
    }
    let res: any = {};
    loading.value = true;
    try {
        let question = (currentCustomerNews.value || selectChat.value)?.trim() || '';
        question = question.replace(/\[/g, '%5B').replace(/\]/g, '%5D');

        if (isChatAccount.value) {
            res = await KfAPI.getGptAnswerForServicerUsingGET(question);
        } else {
            res = await KfAPI.getOneByOneCustomerServiceChannelSidebarUsingGET(conversationId.value as string);
        }
    } catch (e: any) {
        Toast.fail(e.message || e);
    } finally {
        loading.value = false;
    }
    if (res && res.answers) {
        recommendList.value = res.answers;
    }
}
const _debounceSearch = _.debounce(getGptAnswerForServicerUsingGET, 500);
/**
 * @description: 处理回车键
 * @date: 2024-06-13 15:55:54
 * @author: Horace
 * @param {any} e - 事件
 * @return
 */
function handleEnterKey(e: any) {
    if (e.keyCode === 13) {
        e.preventDefault(); // 阻止浏览器默认换行操作
        _debounceSearch();
    }
}

const showActionSheet = ref(false);
const currentRecommendItem = ref<any>({});
/**
 * @description: 处理取消按钮点击
 * @date: 2024-06-13 15:56:09
 * @author: Horace
 * @return
 */
function handleRecommendItemCancelClick() {
    showActionSheet.value = false;
    currentRecommendItem.value = {};
}

/**
 * @description: 发送消息到回话框
 * @date: 2023-07-18 10:29:36
 * @author: Horace
 * @return
 */
async function sendRecommendToWeChat() {
    const { status, message, data } = await agentJsConfig([
        'sendChatMessage',
    ]);
    if (status === false) {
        console.error('agentConfig 失败', message, data);
    }
    wx.invoke('sendChatMessage', {
        msgtype: 'text', // 消息类型，必填
        enterChat: true, // 为true时表示发送完成之后顺便进入会话，仅移动端3.1.10及以上版本支持该字段
        text: {
            content: formatKg(currentRecommendItem.value.kg), // 文本内容
        },
    }, (res: any) => {
        if (res.err_msg == 'sendChatMessage:ok') {
            // 发送成功
            handleRecommendItemCancelClick();
            handleRecommendItemSubmit();
            console.log('发送成功');
        }
    });
}
/**
 * @description: 当客服选择答案时提交问题用作记录
 * @date: 2023-07-18 13:42:31
 * @author: Horace
 * @return
 */
async function handleRecommendItemSubmit() {
    if (!activeOpenKF.value?.openKfid || !externalUserId.value) {
        return;
    }
    const postData: AbcAPI.SaveQuestionFromCustomerReq = {
        // 外部联系人id
        externalUserid: externalUserId.value,
        // 客服账号id
        openKfid: activeOpenKF.value?.openKfid,
        // 客户问题
        question: encodeURIComponent((currentCustomerNews.value || selectChat.value)?.trim() || ''),
        // 接待人员id
        servicerUserid: userInfo.userId,
    };
    try {
        await KfAPI.saveQuestionFromCustomerUsingPOST(
            postData,
        );
    } catch (e: any) {
        Toast.fail(e.message || e);
    }
}
/**
 * @description: 处理推荐回复点击
 * @date: 2024-06-13 15:56:45
 * @author: Horace
 * @param {any} item - 推荐回复
 * @param {number} index - 推荐回复索引
 * @return
 */
function handleRecommendItemClick(item: any, index: number) {
    currentRecommendItem.value = item;
    if (isMobile) {
        showActionSheet.value = true;
        currentRecommendItem.value.index = index;
    } else {
        sendRecommendToWeChat();
    }
}

const accountList = ref<any[]>([]);
/**
 * @description: 查询客户人员排班客户通道列表
 * @date: 2023-07-18 09:32:14
 * @author: Horace
 * @return
 */
async function getScheduleUsingGET() {
    let res: any = {};
    try {
        res = await KfAPI.getScheduleUsingGET(
            userInfo.userId,
        );
    } catch (e: any) {
        Toast.fail(e.message || e);
    }
    if (res && res.rows) {
        accountList.value = res.rows;
        activeOpenKF.value = res.rows?.[0];
    }
}

/**
 * @description: 格式化换行
 * @date: 2024-06-13 15:57:14
 * @author: Horace
 * @param {any} item - 推荐回复
 * @return
 */
function formatKg(item: any) {
    return item.replace(/(\r\n|\n|\r|↵)/gm, '\n');
}
/**
 * @description: 处理ActionSheet关闭
 * @date: 2024-06-13 15:57:25
 * @author: Horace
 * @return
 */
function handleActionSheetClose() {
    const content = document.querySelector('.van-action-sheet__content');
    if (content) {
        content.scrollTop = 0;
    }
}
function handleFilterSearch(queryString: string, cb: any) {
    cb(roomRecordList.value);
}
</script>
<template>
    <div class="intelligent-recommend-wrapper">
        <oa-cell-group label-width="70px" cell-height="40px">
            <oa-cell
                label-width="0px"
                class="session0-selection"
            >
                <el-space>
                    <el-autocomplete
                        v-model="selectChat"
                        :fetch-suggestions="handleFilterSearch"
                        select-when-unmatched
                        clearable
                        placeholder="请选择或输入获取回复推荐"
                        @select="handleRoomRecordChange"
                        @blur="handleRoomRecordChange"
                    >
                    </el-autocomplete>
                    <el-button
                        v-if="isChatAccount"
                        type="primary"
                        @click="getGptAnswerForServicerUsingGET"
                    >
                        获取推荐
                    </el-button>
                </el-space>
            </oa-cell>
            <oa-cell v-if="!isChatAccount && accountList.length > 1" label="客服通道" class="customer-account">
                <el-select
                    v-model="activeOpenKF"
                    value-key="openKfid"
                    placeholder="请选择客服通道"
                    @change="_debounceSearch"
                >
                    <el-option
                        v-for="item in accountList"
                        :key="item.openKfid"
                        :label="item.openKfName"
                        :value="item"
                    ></el-option>
                </el-select>
            </oa-cell>
            <div v-if="recommendList?.length" v-loading.lock="loading" class="recommend-wrapper">
                <oa-cell
                    v-for="(item, index) in recommendList"
                    :key="item.id"
                    class="recommend-item-wrapper"
                    @click="handleRecommendItemClick(item, index + 1)"
                    v-html="`
                        <div>
                            <span><span style='font-size: 16px;color: #1bad1b;'>${index + 1}、</span>${formatKg(item.kg)}</span>
                        <div/>
                        `"
                >
                </oa-cell>
            </div>
            <van-empty
                v-else
                v-loading="loading"
                image="https://fastly.jsdelivr.net/npm/@vant/assets/custom-empty-image.png"
                image-size="80"
                description="无智能回复推荐"
            />
            <van-action-sheet
                v-model:show="showActionSheet"
                :title="`推荐回复（${currentRecommendItem.index || 1}）`"
                @close="handleActionSheetClose"
            >
                <div class="recommend-action-sheet-content">
                    <el-input
                        v-model="currentRecommendItem.kg"
                        type="textarea"
                        autosize
                    ></el-input>
                    <div class="recommend-action-sheet-btns flex-between">
                        <el-button
                            size="large"
                            type="primary"
                            @click="sendRecommendToWeChat"
                        >
                            {{ isMobile ? '回复客户' : '复制到发送框' }}
                        </el-button>
                        <el-button size="large" @click="handleRecommendItemCancelClick">取消</el-button>
                    </div>
                </div>
            </van-action-sheet>
        </oa-cell-group>
    </div>
</template>
<style lang="scss">
@use "src/style/mixins/mixins" as mixins;

.intelligent-recommend-wrapper {
    height: calc(100% - 64px);
    overflow-y: auto;
    margin: 0 16px;
    box-shadow: 0 0 12px var(--oa-background-color);
    z-index: 1002;
    position: sticky;
    top: 20px;

    .session0-selection {
        .oa-cell__value-wrapper {
            flex: 1;

            .el-space {
                width: 100%;
            }

            .el-space__item:first-child {
                flex: 1;
            }

            .el-button {
                padding: 8px;
            }
        }
    }

    .customer-news-cell {
        height: auto !important;
        padding: 8px 0;

        .oa-cell__value-wrapper {
            flex: 1;
        }
    }

    .customer-account {
        .oa-cell__value-wrapper {
            flex: 1;

            .el-select {
                width: 100%;
            }
        }
    }

    .recommend-box-label {
        border-bottom: none;
    }

    .recommend-action-sheet-content {
        padding: var(--oa-padding-12);
        margin-bottom: 56px;

        .recommend-action-sheet-btns {
            width: calc(100% - var(--oa-padding-12) * 2);
            background: var(--van-popup-background-color);
            padding: var(--oa-padding-12) 0;
            box-shadow: 0 4px  0  var(--oa-gray-4);
            position: fixed;
            bottom: env(safe-area-inset-bottom);

            .el-button {
                width: 100%;
            }
        }
    }

    .recommend-wrapper {
        height: 400px;
        overflow-y: auto;
    }

    .recommend-item-wrapper {
        align-items: flex-start;
        -webkit-box-align: start;

        & > div {
            white-space: pre-line;
        }

        &:hover {
            background: #eaf1f1;
        }

        white-space: pre-wrap !important;
        min-height: 24px !important;
        height: auto !important;

        .oa-cell__value-wrapper {
            margin-left: 0;
            width: 100%;
        }
    }

    .recommend-item-wrapper + .recommend-item-wrapper {
        margin-top: 12px;
    }
}
</style>
