<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';

import { Search } from '@element-plus/icons-vue';
import { CorpAPI } from '@/api/corp-api';
import { agentJsConfig } from '@/utils/wx';

import { useSupportEmployeeHook } from '@/views/client-service/technical-support/hook/employee';

const searchKey = ref('');
const categoryId = ref('');

const goodsRecommendList = ref<any[]>([]);
const goodsTypeList = ref<any[]>([]); const recommendId = ref('');
// 获取商品类型
const fetchCategory = async () => {
    try {
        const data = await CorpAPI.getGoodsSkuCategoryListUsingGET();
        goodsTypeList.value = data?.rows || [];
    } catch (e) {
        console.log('获取商品类型失败', e);
    }
};

// 获取推荐类型
const fetchRecommend = async () => {
    try {
        const data = await CorpAPI.getGoodsSkuRecommendListUsingGET1();
        goodsRecommendList.value = data?.rows || [];
    } catch (e) {
        console.log('获取商品推荐失败', e);
    }
};

const goodsList = ref<any[]>([]);
const loading = ref(false);
const totalCount = ref(0);
const fetchParams = ref({
    offset: 0,
    limit: 10,
    keyword: '',
    categoryId: '',
    recommendId: '',
});

const { employeeInfo } = useSupportEmployeeHook();
const clinicId = computed(() => employeeInfo.value.clinic?.id);
watch(() => employeeInfo.value?.clinic?.id, (val) => {
    if (val) {
        fetchParams.value.offset = 0;
        goodsList.value = [];
        fetchData();
    }
});
// 获取商品列表
const fetchGoodsList = async () => {
    try {
        if (!clinicId.value) {
            return;
        }
        const { rows, total } = await CorpAPI.getGoodsSkuRecommendListUsingGET(
            clinicId.value,
            fetchParams.value.categoryId,
            fetchParams.value.keyword,
            fetchParams.value.limit,
            fetchParams.value.offset,
            fetchParams.value.recommendId,
        );
        goodsList.value = goodsList.value.concat(rows || []);
        totalCount.value = total || 0;
    } catch (e) {
        console.log('获取商品列表失败', e);
    }
};
const fetchData = async () => {
    try {
        loading.value = true;
        await fetchGoodsList();
    } catch (e) {
        console.log('获取推荐商品失败', e);
    } finally {
        loading.value = false;
    }
};
onMounted(() => {
    fetchCategory();
    fetchRecommend();
    fetchData();
});

const handleChangeParams = async () => {
    fetchParams.value.offset = 0;
    goodsList.value = [];
    await fetchData();
};

const loadMore = async () => {
    if (fetchParams.value.offset + fetchParams.value.limit < totalCount.value) {
        fetchParams.value.offset += fetchParams.value.limit;
        await fetchData();
    }
};

const handleSearch = async () => {
    fetchParams.value.offset = 0;
    goodsList.value = [];
    fetchParams.value.keyword = searchKey.value;
    await fetchData();
};

const handleSend = async (item: any) => {
    const { status, message, data } = await agentJsConfig([
        'sendChatMessage',
    ]);
    if (status === false) {
        console.error('agentConfig 失败', message, data);
    }
    const url = `/pages/goods/goods-validate/index.html?goodsId=${item.id}&clinicId=${clinicId.value}`;
    console.log('企微发送商品首页', url, import.meta.env.VITE_APP_WE_MALL_APP_ID);
    wx.invoke('sendChatMessage', {
        msgtype: 'miniprogram', // 消息类型，必填
        enterChat: true, // 为true时表示发送完成之后顺便进入会话，仅移动端3.1.10及以上版本支持该字段
        miniprogram: {
            appid: import.meta.env.VITE_APP_WE_MALL_APP_ID, // 小程序的appid
            title: item.skuGoodsName, // 小程序消息的title
            imgUrl: item.posterImageUrl, // 小程序消息的封面图
            page: url, // 小程序消息打开后的路径，注意要以.html作为后缀，否则在微信端打开会提示找不到页面
        },
    }, (res: any) => {
        if (res.err_msg == 'sendChatMessage:ok') {
            console.log('发送成功');
        } else {
            console.log('发送失败', res);
        }
    });
};

const handleSendAll = async () => {
    const { status, message, data } = await agentJsConfig([
        'sendChatMessage',
    ]);
    const url = `/pages/goods/goods-validate/index.html?toHome=1&clinicId=${clinicId.value}`;

    if (status === false) {
        console.error('agentConfig 失败', message, data);
    }
    console.log('企微发送商品首页', url, import.meta.env.VITE_APP_WE_MALL_APP_ID);
    wx.invoke('sendChatMessage', {
        msgtype: 'miniprogram', // 消息类型，必填
        enterChat: true, // 为true时表示发送完成之后顺便进入会话，仅移动端3.1.10及以上版本支持该字段
        miniprogram: {
            appid: import.meta.env.VITE_APP_WE_MALL_APP_ID, // 小程序的appid
            title: 'ABC直采商城', // 小程序消息的title
            imgUrl: 'https://static-common-cdn.abcyun.cn/img/abc-we-mall-index.png', // 小程序消息的封面图
            page: url, // 小程序消息打开后的路径，注意要以.html作为后缀，否则在微信端打开会提示找不到页面
        },
    }, (res: any) => {
        if (res.err_msg == 'sendChatMessage:ok') {
            console.log('发送成功');
        } else {
            console.log('发送失败', res);
        }
    });
};
</script>
<template>
    <div v-loading="loading" class="technical-support-personal-recommend-wrapper">
        <div class="personal-recommend-filter">
            <el-input
                v-model="searchKey"
                placeholder="输入搜索关键词"
                :prefix-icon="Search"
                clearable
                @change="handleSearch"
            />
            <el-row style="margin-top: 8px;" :gutter="8">
                <el-col :span="8">
                    <el-select
                        v-model="fetchParams.recommendId"
                        placeholder="推荐类型"
                        clearable
                        @change="handleChangeParams"
                    >
                        <el-option
                            v-for="item in goodsRecommendList"
                            :key="item.recommendId"
                            :label="item.recommendName"
                            :value="item.recommendId"
                        />
                    </el-select>
                </el-col>
                <el-col :span="8">
                    <el-select
                        v-model="fetchParams.categoryId"
                        placeholder="商品类型"
                        clearable
                        @change="handleChangeParams"
                    >
                        <el-option
                            v-for="item in goodsTypeList"
                            :key="item.categoryId"
                            :label="item.categoryName"
                            :value="item.categoryId"
                        />
                    </el-select>
                </el-col>
                <el-col :span="8" style="text-align: right;">
                    <el-button type="primary" style="margin-left: auto;" @click="handleSendAll">发送全部</el-button>
                </el-col>
            </el-row>
        </div>
        <div v-infinite-scroll="loadMore" class="personal-recommend-list-wrapper" :infinite-scroll-distance="40">
            <div
                v-for="item in goodsList"
                :key="item.id"
                class="personal-recommend-list-item"
            >
                <div>
                    {{ item.skuGoodsName }}
                    <el-space :size="4">
                        <el-tag v-for="tag in item.recommendView" :key="tag.recommendId" size="small">{{ tag.recommendName }}</el-tag>
                    </el-space>
                </div>
                <el-row style="margin-top: 12px;">
                    <el-col :span="24">
                        <el-space :size="8" class="personal-recommend-item-price">
                            <span>厂家：{{ item.manufacturer }}</span>
                        </el-space>
                    </el-col>
                    <el-col :span="24">
                        <div class="personal-recommend-item-price">
                            <span v-if="item.specification" style="margin-right: 8px;">规格：{{ item.specification }}</span>
                            <span>
                                售价：{{
                                    item?.seckillPromotion?.seckillPromotionDetail?.seckillPrice ||
                                        item.postDiscountPrice || item.originSalesUnitPrice
                                }}</span>
                            <el-button type="primary" style="margin-left: auto;" @click="handleSend(item)">发送</el-button>
                        </div>
                    </el-col>
                </el-row>
            </div>
        </div>
    </div>
</template>
<style lang="scss">
.technical-support-personal-recommend-wrapper {
    height: 100%;
    padding-top: 88px;
    position: relative;

    .personal-recommend-filter {
        position: absolute;
        top: 0;
        left: 0;
    }

    .personal-recommend-filter {
        padding: 8px 12px;
    }

    .personal-recommend-list-wrapper {
        padding: 0 12px 40px;
        height: 100%;
        overflow: scroll;
    }

    .personal-recommend-list-item {
        padding: 8px;
        background: #fff;
        border-radius: 4px;

        & + .personal-recommend-list-item {
            margin-top: 8px;
        }
    }

    .personal-recommend-item-price {
        display: flex;
        gap: 8px;
        align-items: center;
        font-size: 12px;
        color: #606266;
    }
}
</style>
