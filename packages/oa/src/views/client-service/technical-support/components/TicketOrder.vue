<script lang="ts" setup>

import { computed, nextTick, PropType, reactive, ref, watch, onMounted, onUnmounted } from 'vue';
import { onBeforeRouteLeave } from 'vue-router';
import { useTechnicalSupportHook } from '@/views/client-service/technical-support/hook';
import { TicketForm } from '@/views/client-service/technical-support/model';
import _ from 'lodash';
import {
    APPID,
    formatTicketStatus,
    TicketOrderTypeEnum,
    TicketOrderTypeOptions,
    TicketStatusEnum,
    TicketStatusOptions,
} from '@/utils/ticket';
import {
    getClinicTypeDisplayName,
    getHisTypeDisplayName,
    HisType,
    HisTypeClinic,
    isChainSubClinic,
    NodeTypeClinic,
} from '@/utils/clinic';
import { ClinicTicketAPI } from '@/api/clinic-ticket-api';
import { Toast, Dialog } from 'vant';
import { AfterSalesApi } from '@/api/after-sales-api';
import { ElMessage } from 'element-plus/es';
import ReplyInput from '@/components/reply-input.vue';
import dayjs from 'dayjs';
import { useUserStore } from '@/store/user';
import { useFormat } from '@/composables/date';
import Conversation from '@/components/chat-record/conversation.vue';
import OaCell from '@/components/oa-cell.vue';
import OaCellGroup from '@/components/oa-cell-group.vue';
import { concatAddress, formatHisTypeName } from '@/utils/format';
import { useSupportEmployeeHook } from '@/views/client-service/technical-support/hook/employee';
import { useRoomChatHook } from '@/views/client-service/technical-support/hook/roomChat';
import { useSupportOrganHook } from '@/views/client-service/technical-support/hook/organ';
import { useAfterSalesChatHook } from '@/views/client-service/technical-support/hook/afterSalesChat';
import { delRead, setFocus } from '@/utils/mobile-select';
// import ChatTagSelect from '@/views/client-service/technical-support/components/ChatTagSelect.vue';
import { useChatTagHook } from '@/views/client-service/technical-support/hook/chatTag';
import UserWorkspace = AbcAPI.UserWorkspace;
import QwConversationMsg = AbcAPI.QwConversationMsg;

const {
    dividerStyle,
    showLoading,
    hideLoading,
    fetchTicketOrderCount,
    fetchTicketList,
} = useTechnicalSupportHook();
const { roomId, isChatAccount, getRoomRecordDetail } = useRoomChatHook();
const { fetchOrganList } = useSupportOrganHook();
const { getApiLowCodeAfterSales, getConversationDetail, getOriginBySender } = useAfterSalesChatHook();
const { employeeInfo, externalUserId, fetchEmployeeList } = useSupportEmployeeHook();
const { tagId, tagName } = useChatTagHook();

const props = defineProps({
    type: {
        type: String as PropType<'create' | 'detail'>,
        default: 'create',
    },
    visible: {
        type: Boolean,
        default: false,
    },
    currentTicket: {
        type: Object,
        default: () => ({}),
    },
});
const emit = defineEmits(['update:visible', 'refresh']);
const ticketPopupVisible = computed({
    get: () => props.visible,
    set: (value) => {
        emit('update:visible', value);
    },
});
const popupTitle = computed(() => (props.type === 'create' ? '新建工单' : '工单详情'));
const formRef = ref<any>(null);
const nowDate = new Date();
const min = nowDate.getMinutes();
const beginDate = ref<Date>(
    new Date(nowDate.setMinutes(min - 10)),
);
const endDate = ref<Date>(
    new Date(),
);
const form = ref<TicketForm>({
    clinicId: '',
    employeeId: '',
    type: undefined,
    hisType: undefined,
    question: undefined,
    tag: undefined,
    owner: undefined,
    isEmergency: false,
});
const initialForm = ref<TicketForm>({
    clinicId: '',
    employeeId: '',
    type: undefined,
    hisType: undefined,
    question: undefined,
    tag: undefined,
    owner: undefined,
    isEmergency: false,
});

watch(() => tagId.value, (value) => {
    form.value.tag = value;
});

const organList = ref<any[]>([]);
const employeeList = ref<any[]>([]);

const organInfo = computed(() => organList.value.find((item) => item.value === form.value.clinicId));
const curEmployeeInfo = computed(() => employeeList.value.find((item) => item.value === form.value.employeeId));

const isDetailOrder = computed(() => props.type === 'detail');
const isSupportOrder = ref(false);
const isBusinessOrder = ref(false);
const orderDetailForm = ref<any>({});
watch(() => props.currentTicket, async (value) => {
    if (!value || !value.id || !isDetailOrder.value) {
        return;
    }
    organList.value = [{
        value: value.clinicId,
        label: value.clinicName,
        chainId: value.chainId,
        clinicId: value.clinicId,
        clinicName: value.clinicName,
        hisType: value.hisType,
    }];
    form.value = {
        ...value,
        question: value.title,
        isEmergency: value.isEmergency === 1,
    };
    orderDetailForm.value = _.cloneDeep(form.value);
    isSupportOrder.value = form.value.type === TicketOrderTypeEnum.SUPPORT;
    isBusinessOrder.value = form.value.type === TicketOrderTypeEnum.BUSINESS;
    employeeList.value = await fetchEmployeeList(organInfo.value?.chainId as string, form.value.clinicId as string);
}, {
    immediate: true,
    deep: true,
});
const isOrderDetailChange = computed(() => JSON.stringify(orderDetailForm.value) !== JSON.stringify(form.value));
const isFormChanged = computed(() => {
    if (isDetailOrder.value) {
        return isOrderDetailChange.value;
    }
    return JSON.stringify(initialForm.value) !== JSON.stringify(form.value) || JSON.stringify(echoValues.value) !== JSON.stringify(form.value.chatInputValue);
});
const echoValues = ref<any[]>([]);
watch(ticketPopupVisible, async (value) => {
    if (value) {
        nextTick(() => {
            delRead();
        });
        if (isDetailOrder.value) {
            if (isSupportOrder.value) {
                await getTapdWorkSpacesUsers();
            }
            return;
        }
        await handleNonDetailOrder();
        // 查询草稿
        if (form.value.clinicId) {
            let res: any = {};
            try {
                res = await ClinicTicketAPI.findRecentlyDraftTicketUsingGET(form.value.clinicId);
            } catch (e: any) {
                ElMessage.error(e.message || e);
            }
            if (res?.clinicId) {
                form.value = {
                    ...res,
                    question: res.title,
                    isEmergency: res.isEmergency === 1,
                    owner: res.tapdDealerId,
                };
                echoValues.value = res.chatInputValueList || [];
                console.log(form.value);
            }
        }
        // 记录初始表单状态
        initialForm.value = _.cloneDeep(form.value);
    }
});

async function handleNonDetailOrder() {
    await getTapdWorkSpacesUsers();
    if (!isChatAccount.value) {
        conversationId.value = await getApiLowCodeAfterSales(externalUserId.value, userInfo.userId || '');
    } else {
        await refreshQuery();
    }
    if (conversationId.value) {
        roomRecordList.value = await getConversationDetail(conversationId.value);
    }
    setOrganAndEmployeeInfo();
}

function setOrganAndEmployeeInfo() {
    if (employeeInfo.value.clinic) {
        organList.value = [{
            ...employeeInfo.value.clinic,
            chainId: employeeInfo.value.clinic?.parentId,
            value: employeeInfo.value.clinic?.id,
            label: employeeInfo.value.clinic?.name,
        }];
    }
    employeeList.value = [{
        ...employeeInfo.value,
        value: employeeInfo.value.id,
        label: employeeInfo.value.name,
    }];
    form.value.clinicId = employeeInfo.value.clinic?.id;
    form.value.hisType = employeeInfo.value.clinic?.hisType;
    form.value.employeeId = employeeInfo.value.id;
}

const _debounceSearch = _.debounce(async (keyword: string) => {
    organList.value = await fetchOrganList(keyword);
}, 500);

async function handleOrganChange() {
    form.value.employeeId = '';
    form.value.hisType = organInfo.value?.hisType;
    employeeList.value = await fetchEmployeeList(organInfo.value?.chainId as string, form.value.clinicId as string);
    handleOrderTypeChange();
}

const userStore = useUserStore();
const userInfo = userStore.userInfo;

const conversationId = ref<any>(null);
const roomRecordList = ref<any[]>([]);

const workspaceUsers = ref<any[]>([]);
/**
 * @description: 获取tapd人员信息列表
 * @date: 2023-11-21 09:54:59
 * @author: Horace
 * @return
 */
async function getTapdWorkSpacesUsers() {
    let res: any = {};
    showLoading();
    try {
        res = await AfterSalesApi.getApiLowCodeTapdWorkspaceUsers();
    } catch (e: any) {
        Toast.fail(e.message || e);
    } finally {
        hideLoading();
    }
    workspaceUsers.value = res?.map((item: UserWorkspace) => ({
        label: item.name || item.user,
        value: item.user,
    }));
}

const replyInputRef = ref<any>(null);
const isWatchValue = ref(false);
const chatInputValue = ref<any[]>([]);
/**
 * @description: 处理输入框的值变化
 * @date: 2024-06-12 19:29:07
 * @author: Horace
 * @param {any[]} nodes
 * @return
*/
function handleChatInputChange(nodes: any[]) {
    console.log('输入框的值变化', nodes);
    isWatchValue.value = false;
    chatInputValue.value = [];
    if (nodes && nodes.length) {
        nodes.forEach((node: any) => {
            let msg: AbcAPI.QwConversationMsg = {
                origin: 1,
                msgTime: dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss'),
            };
            if (node.type === 'text') {
                msg.msgType = 'text';
                msg.msgText = {
                    content: node.content.trim(),
                };
            } else if (node.type === 'image') {
                msg.msgType = node.type;
                msg.msgImage = {
                    ossUrl: node.url,
                };
            } else if (node.type === 'video') {
                msg.msgType = node.type;
                msg.msgVideo = {
                    ossUrl: node.url,
                };
            } else if (node.type === 'File') {
                msg.msgType = 'file';
                msg.msgFile = {
                    ossUrl: node.url,
                    filename: node.fileName,
                };
            } else { return; }
            chatInputValue.value.push(msg);
        });
    }
}
/**
 * @description: 获取处理人姓名
 * @date: 2024-06-12 20:14:25
 * @author: Horace
 * @param {string} owner 处理人
 * @return
*/
function getDealerName(owner: string | undefined) {
    const option = workspaceUsers.value.find((item: any) => item.user === owner || item.name === owner);
    if (option) {
        return option.name || option.user;
    }
    return '';
}

const checkedRoomRecords = ref<any[]>([]);
/**
 * @description: 格式化会话记录字段
 * @date: 2024-06-12 20:19:14
 * @author: Horace
 * @return
*/
function formatRecordField(): Array<QwConversationMsg> {
    if (!checkedRoomRecords.value || !checkedRoomRecords.value.length) {
        return [];
    }
    const msgList: Array<QwConversationMsg> = [];
    for (const item of checkedRoomRecords.value) {
        const origin = getOriginBySender(item.sender);
        if (origin < 0) {
            break;
        }
        const msgTypeOptions: Record<any, string> = {
            text: 'msgText',
            image: 'msgImage',
            file: 'msgFile',
            video: 'msgVideo',
            voice: 'msgVoice',
            mixed: 'msgMixed',
        };
        const key: string = msgTypeOptions[item.msgType];
        const msg = {
            externalUserId: origin ? null : item.sender,
            externalUserName: origin ? null : item.senderName,
            servicerId: origin ? item.sender : null,
            servicerName: origin ? item.senderName : null,
            origin,
            msgType: item.msgType,
            msgTime: dayjs(item.msgTime).format('YYYY-MM-DD HH:mm:ss'),
            [key]: item[key],
        };
        msgList.push(msg);
    }
    return msgList;
}
/**
 * @description: 格式化B2B会话记录字段
 * @date: 2024-06-12 20:19:32
 * @author: Horace
 * @return
*/
function formatB2BRecordField(): Array<QwConversationMsg> {
    if (!roomRecordList.value || !roomRecordList.value.length) {
        return [];
    }
    const msgList: Array<QwConversationMsg> = [];
    for (const record of roomRecordList.value) {
        const msgTypeOptions: Record<any, string> = {
            text: 'msgText',
            image: 'msgImage',
            file: 'msgFile',
            video: 'msgVideo',
            voice: 'msgVoice',
        };
        const key: string = msgTypeOptions[record.msgType];
        const msgContent = record.msgType === 'text' ? record[key] : {
            ossUrl: record.msgMedia?.oss_file_name,
        };
        const isClient = record.origin === 3;
        const msg: any = {
            externalUserId: isClient ? record.clientId : null,
            externalUserName: isClient ? record.clientName : null,
            servicerId: isClient ? null : record.servicerId,
            servicerName: isClient ? null : record.servicerName,
            origin: isClient ? 0 : 1,
            msgType: record.msgType,
            msgTime: dayjs(record.sendTime).format('YYYY-MM-DD HH:mm:ss'),
            [key]: msgContent,
        };
        msgList.push(msg);
    }
    return msgList;
}
const loading = ref(false);

function getParams(isDraft = 0) {
    if (!curEmployeeInfo.value) {
        !isDraft && ElMessage.error('请检查反馈人员信息是否准确！');
        return;
    }
    const params = {
        clientId: curEmployeeInfo.value.externalUserId || externalUserId.value || '',
        clientName: curEmployeeInfo.value.label || '',
        roomName: roomRecordList.value?.[0]?.roomName || '',
        mobile: curEmployeeInfo.value.mobile || '',
        employeeId: form.value.employeeId || '',
        openId: curEmployeeInfo.value.wechatOpenIdMp || '',
        clinicId: form.value.clinicId || '',
        hisType: form.value.hisType,
        edition: organInfo.value.editionName || organInfo.value.edition,
        question: form.value.question,
        userId: userInfo.userId || '',
        qwUserId: userInfo.id?.toString() || '',
        qwUserName: userInfo.name || '',
        owner: form.value.owner,
        dealerName: getDealerName(form.value.owner || ''),
        isEmergency: form.value.isEmergency ? 1 : 0,
        tagList: form.value.tag ? [form.value.tag] : [],
        tagNameList: tagName.value ? [tagName.value] : [],
        isChatAccount: isChatAccount.value ? 1 : 0,
        msgList: isChatAccount.value ? formatRecordField() : formatB2BRecordField(),
        qwCorpId: APPID,
        feedbackTime: dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss'),
        conversationId: checkedRoomRecords.value?.length ? checkedRoomRecords.value[0].conversationId : conversationId.value,
        chatInputValue: props.currentTicket?.chatInputValueList?.length
            ? props.currentTicket.chatInputValueList.concat(chatInputValue.value)
            : chatInputValue.value,
        adminInfo: {
            name: organInfo.value.admin?.name,
            mobile: organInfo.value.admin?.mobile,
            openId: organInfo.value.admin?.wehcatOpenIdMp,
        },
        fromWay: isChatAccount.value ? 1 : 0,
        type: form.value.type,
        isDraft,
    };
    return params;
}
async function handleOrderDelete(ticketId: string) {
    let res: any = {};
    showLoading();
    try {
        res = await ClinicTicketAPI.deleteUsingDELETE(ticketId);
    } catch (e: any) {
        Toast.fail(e.message || e);
    } finally {
        hideLoading();
    }
    if (res && res.code === 200) {
        Toast.success('工单删除成功');
        fetchTicketList();
        emit('refresh');
    }
}
/**
 * @description: 更新工单反馈门店以及反馈人
 * @date: 2024-06-16 17:31:23
 * @author: Horace
 * @return
*/
async function handleOrderUpdate() {
    let res: any = {};
    showLoading();
    try {
        res = await ClinicTicketAPI.updateUsingPUT(form.value.id as string, {
            employeeId: form.value.employeeId,
            clinicId: form.value.clinicId,
            status: form.value.status,
        });
    } catch (e: any) {
        Toast.fail(e.message || e);
    } finally {
        hideLoading();
    }
    if (res && res.code === 200) {
        ElMessage.success('工单更新成功');
        handlePopupCancel();
        fetchTicketList();
        fetchTicketOrderCount();
        emit('refresh');
    }
}
function handleTicketOrderStatusChange() {
    if (!form.value.status && form.value.status !== 0) {
        return;
    }
    if (isDetailOrder.value) {
        handleOrderUpdate();
        return;
    }
    handlePopupSubmit();
}
/**
 * @description: 提交工单
 * @date: 2024-06-12 20:03:30
 * @author: Horace
 * @return
*/
async function handlePopupSubmit() {
    if (isDetailOrder.value && !isSupportOrder.value) {
        handleOrderUpdate();
        return;
    }
    
    formRef.value.validate(async (valid: boolean) => {
        if (!valid) {
            return;
        }
        
        // 设置提交状态并显示提交中的Toast
        loading.value = true;
        
        const submitToast = Toast({
            type: 'loading',
            message: '工单提交中...',
            duration: 0, // 不自动关闭
            forbidClick: true, // 禁止点击
        });
        
        let res: any = {};
        try {
            const params = getParams();
            if (!params) {
                // 关闭提交中的Toast
                submitToast?.clear();
                return;
            }
            res = await AfterSalesApi.postApiLowCodeTapd(params);
        } catch (e: any) {
            Toast({
                type: 'fail',
                message: e.message || e,
            });
        } finally {
            loading.value = false;
            // 关闭提交中的Toast
            submitToast?.clear();
        }
        
        if (res?.ticket?.code === 200) {
            Toast({
                type: 'success',
                message: '工单创建成功',
            });
            if (isSupportOrder.value) {
                await handleOrderDelete(form.value.id as string);
            }
            emit('refresh');
            fetchTicketList();
            fetchTicketOrderCount();
            resetForm();
        }
    });
}

async function handlePopupCancel() {
    if (isFormChanged.value) {
        saveDraft();
    } else {
        resetForm();
    }
}
const resetForm = () => {
    formRef.value?.resetFields();
    form.value = _.cloneDeep(initialForm.value);
    echoValues.value = [];
    chatInputValue.value = [];
    ticketPopupVisible.value = false;
};
const saveDraft = () => {
    try {
        const params = getParams(1);

        // 使用 sendBeacon 发送数据
        const blob = new Blob([JSON.stringify(params)], {
            type: 'application/json',
        });

        const success = navigator.sendBeacon('/api/low-code/tapd', blob);

        if (success) {
            resetForm();
        }
    } catch (error: any) {
        console.log(error);
    }
};

// 分页参数
const pageParams = reactive({
    offset: 0,
    limit: 20,
    total: 0,
});
const isHitBottom = ref(false);
async function refreshQuery() {
    pageParams.offset = 0;
    pageParams.total = 0;
    isHitBottom.value = false;
    roomRecordList.value = [];
    await getRoomRecordList();
}

/**
 * @description: 获取当前群聊的群聊记录
 * @date: 2023-11-21 09:53:44
 * @author: Horace
 * @return
 */
async function getRoomRecordList() {
    if (isHitBottom.value) {
        return false;
    }
    let res: any = await getRoomRecordDetail(
        pageParams.offset,
        pageParams.limit,
        <string>roomId.value,
        beginDate.value,
        endDate.value,
    );
    if (res && res.rows && res.rows.length) {
        Toast.success({
            message: '获取群聊记录成功！',
            duration: 400,
        });
    }
    pageParams.total = +(res.total || 0);
    pageParams.offset = (+(res.offset || 0)) + (+(res.limit || 0));
    pageParams.limit = +(res.limit || 0);
    roomRecordList.value = roomRecordList.value?.length ? roomRecordList.value.concat(res.rows || []) : res.rows || [];
    if (pageParams.offset >= pageParams.total) {
        isHitBottom.value = true;
    }
    handleCheckAllChange(true);
}
const checkAll = ref(false);
const isIndeterminate = ref(false);

/**
 * @description: 群聊记录全选
 * @date: 2023-11-21 14:44:29
 * @author: Horace
 * @param {boolean} val 是否全选
 * @return
 */
function handleCheckAllChange(val: boolean) {
    checkedRoomRecords.value = val ? roomRecordList.value : [];
    isIndeterminate.value = false;
}
/**
 * @description: 单个群聊记录选择
 * @date: 2023-11-21 14:44:55
 * @author: Horace
 * @param {any[]} value 当前所有选中的群聊记录
 * @return
 */
function handleCheckedChange(value: any[]) {
    const checkedCount = value.length;
    checkedRoomRecords.value = value;
    checkAll.value = checkedCount === roomRecordList.value.length;
    isIndeterminate.value = checkedCount > 0 && checkedCount < roomRecordList.value.length;
}
function handleRangeChange() {
    if (beginDate.value > endDate.value) {
        [beginDate.value, endDate.value] = [endDate.value, beginDate.value];
    }
    refreshQuery();
}

/**
 * 获取会话主体类型
 * @param timeline 会话数据
 */
function getUserName(timeline: any) {
    let userName = '';
    switch (timeline.origin) {
        // 群聊客户发送的消息
        case 0:
            userName = timeline.externalUserName || '客户消息';
            break;
        // 群聊工作人员发送的消息
        case 1:
            userName = timeline.servicerName || '客户消息';
            break;
        // 微信客户发送的消息
        case 3:
            userName = timeline.clientName || '客户消息';
            break;
        // 系统推送的事件消息
        case 4:
            userName = '系统消息';
            break;
        // 接待人员在企业微信客户端发送的消息
        case 5:
            userName = timeline.servicerName || '客服消息';
            break;
        default:
            userName = timeline.senderName || '客服消息';
    }
    return userName;
}

const ticketOrderType = computed(() => {
    const option = TicketOrderTypeOptions.find((item) => item.value === form.value.type);
    return option?.label || '';
});
function viewConversationRecord(data: any) {
    window.open(
        `/after-sales/service-review?conversationId=${
            data.conversationId || ''
        }&servicerId=${
            data.servicerId || ''
        }&clientId=${
            data.clientId || ''
        }&roomName=${
            data.roomName || ''
        }`,
    );
}
const rules = {
    clinicId: [
        { required: true, message: '请选择门店', trigger: 'change' },
    ],
    employeeId: [
        { required: true, message: '请选择人员', trigger: 'change' },
    ],
    type: [
        { required: true, message: '请选择类型', trigger: 'change' },
    ],
    hisType: [
        { required: true, message: '请选择产品', trigger: 'change' },
    ],
    question: [
        { required: true, message: '请输入标题', trigger: 'blur' },
    ],
    // tag: [
    //     { required: true, message: '请选择分类', trigger: 'change' },
    // ],
    owner: [
        { required: true, message: '请选择处理人', trigger: 'change' },
    ],
};
function displayOrgan(organ: any) {
    return {
        name: organ.shortName || organ.name,
        address: concatAddress(organ),
        adminName: organ.adminName,
        adminMobile: organ.adminMobile,
        clinicTypeName: getClinicTypeDisplayName(<NodeTypeClinic>organ),
        hisTypeName: getHisTypeDisplayName(<HisTypeClinic>organ),
        chainName: isChainSubClinic(<any>organ) ? organ.chainName : '',
    };
}
const formStatusDisabled = computed(() => form.value.type === TicketOrderTypeEnum.BUG
            || form.value.type === TicketOrderTypeEnum.REQUIREMENT
            || form.value.status !== TicketStatusEnum.PROCESSING);
const updateOrderTypeOptions = computed(() => TicketOrderTypeOptions.filter((item) => item.value !== TicketOrderTypeEnum.SUPPORT));
const showOwnerItem = computed(() => !isDetailOrder.value && form.value.type !== TicketOrderTypeEnum.BUSINESS);
const handleOrderTypeChange = () => {
    if (form.value.type === TicketOrderTypeEnum.SPECIAL_NETWORK) {
        form.value.question = `【专网】${organInfo.value?.name || ''}`;
    }
};

// 处理页面刷新和关闭
let isBeforeUnloadTriggered = false;

const handleBeforeUnload = async () => {
    if (isFormChanged.value && ticketPopupVisible.value && !isBeforeUnloadTriggered) {
        await saveDraft();
    }
};

onMounted(() => {
    window.addEventListener('beforeunload', handleBeforeUnload);
});

onUnmounted(() => {
    saveDraft();
    window.removeEventListener('beforeunload', handleBeforeUnload);
});

onBeforeRouteLeave(async (to, from, next) => {
    if (isFormChanged.value && ticketPopupVisible.value) {
        try {
            await saveDraft();
            next();
        } catch (e) {
            // 用户选择不保存，直接离开
            console.log(e);
            next();
        }
    } else {
        next();
    }
});
</script>
<template>
    <van-popup
        v-model:show="ticketPopupVisible"
        class="technical-support-popup-wrapper ticket-order-popup-wrapper"
        safe-area-inset-bottom
        round
        closeable
        :before-close="handlePopupCancel"
    >
        <div class="technical-support-popup-title mb-3">
            {{ popupTitle }}
        </div>
        <div class="ticket-order-popup-content">
            <el-form
                ref="formRef"
                :model="form"
                label-width="5em"
                :rules="rules"
                class="ticket-order-popup-form"
            >
                <el-space class="ticket-order-popup-organ-space">
                    <el-form-item prop="clinicId" label="" label-width="0">
                        <el-select
                            v-model="form.clinicId"
                            clearable
                            filterable
                            remote
                            class="filter-select"
                            placement="bottom-start"
                            :remote-method="_debounceSearch"
                            @hoot="setFocus"
                            @visible-change="setFocus"
                            @change="handleOrganChange"
                        >
                            <el-option
                                v-for="item in organList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                                <p class="flex-between">
                                    <span>{{ item.label }}</span>
                                    <van-tag class="clinic-type" type="primary">
                                        {{ displayOrgan(item)?.clinicTypeName }}
                                    </van-tag>
                                    <van-tag class="his-type">{{ displayOrgan(item)?.hisTypeName }}</van-tag>
                                </p>
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item prop="employeeId" label="" label-width="0">
                        <el-select
                            v-model="form.employeeId"
                            clearable
                            filterable
                            class="filter-select"
                            placement="bottom-start"
                            @hoot="setFocus"
                            @visible-change="setFocus"
                        >
                            <el-option
                                v-for="item in employeeList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-space>
                <oa-cell-group v-if="isDetailOrder" label-width="5em" class="ticket-order-detail">
                    <oa-cell label="单号">
                        <p class="flex-between">
                            <span>{{ form.code }}</span>
                            <span v-if="formStatusDisabled">{{ formatTicketStatus(form.status as number) }}</span>
                            <el-select
                                v-else
                                v-model="form.status"
                                style="width: 7.5em; margin-top: 8px;"
                                placement="bottom-start"
                                @change="handleTicketOrderStatusChange"
                            >
                                <el-option
                                    v-for="item in TicketStatusOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </p>
                    </oa-cell>
                    <oa-cell label="类型">
                        <span>{{ ticketOrderType }}</span>
                    </oa-cell>
                    <oa-cell label="标题">
                        <span>{{ form.question }}</span>
                    </oa-cell>
                    <oa-cell label="产品">
                        <span>{{ formatHisTypeName(form.hisType as HisType) }}</span>
                    </oa-cell>
                    <oa-cell label="创建人">
                        <span>{{ form.createByName }}</span>
                    </oa-cell>
                    <oa-cell label="会话">
                        <el-link @click="viewConversationRecord(form)">查看原始会话记录</el-link>
                    </oa-cell>
                    <van-divider :style="dividerStyle" />
                    <oa-cell v-if="!isSupportOrder" label="处理人">
                        <span>{{ form.dealerName || form.tapdDealerId }}</span>
                    </oa-cell>
                    <oa-cell v-if="!isBusinessOrder" label="TAPD" class="ticket-order-tapd-url">
                        <a :href="form.tapdLick" target="_blank">查看对应TAPD单据</a>
                    </oa-cell>
                    <oa-cell v-if="!isBusinessOrder && form.status" label="结果">
                        <p><span>【{{ formatTicketStatus(form.status) }}】</span><span>{{ form.reason }}</span></p>
                    </oa-cell>
                </oa-cell-group>
                <el-form-item v-if="!isDetailOrder" prop="type" label="类型">
                    <el-radio-group v-model="form.type" @change="handleOrderTypeChange">
                        <el-radio
                            v-for="option in TicketOrderTypeOptions"
                            :key="option.value"
                            size="small"
                            :label="option.value"
                        >
                            {{ option.label }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item v-if="!isDetailOrder" prop="question" label="标题">
                    <el-input
                        v-model="form.question"
                        placeholder="输入工单标题"
                        clearable
                        :maxlength="50"
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-checkbox v-model="form.isEmergency" :disabled="isDetailOrder">
                        <el-space>
                            紧急问题
                            <el-tooltip>
                                <template #content>
                                    如问题有如下特点请勾选：<br />
                                    1. 有时效性且迫在眉睫<br />
                                    2. 导致客户业务受阻<br />
                                    3. 可能造成客户财产损失<br />
                                    4. 高频反馈，影响面大<br />
                                </template>
                                <van-icon name="question-o" />
                            </el-tooltip>
                        </el-space>
                    </el-checkbox>
                </el-form-item>
                <!-- <el-form-item v-if="!isDetailOrder" prop="tag" label="分类">
                    <chat-tag-select :conversation-id="conversationId || checkedRoomRecords?.[0]?.conversationId" />
                </el-form-item> -->
                <el-form-item v-if="isSupportOrder && form.status === TicketStatusEnum.PROCESSING" class="margin-left-none" prop="type">
                    <el-radio-group v-model="form.type">
                        <el-radio v-for="option in updateOrderTypeOptions" :key="option.value" :label="option.value">转{{ option.label }}</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item
                    v-if="showOwnerItem"
                    prop="owner"
                    label="处理人"
                >
                    <el-select
                        v-model="form.owner"
                        class="filter-select"
                        filterable
                        clearable
                        @hoot="setFocus"
                        @visible-change="setFocus"
                    >
                        <el-option
                            v-for="item in workspaceUsers"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        ></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item
                    v-if="isChatAccount && !isDetailOrder"
                    class="chat-record-form-item"
                    prop="range"
                    label="记录时间"
                >
                    <el-space>
                        <el-date-picker
                            v-model="beginDate"
                            size="small"
                            type="datetime"
                            style="margin-bottom: 8px;"
                            placeholder="开始时间"
                            format="YYYY-MM-DD HH:mm:ss"
                            date-format="YYYY/MM/DD ddd"
                            time-format="A hh:mm:ss"
                            @change="handleRangeChange"
                        />
                        <el-date-picker
                            v-model="endDate"
                            size="small"
                            type="datetime"
                            placeholder="结束时间"
                            format="YYYY-MM-DD HH:mm:ss"
                            date-format="YYYY/M/DD ddd"
                            time-format="A hh:mm:ss"
                            @change="handleRangeChange"
                        />
                    </el-space>
                    <el-checkbox
                        v-if="roomRecordList.length"
                        v-model="checkAll"
                        :indeterminate="isIndeterminate"
                        @change="handleCheckAllChange"
                    >
                        全选当前群聊记录
                    </el-checkbox>
                </el-form-item>
                <div v-if="isChatAccount && !isDetailOrder" class="chat-account-form-wrapper">
                    <oa-cell-group>
                        <el-checkbox-group
                            v-if="roomRecordList.length"
                            v-model="checkedRoomRecords"
                            v-infinite-scroll="getRoomRecordList"
                            class="session-list-wrapper"
                            :infinite-scroll-distance="40"
                            :infinite-scroll-delay="400"
                            @change="handleCheckedChange"
                        >
                            <oa-cell v-for="record in roomRecordList" :key="record">
                                <el-checkbox :label="record">
                                    <p>
                                        <span style="padding-right: 4px;">{{ getUserName(record) }}</span>
                                        <span>{{ useFormat(record.msgTime, 'YYYY-MM-DD HH:mm:ss') }}</span>
                                    </p>
                                    <conversation :timeline="record"></conversation>
                                </el-checkbox>
                            </oa-cell>
                        </el-checkbox-group>
                    </oa-cell-group>
                </div>
                <el-form-item
                    v-if="!isDetailOrder || isSupportOrder && form.status === TicketStatusEnum.PROCESSING"
                    class="reply-input-form-item"
                    prop="chatInputValue"
                    label="补充信息"
                >
                    <reply-input
                        v-if="ticketPopupVisible"
                        ref="replyInputRef"
                        :echo-value="echoValues"
                        :chat-input-value="chatInputValue"
                        :show-submit-btn="false"
                        placeholder="粘贴文字/截图/视频等，方便研发更高效定位问题"
                        :editor-style="{width: '100%'}"
                        :is-watch-value="isWatchValue"
                        :enter="true"
                        @change="handleChatInputChange"
                    >
                    </reply-input>
                </el-form-item>
            </el-form>
            <el-space v-if="!isChatAccount && !isDetailOrder"><el-icon><Paperclip /></el-icon>已自动添加本次会话记录为工单附件</el-space>
        </div>
        <div class="technical-support-popup-footer">
            <el-button
                type="primary"
                :loading="loading"
                :disabled="isDetailOrder && !isOrderDetailChange"
                @click="handlePopupSubmit"
            >
                确定
            </el-button>
            <el-button @click="handlePopupCancel">取消</el-button>
        </div>
    </van-popup>
</template>
<style lang="scss">
@use '@/style/mixins/mixins.scss' as mixins;

.ticket-order-select-item {
    width: 100%;

    @include mixins.text-no-wrap();
}

.ticket-order-popup-wrapper {
    max-height: 90vh;

    .ticket-order-popup-organ-space {
        .el-space__item:first-child {
            width: 65%;
            min-width: 65%;
        }

        .el-input.is-disabled .el-input__inner {
            color: var(--el-input-text-color, var(--el-text-color-regular));
        }
    }

    .ticket-order-popup-content {
        .el-select {
            width: 100%;
        }

        .ticket-order-detail {
            .oa-cell-wrapper {
                height: auto !important;
                min-height: 32px !important;
                border-bottom: none;

                .oa-cell__value-wrapper {
                    flex: 1;
                }
            }

            .ticket-order-tapd-url {
                .oa-cell__value-wrapper {
                    flex: 1;
                    overflow: hidden;

                    a {
                        display: block;

                        @include mixins.text-no-wrap();
                    }
                }
            }
        }

        .ticket-order-detail + .ticket-order-popup-form {
            margin-top: 12px;
        }

        .ticket-order-popup-form {
            .margin-left-none {
                .el-form-item__content {
                    margin-left: 0 !important;
                }
            }

            .el-input.is-disabled .el-input__inner {
                color: var(--el-input-text-color, var(--el-text-color-regular));
            }

            .el-form-item {
                margin-bottom: 8px;

                .el-form-item__label {
                    line-height: 18px;
                    align-items: center;
                }

                &.is-error {
                    margin-bottom: 18px;
                }
            }

            .van-cell {
                padding: var(--oa-padding-12) 0;
            }

            .el-radio {
                margin-right: 14px;
            }

            .reply-input-form-item {
                .el-form-item__content {
                    width: calc(100% - 5em);
                    min-width: calc(100% - 5em);
                }
            }

            .chat-account-form-wrapper {
                .oa-cell-wrapper.oa-cell-wrapper--normal.oa-cell-wrapper--center {
                    height: auto !important;
                    min-height: auto !important;

                    .oa-cell__value-wrapper {
                        width: 100%;
                        min-width: 100%;
                    }

                    .el-checkbox {
                        height: auto;
                        width: 90%;
                        min-width: 90%;
                    }

                    .el-checkbox__label {
                        white-space: pre-wrap;
                        word-break: break-all;
                        padding: 8px;
                        line-height: 18px;
                        flex: 1;
                    }

                    .el-checkbox__input {
                        z-index: 0;
                    }
                }

                .session-list-wrapper {
                    max-height: 200px;
                    overflow-y: auto;
                    overflow-x: hidden;
                    border: 1px solid var(--oa-border-color);
                    background: #f5f5f5;
                    margin: 0 4px 12px;

                    .oa-cell__label-wrapper {
                        min-width: var(--oa-padding-12) !important;
                    }

                    .oa-cell__value-wrapper {
                        .el-checkbox {
                            align-items: flex-start;
                            height: auto;

                            .el-checkbox__input {
                                padding-top: 4px;
                            }

                            .el-checkbox__label {
                                padding-top: 0;
                            }
                        }
                    }
                }
            }
        }
    }

    .chat-record-form-item {
        .el-space {
            flex-wrap: wrap;

            .el-space__item {
                min-width: 100%;
            }
        }
    }
}
</style>
