import { ref } from 'vue';
import { CorpAPI } from '@/api/corp-api';
import { Toast } from 'vant';
import { AfterSalesApi } from '@/api/after-sales-api';
import ClinicEmployeeListV2Function from '@/vendor/cloud-function/functions/clinic-employee-list-v2';
import { useSupportOrganHook } from '@/views/client-service/technical-support/hook/organ';
import { CsChatAPI } from '@/api/cs-chat-api';

const { clinicList } = useSupportOrganHook();
const employeeInfo = ref<any>({
    id: '',
    name: '',
    mobile: '',
    wechatNickName: '',
    wechatOpenIdMp: '',
    wechatUnionId: '',
    headImgUrl: '',
    clinic: {
        id: '',
        name: '',
        shortName: '',
        parentId: '',
        parentName: '',
        edition: '',
        mpId: '',
        weappId: '',
        address: '',
        addressAll: '',
        sellerName: '',
        corpUserId: '',
        rolesName: [],
    },
    deviceInfo: [],
});

/**
     * @description: 获取外部用户信息
     * @date: 2024-06-11 20:49:35
     * @author: <PERSON>
     * @param {string} userId 用户id
     * @param {boolean} type 类型 0:1v1 客服,1:客户外部群群聊
     * @param {string | unknown} chatId 群聊id
     * @return
     */
async function fetchEmployeeInfo(userId: string, type = false, chatId?: string | unknown) {
    let res: any = {};
    Toast.loading({
        message: '加载中...',
        duration: 0,
    });
    try {
        if (type) {
            res = await CsChatAPI.getBindOrganByMobileOrUserIdUsingGET(chatId as string, userId);
        } else {
            res = await CorpAPI.getUserByExternalUseridUsingGET(userId, type ? 1 : 0, chatId as string);
        }
    } catch (e: any) {
        Toast.fail(e.message);
    } finally {
        Toast.clear();
    }
    resolveUserInfo(res);
}

/**
     * @description: 处理外部用户信息
     * @date: 2024-06-11 20:51:30
     * @author: Horace
     * @param {AbcAPI.EmployeeWithAllClinicsView} data 用户信息
     * @return
     */
function resolveUserInfo(data: AbcAPI.EmployeeWithAllClinicsView) {
    clinicList.value = data.clinicList || [];
    const clinic: any = data.clinicList?.[0] || {};
    employeeInfo.value = {
        ...employeeInfo.value,
    };
    updateEmployeeInfo(data);
    updateEmployeeClinicInfo(clinic);
}

const externalUserId = ref<string>('');
function updateEmployeeInfo(data: any) {
    employeeInfo.value.id = data.id || '';
    employeeInfo.value.name = data.name || '';
    employeeInfo.value.mobile = data.mobile || '';
    employeeInfo.value.bindFlag = data.bindFlag || '';
    employeeInfo.value.wechatNickName = data.wechatNickName || '';
    employeeInfo.value.wechatOpenIdMp = data.wechatOpenIdMp || '';
    employeeInfo.value.wechatUnionId = data.wechatUnionId || '';
    employeeInfo.value.headImgUrl = data.headImgUrl || '';
    employeeInfo.value.externalUserId = data.externalUserId || externalUserId.value || '';
}
function updateEmployeeClinicInfo(clinic: any) {
    employeeInfo.value.clinic.id = clinic.id || '';
    employeeInfo.value.clinic.name = clinic.name || '';
    employeeInfo.value.clinic.shortName = clinic.shortName || '';
    employeeInfo.value.clinic.rolesName = clinic.rolesName || [];
    employeeInfo.value.clinic.parentId = clinic.parentId || '';
    employeeInfo.value.clinic.parentName = clinic.parentName || '';
    employeeInfo.value.clinic.edition = clinic.edition || '';
    employeeInfo.value.clinic.hisType = clinic.hisType;
    employeeInfo.value.clinic.mpId = clinic.mpId || '';
    employeeInfo.value.clinic.weappId = clinic.weappId || '';
    employeeInfo.value.clinic.address = [clinic.addressProvinceName, clinic.addressCityName].filter(Boolean).join('') || '';
    employeeInfo.value.clinic.addressAll = [clinic.addressProvinceName, clinic.addressCityName, clinic.addressDistrictName].filter(Boolean).join('') || '';
    employeeInfo.value.clinic.sellerName = clinic.sellerName || '';
    employeeInfo.value.clinic.corpUserId = clinic.corpUserId || '';
    employeeInfo.value.clinic.admin = clinic?.admin || {};
    employeeInfo.value.clinic.regionId = clinic?.regionId || '';
    employeeInfo.value.clinic.envName = clinic?.envName || '';
    employeeInfo.value.clinic.firstShebaoTime = clinic?.firstShebaoTime || '';
}

/**
     * @description: 获取员工列表
     * @date: 2024-06-16 19:27:01
     * @author: Horace
     * @param {string} chainId 集团id
     * @param {string} clinicId 机构id
     * @return
     */
async function fetchEmployeeList(chainId: string, clinicId: string) {
    let res: any = {};
    Toast.loading({
        message: '加载中...',
        duration: 0,
    });
    try {
        res = await ClinicEmployeeListV2Function.exec({ chainId, clinicId });
    } catch (e: any) {
        console.error(e.message || e);
    } finally {
        Toast.clear();
    }
    if (!res.status) {
        console.error('ClinicEmployeeListV2Function Response', res.message);
    }
    return res.data?.rows?.map((item: any) => ({
        ...item,
        label: item.name,
        value: item.id,
    })) || [];
}

/**
     * @description: 根据当前员工id获取员工设备
     * @date: 2022-11-22 17:58:11
     * @author: Horace
     * @return
     */
async function getDevicesByEmployee() {
    let res: any = {};
    Toast.loading({
        message: '加载中...',
        duration: 0,
    });
    try {
        res = await AfterSalesApi.getApiLowCodeAfterSalesDevicesByEmployee(employeeInfo.value.id);
    } catch (e: any) {
        Toast.fail(e.message || e);
    } finally {
        Toast.clear();
    }
    if (res && res.devices) {
        employeeInfo.value.deviceInfo = res.devices;
    }
}
export function useSupportEmployeeHook() {
    return {
        employeeInfo,
        externalUserId,
        fetchEmployeeInfo,
        updateEmployeeInfo,
        updateEmployeeClinicInfo,
        getDevicesByEmployee,
        fetchEmployeeList,
    };
}
