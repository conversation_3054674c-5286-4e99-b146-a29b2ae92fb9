import { computed, ref } from 'vue';
import { CorpAPI } from '@/api/corp-api';
import { Toast } from 'vant';
import { useSupportEmployeeHook } from '@/views/client-service/technical-support/hook/employee';
import { ConversationApi } from '@/api/conversation-api';
import { APPID } from '@/utils/ticket';
import dayjs from 'dayjs';
import { agentJsConfig } from '@/utils/wx';
import { useTechnicalSupportHook } from '@/views/client-service/technical-support/hook/index';

const { showLoading, hideLoading } = useTechnicalSupportHook();
const { externalUserId, employeeInfo, fetchEmployeeInfo } = useSupportEmployeeHook();
const roomId = ref<string | unknown>('');
// 是否处于群聊内
const isChatAccount = computed(() => !!roomId.value);

const roomExternalUserList = ref<any[]>([]);

/**
 * @description: 获取会话记录
 * @date: 2023-11-26 09:36:49
 * @author: Horace
 * @param {number} offset
 * @param {number} limit
 * @param {string} roomId
 * @param {Date | string} beginDate
 * @param {Date | string} endDate
 * @return
 */
const getRoomRecordDetail = async (offset: number, limit: number, roomId: string, beginDate: Date | string, endDate: Date | string) => {
    let res: any = {};
    showLoading('正在获取群聊记录！');
    try {
        res = await ConversationApi.getApiLowCodeConversationPageListRoomDetail(
            0,
            100,
            roomId,
            APPID,
            dayjs(beginDate).format('YYYY-MM-DD HH:mm:ss'),
            dayjs(endDate).format('YYYY-MM-DD HH:mm:ss'),
        );
    } catch (e: any) {
        Toast.fail(e.message || e);
        return {
            rows: [],
        };
    } finally {
        hideLoading();
    }
    return res;
};

/**
     * @description: 获取群聊内员工信息
     * @date: 2024-06-16 18:07:09
     * @author: Horace
     * @param {string} roomId 群聊id
     * @return
     */
const getRoomChatExternalUserInfo = async (roomId: string | unknown) => {
    let res: any = {};
    showLoading();
    try {
        res = await CorpAPI.getCustomerGroupDetailUsingGET(roomId as string);
    } catch (e: any) {
        Toast.fail(e.message || e);
    } finally {
        hideLoading();
    }
    roomExternalUserList.value = res.member_list?.filter((item:any) => item.type === 2)?.map((item: any) => ({
        ...item,
        label: item.name,
        value: item.userid,
    })) || [];
};

const roomSortUserList = ref<any[]>([]);

// 专属群绑定状态管理
const boundOrganList = ref<any[]>([]);
const isExclusiveGroupBound = computed(() => boundOrganList.value.length > 0);

/**
     * @description: 获取群聊内最后排序用户
     * @date: 2024-06-16 19:27:32
     * @author: Horace
     * @param {string} roomId 群聊id
     * @return
     */
const getRoomChatLastSortUserList = async (roomId: string | unknown) => {
    let res: any = {};
    showLoading();
    try {
        res = await CorpAPI.getLastChatTicketUserUsingGET(roomId as string);
    } catch (e: any) {
        Toast.fail(e.message || e);
    } finally {
        hideLoading();
    }
    roomSortUserList.value = res.externalUserList?.map((item: any) => ({
        ...item,
        label: item.name,
        value: item.externalUserId,
    })) || [];
    handleRoomSortUserTabChange(roomSortUserList.value?.[0]);
};
    /**
     * @description: 更新群聊用户记录排序
     * @date: 2024-06-16 19:27:15
     * @author: Horace
     * @param {any} data 外部用户信息
     * @return
     */
const updateRoomSortUserList = async (data?: any) => {
    if (data) {
        data.label = data.label || data.wechatNickName || '';
        data.value = data.value || data.externalUserId || '';
        roomSortUserList.value = roomSortUserList.value || [];
        roomSortUserList.value = roomSortUserList.value.filter((item) => item.value !== data.value);
        roomSortUserList.value.unshift({ ...data, label: data.label, value: data.value });
    }
    let res: any = {};
    showLoading();
    try {
        res = await CorpAPI.recordLastChatTicketUsingPOST({
            chatId: roomId.value as string,
            externalUserIdList: roomSortUserList.value?.map((item) => item.value) || [],
        });
    } catch (e: any) {
        Toast.fail(e.message || e);
    } finally {
        hideLoading();
    }
    if (res && res.status) {
        console.log('更新排序成功');
    }
};

async function handleRoomSortUserTabChange(tab: any, isUpdate = false) {
    if (!tab || !tab.value) {
        return;
    }
    externalUserId.value = tab.value;
    await fetchEmployeeInfo(tab.value, isChatAccount.value, roomId.value);
    if (isUpdate) {
        await updateRoomSortUserList(employeeInfo.value);
    }
}
/**
     * @description: 获取当前上下游群聊id
     * @date: 2023-11-21 09:54:24
     * @author: Horace
     * @return
     */
const getCurExternalChat = async () => {
    const { status, message, data } = await agentJsConfig([
        'getContext',
        'getCurExternalChat',
    ]);
    if (status === false) {
        console.error('agentConfig 失败', message, data);
    }
    wx.invoke('getContext', {
    }, (res: any) => {
        if (res.err_msg == 'getContext:ok') {
            console.log('entry', res.entry); // 返回进入H5页面的入口类型，目前有normal、contact_profile、single_chat_tools、group_chat_tools、chat_attachment
            console.log('shareTicket', res.shareTicket); // 可用于调用getShareInfo接口
        } else {
            // 错误处理
            console.log('getContexterror', res);
        }
    });
    return new Promise((resolve) => {
        wx.invoke('getCurExternalChat', {
        }, (res: any) => {
            if (res.err_msg == 'getCurExternalChat:ok') {
                console.log('getCurExternalChat', res);

                resolve(res.chatId);// 返回当前互联群的群ID
            } else {
                console.error('getCurExternalChat Error:', res);
                resolve('');
            }
        });
    });
};

export function useRoomChatHook() {
    return {
        roomId,
        isChatAccount,
        roomExternalUserList,
        boundOrganList,
        isExclusiveGroupBound,
        getRoomChatExternalUserInfo,
        roomSortUserList,
        getRoomChatLastSortUserList,
        updateRoomSortUserList,
        handleRoomSortUserTabChange,
        getRoomRecordDetail,
        getCurExternalChat,
    };
}
