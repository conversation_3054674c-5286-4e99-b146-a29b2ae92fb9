import { useFormat } from '@/composables/date';
import { ElMessage } from 'element-plus';
import dayjs from 'dayjs';
import { ClinicActivityAPI } from '@/api/clinic-activity-api';

export const querySchema = {
    type: 'object',
    component: 'QueryTable',
    name: '查询条件',
    actions: {
        search: {
            type: 'query',
            component: 'Button',
            loading: false,
            componentProps: {
                buttonText: '搜索',
                type: 'primary',
            },
        },
    },
    properties: {
        range: {
            label: '',
            type: 'object',
            component: 'DateRangePicker',
            colSpan: 6,
            properties: {
                beginDate: {
                    type: 'date',
                },
                endDate: {
                    type: 'date',
                },
            },
            flatten: true,
            defaultValue: () => ({
                beginDate: useFormat(dayjs().startOf('month'), 'YYYY-MM-DD', 'start'),
                endDate: useFormat(new Date(), 'YYYY-MM-DD', 'end'),
            }),
            componentProps: {
                startPlaceholder: '开始时间',
                endPlaceholder: '结束时间',
                format: 'YYYY-MM-DD',
                valueFormat: 'YYYY-MM-DD',
                minDate: dayjs().subtract(1, 'year').toDate(), // 最小时间
                maxDate: new Date(), // 最大时间
                clearable: false,
            },
        },
        rewardStatus: {
            label: '',
            type: 'string',
            colSpan: 4,
            offset: 1,
            component: 'Select',
            componentProps: {
                placeholder: '发放状态',
                filterable: true,
                clearable: true,
            },
            selectOptions: [
                { label: '发放中', value: 0 },
                { label: '已发放', value: 10 },
            ],
        },
        keyword: {
            label: '',
            type: 'string',
            colSpan: 4,
            offset: 1,
            component: 'Input',
            componentProps: {
                placeholder: '请输入新购客户名称',
            },
        },
    },
};
export const tableSchema = {
    // 数据源
    dataSource: async (formData: any) => {
        let res: any = {};
        try {
            res = await ClinicActivityAPI.listRewardsUsingGET(
                formData.beginDate,
                formData.endDate,
                formData.limit,
                formData.offset,
                formData.keyword,
                formData.rewardStatus,
            );
        } catch (e: any) {
            ElMessage({
                type: 'error',
                message: e.message || e,
            });
        }
        if (res && res.rows) {
            return { rows: res.rows, total: res.total };
        }
        return [];
    },
    columns: [
        { prop: 'clinicName', name: '新购客户', minWidth: 60 },
        { prop: 'paidTime', name: '新购时间', slot: 'formatTime', minWidth: 60 },
        { prop: 'clueId', name: '转介绍线索ID', slot: 'clueId', minWidth: 60 },
        { prop: 'referrerCode', name: '推荐码', minWidth: 60 },
        { prop: 'referrerWechatNickName', name: '推荐人微信昵称', minWidth: 60 },
        { prop: 'referrerMobile', name: '推荐人手机', minWidth: 60 },
        { prop: 'referrerAbcName', name: '推荐人ABC姓名', minWidth: 60 },
        { prop: 'referrerAbcEmployeeId', name: '推荐人ABC ID', minWidth: 60 },
        { prop: 'rewardStatus', name: '奖励发放状态', slot: 'rewardStatus', minWidth: 60 },
        { prop: 'operate', slot: 'operate', name: '操作' },
    ],
    // 是否在查询条件就绪后立即请求数据
    requestWhenQueryPrepared: true,
    // 是否显示分页
    visiblePagination: true,
    // 是否在查询条件改变后请求数据
    requestWhenQueryChanged: true,
};
