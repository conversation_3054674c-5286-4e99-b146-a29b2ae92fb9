import { RouteRecordRaw } from 'vue-router';

import ClueManagement from './index.vue';
import RewardDistribution from './components/reward-distribution.vue';
import { PermissionClueManagement } from '@/views/clue-management/permission';

const Entry = () => import('@/layout/components/main.vue');

export default [
    {
        path: 'clue-management',
        name: '@ClueManagement',
        component: Entry,
        meta: {
            isEntry: true,
            name: '线索后台',
            icon: 'Files',
            roles: PermissionClueManagement,
        },
        redirect: {
            path: '/clue-management/entry',
        },
        children: [
            {
                path: 'backstage',
                name: '@clue-management/backstage',
                component: ClueManagement,
                alias: '/clue-management/entry',
                meta: {
                    isEntry: true,
                    name: '线索记录管理',
                    icon: 'Link',
                    roles: PermissionClueManagement,
                },
            },
            {
                path: 'reward-distribution',
                name: '@clue-management/reward-distribution',
                component: RewardDistribution,
                meta: {
                    isEntry: true,
                    name: '奖励发放管理',
                    icon: 'Present',
                    roles: PermissionClueManagement,
                },
            },
        ],
    },
] as RouteRecordRaw[];
