import { useFormat } from '@/composables/date';
import { ElMessage } from 'element-plus';
import dayjs from 'dayjs';
import { FOLLOW_UP_STATUS_OPTIONS } from '@/views/clue-management/constant';
import { ClinicActivityAPI } from '@/api/clinic-activity-api';

export const querySchema = {
    type: 'object',
    component: 'QueryTable',
    name: '查询条件',
    actions: {
        search: {
            type: 'query',
            component: 'Button',
            loading: false,
            componentProps: {
                buttonText: '搜索',
                type: 'primary',
            },
        },
    },
    properties: {
        range: {
            label: '',
            type: 'object',
            component: 'DateRangePicker',
            colSpan: 6,
            properties: {
                beginDate: {
                    type: 'date',
                },
                endDate: {
                    type: 'date',
                },
            },
            flatten: true,
            defaultValue: () => ({
                beginDate: useFormat(dayjs().startOf('month'), 'YYYY-MM-DD', 'start'),
                endDate: useFormat(new Date(), 'YYYY-MM-DD', 'end'),
            }),
            componentProps: {
                startPlaceholder: '开始时间',
                endPlaceholder: '结束时间',
                format: 'YYYY-MM-DD',
                valueFormat: 'YYYY-MM-DD',
                minDate: dayjs().subtract(1, 'year').toDate(), // 最小时间
                maxDate: new Date(), // 最大时间
                clearable: false,
            },
        },
        status: {
            label: '',
            type: 'string',
            colSpan: 4,
            offset: 1,
            component: 'Select',
            componentProps: {
                placeholder: '跟进状态',
                filterable: true,
                clearable: true,
            },
            selectOptions: FOLLOW_UP_STATUS_OPTIONS,
        },
        keyword: {
            label: '',
            type: 'string',
            colSpan: 4,
            offset: 1,
            component: 'Input',
            componentProps: {
                placeholder: '请输入电话',
                clearable: true,
            },
        },
    },
};
export const tableSchema = {
    // 数据源
    dataSource: async (formData: any) => {
        let res: any = {};
        try {
            res = await ClinicActivityAPI.listApplyUseUsingGET(
                formData.beginDate,
                formData.endDate,
                formData.limit,
                formData.offset,
                formData.keyword,
                formData.status,
            );
        } catch (e: any) {
            ElMessage({
                type: 'error',
                message: e.message || e,
            });
        }
        if (res && res.rows) {
            return { rows: res.rows, total: res.total };
        }
        return [];
    },
    columns: [
        { prop: 'createTime', name: '申请时间', slot: 'formatTime' },
        { prop: 'id', name: '线索ID', width: 100 },
        { prop: 'contact', name: '联系人' },
        { prop: 'clinicName', name: '机构名称' },
        { prop: 'mobile', name: '电话', slot: 'mobile', align: 'center' },
        { prop: 'channel', name: '来源', slot: 'channel', width: 100 },
        { prop: 'clientIp', name: 'IP' },
        { prop: 'referrerWechatOpenId', name: '推荐人微信openID' },
        { prop: 'referrerMobile', name: '推荐人手机' },
        { prop: 'referrerAbcEmployeeName', name: '推荐人ABC姓名' },
        { prop: 'referrerAbcEmployeeId', name: '推荐人ABC ID' },
        { prop: 'referrerCode', name: '推荐码', width: 100 },
    ],
    // 是否在查询条件就绪后立即请求数据
    requestWhenQueryPrepared: true,
    // 是否显示分页
    visiblePagination: true,
    // 是否在查询条件改变后请求数据
    requestWhenQueryChanged: true,
};
