import CmsPushSimpleVo = AbcAPI.CmsPushSimpleVo;
import QueryCmsPushReq = AbcAPI.QueryCmsPushReq;
import { CmsAPI } from '@/api/cms-api';

export default class AdvertiseRecordService {
    private dataList: CmsPushSimpleVo[];

    private totalCount: number;

    constructor() {
        this.dataList = [];
        this.totalCount = 0;
    }

    async fetchData(params: QueryCmsPushReq) {
        const res = await CmsAPI.queryCmsPushPageUsingPOST(params);
        this.dataList = res?.rows || [];
        this.totalCount = res.total;
    }

    async deleteItem(id: string) {
        const res = await CmsAPI.deleteCmsPushUsingDELETE(id);
        return res;
    }

    getDataList() {
        return this.dataList;
    }

    getTotalCount() {
        return this.totalCount;
    }
}