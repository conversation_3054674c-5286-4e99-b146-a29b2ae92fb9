import {
    AdTypeEnum,
    CmsResourceType,
} from '@/views/content-management/advertisement/advertisement-resource-config/config';

export const ADVERTISEMENT_TYPE = Object.freeze(
    [
        {
            label: '全量展示',
            value: AdTypeEnum.FULL,
            key: AdTypeEnum.FULL,
        },
        {
            label: '条件展示',
            value: AdTypeEnum.CONDITION,
            key: AdTypeEnum.CONDITION,
        },
        {
            label: '迭代推送',
            value: AdTypeEnum.ITERATION,
            key: AdTypeEnum.ITERATION,
        },
        {
            label: '自动推送',
            value: AdTypeEnum.AUTO,
            key: AdTypeEnum.AUTO,
        },
    ],
);

export const ADVERTISEMENT_ENABLE_STATUS = Object.freeze(
    [
        {
            label: '未开始',
            value: 0,
            key: 0,
        },
        {
            label: '已开始',
            value: 1,
            key: 1,
        },
        {
            label: '已结束',
            value: 2,
            key: 2,
        },
    ],
);

export const PRODUCT_TYPE_LIST = Object.freeze(
    [
        {
            label: '诊所管家',
            value: 1,
            key: 1,
        },
        {
            label: '口腔管家',
            value: 2,
            key: 2,
        },
        {
            label: '眼科管家',
            value: 4,
            key: 4,
        },
        {
            label: '医院管家',
            value: 8,
            key: 8,
        },
        {
            label: '药店管家',
            value: 16,
            key: 16,
        },
    ],
);

// 机构类型
export const ORGAN_TYPE_LIST = Object.freeze(
    [
        {
            label: '纯中药',
            value: 1,
            key: 1,
        },
        {
            label: '纯西药',
            value: 2,
            key: 2,
        },
        {
            label: '中西药',
            value: 4,
            key: 4,
        },
        {
            label: '无用药记录',
            value: 8,
            key: 8,
        },
    ],
);

export const PRODUCT_VERSION_LIST = Object.freeze(
    [
        {
            label: '基础版',
            value: 1,
            key: 1,
        },
        {
            label: '专业版',
            value: 2,
            key: 2,
        },
        {
            label: '旗舰版',
            value: 4,
            key: 4,
        },
        {
            label: '大客户版',
            value: 8,
            key: 8,
        },
        {
            label: '眼科基础版',
            value: 16,
            key: 16,
        },
        {
            label: '眼科标准版',
            value: 32,
            key: 32,
        },
        {
            label: '眼科专业版',
            value: 64,
            key: 64,
        },
        {
            label: '眼科旗舰版',
            value: 128,
            key: 128,
        },
    ],
);

export const CLINIC_ROLE_LIST = Object.freeze(
    [
        {
            label: '管理员',
            value: 1,
            key: 1,
        },
        {
            label: '医生',
            value: 2,
            key: 2,
        },
        {
            label: '护士',
            value: 4,
            key: 4,
        },
        {
            label: '检验师',
            value: 8,
            key: 8,
        },
        {
            label: '理疗师',
            value: 16,
            key: 16,
        },
        {
            label: '视光师',
            value: 32,
            key: 32,
        },
        {
            label: '医助',
            value: 64,
            key: 64,
        },
        {
            label: '其他',
            value: 128,
            key: 128,
        },
        {
            label: '采购',
            value: 256,
            key: 256,
        },
    ],
);
export const FEATURE_LIST = Object.freeze(
    [
        {
            label: '医保',
            value: 1,
            key: 1,
        },
        {
            label: '微诊所',
            value: 2,
            key: 2,
        },
        {
            label: 'Lis(联机)',
            value: 4,
            key: 4,
        },
        {
            label: 'Lis(有项目)',
            value: 8,
            key: 8,
        },
        {
            label: '空中药房',
            value: 16,
            key: 16,
        },
        {
            label: '儿保',
            value: 32,
            key: 32,
        },
        {
            label: 'ABC支付',
            value: 64,
            key: 64,
        },
    ],
);

export const RESOURCE_POSITIONS = [
    {
        label: '全量更新通知',
        resType: CmsResourceType.FULL_UPDATE_NOTICE,
        _supportAdType: [AdTypeEnum.ITERATION],
    }, {
        label: '登录页Banner',
        resType: CmsResourceType.LOGIN_BANNER,
        _supportAdType: [AdTypeEnum.FULL],
    }, {
        label: '工作台全屏弹窗',
        resType: CmsResourceType.DASHBOARD_DIALOG,
        _supportAdType: [AdTypeEnum.FULL, AdTypeEnum.CONDITION, AdTypeEnum.ITERATION, AdTypeEnum.AUTO],
    }, {
        label: '工作台右侧Banner',
        resType: CmsResourceType.DASHBOARD_BANNER,
        _supportAdType: [AdTypeEnum.FULL, AdTypeEnum.CONDITION, AdTypeEnum.ITERATION, AdTypeEnum.AUTO],
    }, {
        label: '工作台顶部公告',
        resType: CmsResourceType.DASHBOARD_TOP,
        _supportAdType: [AdTypeEnum.FULL, AdTypeEnum.CONDITION, AdTypeEnum.ITERATION, AdTypeEnum.AUTO],
    }, {
        label: '工作台右侧公告',
        resType: CmsResourceType.DASHBOARD_RIGHT,
        _supportAdType: [AdTypeEnum.FULL, AdTypeEnum.CONDITION, AdTypeEnum.ITERATION, AdTypeEnum.AUTO],
    }, {
        label: '全局紧急通知',
        resType: CmsResourceType.URGENT_NOTICE,
        _supportAdType: [AdTypeEnum.FULL, AdTypeEnum.CONDITION, AdTypeEnum.ITERATION, AdTypeEnum.AUTO],
    }, {
        label: '导航栏气泡',
        resType: CmsResourceType.NAVIGATION_BUBBLE,
        _supportAdType: [AdTypeEnum.FULL, AdTypeEnum.CONDITION, AdTypeEnum.ITERATION, AdTypeEnum.AUTO],
    }, {
        label: ' 内容中心',
        resType: CmsResourceType.CONTENT_CENTER,
        _supportAdType: [AdTypeEnum.FULL, AdTypeEnum.CONDITION, AdTypeEnum.ITERATION, AdTypeEnum.AUTO],
    }, {
        label: 'App推送',
        resType: CmsResourceType.APP_PUSH,
        _supportAdType: [AdTypeEnum.FULL, AdTypeEnum.CONDITION, AdTypeEnum.ITERATION, AdTypeEnum.AUTO],
    }, {
        label: '门诊-中药处方卡片',
        resType: CmsResourceType.OUTPATIENT_CHINESE_MEDICINE_PRESCRIPTION,
        _supportAdType: [AdTypeEnum.FULL, AdTypeEnum.CONDITION],
    }, {
        label: '空中药房卡片-顶部广告语',
        resType: CmsResourceType.AIR_PHARMACY_TOP_ADVERTISEMENT,
        _supportAdType: [AdTypeEnum.FULL, AdTypeEnum.CONDITION],
    }, {
        label: '空中药房卡片-开通广告',
        resType: CmsResourceType.AIR_PHARMACY_OPEN_ADVERTISEMENT,
        _supportAdType: [AdTypeEnum.FULL, AdTypeEnum.CONDITION],
    }, {
        label: '工作台顶部广告位',
        resType: CmsResourceType.DASHBOARD_TOP_ADVERTISEMENT,
        _supportAdType: [AdTypeEnum.FULL, AdTypeEnum.CONDITION, AdTypeEnum.ITERATION, AdTypeEnum.AUTO],
    }, {
        label: '商城PC全屏弹窗',
        resType: CmsResourceType.MALL_PC_FULL_DIALOG,
        _supportAdType: [AdTypeEnum.FULL, AdTypeEnum.CONDITION, AdTypeEnum.ITERATION, AdTypeEnum.AUTO],
    }, {
        label: '商城PC底部弹窗',
        resType: CmsResourceType.MALL_PC_BOTTOM_DIALOG,
        _supportAdType: [AdTypeEnum.FULL, AdTypeEnum.CONDITION, AdTypeEnum.ITERATION, AdTypeEnum.AUTO],
    }, {
        label: '商城小程序全屏弹窗',
        resType: CmsResourceType.MALL_MINIAPP_FULL_DIALOG,
        _supportAdType: [AdTypeEnum.FULL, AdTypeEnum.CONDITION, AdTypeEnum.ITERATION, AdTypeEnum.AUTO],
    }, {
        label: '商城小程序底部弹窗',
        resType: CmsResourceType.MALL_MINIAPP_BOTTOM_DIALOG,
        _supportAdType: [AdTypeEnum.FULL, AdTypeEnum.CONDITION, AdTypeEnum.ITERATION, AdTypeEnum.AUTO],
    },
];

export const NewOrOldCustomer = {
    ALL: 0,
    NEW: 1,
    OLD: 2,
};

export const OrganEnvList = [
    {
        label: '预发布',
        value: 1,
    },
    {
        label: '灰度',
        value: 2,
    },
    {
        label: '正式',
        value: 4,
    },
];

// 产品使用度
export const PROD_USAGE_TYPE_LIST = [
    {
        label: '活跃中',
        value: 1,
    },
    {
        label: '待促活',
        value: 2,
    },
    {
        label: '待新装',
        value: 4,
    },
    {
        label: '已新装',
        value: 8,
    },
    {
        label: '待激活',
        value: 16,
    },
    {
        label: '已激活',
        value: 32,
    },
];

// 商城药品新老客
export const MALL_NEW_OLD_CUSTOMER = [
    {
        label: '流失',
        value: 1,
    },
    {
        label: '意向',
        value: 2,
    },
    {
        label: '活跃',
        value: 4,
    },
    {
        label: '流失预警',
        value: 8,
    },
];
