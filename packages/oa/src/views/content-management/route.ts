import { RouteRecordRaw } from 'vue-router';
import { PermissionContentManagementSet } from './permission';

const ModuleEntry = () => import('@/layout/components/module-entry.vue');
const Advertisement = () => import('@/views/content-management/advertisement/index.vue');
const AdvertisementRecord = () => import('@/views/content-management/advertisement/record/index.vue'); // 广告记录
const AdvertisementRecordForm = () => import('@/views/content-management/advertisement/advertisement-form/index.vue'); // 新增编辑广告
const ResourceManagement = () => import('@/views/content-management/advertisement/resource/index.vue');

const Content = () => import('@/views/content-management/content/index.vue');
const ArticleList = () => import('@/views/content-management/content/article-list/index.vue');
const ArticleForm = () => import('@/views/content-management/content/article-form/index.vue');

export default [
    {
        path: '/content-management',
        name: '@contentManagement',
        component: ModuleEntry,
        meta: {
            isEntry: true,
            name: '内容管理',
            icon: 'postcard',
            roles: PermissionContentManagementSet,
        },
        children: [
            {
                path: '/content-management/advertisement',
                name: '@contentManagement/advertisement',
                component: Advertisement,
                meta: {
                    name: '广告管理',
                    roles: PermissionContentManagementSet,

                },
                redirect: {
                    path: '/content-management/advertisement/record',
                },
                children: [
                    {
                        path: '/content-management/advertisement/record',
                        name: '@contentManagement/advertisement/record',
                        component: AdvertisementRecord,
                        meta: {
                            isEntryPage: false,
                            name: '广告记录',
                            roles: PermissionContentManagementSet,

                        },
                    },
                    {
                        path: '/content-management/advertisement/resource',
                        name: '@contentManagement/advertisement/resource',
                        component: ResourceManagement,
                        meta: {
                            name: '资源位管理',
                            roles: PermissionContentManagementSet,

                        },
                    },
                    {
                        path: '/content-management/advertisement/add',
                        name: '@content-management/advertisement/add',
                        component: AdvertisementRecordForm,
                        meta: {
                            hidden: true, // 隐藏subMenu子模块展示
                            name: '新增广告',
                            roles: PermissionContentManagementSet,

                        },
                    },
                    {
                        path: '/content-management/advertisement/edit/:id',
                        name: '@content-management/advertisement/edit',
                        component: AdvertisementRecordForm,
                        meta: {
                            hidden: true,
                            name: '编辑广告',
                            roles: PermissionContentManagementSet,

                        },
                    },
                ],
            },
            {
                path: '/content-management/content',
                name: '@contentManagement/content',
                component: Content,
                meta: {
                    name: '内容管理',
                    roles: PermissionContentManagementSet,

                },
                redirect: {
                    path: '/content-management/content/article-list',
                },
                children: [
                    {
                        path: '/content-management/content/article-list',
                        name: '@contentManagement/content/article-list',
                        component: ArticleList,
                        meta: {
                            name: '文章列表',
                            roles: PermissionContentManagementSet,

                        },
                    },
                    {
                        path: '/content-management/content/article-form/add',
                        name: '@contentManagement/content/article-form/add',
                        component: ArticleForm,
                        meta: {
                            name: '新增文章',
                            hidden: true,
                            roles: PermissionContentManagementSet,

                        },
                    },
                    {
                        path: '/content-management/content/article-form/:id',
                        name: '@contentManagement/content/article-form',
                        component: ArticleForm,
                        meta: {
                            name: '编辑文章',
                            hidden: true,
                            roles: PermissionContentManagementSet,

                        },
                    },
                ],
            },
        ],
    },
] as RouteRecordRaw[];