<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElDatePicker, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElButton } from 'element-plus';
import { Search, Refresh } from '@element-plus/icons-vue';
import { CrawlerWarningAction, actionLabelMap, ClinicLimitStatus, statusLabelMap } from '../model';

const props = defineProps<{
  loading: boolean;
}>();

const emit = defineEmits<{(e: 'search'): void;
  (e: 'reset'): void;
}>();

const formRef = ref();
const form = reactive({
    clinicId: '',
    employeeId: '',
    status: undefined as ClinicLimitStatus | undefined,
    timeRange: null as [string, string] | null,
    action: undefined as string | undefined,
});

// 获取今天的开始和结束时间
const getTodayRange = (): [string, string] => {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0);
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);
    
    const formatDate = (date: Date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    };
    
    return [formatDate(startOfDay), formatDate(endOfDay)];
};

// 时间范围快捷选项
const shortcuts = [
    {
        text: '今天',
        value: () => {
            const today = new Date();
            const start = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0);
            const end = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);
            return [start, end];
        },
    },
    {
        text: '昨天',
        value: () => {
            const yesterday = new Date();
            yesterday.setDate(yesterday.getDate() - 1);
            const start = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 0, 0, 0);
            const end = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59);
            return [start, end];
        },
    },
    {
        text: '近一周',
        value: () => {
            const end = new Date();
            end.setHours(23, 59, 59, 999);
            const start = new Date();
            start.setDate(start.getDate() - 6);
            start.setHours(0, 0, 0, 0);
            return [start, end];
        },
    },
    {
        text: '近一月',
        value: () => {
            const end = new Date();
            end.setHours(23, 59, 59, 999);
            const start = new Date();
            start.setDate(start.getDate() - 29);
            start.setHours(0, 0, 0, 0);
            return [start, end];
        },
    },
];

// 初始化默认时间为今天
onMounted(() => {
    form.timeRange = getTodayRange();
});

// 重置表单
const resetForm = () => {
    formRef.value?.resetFields();
    form.timeRange = getTodayRange();
    form.clinicId = '';
    form.employeeId = '';
    form.status = undefined;
    form.action = undefined;
    emit('reset');
};

// 提交查询
const submitForm = () => {
    emit('search');
};

// 将表单数据转换为查询参数
const getQueryParams = () => {
    const { clinicId, employeeId, status, timeRange, action } = form;
    return {
        clinicId: clinicId || undefined,
        employeeId: employeeId || undefined,
        status,
        beginDate: timeRange ? timeRange[0] : undefined,
        endDate: timeRange ? timeRange[1] : undefined,
        action,
    };
};

// 暴露方法给父组件
defineExpose({
    getQueryParams,
    resetForm,
});
</script>

<template>
    <el-form
        ref="formRef"
        :model="form"
        :inline="true"
        class="crawler-warning-search-form"
    >
        <el-form-item label="门店ID">
            <el-input
                v-model="form.clinicId"
                placeholder="请输入门店ID"
                clearable
                :disabled="loading"
            />
        </el-form-item>
        <el-form-item label="员工ID">
            <el-input
                v-model="form.employeeId"
                placeholder="请输入员工ID"
                clearable
                :disabled="loading"
            />
        </el-form-item>
        <el-form-item label="状态">
            <el-select
                v-model="form.status"
                placeholder="请选择状态"
                clearable
                :disabled="loading"
            >
                <el-option
                    v-for="(label, value) in statusLabelMap"
                    :key="value"
                    :label="label"
                    :value="Number(value)"
                />
            </el-select>
        </el-form-item>
        <el-form-item label="告警时间">
            <el-date-picker
                v-model="form.timeRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                value-format="YYYY-MM-DD HH:mm:ss"
                :disabled="loading"
                :clearable="false"
                :shortcuts="shortcuts"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
            />
        </el-form-item>
        <el-form-item label="执行动作">
            <el-select
                v-model="form.action"
                placeholder="请选择执行动作"
                clearable
                :disabled="loading"
            >
                <el-option label="强制下线" value="force_logout" />
                <el-option label="禁用账号" value="disable_employee" />
                <el-option label="数据混淆" value="data_obfuscation" />
                <el-option label="观察" value="observe" />
                <el-option label="人机验证" value="captcha" />
            </el-select>
        </el-form-item>
        <el-form-item>
            <el-button
                type="primary"
                :icon="Search"
                :loading="loading"
                @click="submitForm"
            >
                查询
            </el-button>
            <el-button :icon="Refresh" :disabled="loading" @click="resetForm">
                重置
            </el-button>
        </el-form-item>
    </el-form>
</template>

<style lang="scss" scoped>
.crawler-warning-search-form {
    margin-bottom: 16px;
    padding: 18px;
    background-color: #fff;
    border-radius: 4px;
}
</style>
