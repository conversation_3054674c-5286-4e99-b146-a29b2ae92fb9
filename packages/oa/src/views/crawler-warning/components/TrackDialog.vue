<script setup lang="ts">
import { ref, onMounted, watch, onBeforeUnmount, nextTick } from 'vue';
import { ElDialog, ElEmpty, ElButton, ElButtonGroup, ElDatePicker, ElFormItem, ElSlider, ElLoading, ElMessage } from 'element-plus';
import { AntBotAPI } from '@/api/ant-bot-api';
import dayjs from 'dayjs';
import MouseTimelineDialog from './MouseTimelineDialog.vue';

const props = defineProps<{
  visible: boolean;
  warningId?: number | string;
  warningInfo?: any;
  defaultTimeRange?: [string, string] | null;
}>();

const emit = defineEmits<{(e: 'update:visible', value: boolean): void;
}>();

const dialogVisible = ref(false);
const canvasRef = ref<HTMLCanvasElement | null>(null);
const canvasCtx = ref<CanvasRenderingContext2D | null>(null);
const scale = ref(1);
const offsetX = ref(0);
const offsetY = ref(0);
const canvasWidth = ref(0);
const canvasHeight = ref(0);
const animationProgress = ref(0);
const isAnimating = ref(false);
const animationFrameId = ref<number | null>(null);
const animationSpeed = ref(0.02); // 动画速度控制
const timeRange = ref<any>(null);
const loading = ref(false);
const trackData = ref<any>(null);
const timelineDialogVisible = ref(false);

watch(() => props.visible, (val) => {
    dialogVisible.value = val;
    if (val) {
        // 重置所有状态
        resetAllState();
        // 初始化默认时间范围
        initDefaultTimeRange();
        // 如果有轨迹ID，自动加载轨迹数据
        if (props.warningId) {
            loadTrackData();
        }
    } else {
        // 弹窗关闭时清理状态
        cleanupOnClose();
    }
});

watch(() => trackData.value, (val) => {
    if (val && val.positions && val.positions.length > 0) {
        nextTick(() => {
            initCanvas();
        });
    }
});

watch(() => dialogVisible.value, (val) => {
    emit('update:visible', val);
});

// 初始化默认时间范围
const initDefaultTimeRange = () => {
    // 优先使用外部传入的时间范围
    if (props.defaultTimeRange && props.defaultTimeRange[0] && props.defaultTimeRange[1]) {
        timeRange.value = props.defaultTimeRange;
    } else {
        // 否则使用：列表 created 之前一小时到 created 的时间范围；若无 created 则回退为今天
        const created = (props.warningInfo as any)?.created;
        if (created) {
            const end = dayjs(created);
            const begin = end.subtract(1, 'hour');
            timeRange.value = [begin.format('YYYY-MM-DD HH:mm:ss'), end.format('YYYY-MM-DD HH:mm:ss')];
        } else {
            timeRange.value = [
                dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
            ];
        }
    }
};

// 加载轨迹数据
const loadTrackData = async () => {
    if (!props.warningId || !props.warningInfo) return;
    
    loading.value = true;
    try {
        // 使用当前时间范围查询轨迹数据
        const [beginDate, endDate] = timeRange.value || [
            dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
            dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
        ];
        
        console.log('查询轨迹数据:', beginDate, endDate);
        
        // 调用鼠标轨迹查询API
        const response: any = await AntBotAPI.queryMouseTraceUsingGET(
            props.warningInfo.clinicId,
            1000, // limit
            0, // offset
            props.warningInfo.uuid,
            beginDate,
            endDate,
        );

        if (response && response.rows) {
            // 转换轨迹数据格式
            const newTrackData = {
                positions: [] as any[],
            };

            // 处理前端事件数据，转换为轨迹点
            response.rows.forEach((item: AbcAPI.FrontEventReportItem) => {
                if (item.evts && item.evts.length > 0) {
                    item.evts.forEach((evt: AbcAPI.FrontEvent) => {
                        newTrackData.positions.push({
                            x: evt.cx,
                            y: evt.cy,
                            timestamp: evt.t,
                            type: evt.et === 'mc' ? 'click' : 'move',
                            url: item.url || '', // 添加页面URL
                            dc: evt.dc || '', // 添加点击内容
                        });
                    });
                }
            });

            // 按时间戳排序
            newTrackData.positions.sort((a, b) => a.timestamp - b.timestamp);
            
            trackData.value = newTrackData;
        } else {
            // 即使没有轨迹数据也设置空对象
            trackData.value = { positions: [] };
        }
    } catch (error) {
        console.error('获取轨迹数据失败', error);
        ElMessage.error('获取轨迹数据失败');
        trackData.value = { positions: [] };
    } finally {
        loading.value = false;
    }
};

// 快捷时间范围设置
const setQuickTimeRange = (type: 'today' | 'yesterday' | 'week' | 'month') => {
    const now = dayjs();
    
    switch (type) {
        case 'today':
            timeRange.value = [
                now.startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                now.endOf('day').format('YYYY-MM-DD HH:mm:ss'),
            ];
            break;
        case 'yesterday':
            const yesterday = now.subtract(1, 'day');
            timeRange.value = [
                yesterday.startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                yesterday.endOf('day').format('YYYY-MM-DD HH:mm:ss'),
            ];
            break;
        case 'week':
            timeRange.value = [
                now.subtract(7, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                now.endOf('day').format('YYYY-MM-DD HH:mm:ss'),
            ];
            break;
        case 'month':
            timeRange.value = [
                now.subtract(30, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                now.endOf('day').format('YYYY-MM-DD HH:mm:ss'),
            ];
            break;
    }
    
    // 设置时间范围后自动查询
    loadTrackData();
};

// 时间范围变化处理
const handleTimeRangeChange = () => {
    console.log('时间范围变化:', timeRange.value);
    loadTrackData();
};

// 初始化Canvas
const initCanvas = () => {
    if (!canvasRef.value) return;
    
    // 获取画布和上下文
    const canvas = canvasRef.value;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    canvasCtx.value = ctx;
    
    // 设置画布尺寸
    resizeCanvas();
    
    // 启动动画绘制轨迹
    startTrackAnimation();
};

// 启动轨迹绘制动画
const startTrackAnimation = () => {
    if (isAnimating.value) return;
    
    isAnimating.value = true;
    animationProgress.value = 0;
    
    const animate = () => {
        animationProgress.value += animationSpeed.value; // 使用动态速度控制
        
        if (animationProgress.value >= 1) {
            animationProgress.value = 1;
            isAnimating.value = false;
        }
        
        drawTrack(animationProgress.value);
        
        if (isAnimating.value) {
            animationFrameId.value = requestAnimationFrame(animate);
        }
    };
    
    animate();
};

// 停止动画
const stopAnimation = () => {
    if (animationFrameId.value) {
        cancelAnimationFrame(animationFrameId.value);
        animationFrameId.value = null;
    }
    isAnimating.value = false;
};

// 重置所有状态
const resetAllState = () => {
    // 停止当前动画
    stopAnimation();
    
    // 重置画布状态
    scale.value = 1;
    offsetX.value = 0;
    offsetY.value = 0;
    animationProgress.value = 0;
    
    // 清空轨迹数据
    trackData.value = null;
    
    // 重置画布上下文
    canvasCtx.value = null;
    
    // 关闭时间轴弹窗
    timelineDialogVisible.value = false;
};

// 弹窗关闭时清理状态
const cleanupOnClose = () => {
    // 停止动画
    stopAnimation();
    
    // 清空画布
    if (canvasCtx.value && canvasRef.value) {
        canvasCtx.value.clearRect(0, 0, canvasRef.value.width, canvasRef.value.height);
    }
    
    // 重置所有状态
    animationProgress.value = 0;
    isAnimating.value = false;
    
    // 重置时间范围
    timeRange.value = null;
    
    // 重置播放倍数
    animationSpeed.value = 0.02;
    
    // 重置画布状态
    scale.value = 1;
    offsetX.value = 0;
    offsetY.value = 0;
    
    // 清空轨迹数据
    trackData.value = null;
    
    // 关闭子弹窗
    timelineDialogVisible.value = false;
};

// 调整画布大小
const resizeCanvas = () => {
    if (!canvasRef.value) return;
    
    // 获取容器大小
    const container = canvasRef.value.parentElement;
    if (!container) return;
    
    canvasWidth.value = container.clientWidth;
    canvasHeight.value = container.clientHeight;
    
    // 设置Canvas尺寸
    canvasRef.value.width = canvasWidth.value;
    canvasRef.value.height = canvasHeight.value;
};

// 绘制鼠标轨迹
const drawTrack = (progress = 1) => {
    if (!canvasCtx.value || !canvasRef.value) return;
    if (!trackData.value || !trackData.value.positions || trackData.value.positions.length === 0) return;
    
    const ctx = canvasCtx.value;
    const positions = trackData.value.positions;
    
    // 清空画布
    ctx.clearRect(0, 0, canvasRef.value.width, canvasRef.value.height);
    
    // 找到坐标范围
    let minX = positions[0].x;
    let maxX = positions[0].x;
    let minY = positions[0].y;
    let maxY = positions[0].y;
    
    positions.forEach((point: any) => {
        minX = Math.min(minX, point.x);
        maxX = Math.max(maxX, point.x);
        minY = Math.min(minY, point.y);
        maxY = Math.max(maxY, point.y);
    });
    
    // 添加边距
    const padding = 50;
    const rangeX = maxX - minX + padding * 2;
    const rangeY = maxY - minY + padding * 2;
    
    // 计算缩放比例
    const scaleX = canvasWidth.value / rangeX;
    const scaleY = canvasHeight.value / rangeY;
    const baseScale = Math.min(scaleX, scaleY);
    
    // 应用缩放和偏移
    const effectiveScale = baseScale * scale.value;
    
    // 绘制网格线
    drawGrid(ctx, minX - padding, minY - padding, rangeX, rangeY, effectiveScale);
    
    // 开始绘制轨迹
    ctx.save();
    ctx.translate(offsetX.value, offsetY.value);
    ctx.scale(effectiveScale, effectiveScale);
    ctx.translate(-(minX - padding), -(minY - padding));
    
    // 计算要绘制的点数量（基于动画进度）
    const pointsToShow = Math.floor(positions.length * progress);
    
    if (pointsToShow > 1) {
        // 绘制平滑曲线路径
        drawSmoothPath(ctx, positions.slice(0, pointsToShow), effectiveScale);
    }
    
    // 绘制点标记
    drawPoints(ctx, positions.slice(0, pointsToShow), effectiveScale, progress);
    
    ctx.restore();
};

// 绘制准确连接的路径
const drawSmoothPath = (ctx: CanvasRenderingContext2D, positions: any[], effectiveScale: number) => {
    if (positions.length < 2) return;
    
    // 创建渐变色（带退化回退）
    const start = positions[0];
    const end = positions[positions.length - 1];
    let strokeStyle: string | CanvasGradient = '#1890ff';
    // 当首尾点不重合时使用渐变，避免某些环境下零长度渐变导致不可见
    if (!(start.x === end.x && start.y === end.y)) {
        const gradient = ctx.createLinearGradient(start.x, start.y, end.x, end.y);
        gradient.addColorStop(0, 'rgba(82, 196, 26, 1)');
        gradient.addColorStop(0.5, 'rgba(24, 144, 255, 1)');
        gradient.addColorStop(1, 'rgba(245, 34, 45, 1)');
        strokeStyle = gradient;
    } else {
        // 退化为纯色
        strokeStyle = 'rgba(24, 244, 64, 1)';
    }
    
    // 绘制阴影
    ctx.shadowColor = 'rgba(24, 144, 255, 0.3)';
    ctx.shadowBlur = 4 / effectiveScale;
    ctx.shadowOffsetX = 1 / effectiveScale;
    ctx.shadowOffsetY = 1 / effectiveScale;
    
    // 绘制主路径 - 直接连接各点，确保线条经过每个点
    ctx.beginPath();
    ctx.moveTo(positions[0].x, positions[0].y);
    
    // 直接连线到每个点，不使用贝塞尔曲线
    for (let i = 1; i < positions.length; i++) {
        ctx.lineTo(positions[i].x, positions[i].y);
    }
    
    // 明确设置绘制合成模式，避免受其他操作影响
    const prevComposite = ctx.globalCompositeOperation;
    ctx.globalCompositeOperation = 'source-over';
    ctx.strokeStyle = strokeStyle as any;
    // 设置最小线宽，避免缩放导致线宽过小而不可见
    ctx.lineWidth = Math.max(1.5, 3 / effectiveScale);
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    ctx.stroke();
    // 还原合成模式
    ctx.globalCompositeOperation = prevComposite;
    
    // 清除阴影
    ctx.shadowColor = 'transparent';
    ctx.shadowBlur = 0;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;
};

// 绘制点标记
const drawPoints = (ctx: CanvasRenderingContext2D, positions: any[], effectiveScale: number, progress: number) => {
    positions.forEach((point: any, index: number) => {
        const isLastPoint = index === positions.length - 1;
        const isFirstPoint = index === 0;
        const isClickPoint = point.type === 'click';
        
        // 绘制点击位置的特殊效果
        if (isClickPoint) {
            drawClickEffect(ctx, point, effectiveScale);
        }
        
        // 绘制主要点标记
        ctx.beginPath();
        let radius = 4 / effectiveScale;
        
        if (isFirstPoint || isLastPoint) {
            radius = 6 / effectiveScale;
            // 绘制外圈光晕
            ctx.beginPath();
            ctx.arc(point.x, point.y, radius + 3 / effectiveScale, 0, Math.PI * 2);
            ctx.fillStyle = isFirstPoint ? 'rgba(82, 196, 26, 0.3)' : 'rgba(245, 34, 45, 0.3)';
            ctx.fill();
        }
        
        // 主点
        ctx.beginPath();
        ctx.arc(point.x, point.y, radius, 0, Math.PI * 2);
        
        if (isFirstPoint) {
            // 起点 - 绿色渐变
            const startGradient = ctx.createRadialGradient(point.x, point.y, 0, point.x, point.y, radius);
            startGradient.addColorStop(0, '#73d13d');
            startGradient.addColorStop(1, '#52c41a');
            ctx.fillStyle = startGradient;
        } else if (isLastPoint) {
            // 终点 - 红色渐变
            const endGradient = ctx.createRadialGradient(point.x, point.y, 0, point.x, point.y, radius);
            endGradient.addColorStop(0, '#ff7875');
            endGradient.addColorStop(1, '#f5222d');
            ctx.fillStyle = endGradient;
        } else {
            // 中间点 - 蓝色
            ctx.fillStyle = '#1890ff';
        }
        
        ctx.fill();
        
        // 为起点和终点添加边框
        if (isFirstPoint || isLastPoint) {
            ctx.beginPath();
            ctx.arc(point.x, point.y, radius, 0, Math.PI * 2);
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 2 / effectiveScale;
            ctx.stroke();
        }
    });
};

// 绘制点击效果
const drawClickEffect = (ctx: CanvasRenderingContext2D, point: any, effectiveScale: number) => {
    const radius = 12 / effectiveScale;
    
    // 绘制脉冲圆圈
    for (let i = 0; i < 3; i++) {
        ctx.beginPath();
        ctx.arc(point.x, point.y, radius - i * 3 / effectiveScale, 0, Math.PI * 2);
        ctx.strokeStyle = `rgba(245, 34, 45, ${0.6 - i * 0.2})`;
        ctx.lineWidth = 2 / effectiveScale;
        ctx.stroke();
    }
    
    // 绘制十字标记
    ctx.beginPath();
    const crossSize = 8 / effectiveScale;
    ctx.moveTo(point.x - crossSize, point.y);
    ctx.lineTo(point.x + crossSize, point.y);
    ctx.moveTo(point.x, point.y - crossSize);
    ctx.lineTo(point.x, point.y + crossSize);
    ctx.strokeStyle = '#f5222d';
    ctx.lineWidth = 3 / effectiveScale;
    ctx.lineCap = 'round';
    ctx.stroke();
};

// 绘制网格线
const drawGrid = (ctx: CanvasRenderingContext2D, startX: number, startY: number, width: number, height: number, scale: number) => {
    ctx.save();
    
    // 绘制背景渐变
    const bgGradient = ctx.createLinearGradient(0, 0, canvasWidth.value, canvasHeight.value);
    bgGradient.addColorStop(0, '#fafafa');
    bgGradient.addColorStop(1, '#f5f5f5');
    ctx.fillStyle = bgGradient;
    ctx.fillRect(0, 0, canvasWidth.value, canvasHeight.value);
    
    // 绘制细网格
    ctx.strokeStyle = 'rgba(0, 0, 0, 0.05)';
    ctx.lineWidth = 0.5;
    
    const gridSizeY = 25;
    const gridCountY = Math.ceil(height / gridSizeY);
    
    for (let i = 0; i <= gridCountY; i++) {
        const y = i * gridSizeY;
        ctx.beginPath();
        ctx.moveTo(0, y * scale + offsetY.value);
        ctx.lineTo(canvasWidth.value, y * scale + offsetY.value);
        ctx.stroke();
    }
    
    const gridSizeX = 25;
    const gridCountX = Math.ceil(width / gridSizeX);
    
    for (let i = 0; i <= gridCountX; i++) {
        const x = i * gridSizeX;
        ctx.beginPath();
        ctx.moveTo(x * scale + offsetX.value, 0);
        ctx.lineTo(x * scale + offsetX.value, canvasHeight.value);
        ctx.stroke();
    }
    
    // 绘制主网格线
    ctx.strokeStyle = 'rgba(0, 0, 0, 0.1)';
    ctx.lineWidth = 1;
    
    const mainGridSizeY = 100;
    const mainGridCountY = Math.ceil(height / mainGridSizeY);
    
    for (let i = 0; i <= mainGridCountY; i++) {
        const y = i * mainGridSizeY;
        ctx.beginPath();
        ctx.moveTo(0, y * scale + offsetY.value);
        ctx.lineTo(canvasWidth.value, y * scale + offsetY.value);
        ctx.stroke();
    }
    
    const mainGridSizeX = 100;
    const mainGridCountX = Math.ceil(width / mainGridSizeX);
    
    for (let i = 0; i <= mainGridCountX; i++) {
        const x = i * mainGridSizeX;
        ctx.beginPath();
        ctx.moveTo(x * scale + offsetX.value, 0);
        ctx.lineTo(x * scale + offsetX.value, canvasHeight.value);
        ctx.stroke();
    }
    
    ctx.restore();
};

// 放大
const zoomIn = () => {
    scale.value *= 1.2;
    drawTrack(1);
};

// 缩小
const zoomOut = () => {
    scale.value /= 1.2;
    drawTrack(1);
};

// 重置视图 - 重置到网格画布
const resetView = () => {
    // 停止当前动画
    stopAnimation();
    
    // 重置视图参数
    scale.value = 1;
    offsetX.value = 0;
    offsetY.value = 0;
    animationProgress.value = 0;
    
    // 重绘网格背景（不显示轨迹）
    if (canvasCtx.value && canvasRef.value && trackData.value && trackData.value.positions && trackData.value.positions.length > 0) {
        const ctx = canvasCtx.value;
        const positions = trackData.value.positions;
        
        // 清空画布
        ctx.clearRect(0, 0, canvasRef.value.width, canvasRef.value.height);
        
        // 计算坐标范围（用于绘制网格）
        let minX = positions[0].x;
        let maxX = positions[0].x;
        let minY = positions[0].y;
        let maxY = positions[0].y;
        
        positions.forEach((point: any) => {
            minX = Math.min(minX, point.x);
            maxX = Math.max(maxX, point.x);
            minY = Math.min(minY, point.y);
            maxY = Math.max(maxY, point.y);
        });
        
        // 添加边距
        const padding = 50;
        const rangeX = maxX - minX + padding * 2;
        const rangeY = maxY - minY + padding * 2;
        
        // 计算缩放比例
        const scaleX = canvasWidth.value / rangeX;
        const scaleY = canvasHeight.value / rangeY;
        const baseScale = Math.min(scaleX, scaleY);
        
        // 只绘制网格背景
        drawGrid(ctx, minX - padding, minY - padding, rangeX, rangeY, baseScale);
    } else if (canvasCtx.value && canvasRef.value) {
        // 如果没有轨迹数据，只绘制简单的网格背景
        const ctx = canvasCtx.value;
        ctx.clearRect(0, 0, canvasRef.value.width, canvasRef.value.height);
        drawGrid(ctx, 0, 0, canvasWidth.value, canvasHeight.value, 1);
    }
};

// 重播动画
const replayAnimation = () => {
    stopAnimation();
    startTrackAnimation();
};

// 显示鼠标时间轴
const showMouseTimeline = () => {
    timelineDialogVisible.value = true;
};

// 处理鼠标拖拽
const isDragging = ref(false);
const lastPosition = ref({ x: 0, y: 0 });

const handleMouseDown = (e: MouseEvent) => {
    isDragging.value = true;
    lastPosition.value = { x: e.clientX, y: e.clientY };
};

const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging.value) return;
    
    const deltaX = e.clientX - lastPosition.value.x;
    const deltaY = e.clientY - lastPosition.value.y;
    
    offsetX.value += deltaX;
    offsetY.value += deltaY;
    
    lastPosition.value = { x: e.clientX, y: e.clientY };
    drawTrack(1);
};

const handleMouseUp = () => {
    isDragging.value = false;
};

// 处理滚轮缩放
const handleWheel = (e: WheelEvent) => {
    e.preventDefault();
    
    // 根据滚轮方向放大或缩小
    if (e.deltaY < 0) {
        scale.value *= 1.1;
    } else {
        scale.value /= 1.1;
    }
    
    drawTrack(1);
};

// 窗口大小改变时重绘
const handleResize = () => {
    resizeCanvas();
    drawTrack(1);
};

// 监听窗口大小改变
onMounted(() => {
    if (props.visible && props.trackData) {
        nextTick(() => {
            initCanvas();
        });
    }
    
    window.addEventListener('resize', handleResize);
});

// 移除事件监听器
onBeforeUnmount(() => {
    window.removeEventListener('resize', handleResize);
    // 确保清理所有状态
    cleanupOnClose();
});

defineExpose({});
</script>

<template>
    <el-dialog
        v-model="dialogVisible"
        title="用户鼠标轨迹"
        width="900px"
        destroy-on-close
        append-to-body
    >
        <!-- 时间范围筛选 - 始终显示 -->
        <div class="track-time-filter">
            <el-form-item label="时间范围：" class="time-range-item">
                <div class="time-range-controls">
                    <el-date-picker
                        v-model="timeRange"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                        style="max-width: 400px;"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        format="YYYY-MM-DD HH:mm:ss"
                        :disabled="loading"
                        :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                    />
                    <el-button
                        type="primary"
                        size="small"
                        :loading="loading"
                        @click="handleTimeRangeChange"
                    >
                        查询
                    </el-button>
                </div>
            </el-form-item>
            
            <!-- 快捷时间范围选择 -->
            <div class="quick-time-range">
                <span class="quick-time-label">快捷选择：</span>
                <el-button-group>
                    <el-button size="small" @click="setQuickTimeRange('today')">今天</el-button>
                    <el-button size="small" @click="setQuickTimeRange('yesterday')">昨天</el-button>
                    <el-button size="small" @click="setQuickTimeRange('week')">近一周</el-button>
                    <el-button size="small" @click="setQuickTimeRange('month')">近一月</el-button>
                </el-button-group>
            </div>
        </div>

        <div v-if="trackData && trackData.positions && trackData.positions.length > 0">
            <div class="track-dialog-toolbar">
                <el-button-group>
                    <el-button size="small" @click="zoomIn">放大</el-button>
                    <el-button size="small" @click="zoomOut">缩小</el-button>
                    <el-button size="small" @click="resetView">重置</el-button>
                </el-button-group>
                <div class="animation-speed-control">
                    <span class="speed-label">动画速度：</span>
                    <el-slider
                        v-model="animationSpeed"
                        :min="0.001"
                        :max="0.1"
                        :step="0.005"
                        :show-tooltip="false"
                        style="width: 120px;"
                    />
                    <span class="speed-value">{{ Math.round(animationSpeed * 1000) }}x</span>
                </div>
                <div class="track-info">
                    <span>鼠标轨迹点数：{{ trackData.positions.length }}</span>
                    <span v-if="trackData.startTime && trackData.endTime">
                        时间区间：{{ new Date(trackData.startTime).toLocaleTimeString() }} - {{ new Date(trackData.endTime).toLocaleTimeString() }}
                    </span>
                </div>
                <el-space>
                    <el-button size="small" :disabled="isAnimating" @click="replayAnimation">重播动画</el-button>
                    <el-button size="small" type="primary" @click="showMouseTimeline">轨迹详情</el-button>
                </el-space>
            </div>
            <div class="canvas-container">
                <canvas 
                    ref="canvasRef" 
                    style="cursor: move;" 
                    @mousedown="handleMouseDown" 
                    @mousemove="handleMouseMove" 
                    @mouseup="handleMouseUp"
                    @mouseleave="handleMouseUp"
                    @wheel="handleWheel"
                ></canvas>
            </div>
            <div class="track-legend">
                <div class="legend-item">
                    <span class="legend-marker start-point"></span>
                    <span>起始点</span>
                </div>
                <div class="legend-item">
                    <span class="legend-marker end-point"></span>
                    <span>终点</span>
                </div>
                <div class="legend-item">
                    <span class="legend-marker click-point"></span>
                    <span>点击位置</span>
                </div>
            </div>
        </div>
        <el-empty v-else class="canvas-container" description="暂无轨迹数据" />
        
        <!-- 鼠标时间轴弹窗 -->
        <MouseTimelineDialog
            :visible="timelineDialogVisible"
            :track-data="trackData"
            :warning-info="warningInfo"
            @update:visible="timelineDialogVisible = $event"
        />
    </el-dialog>
</template>

<style lang="scss" scoped>
.track-time-filter {
    margin-bottom: 16px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;

    .time-range-item {
        margin-bottom: 0;

        :deep(.el-form-item__label) {
            font-weight: 500;
            color: #495057;
        }
    }

    .time-range-controls {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .quick-time-range {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-top: 12px;

        .quick-time-label {
            font-size: 13px;
            color: #666;
            white-space: nowrap;
        }
    }
}

.track-dialog-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    gap: 16px;
}

.animation-speed-control {
    display: flex;
    align-items: center;
    gap: 8px;

    .speed-label {
        font-size: 13px;
        color: #666;
        white-space: nowrap;
    }

    .speed-value {
        font-size: 12px;
        color: #1890ff;
        font-weight: 500;
        min-width: 30px;
        text-align: center;
    }
}

.track-info {
    display: flex;
    gap: 12px;
    font-size: 13px;
    color: #666;
}

.canvas-container {
    width: 100%;
    height: 450px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, .06);
}

.track-legend {
    display: flex;
    gap: 24px;
    margin-top: 12px;
    padding: 8px 12px;
    background: #f5f5f5;
    border-radius: 4px;
    font-size: 13px;

    .legend-item {
        display: flex;
        align-items: center;
        gap: 6px;
    }

    .legend-marker {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;

        &.start-point {
            background-color: #52c41a;
        }

        &.end-point {
            background-color: #f5222d;
        }

        &.click-point {
            background-color: rgba(245, 34, 45, .2);
            border: 2px solid #f5222d;
            position: relative;

            &::before,
            &::after {
                content: '';
                position: absolute;
                background-color: #f5222d;
            }

            &::before {
                width: 6px;
                height: 1.5px;
                top: 4.25px;
                left: 1px;
            }

            &::after {
                width: 1.5px;
                height: 6px;
                top: 1px;
                left: 4.25px;
            }
        }
    }
}
</style>
