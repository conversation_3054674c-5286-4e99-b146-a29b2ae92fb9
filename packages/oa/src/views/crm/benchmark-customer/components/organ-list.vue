<script setup lang="ts">
import { nextTick, onMounted, reactive, ref, watch } from 'vue';
import { useDebounceFn } from '@vueuse/core';
import { PageParams } from '@/common/model/page-params';
import { CrmClientApi } from '@/api/crm-client-api';
import { ElMessage } from 'element-plus/es';
import { tableSchema } from './schema';
import OaList from '@/components/oa-list.vue';
import { formatEdition, formatHisTypeName } from '@/utils/format';
import { useRouter } from 'vue-router';
import { Toast } from 'vant';

interface Query {
    page: number;
    pageSize: number;
    keyword: string;
    [key: string]: any;
}

const query = ref<Query>({
    keyword: '',
    status: '',
    version: '',
    type: '',
    wechat: '',
    sellerId: '',
    page: 1,
    pageSize: 100,

});
// 清空搜索框时，重新搜索
watch(() => query.value.keyword, (val) => {
    if (!val) {
        list.value.tableData = [];
        list.value.totalCount = 0;
        paginationListRef.value?.handleClear();
    }
});

const list = ref<any>({
    tableData: [],
    totalCount: 0,
    addedTotal: 0,
    additionRate: '0%',
    isTableLoading: false,
    finished: false,
});

const paginationListRef = ref();
const externalSearchValue = ref();
/**
 * 筛选店铺
 * @param val
 */
const handleQueryKeywordChange = async (val: string) => {
    externalSearchValue.value = val;
    query.value.keyword = val;
    query.value.page = 1;
    await nextTick();
    paginationListRef.value?.check();
};
/**
 * 分页切换
 * @param pageParams
 */
async function paginationListProvider(pageParams: PageParams, isReset = false) {
    query.value.page = pageParams.offset / pageParams.limit + 1;
    !isReset && await queryTableData(query.value);
    return {
        rows: list.value.tableData,
        limit: pageParams.limit,
        offset: pageParams.offset,
        total: list.value.totalCount,
    };
}
const debounceQueryKeyword = useDebounceFn(handleQueryKeywordChange, 1000);
async function queryTableData(formData: any) {
    const { keyword } = formData;
    let res = <any>{};
    list.value.isTableLoading = true;
    try {
        res = await CrmClientApi.getApiLowCodeCrmOrganListPage(
            (formData.page - 1) * formData.pageSize,
            formData.pageSize,
            keyword,
        );
    } catch (e:any) {
        list.value.isTableLoading = false;
        Toast.fail(e.message || e);
    }
    if (res && res.rows) {
        list.value.tableData = res.rows;
        list.value.totalCount = res.total;
        list.value.isTableLoading = false;
        list.value.finished = res.rows.length < formData.pageSize;
    }
}

const debounceQuery = useDebounceFn(queryTableData, 500);

const formatAddressName = (row: any) => `${row.addressCityName || ''}${row.addressDistrictName || ''}`;

const router = useRouter();
const handleItemClick = (row: any) => {
    const { id } = row;
    router.push({
        name: '@crm/benchmark-customer/detail',
        params: { id, type: 'add' },
    });
};
</script>

<template>
    <div class="query-table__h5-wrapper organ-list-wrapper">
        <van-cell class="client-list-card">
            <template #value>
                <div class="h5-select__search-bar-wrapper">
                    <van-search
                        ref="searchRef"
                        v-model="query.keyword"
                        class="client-list-search"
                        show-action
                        shape="round"
                        :disabled="list.isTableLoading"
                        placeholder="门店名称"
                    >
                        <template #action>
                            <div @click="debounceQueryKeyword(query.keyword)">搜索</div>
                        </template>
                    </van-search>
                </div>
            </template>
        </van-cell>
        <oa-list
            ref="paginationListRef"
            class="organ-list-data-wrapper"
            :immediate-check="false"
            :data-provider="paginationListProvider"
            :page-size="query.pageSize"
            :external-search-value="externalSearchValue"
            :searchable="tableSchema.search?.enable"
        >
            <template #default="{item}">
                <van-cell class="organ-list-item" @click="handleItemClick(item)">
                    <template #value>
                        <div>
                            <p class="organ-list-vendor-name">{{ item.name }}</p>
                            <p class="organ-list-item-info">
                                <span v-if="item.hisType || item.hisType === 0">{{ formatHisTypeName(item.hisType, false) }}</span>
                                <span v-if="item.edition?.editionId">{{ formatEdition(item.edition?.editionId) }}</span>
                                <span v-if="formatAddressName(item)">{{ formatAddressName(item) }}</span>
                                <span v-if="item.sellerInfo?.sellerName">{{ item.sellerInfo?.sellerName || '' }}</span>
                            </p>
                        </div>
                        <van-icon name="arrow" />
                    </template>
                </van-cell>
            </template>
        </oa-list>
    </div>
</template>
<style lang="scss">
@mixin white {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.organ-list-wrapper {
    margin-top: 0;

    .organ-list-search {
        .el-button.is-text:not(.is-disabled):focus,
        .el-button.is-text:not(.is-disabled):hover {
            background: none;
        }
    }

    .client-list-search {
        .el-input__wrapper {
            padding-right: 0;
        }
    }

    .organ-list-card + .organ-list-card {
        margin-top: 8px;
    }

    .organ-list-card {
        border-radius: 4px;

        .van-field {
            background: #f7f7f7;
        }

        .van-popup.van-popup--top {
            margin: 0 var(--oa-page-padding);
            width: calc(100% - var(--oa-page-padding) * 2);
        }
    }

    .organ-list-divider {
        color: #6e6e6e;
        border-color: #ccc;
    }

    .organ-list-data-wrapper {
        padding: 16px;

        .organ-list-item {
            border: 1px solid #ccc;
            border-radius: 4px;

            .van-cell__value {
                display: flex;
                align-items: center;
                justify-content: space-between;
            }

            .organ-list-vendor-name {
                display: flex;
                align-items: center;
                justify-content: space-between;
                color: #000;
                font-size: 14px;
                font-weight: 700;
            }

            .organ-list-item-info {
                span:first-child {
                    padding-right: 4px;
                }

                span + span {
                    border-left: 1px solid #ccc;
                    padding: 0 4px;
                }
            }

            .organ-list-item-info-item {
                font-size: 12px;
                color: #383838;
            }

            .tag-list + .tag-list {
                margin-left: 8px;
            }
        }

        .organ-list-item + .organ-list-item {
            margin-top: 8px;
        }
    }
}
</style>
