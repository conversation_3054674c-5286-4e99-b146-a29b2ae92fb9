<script setup lang="ts">
import GridMenu from '@/components/grid-menu.vue';
import useNavigator from '@/composables/navigator';
import { useMenuStore } from '@/store/menu';
import { computed } from 'vue';
import { useRoute } from 'vue-router';

// const userStore = useUserStore();
// const columns = reactive([
//     { name: '姓名', prop: 'name' },
//     { name: '用户ID', prop: 'userId' },
//     { name: '职位', prop: 'position' },
//     { name: '手机号', prop: 'mobile' },
//     { name: '性别', prop: 'gender' },
//     { name: '邮箱', prop: 'email' },
//     { name: '部门', prop: 'departments' },
//     { name: 'Tags', prop: 'tags' },
// ]);

const menuStore = useMenuStore();
const { toMenu } = useNavigator();
const route = useRoute();
const menus = computed(() => menuStore.menus.filter(menu => menu.name !== route.name));

</script>
<template>
    <div class="dashboard-wrapper layout-page-wrapper">
        <grid-menu :menus="menus" @click="toMenu"></grid-menu>
        <!--        <h1>欢迎你，{{ userStore.userInfo.name }}</h1>-->
        <!--        <kv-card :info="userStore.userInfo" :columns="columns"></kv-card>-->
    </div>
</template>
