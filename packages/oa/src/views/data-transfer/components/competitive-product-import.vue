<script lang="ts" setup>
import { competitiveProductImportFormSchema as formSchema } from '@/views/data-transfer/model/schema';
import { onBeforeUnmount, onMounted, ref } from 'vue';
import { H5Form } from '@/vendor/x-form';
import { Form } from '@/vendor/x-form/core/form';
import { ElMessage } from 'element-plus';
import { DataTransferApi } from '@/api/data-transfer-api';
import { Toast } from 'vant';
import { sleep } from '@/utils/utils';
import { useRoute, useRouter } from 'vue-router';
import DataTransferDataTypes = AbcAPI.DataTransferDataTypes;
import { useUserStore } from '@/store/user';
import CreateCompetitiveProductDto = AbcAPI.CreateCompetitiveProductDto;
import { TransferTypeEnum } from '@/views/data-transfer/model/model';

const dataTypes = ref<DataTransferDataTypes[]>([]);
const route = useRoute();
const router = useRouter();
const operateType = ref('add');
const dataSource = ref<any>(null);
onMounted(async () => {
    await getDataTypes();
    await getCompetitiveProductList();
    const { transferDataId } = route.query;
    if (transferDataId) {
        await getTransferDetailById(TransferTypeEnum.COMPETITIVE_PRODUCT_IMPORT, transferDataId);
        operateType.value = 'edit';
    } else {
        dataSource.value = {};
    }
});
const getTransferDetailById = async (type: number, id: any) => {
    let res: any = {};
    try {
        res = await DataTransferApi.getApiLowCodeDataTransferRecordDetailById(type, id);
    } catch (e: any) {
        ElMessage.error(e.message || e);
    }
    dataSource.value = res;
};
let formControl: Form;
const handlePrepared = (form: Form) => {
    formControl = form;
};

/**
 * @description: 获取数据类型
 * @date: 2022-12-14 13:38:36
 * @author: Horace
 * @param null:
 * @return
*/
const getDataTypes = async () => {
    let res: any = {};
    try {
        res = await DataTransferApi.getApiLowCodeDataTransferDataTransferTypeList();
    } catch (e: any) {
        ElMessage.error(e.message || e);
    }
    if (res && res.rows) {
        dataTypes.value = res.rows.map((item: any) => ({
            label: item.name,
            value: item.id,
            ...item,
        }));
    }
};

const otherCompetitiveProductIds = ref<number[]>([]);
/**
 * 获取竞品名称列表
 */
const getCompetitiveProductList = async () => {
    let res: any = {};
    try {
        res = await DataTransferApi.getApiLowCodeDataTransferCompetitiveProductList(formControl?.formData?.isSaas);
    } catch (e: any) {
        ElMessage({
            type: 'error',
            message: e.message || e,
        });
    }
    if (res && res.rows) {
        const rows = res.rows.filter((item: any) => item.name !== 'ABC数字医疗云').map((item: any) => ({
            label: item.name,
            value: item.id + '',
            ...item,
        }));
        otherCompetitiveProductIds.value = rows.filter((item: any) => item.name === '其他')?.map((item: any) => item.id + '') || [];
        formControl?.setQuerySchemaSelectOptions('competitorSupportId', rows);
    }
};
const userStore = useUserStore();
const userInfo = userStore.userInfo;
const handleAction = async ({ action, formData, valid }: any) => {
    const organInfo: any = formControl.getField('clinicId').getBundle();
    if (action.name === 'submit') {
        if (!organInfo?.name) {
            Toast.fail('请重新选择确认导入门店信息');
            return;
        }
        if (!valid) {
            Toast.fail('请完善表单内容');
            return;
        }
        if (checkVersionAndCompetitorSupport(formData)) {
            return;
        }
        let res: any = {};
        delete formData.id;
        const postData: CreateCompetitiveProductDto = {
            chainId: organInfo?.chainId || '',
            chainName: organInfo?.chainName || '',
            clinicName: organInfo?.name || '',
            editionName: organInfo?.editionName || '',
            adminName: organInfo?.adminName || '',
            adminMobile: organInfo?.adminMobile || '',
            userName: userInfo.name || '',
            ...formData,
        };

        Toast.loading(`正在${operateType.value === 'add' ? '创建' : '编辑' }竞品数据迁移单`);
        const { transferDataId, recordId } = route.query;
        try {
            if (operateType.value === 'add') {
                res = await DataTransferApi.postApiLowCodeDataTransferCompetitiveProductImportCreate(postData);
            } else {
                res = await DataTransferApi.putApiLowCodeDataTransferCompetitiveProductImportUpdateById(
                    { ...postData, transferId: recordId + '' }, transferDataId + '',
                );
            }
        } catch (e: any) {
            Toast.fail(e.message || e);
        }
        if (res && res.result) {
            await sleep(2000);
            Toast.success(`${operateType.value === 'add' ? '创建' : '编辑' }单据成功`);
            await router.push({ name: '@data-transfer/record' });
        }
    }
};
let isSaas: number;
let competitorSupportId = '';
let clinicId = '';
const handleChange = ({ formData, property }: any) => {
    if (property === 'isSaas') {
        isSaas = formData.isSaas;
        getCompetitiveProductList();
    }

    if (clinicId !== formData.clinicId || competitorSupportId !== formData.competitorSupportId) {
        checkVersionAndCompetitorSupport(formData);
    }
    checkCompetitorSupport(formData);
};
/**
 * @description: 校验基础版不能迁移其他竞品
 * @date: 2022-12-06 11:15:41
 * @author: Horace
 * @param data: 表单数据
 * @return
*/
const checkVersionAndCompetitorSupport = (data: any) => {
    const organInfo = formControl?.getField('clinicId')?.getBundle();
    if (
        organInfo?.editionName === '基础版'
            && data.competitorSupportId
            && otherCompetitiveProductIds.value.includes(data.competitorSupportId)
    ) {
        Toast.fail('基础版不支持其他竞品系统的免费数据迁移服务，请联系自己leader确认数据迁移费用！');
        return true;
    }
    clinicId = data.clinicId;
    return false;
};
/**
 * @description: 当竞品改变支持迁移当数据类型也发生改变
 * @date: 2022-12-06 11:17:38
 * @author: Horace
 * @param data: 表单数据
 * @return
*/
const checkCompetitorSupport = (data: any) => {
    competitorSupportId = data.competitorSupportId;
    const selectOptions = formControl?.getQuerySchemaSelectOptions('competitorSupportId');
    const competitorSupport = selectOptions.find(
        (item: any) => item.value === data.competitorSupportId,
    );
    const types = dataTypes.value.filter((item: any) => competitorSupport?.dataTypeIdsSupport?.includes(item.id));
    formControl?.setQuerySchemaOptions('types', types.length ? types : []);
};
</script>
<template>
    <h5-form
        v-if="dataSource"
        :schema="formSchema"
        :data="dataSource"
        class="competitive-product-import-wrapper"
        @action="handleAction"
        @prepared="handlePrepared"
        @change="handleChange"
    >
    </h5-form>
</template>
<style lang="scss">
.competitive-product-import-wrapper {
    .van-checkbox-group {
        flex-direction: column;

        .van-checkbox + .van-checkbox {
            margin-top: 12px;
        }
    }

    .data-transfer-order-status {
        .h5-field__value-wrapper {
            color: #de0c0c;
        }
    }
}
</style>
