<script lang="ts" setup>
import { migrateRecordTableSchema as tableSchema, migrateRecordQuerySchema as querySchema } from '@/views/data-transfer/model/schema';
import QueryTable from '@/components/query-table.vue';
import { useFormat } from '@/composables/date';
import { TransferType, TransferRecordStatus, TransferTypeEnum } from '@/views/data-transfer/model/model';
import { useRouter } from 'vue-router';
import { DataTransferApi } from '@/api/data-transfer-api';
import DeletedDataTransferDto = AbcAPI.DeletedDataTransferDto;
import { Dialog, Toast } from 'vant';
import { nextTick, ref } from 'vue';
import { ElMessage } from 'element-plus/es';
import { useDataTransferStore } from '@/views/data-transfer/store';

const router = useRouter();
const dataTransferStore = useDataTransferStore();
const editTransfer = async (record: any) => {
    const pathNameOptions:any = {
        [TransferTypeEnum.TABLE_IMPORT]: '@data-transfer/table-import',
        [TransferTypeEnum.COMPETITIVE_PRODUCT_IMPORT]: '@data-transfer/competitive-product-import',
        [TransferTypeEnum.ABC_CLINIC_IMPORT]: '@data-transfer/clinic-import',
        [TransferTypeEnum.DATA_CLEARING]: '@data-transfer/data-clearing' };
    const pathName = pathNameOptions[record.type];
    await router.push({
        name: pathName,
        query: {
            transferDataId: record.transferDataId,
            recordId: record.id,
        },
    });
};
const recordRef = ref<any>(null);
const formData = ref<any>({});
const handlePrepared = (form: any) => {
    formData.value = form?.formData;
};
const deletedTransfer = async (record: any) => {
    Dialog.confirm({
        title: '删除数据迁移单',
        message:
            '是否确认删除该迁移单？',
    })
                    .then(async () => {
                        let res: any = {};
                        const data: DeletedDataTransferDto = {
                            type: record.type,
                            transferId: record.id,
                        };
                        try {
                            res = await DataTransferApi.deleteApiLowCodeDataTransferDeletedById(data, record.transferDataId);
                        } catch (e: any) {
                            Toast.fail(e.message || e);
                        }
                        if (res && res.length) {
                            Toast.success('删除成功！');
                            await nextTick();
                            recordRef.value?.debounceQuery(formData.value || {});
                        }
                    })
                    .catch(() => {
                        Toast('取消删除！');
                    });
};
</script>
<template>
    <div class="migrate-record-wrapper">
        <query-table
            ref="recordRef"
            :table-schema="tableSchema"
            :query-schema="querySchema"
            :use-kv-card="false"
            @prepared="handlePrepared"
        >
            <template #formatTime="{column, row}">
                {{ useFormat(row[column.prop],'YYYY-MM-DD HH:mm:ss') }}
            </template>
            <template #type="{column, row}">
                {{ TransferType[row[column.prop]] || '' }}
            </template>
            <template #status="{column, row}">
                <div class="reject-reason-box">
                    {{ TransferRecordStatus[row[column.prop]] || '' }}
                    <el-tooltip
                        v-if="row[column.prop] === 4"
                        effect="dark"
                    >
                        <template #content>
                            <span v-html="row.rejectReason"></span>
                        </template>
                        <el-icon
                            class="reject-reason-tooltip"
                        >
                            <QuestionFilled />
                        </el-icon>
                    </el-tooltip>
                </div>
            </template>
            <template #operate="{row}">
                <el-link
                    v-if="row.status !== 3 && row.status !== 5 "
                    class="migrate-record-operate-btn"
                    type="primary"
                    @click="editTransfer(row)"
                >
                    编辑
                </el-link>
                <el-link
                    v-if="row.status !== 3 "
                    class="migrate-record-operate-btn"
                    type="danger"
                    @click="deletedTransfer(row)"
                >
                    删除
                </el-link>
                <el-link
                    v-if="row.status === 3"
                    class="migrate-record-operate-btn"
                    type="primary"
                    @click="editTransfer(row)"
                >
                    查看
                </el-link>
            </template>
        </query-table>
    </div>
</template>
<style lang="scss">
.migrate-record-operate-btn + .migrate-record-operate-btn {
    margin-left: 12px;
}

.migrate-record-wrapper {
    .reject-reason-box {
        display: flex;
        align-items: center;

        .reject-reason-tooltip {
            margin-left: 12px;
        }
    }
}
</style>