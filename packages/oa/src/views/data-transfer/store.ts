import { useLocalStorage } from '@vueuse/core';
import { defineStore } from 'pinia';

export const useDataTransferStore = defineStore('dataTransfer', {
    state: () => ({
        // 重新提交时正在编辑的数据
        editFormData: useLocalStorage<any>('__edit_form_data__', null),
    }),

    getters: {
        currentFormData(state) {
            if (state.editFormData) {
                try {
                    return JSON.parse(state.editFormData);
                } catch (e) {

                }
            }
            return null;
        },
    },

    actions: {
        setEditFormData(formData: any) {
            this.editFormData = JSON.stringify(formData);
        },

        clearEditFormData() {
            this.editFormData = null;
        },
    },
});
