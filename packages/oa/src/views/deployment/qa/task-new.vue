<script setup lang="ts">
import { DeploymentApi } from '@/api/deployment-api';
import { OpsAPI } from '@/api/ops-api';
import { useFormat } from '@/composables/date';
import { <PERSON> } from '@/vendor/jenkins';
import { ref, computed, onMounted, onUnmounted, reactive } from 'vue';
import {
    DeployTagStatus,
    ExecType,
    formatDeployTagStatus,
    formatDeployTagStatusClass, getIngStatus,
    isBuild,
    isDeployed,
    K8sInfo,
    NormalRowServiceOptions, isIngStatus,
} from '@/views/deployment/model';
import { useDeploymentStore } from '@/views/deployment/store';
import { ElMessage, ElMessageBox, ElSwitch } from 'element-plus/es';
import { clone, isNumber } from '@/utils/utils';
import { useThemeConfigStore } from '@/store/theme-config';
import DeployTask = AbcAPI.DeployTask;
import _ from 'lodash';
import TagRegionStatus from '@/views/deployment/task/tag-region-status.vue';
import TagOperateBtns from '@/views/deployment/task/tag-operate-btns.vue';

const props = defineProps({
    id: {
        type: String,
        required: true,
    },
});
const vmData = reactive({
    currentBusiness: '',
    rows: <any>[],
    selectedDeployMap: <Record<string, Array<string>>> {},
    task: <DeployTask><any>null,
    k8sInfoMap: <Record<string, Record<string, K8sInfo[]>>> {},
    k8sRegionInfoMap: <Record<string, Record<string, K8sInfo[]>>> {},
    checkK8s: false,
});
const deploymentStore = useDeploymentStore();

const deployRegionList = [
    { id: 1, name: 'region1' },
    { id: 2, name: 'region2' },
];

const latestTagMap = ref<any>(new Map());

const isTaskOpen = computed(() => vmData.task?.status === 0);

const businessGroups = computed(() => {
    const rows = clone(vmData.rows);
    const businessGroups = rows.reduce((acc: any, cur: any) => {
        const businessList = cur.jenkinsInfo?.length
            // 根据 business 去重
            ? [...new Set<string>(cur.jenkinsInfo.map((item: any) => item.business))]
            : [cur.jenkinsBusiness];
        const env = cur.env;
        const k8sKey = cur.serviceName;

        businessList.forEach((business: string) => {
            // 先按照 business 分组
            acc[business] = acc[business] || {};
            // 再按照 env 分组
            acc[business][env] = acc[business][env] || {};
            acc[business][env].pods = acc[business][env].pods || [];
            if (business != 'abc-his') {
                // 补充 K8s 信息，abc-his 补充到 regionK8sInfo 上
                cur.k8sInfo = vmData.k8sRegionInfoMap[business + '-' + env]?.[k8sKey];
                // 容器的 tag >= 当前发布的 tag
                cur.isPublished = cur.k8sInfo?.every((pod: K8sInfo) => pod.Version >= cur.name);
            }
            // 过滤出属于该 business 的 jenkins
            const pod = {
                ...cur,
                jenkinsInfo: cur.jenkinsInfo?.filter((item: any) => item.business === business),
            };
            acc[business][env].pods.push(pod);
        });
        return acc;
    }, {});

    const businessArr = Object.keys(businessGroups).map((business: string) => {
        const envGroups = businessGroups[business];
        const envArr: Array<{
          env: string,
          envLabel: string|undefined,
          pods: Array<K8sInfo>,
          successCount: number,
          totalCount: number
        }> = Object.keys(envGroups).map((env: string) => {
            let { pods } = envGroups[env];
            pods?.forEach((pod: any) => {
                if (!NormalRowServiceOptions.includes(pod.prefix)) {
                    if (latestTagMap.value.has(pod.prefix)) {
                        const created = latestTagMap.value.get(pod.prefix)?.created;
                        if (pod.created > created) {
                            latestTagMap.value.set(pod.prefix, { created: pod.created, id: pod.id });
                        }
                    } else {
                        latestTagMap.value.set(pod.prefix, { created: pod.created, id: pod.id });
                    }
                }
                pod.regionStatus = pod.regionInfo?.reduce((acc: any, cur: any) => {
                    const regionName = cur.name;
                    const regionK8sInfo = vmData.k8sRegionInfoMap[regionName + '-' + env]?.[pod.serviceName];
                    acc[cur.name] = {
                        status: cur.status,
                        k8sInfo: regionK8sInfo,
                        isPublished: regionK8sInfo?.every((podK8s: K8sInfo) => podK8s.Version >= pod.name),
                    };
                    return acc;
                }, {});

                pod.regionComposeStatus = pod.status;
                const allRegionDeployed = pod.regionInfo.length && pod.regionInfo.every((region: any) => isDeployed(region.status));
                const anyRegionBuild = pod.regionInfo.some((region: any) => isBuild(region.status));
                if (!isIngStatus(pod.status)) {
                    if (allRegionDeployed) {
                        pod.regionComposeStatus = pod.status;
                    } else if (anyRegionBuild) {
                        pod.regionComposeStatus = DeployTagStatus.ONLY_BUILD_SUCCESS;
                    }
                }
            });
            const successCount = pods.filter((pod: any) => [DeployTagStatus.BUILD_SUCCESS, DeployTagStatus.ONLY_DEPLOY_SUCCESS].includes(pod.status)).length;
            const totalCount = pods.length;
            return {
                business,
                env,
                envLabel: { pre: '预发布P', gray: '灰度G', prod: '正式V' }[env],
                pods,
                successCount,
                totalCount,
            };
        }).sort((a:any, b: any) => a.priority - b.priority);
        return { business, envArr };
    });
    return businessArr;
});

const businessList = computed(() => businessGroups.value.reduce((acc: any, cur: any) => {
    const envs = cur.envArr.map((env: any) => env.env);
    if (cur.business === 'abc-his') {
    // abc-his 特殊处理，同时 push region1 和 region2
        acc.push({
            name: 'region1',
            envs,
        });
        acc.push({
            name: 'region2',
            envs,
        });
    } else {
        acc.push({
            name: cur.business,
            envs,
        });
    }
    return acc;
}, []));

const businessOptions = computed(() => businessGroups.value.reduce((acc: any, cur: any) => {
    const envs = cur.envArr.map((env: any) => env.env);
    acc.push({
        name: cur.business,
        envs,
    });
    return acc;
}, []));

// 根据本文件中 el-table-column 的 prop 生成一个表头配置columns
const columns = [
    {
        title: 'TAG',
        dataKey: 'name',
        width: 240,
    },
    {
        title: '需求/BUG',
        dataKey: 'remark',
        width: 240,
    },
    {
        title: '影响面评估',
        dataKey: 'operation',
        width: 180,
    },
    {
        title: '需要配合提测的服务',
        dataKey: 'deps',
        width: 120,
    },
    {
        title: 'Jenkins状态',
        dataKey: 'status',
        width: 240,
    },
    {
        title: '构建部署',
        dataKey: 'btns',
        width: 280,
    },
    {
        title: '创建人',
        dataKey: 'createdBy',
        width: 110,
    },
    {
        title: '创建时间',
        dataKey: 'created',
        width: 136,
    },
    {
        title: '操作',
        dataKey: 'buttons',
        width: 60,
    },
];

const jenkins = new Jenkins();
const themeConfig = useThemeConfigStore();
onMounted(async () => {
    await getGroupOptions();
    await getEnvironmentOptions();
    themeConfig.setCollapse(true);
    await fetchDetail();
    // 当前处于打开状态，才轮询状态
    if (isTaskOpen.value) {
        await checkPublishStatus();
        fetchAllK8sInfo();
    }
});

onUnmounted(() => {
    if (timer) {
        clearTimeout(timer);
    }
});

async function fetchDetail() {
    const task = await DeploymentApi.getApiLowCodeDeploymentDeployTaskById(props.id);
    vmData.task = task;
    vmData.rows = task.tags;
}

async function handlePublishClick(tag: any, execType: ExecType = ExecType.PUBLISH) {
    let selectedDeployRegion = vmData.selectedDeployMap[tag.business + tag.jenkinsId + tag.name];
    if (tag.supportRegionDeploy && !selectedDeployRegion?.length) {
        ElMessage({
            type: 'error',
            message: '请至少选择一个分区发布',
        });
        return;
    }
    console.log('tag', tag.jenkinsUrl, 'execType', execType, 'deployRegion', selectedDeployRegion, tag.name);
    let featureNo;
    let jenkinsUrl = tag.jenkinsUrl;
    // feature 需要输入no
    if (tag.jenkinsCount > 1) {
        try {
            const { value } = await ElMessageBox.prompt(
                '请输入 test 或者 featureNo (1-40)',
                '提示',
                {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                    showInput: true,
                    inputValidator: (str) => str === 'test' || isNumber(+str) && (+str >= 1 && +str <= 40),
                    inputErrorMessage: '请输入 test 或者 featureNo (1-40)',
                },
            );
            featureNo = value;
        } catch (e) {
            return;
        }
        if (featureNo === 'test') {
            featureNo = '';
            jenkinsUrl = tag.jenkinsInfo.find((item: any) => !item.name.toLowerCase().includes('feature')).url;
        } else {
            featureNo = +featureNo;
            jenkinsUrl = tag.jenkinsInfo.find((item: any) => item.name.toLowerCase().includes('feature')).url;
        }

        console.log('使用url', jenkinsUrl, featureNo);
    }
    const response = await jenkins.triggerJob(jenkinsUrl, {
        repoTag: tag.name,
        tagName: tag.name,
        featureNo,
        envNo: featureNo,
        execType,
        deployRegion: selectedDeployRegion,
    });
    if (!response.status) {
        ElMessage({
            type: 'error',
            message: response.message || '构建失败，请确认是否正确配置 Jenkins 密钥',
        });
        return;
    }
    try {
        await DeploymentApi.putApiLowCodeDeploymentDeployTagById({
            status: getIngStatus(execType),
        }, tag.id);
        await fetchDetail();
    } catch (e: any) {
        ElMessage({
            type: 'error',
            message: e.message || '发布失败',
        });
    }
}

async function handleDeleteClick(tag: any) {
    try {
        await DeploymentApi.deleteApiLowCodeDeploymentDeployTagById(tag.id);
        fetchDetail();
    } catch (e: any) {
        ElMessage({
            type: 'error',
            message: e.message || '删除失败',
        });
    }
}

let timer:any = null;
async function checkPublishStatus() {
    if (timer) {
        clearTimeout(timer);
    }
    timer = setTimeout(async () => {
        await fetchDetail();
        if (vmData.checkK8s) {
            await fetchAllK8sInfo();
        }
        await checkPublishStatus();
    }, 5000);
}

function tableRowClassName({ row }: {row: any}) {
    let className = '';
    if (latestTagMap.value.get(row.prefix) && latestTagMap.value.get(row.prefix)?.id !== row.id) {
        className += ' disabled-row';
    }
    if (!row.jenkinsId) {
        className += ' warning-row';
    }
    return className;
}

async function fetchK8sServiceInfoByRegion(region: string, env: string) {
    try {
        const k8sInfo = await OpsAPI.getK8sServiceInfoByRegion(region, env);
        const k8sRegionInfoList = k8sInfo.Pods.filter((item: K8sInfo) => !!item.ServiceName).reduce((acc: any, cur: K8sInfo) => {
            acc[cur.ServiceName] = acc[cur.ServiceName] || [];
            acc[cur.ServiceName].push(cur);
            return acc;
        }, {});
        vmData.k8sRegionInfoMap[`${region}-${env}`] = k8sRegionInfoList;
    } catch (e) {

    }
}

async function fetchAllK8sInfo() {
    const fetchArr = businessList.value.reduce((acc: any, cur: any) => {
        acc = acc.concat(cur.envs.map((env: string) => fetchK8sServiceInfoByRegion(cur.name, env)));
        return acc;
    }, []);
    return Promise.all(fetchArr);
}
const groups = ref<any[]>([]);
/**
 * @description: 获取班车分组列表
 * @date: 2023-04-19 10:41:06
 * @author: Horace
 * @param null:
 * @return
*/
const getGroupOptions = async () => {
    let res: any = {};
    try {
        res = await DeploymentApi.getApiLowCodeDeploymentDeployTaskByIdGroupOptions(props.id);
    } catch (e: any) {
        ElMessage.error(e.message || e);
    }
    groups.value = res.groupOptions || [];
};
const environmentOptions = ref<any[]>([]);

/**
 * @description: 获取发布环境选项
 * @date: 2023-04-19 17:49:56
 * @author: Horace
 * @param null:
 * @return
*/
const getEnvironmentOptions = async () => {
    let res: any = {};
    try {
        res = await DeploymentApi.getApiLowCodeDeploymentDeployTagEnvironment();
    } catch (e: any) {
        ElMessage.error(e.message || e);
    }
    environmentOptions.value = res.rows?.map((item: any) => ({
        value: item.id,
        label: item.environment,
    })) || [];
};
const bindTagEnvironment = async (value: string | number, tagId: string) => {
    try {
        await DeploymentApi.putApiLowCodeDeploymentDeployTagById({
            environmentId: value,
        }, tagId);
        await fetchDetail();
    } catch (e: any) {
        ElMessage({
            type: 'error',
            message: e.message || '发布失败',
        });
    }
};
const getPrefixList = (data: any[]) => {
    const arr = data?.map((item: any) => ({
        text: item.prefix,
        value: item.prefix,
    }));
    return _.uniqBy(arr, 'value');
};
const filterHandler = (
    value: string,
    row: any,
) => {
    const prefix = row.prefix;
    return prefix === value;
};
const loading = ref<boolean>(false);

const businessGroup = computed(() => {
    let businessName = vmData.currentBusiness || 'abc-his';
    if (businessName.includes('region')) {
        businessName = 'abc-his';
    }
    return businessGroups.value.find((item: any) => item.business === businessName);
});
</script>
<template>
    <h1 style="display: flex; margin-bottom: 8px;">
        <el-space>
            班车【{{ vmData.task?.name }}】
            <el-radio-group v-model="vmData.currentBusiness">
                <el-radio-button
                    v-for="businessOption in businessOptions"
                    :label="businessOption.name"
                ></el-radio-button>
            </el-radio-group>
        </el-space>
        <span style="margin-left: auto;">
            <el-switch v-model="vmData.checkK8s" :disabled="!isTaskOpen">
            </el-switch>
            定时检查容器
        </span>
    </h1>

    <template v-if="businessGroup">
        <h2 style="text-align: center; width: 100%;">
            {{ businessGroup.business }}
        </h2>
        <el-tabs
            stretch
            type="border-card"
            :class="themeConfig.isMobile ? 'is-mobile' : ''"
        >
            <el-tab-pane
                v-for="group in businessGroup.envArr"
                :key="group.env"
                :label="group.envLabel"
                class="deploy-tag-panel"
            >
                <template #label>
                    <span class="custom-label">
                        {{ group.envLabel }}
                        <el-tag
                            round
                            size="small"
                            style="margin-left: 4px;"
                        >{{ group.successCount + '/' + group.totalCount }}</el-tag>
                    </span>
                </template>
                <el-table
                    ref="deployTable"
                    :data="group.pods"
                    style="width: 100%;"
                    height="100%"
                    class="deploy-tag-table"
                    :row-class-name="tableRowClassName"
                    row-key="name"
                    resizable
                    border
                >
                    <el-table-column
                        prop="name"
                        label="TAG"
                        width="240"
                        fixed
                        :filters="getPrefixList(group.pods)"
                        :filter-method="filterHandler"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="remark"
                        label="需求/BUG"
                        min-width="240"
                    >
                        <template #default="{row}">
                            <template v-if="row.remarks">
                                <p v-for="(remark, index) in row.remarks" :key="index">{{ remark }}</p>
                            </template>
                            <span v-else>{{ row.remark }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="operation"
                        label="影响面评估"
                        width="180"
                    >
                        <template #default="{row}">
                            <template v-if="row.operations">
                                <p v-for="(operation, index) in row.operations" :key="index">{{ operation }}</p>
                            </template>
                            <span v-else>{{ row.operation }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="deps"
                        label="需要配合提测的服务"
                        width="120"
                    >
                        <template #default="{row}">
                            <template v-if="row.depList">
                                <p v-for="(deps, index) in row.depList" :key="index">{{ deps }}</p>
                            </template>
                            <span v-else>{{ row.deps }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="status"
                        label="Jenkins状态"
                        :width="themeConfig.isMobile ? 120 : 240"
                    >
                        <template #default="{row}">
                            <div
                                v-if="(!row.isChildren || row.jenkinsCount > 1) && row.jenkinsUrl"
                                style="display: flex; justify-content: space-between; align-items: flex-start; flex-direction: column;"
                            >
                                <template v-if="row.jenkinsCount > 1">
                                    <div v-for="jenkinsInfo in row.jenkinsInfo" :key="jenkinsInfo.id">
                                        <a :href="jenkinsInfo.url" target="_blank" style="margin-right: 6px; vertical-align: middle;">
                                            <img width="16" src="@/assets/jenkins-favicon.ico" /> {{ jenkinsInfo.name }}
                                        </a>
                                    </div>
                                </template>
                                <div v-else>
                                    <a :href="row.jenkinsUrl" target="_blank" style="margin-right: 6px; vertical-align: middle;">
                                        <img width="16" src="@/assets/jenkins-favicon.ico" />
                                    </a>
                                    <span :class="formatDeployTagStatusClass(row.status)">{{ formatDeployTagStatus(row.status) }}</span>
                                </div>
                            </div>
                            <span v-else>-</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="btns"
                        label="构建部署"
                        :width="themeConfig.isMobile ? 120 : 280"
                        class-name="btns-columns"
                        fixed="right"
                    >
                        <template #default="{row}">
                            <div v-if="(!row.isChildren || row.jenkinsCount > 1) && row.jenkinsUrl" style="margin-bottom: 8px;">
                                <el-checkbox-group
                                    v-if="row.supportRegionDeploy"
                                    v-model="vmData.selectedDeployMap[row.business + row.jenkinsId + row.name]"
                                >
                                    <el-row v-for="regionOption in deployRegionList" :key="regionOption.id" style="margin-bottom: 8px;">
                                        <el-col :span="9">
                                            <el-checkbox :label="regionOption.name">{{ regionOption.name }}</el-checkbox>
                                        </el-col>
                                        <el-col :span="15">
                                            <tag-region-status
                                                :tag="row"
                                                :status="row.regionStatus[regionOption.name]?.status"
                                                :is-published="row.regionStatus[regionOption.name]?.isPublished"
                                                :k8s-info="row.regionStatus[regionOption.name]?.k8sInfo"
                                            ></tag-region-status>
                                        </el-col>
                                    </el-row>
                                    <el-row style="margin-top: 8px;" justify="space-around">
                                        <tag-operate-btns
                                            :status="row.regionComposeStatus"
                                            :tag="row"
                                            is-prev-region-deployed
                                            @click-publish="handlePublishClick"
                                        ></tag-operate-btns>
                                    </el-row>
                                </el-checkbox-group>
                                <template v-else>
                                    <el-row>
                                        <el-col :span="24">
                                            <tag-region-status
                                                :tag="row"
                                                :status="row.status"
                                                :is-published="row.isPublished"
                                                :k8s-info="row.k8sInfo"
                                            ></tag-region-status>
                                        </el-col>
                                    </el-row>
                                    <el-row style="margin-top: 8px;" justify="space-around">
                                        <tag-operate-btns
                                            :status="row.status"
                                            :tag="row"
                                            is-prev-region-deployed
                                            @click-publish="handlePublishClick"
                                        ></tag-operate-btns>
                                    </el-row>
                                </template>
                            </div>
                            <span v-else>-</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="createdBy"
                        label="创建人"
                        width="136"
                    >
                        <template #default="{row}">
                            <div>{{ row.createdBy }}</div>
                            <div>{{ useFormat(row.created, 'YYYY-MM-DD HH:mm') }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="buttons"
                        label="操作"
                        width="60"
                    >
                        <template #default="{row}">
                            <el-popconfirm :title="`确定删除${row.name}?`" @confirm="handleDeleteClick(row)">
                                <template #reference>
                                    <el-button type="danger" link :disabled="!deploymentStore.hasPermissionPublish">删除</el-button>
                                </template>
                            </el-popconfirm>
                        </template>
                    </el-table-column>
                </el-table>
            </el-tab-pane>
        </el-tabs>
    </template>
</template>

<style lang="scss">

.el-tabs.is-mobile {
    .el-tabs__content {
        padding: 0 !important;
    }
}

.deploy-tag-table {
    .el-button {
        min-height: 24PX !important;
        height: 24PX !important;
        padding-top: 4PX !important;
        padding-bottom: 4PX !important;
    }

    &.el-table {
        .disabled-row {
            > td:not(.normal-cell) {
                background: #f2f3f5 !important;
            }
        }

        .el-table__cell {
            padding: 4PX !important;

            .cell {
                padding: 0 4px;
            }

            &.btns-columns {
                padding: 0 !important;
            }
        }
    }

    .cell {
        .error {
            color: var(--oa-danger-color);
        }

        .successful {
            color: var(--el-color-success);
        }

        .warning {
            color: var(--el-color-warning);
        }

        .publishing {
            color: var(--el-color-info);
        }
    }
}
</style>
