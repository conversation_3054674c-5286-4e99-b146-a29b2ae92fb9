<template>
  <div class="zone-task-container">
    <div class="header">
      <h2>分区放量任务管理</h2>
      <el-button type="primary" @click="showCreateDialog = true">
        创建任务
      </el-button>
    </div>

    <!-- 任务列表 -->
    <div class="table-container">
      <div v-if="taskList.length === 0 && !loading" class="empty-state">
        <p>暂无任务数据</p>
      </div>
      <el-table
        :data="taskList"
        v-loading="loading"
        border
        stripe
        style="width: 100%"
      >
        <el-table-column prop="name" label="任务名称" min-width="150" />
        <el-table-column prop="regionId" label="分区" :formatter="formatRegion" width="100" />
        <el-table-column prop="env" label="环境" :formatter="formatEnv" width="100" />
        <el-table-column prop="fromZone" label="从分区" :formatter="formatZone" width="100" />
        <el-table-column prop="toZone" label="到分区" :formatter="formatZone" width="100" />
        <el-table-column prop="status" label="状态" :formatter="formatStatus" width="100" />
        <el-table-column prop="created" label="创建时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewDetail(row)">详情</el-button>
            <el-button
              v-if="canPauseTask(row.status)"
              size="small"
              type="warning"
              @click="pauseTask(row)"
            >
              暂停
            </el-button>
            <el-button
              v-if="canResumeTask(row.status)"
              size="small"
              type="success"
              @click="resumeTask(row)"
            >
              启动
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 创建任务对话框 -->
    <CreateTaskDialog
      v-model="showCreateDialog"
      @success="loadTaskList"
    />

    <!-- 任务详情对话框 -->
    <TaskDetailDialog
      v-model="showDetailDialog"
      :task-id="selectedTaskId"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { HighlyAvailableAPI } from '@/api/highly-available-api';
import { regionOptions, envOptions, zoneOptions, canPauseTask, canResumeTask } from '../model/zone';
import CreateTaskDialog from './CreateTaskDialog.vue';
import TaskDetailDialog from './TaskDetailDialog.vue';

const taskList = ref<AbcAPI.CreateZoneTaskReq[]>([]);
const loading = ref(false);
const showCreateDialog = ref(false);
const showDetailDialog = ref(false);
const selectedTaskId = ref('');

const loadTaskList = async () => {
  loading.value = true;
  try {
    const res = await HighlyAvailableAPI.listZoneTasksUsingGET();
    console.log('API 返回数据:', res);
    console.log('res.data:', res.data);
    console.log('res.data.rows:', res.data?.rows);
    taskList.value = res.data?.rows || [];
    console.log('taskList.value:', taskList.value);
    console.log('taskList.value.length:', taskList.value.length);

    // 检查第一个任务的字段
    if (taskList.value.length > 0) {
      console.log('第一个任务的字段:', Object.keys(taskList.value[0]));
      console.log('第一个任务的数据:', taskList.value[0]);
    }

    // 添加测试数据来验证表格是否能正常渲染
    if (taskList.value.length === 0) {
      console.log('添加测试数据...');
      taskList.value = [
        {
          id: 1,
          name: '测试任务',
          regionId: 1,
          env: 1,
          fromZone: 'primary',
          toZone: 'standby',
          status: 10,
          created: '2023-12-25 10:30:00',
          createdBy: 'test',
          finished: '',
          list: []
        } as AbcAPI.CreateZoneTaskReq
      ];
      console.log('测试数据已添加:', taskList.value);
    }
  } catch (error) {
    console.error('获取任务列表失败:', error);
  } finally {
    loading.value = false;
  }
};

const formatRegion = (row: AbcAPI.CreateZoneTaskReq) => {
  try {
    const result = regionOptions.find(item => item.value === row.regionId)?.label || row.regionId;
    console.log('formatRegion:', row.regionId, '->', result);
    return result;
  } catch (error) {
    console.error('formatRegion error:', error, row);
    return row.regionId;
  }
};

const formatEnv = (row: AbcAPI.CreateZoneTaskReq) => {
  try {
    const result = envOptions.find(item => item.value === row.env)?.label || row.env;
    console.log('formatEnv:', row.env, '->', result);
    return result;
  } catch (error) {
    console.error('formatEnv error:', error, row);
    return row.env;
  }
};

const formatZone = (row: AbcAPI.CreateZoneTaskReq, column: any, cellValue: string) => {
  try {
    const result = zoneOptions.find(item => item.value === cellValue)?.label || cellValue;
    console.log('formatZone:', cellValue, '->', result);
    return result;
  } catch (error) {
    console.error('formatZone error:', error, row, cellValue);
    return cellValue;
  }
};

const formatStatus = (row: AbcAPI.CreateZoneTaskReq) => {
  try {
    const statusMap = {
      10: '进行中',
      20: '完成',
      30: '手动停止'
    };
    const result = statusMap[row.status as keyof typeof statusMap] || row.status;
    console.log('formatStatus:', row, '->', result);
    return result;
  } catch (error) {
    console.error('formatStatus error:', error, row);
    return row.status;
  }
};

const viewDetail = (row: AbcAPI.CreateZoneTaskReq) => {
  selectedTaskId.value = String(row.id);
  showDetailDialog.value = true;
};

const pauseTask = async (row: AbcAPI.CreateZoneTaskReq) => {
  await HighlyAvailableAPI.updateZoneTaskStatusUsingPUT(30, String(row.id));
  loadTaskList();
};

const resumeTask = async (row: AbcAPI.CreateZoneTaskReq) => {
  await HighlyAvailableAPI.updateZoneTaskStatusUsingPUT(10, String(row.id));
  loadTaskList();
};

onMounted(() => {
  loadTaskList();
});
</script>

<style scoped>
.zone-task-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #303133;
}

.table-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #909399;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}
</style>