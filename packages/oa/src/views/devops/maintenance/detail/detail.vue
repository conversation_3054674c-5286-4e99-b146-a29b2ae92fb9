<script setup lang="ts">
import { useModel } from './model';
import { useController } from './controller';
import { computed, onMounted, ref } from 'vue';
import { QuillEditor } from '@vueup/vue-quill';
import { RegionOptionsAll } from '@/utils/clinic';
import '@vueup/vue-quill/dist/vue-quill.snow.prod.css';
// @ts-expect-error
import BlotFormatter from 'quill-blot-formatter/dist/BlotFormatter.js';
import { DevopsNoticeStatus } from '@/views/devops/model/constants';

const treeRef = ref();
const preContentRef = ref();
const contentRef = ref();
const model = useModel(treeRef, preContentRef, contentRef);
const controller = useController(model);

const {
    isEdit,
    saveBtnLoading,
    postData,
    hasPushPermission,
    hasSetPermission,
    stopBtnLoading,
    recoveryBtnLoading,
    normalBtnLoading,
    preBtnLoading,
    pushContentBtnLoading,
} = model;

const EditorOptions = ref({
    modules: [
        {
            name: 'blotFormatter',
            module: BlotFormatter,
            options: {
                overlay: {
                    style: {
                        width: '100%',
                    },
                },
            },
        },
    ],
});

onMounted(async () => {
    await controller.initData();
});

const getStatusName = computed(() => {
    if (postData.value.status === DevopsNoticeStatus.Shutdown) {
        return '停机中';
    }
    if (postData.value.status === DevopsNoticeStatus.Recovery) {
        return '恢复中';
    }
    if (postData.value.status === DevopsNoticeStatus.Normal) {
        return '正常';
    }
    if (postData.value.status === DevopsNoticeStatus.Revoke) {
        return '已撤回';
    }
    if (postData.value.status === DevopsNoticeStatus.Init) {
        return '初始状态';
    }
    return '';
});
</script>
<template>
    <el-row class="devops-maintenance">
        <el-row style="margin-bottom: 12px;">
            <el-col>
                <template v-if="isEdit">
                    <el-button
                        plain
                        :disabled="!hasSetPermission"
                        @click="controller.handleCancelEditClick"
                    >
                        取消
                    </el-button>
                    <el-button
                        type="primary"
                        :disabled="!hasSetPermission"
                        :loading="saveBtnLoading"
                        @click="controller.handleSaveClick"
                    >
                        保存
                    </el-button>
                </template>
                <el-button
                    v-else
                    type="primary"
                    :disabled="!hasSetPermission"
                    @click="controller.handleEditClick"
                >
                    编辑
                </el-button>
            </el-col>
        </el-row>

        <el-row style="margin: 12px 0;">
            <el-col>
                当前状态：{{ getStatusName }}
            </el-col>
        </el-row>
        <el-row>
            <div class="maintenance-editor">
                <el-form>
                    <el-form-item label="数据分区">
                        <el-select
                            v-model="postData.regionId"
                            style="width: 120px;"
                            placeholder="请选择"
                            :disabled="!isEdit"
                        >
                            <el-option
                                v-for="region in RegionOptionsAll"
                                :key="region.value"
                                :label="region.label"
                                :value="region.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="更新标题">
                        <el-input v-model="postData.title" :disabled="!isEdit"></el-input>
                    </el-form-item>
                    <el-form-item label="更新预告">
                        <div>
                            <quill-editor
                                ref="preContentRef"
                                v-model:content="postData.preContent"
                                :content="postData.preContent"
                                content-type="html"
                                toolbar="full"
                                theme="snow"
                                :modules="EditorOptions.modules"
                                :options="EditorOptions"
                            >
                            </quill-editor>
                        </div>
                    </el-form-item>
                    <el-row style="padding-left: 68px; margin-bottom: 24px;">
                        <el-button
                            type="primary"
                            :loading="preBtnLoading"
                            :disabled="!postData.id || !hasPushPermission || isEdit"
                            @click="controller.handlePreContentPublish"
                        >
                            推送更新预告
                        </el-button>
                    </el-row>

                    <el-form-item label="更新内容">
                        <div>
                            <quill-editor
                                ref="contentRef"
                                v-model:content="postData.content"
                                :content="postData.content"
                                content-type="html"
                                toolbar="full"
                                theme="snow"
                                :modules="EditorOptions.modules"
                                :options="EditorOptions"
                            >
                            </quill-editor>
                        </div>
                    </el-form-item>
                    <el-row style="padding-left: 68px; margin-bottom: 24px;">
                        <el-button
                            type="primary" 
                            :loading="pushContentBtnLoading"
                            :disabled="!postData.id || !hasPushPermission || isEdit"
                            @click="controller.handleContentPublish"
                        >
                            推送更新内容
                        </el-button>
                    </el-row>
                    <el-form-item label="文章链接">
                        <el-autocomplete
                            v-model="postData.articleTitle"
                            :disabled="!isEdit"
                            :fetch-suggestions="controller.querySearch"
                            :trigger-on-focus="false"
                            @select="controller.selectHandler"
                        >
                            <template #default="{item}">
                                <div>{{ item.title }}</div>
                            </template>
                        </el-autocomplete>
                        <span style="margin-left: 8px;">{{ postData.articleUrl }}</span>
                    </el-form-item>
                </el-form>
            </div>
        </el-row>
        <el-row>
            <el-button
                type="danger"
                :loading="stopBtnLoading"
                :disabled="!postData.id || !hasPushPermission || isEdit"
                @click="controller.handleStopService"
            >
                停机
            </el-button>
            <el-button :loading="recoveryBtnLoading" :disabled="isEdit || !postData.id || !hasPushPermission" @click="controller.handleRecovery">
                恢复
            </el-button>
            <el-button :loading="normalBtnLoading" :disabled="isEdit || !postData.id || !hasPushPermission" @click="controller.handleChangeNormal">
                正常
            </el-button>
        </el-row>
    </el-row>
</template>
<style lang="scss">
    .devops-maintenance {
        display: flex;
        justify-content: flex-end;
        padding: 12px;
        background-color: var(--oa-white);

        .el-tree-node.is-horizontal {
            .el-tree-node__children {
                display: flex;
                flex-direction: row;

                .el-tree-node__content {
                    background-color: var(--oa-white);
                }
            }
        }

        .ql-container {
            height: 300px;
            max-height: 300px;
            overflow: scroll;
        }
    }
</style>
