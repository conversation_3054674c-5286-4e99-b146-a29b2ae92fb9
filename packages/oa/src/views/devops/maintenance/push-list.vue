<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { useService } from '../service';
import { MaintenancePushType } from '@/views/devops/model/constants';

import { ElMessage, ElMessageBox } from 'element-plus';
import MessageDialog from '@/views/devops/maintenance/message-dialog.vue';

const service = useService();

const totalCount = ref(0);
const tableData = ref(<any>[]);

const loading = ref(false);
const fetchParams = ref(<AbcAPI.QueryCmsOaPushReq>{
    offset: 0,
    limit: 10,
    cmsPushTypes: [MaintenancePushType.PrePublish, MaintenancePushType.Publish],
});
/**
 * @desc 获取推送列表
 * <AUTHOR>
 * @date 2022-12-30 17:29:16
 */
const fetchData = async () => {
    try {
        loading.value = true;
        const data = await service.getPushList(fetchParams.value);
        if (data) {
            // @ts-ignore
            tableData.value = data?.rows || [];
            // @ts-ignore
            totalCount.value = data?.total || 0;
        }

        loading.value = false;
    } catch (e) {
        loading.value = false;
    }
};

const pageChangeHandler = (index: number) => {
    fetchParams.value.offset = (index - 1) * (fetchParams.value.limit || 10);
    fetchData();
};

onMounted(() => {
    fetchData();
});

const showDialog = ref(false);
const message = ref(<any>{});
const handleEditMsg = (msg: any) => {
    message.value = msg;
    showDialog.value = true;
};

const handleDelete = async (msg: any) => {
    ElMessageBox.confirm(
        '是否要删除该条推送',
    ).then(async () => {
        try {
            await service.deletePushItem(msg.pushId);
            ElMessage.success({
                message: '删除成功',
            });
            fetchData();
        } catch (e: any) {
            ElMessage.error({
                message: e.message,
            });
        }
    });
};
</script>

<template>
    <div v-loading="loading">
        <el-table :data="tableData">
            <el-table-column prop="title" label="更新标题"></el-table-column>
            <el-table-column prop="type" label="更新类型">
                <template #default="{row}">
                    {{ row.cmsPushType === MaintenancePushType.PrePublish ? '更新预告' : '更新内容' }}
                </template>
            </el-table-column>
            <el-table-column prop="type" label="操作">
                <template #default="{row}">
                    <el-button @click="handleEditMsg(row)">编辑</el-button>
                    <el-button type="danger" @click="handleDelete(row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            style="margin-top: 24px;"
            background
            layout="prev, pager, next"
            :total="totalCount"
            @current-change="pageChangeHandler"
        ></el-pagination>
        <MessageDialog
            v-if="showDialog"
            v-model="showDialog"
            :message="message"
            @refresh="fetchData"
        ></MessageDialog>
    </div>
</template>

<style lang="scss">

</style>