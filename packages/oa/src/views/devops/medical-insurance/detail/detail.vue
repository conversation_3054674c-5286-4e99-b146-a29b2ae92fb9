<script lang="ts" setup>
import { computed, onMounted, provide, reactive, ref } from 'vue';
import { useModel } from '@/views/devops/medical-insurance/detail/model';
import { useController } from '@/views/devops/medical-insurance/detail/controller';
import {
    MaintenanceRuleOptions,
    MaintenanceRuleOptionsTest,
    MaintenanceRuleOptionsDev, DevopsNoticeStatus,
} from '@/views/devops/model/constants';
import { isProd, isTest } from '@/utils/env';
import OaAddress from '@/components/oa-address/index.vue';
import { QuillEditor } from '@vueup/vue-quill';
import { RegionOptionsAll } from '@/utils/clinic';
import '@vueup/vue-quill/dist/vue-quill.snow.prod.css';
// @ts-expect-error
import BlotFormatter from 'quill-blot-formatter/dist/BlotFormatter.js';
// @ts-expect-error
import ImageUploader from 'quill-image-uploader/src/quill.imageUploader.js';

import useArea from '@/composables/area';
import { FormRules } from 'element-plus';
import OSSUtil from '@/utils/oss';

const EditorOptions = ref({
    modules: [
        {
            name: 'blotFormatter',
            module: BlotFormatter,
            options: {
                overlay: {
                    style: {
                        width: '100%',
                    },
                },
            },
        },
        {
            name: 'imageUploader',
            module: ImageUploader,
            options: {
                upload: async (file: File) => {
                    const { url } = await OSSUtil.upload({
                        bucket: import.meta.env.VITE_APP_OSS_BUCKET,
                        region: import.meta.env.VITE_APP_OSS_REGION,
                        rootDir: 'oa/devops-management/images',
                    }, file);
                    return url;
                },
            },
        },
    ],
});
const treeRef = ref();
const contentRef = ref();
const model = useModel(treeRef, contentRef);
const controller = useController(model);
const { postData, hasPermission, saveBtnLoading, isEdit, loading, renderRegions } = model;

onMounted(async () => {
    await controller.initData();
});

const MaintenanceOptions = isProd ? MaintenanceRuleOptions : isTest ? MaintenanceRuleOptionsTest : MaintenanceRuleOptionsDev;
const TreeDefaultProps = {
    children: 'children',
    label: 'label',
    class: (data: any) => {
        if (data.horizontal) {
            return 'is-horizontal';
        }
        return null;
    },
    disabled: () => {
        if (isEdit.value) {
            return '';
        }
        return 'label';
    },
};

const { areaList, areaTree }: any = useArea();
provide('areaTree', areaTree);
const renderAddressDetail = computed(() => {
    const res = <any[]>[];
    const province = areaList.value.province_list;
    const city = areaList.value.city_list;
    if (!renderRegions.value || !province || !city) {
        return res;
    }
    renderRegions.value?.forEach((item: any) => {
        const provinceName = province[item[0]];
        const cityName = city[item[1]];
        res.push(`${provinceName}-${cityName}`);
    });
    return res;
});

const getStatusName = computed(() => {
    if (postData.value.status === DevopsNoticeStatus.Shutdown) {
        return '停机中';
    }
    if (postData.value.status === DevopsNoticeStatus.Recovery) {
        return '恢复中';
    }
    if (postData.value.status === DevopsNoticeStatus.Normal) {
        return '正常';
    }
    if (postData.value.status === DevopsNoticeStatus.Revoke) {
        return '已撤回';
    }
    if (postData.value.status === DevopsNoticeStatus.Init) {
        return '初始状态';
    }
    return '';
});

const toolbarOptions = [
    ['blockquote', 'code-block'],
    ['link', 'image', 'video', 'formula'],

    [{ header: 1 }, { header: 2 }], // custom button values
    [{ list: 'ordered' }, { list: 'bullet' }, { list: 'check' }],
    [{ script: 'sub' }, { script: 'super' }], // superscript/subscript
    [{ indent: '-1' }, { indent: '+1' }], // outdent/indent
    [{ direction: 'rtl' }], // text direction

    [{ size: ['small', false, 'large', 'huge'] }], // custom dropdown
    [{ header: [1, 2, 3, 4, 5, 6, false] }],
    [{ font: [] }],
    [{ align: [] }],
    ['clean'], // remove formatting button
];
const formRef = ref();
const validateContent = async (rule: FormRules, value: string, callback: (message?: string) => void) => {
    if (!postData.value.content) {
        callback('请输入公告内容');
    } else {
        callback();
    }
};
const validateTime = async (rule: FormRules, value: string, callback: (message?: string) => void) => {
    if (postData.value.showBeginTime === '' || postData.value.showEndTime === '') {
        callback('请选择展示时间');
    } else {
        callback();
    }
};
const rules = reactive<any>({
    content: [{ validator: validateContent, trigger: 'blur' }],
    time: [{ validator: validateTime, trigger: 'blur' }],
});
</script>

<template>
    <div v-loading="loading" class="devops-medical-insurance">
        <el-row style="margin-bottom: 24px;">
            <el-col>
                <h2>{{ postData.id ? '编辑' : '新增' }}政策公告</h2>
            </el-col>
        </el-row>
        <el-row style="margin-bottom: 12px;">
            <el-col v-if="!postData.id">
                <el-button
                    type="primary"
                    :disabled="!hasPermission "
                    :loading="saveBtnLoading"
                    @click="controller.handleSaveClick(formRef)"
                >
                    保存
                </el-button>
            </el-col>
            <el-col v-else>
                <template v-if="isEdit">
                    <el-button
                        plain
                        :disabled="!hasPermission"
                        @click="controller.handleCancelEditClick"
                    >
                        取消
                    </el-button>
                    <el-button
                        type="primary"
                        :disabled="!hasPermission"
                        :loading="saveBtnLoading"
                        @click="controller.handleSaveClick(formRef)"
                    >
                        保存
                    </el-button>
                </template>
                <el-button
                    v-else
                    type="primary"
                    :disabled="!hasPermission || postData.status === DevopsNoticeStatus.Revoke"
                    @click="controller.handleEditClick"
                >
                    编辑
                </el-button>
            </el-col>
        </el-row>

        <el-row style="margin: 12px 0;">
            <el-col>
                当前状态：{{ getStatusName }}
            </el-col>
        </el-row>
        <el-form
            ref="formRef"
            label-width="70px"
            :rules="rules"
            :model="postData"
        >
            <el-form-item label="数据分区">
                <el-select
                    v-model="postData.regionId"
                    style="width: 120px;"
                    placeholder="请选择"
                    :disabled="!isEdit"
                >
                    <el-option
                        v-for="region in RegionOptionsAll"
                        :key="region.value"
                        :label="region.label"
                        :value="region.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="生效范围">
                <el-tree
                    ref="treeRef"
                    :data="MaintenanceOptions"
                    :props="TreeDefaultProps"
                    node-key="nodeKey"
                    show-checkbox
                    :disabeld="!isEdit"
                    default-expand-all
                    :check-on-click-node="isEdit"
                    :expand-on-click-node="false"
                ></el-tree>
            </el-form-item>
            <el-form-item label="地区">
                <oa-address
                    v-model="renderRegions"
                    :disabled="!isEdit"
                    @change="controller.changeRegionHandler"
                >
                </oa-address>
                <div>
                    选择的城市有：
                    <span v-for="item in renderAddressDetail" :key="item" style="margin-right: 8px;">
                        {{ item }}
                    </span>
                </div>
            </el-form-item>
            <el-form-item
                label="公告内容"
                prop="content"
            >
                <div>
                    <quill-editor
                        ref="contentRef"
                        v-model:content="postData.content"
                        :content="postData.content"
                        content-type="html"
                        :toolbar="toolbarOptions"
                        theme="snow"
                        :modules="EditorOptions.modules"
                        :options="EditorOptions"
                    >
                    </quill-editor>
                </div>
            </el-form-item>
            <el-form-item label="文章链接">
                <el-autocomplete
                    v-model="postData.articleTitle"
                    :disabled="!isEdit"
                    :fetch-suggestions="controller.querySearch"
                    :trigger-on-focus="false"
                    @select="controller.selectHandler"
                >
                    <template #default="{item}">
                        <div>{{ item.title }}</div>
                    </template>
                </el-autocomplete>
                <span style="margin-left: 8px;">{{ postData.articleUrl }}</span>
            </el-form-item>

            <el-form-item
                label="展示时间"
                prop="time"
            >
                <el-date-picker
                    v-model="postData.showBeginTime"
                    :disabled="!isEdit"
                    type="datetime"
                    value-format="YYYY-MM-DD HH:mm:ss"
                ></el-date-picker>
                <span style="margin: 0 8px;">至</span>
                <el-date-picker
                    v-model="postData.showEndTime"
                    :disabled="!isEdit"
                    type="datetime"
                    value-format="YYYY-MM-DD HH:mm:ss"
                ></el-date-picker>
            </el-form-item>
        </el-form>
    </div>
</template>

<style lang="scss">

.devops-medical-insurance {
    background: #fff;
    padding: 12px;

    .el-tree-node.is-horizontal {
        .el-tree-node__children {
            display: flex;
            flex-direction: row;

            .el-tree-node__content {
                background-color: var(--oa-white);
            }
        }
    }

    .ql-container {
        min-height: 400px;
    }
}

</style>
