<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useService } from '@/views/devops/service';
import { DevopsNoticeStatus, DevopsNoticeType } from '@/views/devops/model/constants';
import { useDevOpsStore } from '@/views/devops/store';
// @ts-ignore
import { parseTime } from '@tool/date';
import { ElMessage } from 'element-plus';

const store = useDevOpsStore();
const hasEditPermission = computed(() => store.hasMedicalInsurance);
const router = useRouter();
const service = useService(DevopsNoticeType.MedicalInsurance);
const handleCreate = () => {
    router.push({
        name: '@devops/social/add',
    });
};

const handleOff = (row: any) => {
    try {
        service.updateStatus(row.id, DevopsNoticeStatus.Revoke);
        ElMessage({
            message: '撤回成功',
            type: 'success',
        });
    } catch (e) {

    }
};

const handleEditItem = (row: any) => {
    router.push({
        name: '@devops/social/detail',
        params: {
            id: row.id,
        },
    });
};

const handlePageChange = (page: number) => {
    params.value.offset = (page - 1) * params.value.limit;
    fetchList();
};

const loading = ref(false);
const params = ref({
    type: DevopsNoticeType.MedicalInsurance,
    offset: 0,
    limit: 10,
});
const listData = ref(<AbcAPI.MaintainNoticeView[]>[]);
const pagination = ref({
    total: 0,
    pageIndex: 1,
});

const fetchList = async () => {
    try {
        loading.value = true;
        const { rows, total } = await service.getNoticeList(params.value.limit, params.value.offset);
        listData.value = rows || [];
        pagination.value.total = total;
        loading.value = false;
    } catch (e) {
        loading.value = false;
    }
};
onMounted(() => {
    fetchList();
});
</script>

<template>
    <div>
        <el-row style="margin-bottom: 24px;">
            <el-col :span="12">
                <h3>政策公告列表</h3>
            </el-col>

            <el-col :span="12" style="text-align: right;">
                <el-button @click="handleCreate">新增</el-button>
            </el-col>
        </el-row>

        <el-table
            v-loading="loading"
            :data="listData"
            class="devops-medical-insurance-table"
            @row-click="handleEditItem"
        >
            <el-table-column label="公告内容" prop="content">
                <template #default="{row}">
                    <div v-html="row.content"></div>
                </template>
            </el-table-column>
            <el-table-column label="开始时间" prop="showBeginTime">
                <template #default="{row}">{{ parseTime(row.showBeginTime, 'y-m-d h:i:s', true) }} </template>
            </el-table-column>
            <el-table-column label="结束时间" prop="showEndTime">
                <template #default="{row}">{{ parseTime(row.showEndTime, 'y-m-d h:i:s', true) }} </template>
            </el-table-column>
            <el-table-column label="状态" prop="showEndTime">
                <template #default="{row}">{{ row.status !== DevopsNoticeStatus.Revoke ? '生效中' : '已撤回' }} </template>
            </el-table-column>
            <el-table-column label="操作" prop="operation">
                <template #default="{row}">
                    <el-button v-if="row.status !== DevopsNoticeStatus.Revoke" :disabled="!hasEditPermission" @click="handleOff(row)">撤回</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            :current-page="pagination.pageIndex"
            background
            :page-size="params.limit"
            layout="prev, pager, next"
            :total="pagination.total"
            @current-change="handlePageChange"
        />
    </div>
</template>

<style lang="scss">
.devops-medical-insurance-table {
    .el-table__row .cell {
        max-height: 42px;
    }
}

</style>