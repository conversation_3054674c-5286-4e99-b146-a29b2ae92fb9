<template>
    <div>
        <div v-if="feedbackVisible" class="feedback">
            <p>没有找到想了解的问题？</p>
            <p>留下您的疑问我们将尽快为您解答</p>
            <el-input
                v-model="questionContent"
                :rows="6"
                maxlength="240"
                type="textarea"
                :style="{
                    width: '560px',
                }"
            />
            <div class="btn">
                <el-button type="primary" @click="handleLeaveMessage">
                    提交
                </el-button>

                <Transition>
                    <span v-show="showTip" class="empty-tip">
                        请先输入您的问题
                    </span>
                </Transition>
            </div>
        </div>
        <Transition>
            <span v-show="showThanks" class="thanks">
                <i class="ri-checkbox-circle-fill"></i>
                感谢您的反馈
            </span>
        </Transition>
    </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { HelpCenterHisApi } from '@/api/help-center-his-api';
import { useHelpCenter } from '@/store/help-center-customer';

const helpCenter = useHelpCenter();

const questionContent = ref('');

const showThanks = ref(false);

const showTip = ref(false);

const feedbackVisible = computed(() => !helpCenter.giveFeedbackCategoryIds.includes(helpCenter.curCetegoryId));

function doShowThanks() {
    showThanks.value = true;
    let timer = setTimeout(() => {
        showThanks.value = false;
        clearTimeout(timer);
    }, 3000);
}

function doShowTip() {
    showTip.value = true;
    let timer = setTimeout(() => {
        showTip.value = false;
        clearTimeout(timer);
    }, 3000);
}

async function createLeaveMessage(params:{
    content: string,
    type: 0 | 1,
}) {
    await HelpCenterHisApi.postApiHelpCenterMessage(params as any);
}

function handleLeaveMessage() {
    let content = questionContent.value.trim();
    if (!content) {
        doShowTip();
        return;
    }
    try {
        createLeaveMessage({
            content,
            type: 0,
        });
        questionContent.value = '';
        doShowThanks();
        helpCenter.setGiveFeedbackCategoryIds();
    } catch (error) {
        console.log(error);
    }
}
</script>

<style scoped lang="scss">
    .v-enter-active,
    .v-leave-active {
        transition: all .5s;
    }

    .v-enter-from,
    .v-leave-to {
        opacity: 0;
    }

    .feedback {
        background-image: linear-gradient(180deg, #f5f9ff 0%, #fff 100%);
        border-radius: 12px;
        padding: 20px;

        >p:first-child {
            font-weight: bold;
            font-size: 18px;
            color: #005ed9;
            line-height: 24px;
            margin-bottom: 4px;
        }

        >p {
            font-size: 14px;
            color: #7a8794;
            line-height: 20px;
            margin-bottom: 12px;
        }

        :deep(.el-textarea__inner) {
            resize: none;
            background-color: transparent;
        }

        .btn {
            margin-top: 12px;
            display: flex;
            align-items: center;

            :deep(.el-button) {
                padding: 0 30px;
                height: 32px;
                background-color: #2680f7;
                color: var(--oa-white);

                &:active {
                    opacity: .5;
                }
            }

            .thanks {
                color: #1ec761;
                margin-left: 16px;
                transition: all .3s;

                .ri-checkbox-circle-fill {
                    margin-right: 6px;
                    font-size: 14px;
                    color: #1ec761;
                }

                &.hidden {
                    display: none;
                }
            }

            .empty-tip {
                color: #f33;
                margin-left: 16px;
                transition: all .3s;

                &.hidden {
                    display: none;
                }
            }
        }
    }
</style>