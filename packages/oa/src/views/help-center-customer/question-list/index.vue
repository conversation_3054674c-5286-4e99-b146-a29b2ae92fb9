<script setup lang="ts">
import { reactive, ref, watch, computed, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import { useRoute, useRouter } from 'vue-router';
import { useHelpCenter } from '@/store/help-center-customer';
import QuestionDetail from './components/question-detail.vue';
import { HelpCenterHisApi } from '@/api/help-center-his-api';
import _ from 'lodash';

const route = useRoute();

const loading = reactive({
    pageLoading: false,
});

const helpCenter = useHelpCenter();
const searchKey = storeToRefs(helpCenter).searchKey;
const curTitle = storeToRefs(helpCenter).curTitle;

const detailDialogVisible = ref(false);

const dataSource = ref<any[]>([]);

const filterParams = reactive({
    categoryId: '',
    limit: 12,
    offset: 0,
});

const total = ref(0);

const throttleSearchByKeyWord = _.throttle(searchByKeyWord, 250);

const curQuestionId = ref('');

const curPage = computed(() => filterParams.offset / filterParams.limit + 1);

watch(searchKey, throttleSearchByKeyWord);

watch(() => route.params.id, (val) => {
    if (route.name === '@question-list') {
        handleRouteChange(val);
    }
}, {
    deep: true,
});

watch(() => route.query.openQId, () => {
    let id: any = route.query.openQId;
    id && handleClickQuestion(id);
}, {
    deep: true,
    immediate: true,
});

onMounted(() => {
    // 如果初始化带关键字，用关键字拉数据
    handleRouteChange(route.params.id);
});

async function getQuestionList(params:{
    categoryId: string;
    offset: number;
    limit: number;
    keyword?: string;
    roleIds?:Array<number>,
    funcIds?:Array<number>,
    isRecommend?:number,
    id?: string;
}) {
    let _params:any = params;
    try {
        const res = await HelpCenterHisApi.getApiHelpCenterQuestionList(
            _params.offset,
            _params.limit,
            'created',
            'DESC',
            _params.categoryId,
            _params.keyword,
            _params.roleIds,
            _params.funcIds,
            _params.id,
            _params.isRecommend,
            0, // 不隐藏
        );
        const data = (res as any)?.helpDescriptions || [];
        let reg: undefined | RegExp;
        if (params.keyword) {
            reg = new RegExp(params.keyword, 'g');
        }
        data.forEach((item: any) => {
            item.contentText = item.content.replace(/<(\S*?)[^>]*>.*?|<.*? \/>/g, '');
            if (params.keyword) {
                item.title = item.title.replace(reg, `<span style="color: #0090ff;">${params.keyword}</span>`);
                item.contentText = item.contentText.replace(reg, `<span style="color: #0090ff;">${params.keyword}</span>`);
            }
        });
        return {
            data,
            total: (res as any)?.total || 0,
        };
    } catch (error) {
        console.log(error);
    }
}
const router = useRouter();
function handleRouteChange(id:string | string[]) {
    const params = localStorage.getItem('questionListParams');
    if (params) {
        const _params = JSON.parse(params);
        filterParams.categoryId = helpCenter.curCetegoryId || _params.categoryId;
        filterParams.offset = _params.offset;
        filterParams.limit = _params.limit;
        searchByKeyWord(_params.keyword);
        localStorage.removeItem('questionListParams');
        return;
    }

    if (route.query.keyword) {
        helpCenter.setSearchKey(route.query.keyword as string);
    } else {
        // 否则用categoryId
        filterParams.offset = 0;
        filterParams.categoryId = id as string;
    }
}

async function searchByKeyWord(val:string) {
    if (!val) {
        _debounceSearchByKeyWord();
        return;
    }
    loading.pageLoading = true;
    filterParams.offset = 0;

    let params = {
        offset: filterParams.offset,
        limit: 10, // 搜索时，每页显示10条
        keyword: val,
    };
    const res = await getQuestionList(params as any);
    const { data, total: _total } = res as any;
    dataSource.value = data;
    total.value = _total;
    loading.pageLoading = false;
}

async function searchByFilterParams() {
    loading.pageLoading = true;
    const res = await getQuestionList(filterParams) || {};
    const { data, total: _total } = res as any;
    dataSource.value = data || [];
    total.value = _total || 0;
    loading.pageLoading = false;
}
const _debounceSearchByKeyWord = _.debounce(searchByFilterParams, 300);
watch(filterParams, _debounceSearchByKeyWord);
function handlePageChange(page: number) {
    filterParams.offset = (page - 1) * filterParams.limit;
}

function handleClickQuestion(id: string) {
    // detailDialogVisible.value = true;
    curQuestionId.value = id;

    localStorage.setItem('questionListParams', JSON.stringify({
        categoryId: filterParams.categoryId,
        offset: filterParams.offset,
        limit: filterParams.limit,
        keyword: searchKey.value,
    }));

    router.push({
        name: '@question-detail',
        params: {
            id,
        },
    });
}

</script>

<template>
    <div v-loading="loading.pageLoading" class="help-center-customer-question-list-wrapper">
        <div v-if="searchKey" class="help-center-customer-question-list-header">
            <span class="help-center-customer-question-list-header__title">
                搜索结果
            </span>
            <span>找到{{ total }}个结果</span>
        </div>
        <div class="help-center-customer-question-list" :class="{'is-search': !!searchKey}">
            <template v-if="!!dataSource.length">
                <div
                    v-for="(item,idx) in dataSource"
                    :key="idx"
                    class="help-center-customer-question-list-item"
                    @click="handleClickQuestion(item.id)"
                >
                    <div class="help-center-customer-question-list-item__title-wrapper">
                        <span class="help-center-customer-question-list-item__title" v-html="item.title" />
                        <span v-if="!searchKey" class="help-center-customer-question-list-item__view-count">
                            <i class="ri-eye-line" style="font-size: 13px; color: #7a8794;"></i>
                            {{ item.viewsCount }}
                        </span>
                    </div>
                    <div v-if="searchKey" class="help-center-customer-question-list-item__content" v-html="item.contentText" />
                </div>
                <div class="help-center-customer-question-list__pagination">
                    <span>共 <span class="total">{{ total }}</span> 条</span>
                    <el-pagination
                        :current-page="curPage"
                        background
                        layout="prev, pager, next"
                        :page-size="filterParams.limit"
                        :total="total"
                        @update:current-page="handlePageChange"
                    >
                    </el-pagination>
                </div>
            </template>
            <div v-else class="search-empty">
                <img src="@/assets/help-center/<EMAIL>" width="72">
                <span>暂无结果</span>
            </div>
        </div>
        <!--        <div v-if="!searchKey" class="bottom">-->
        <!--            <feed-back></feed-back>-->
        <!--        </div>-->
        <question-detail
            v-if="detailDialogVisible"
            v-model:detailDialogVisible="detailDialogVisible"
            :question-id="curQuestionId"
        ></question-detail>
    </div>
</template>
<style lang="scss">
@import "src/style/mixins/mixins";

.help-center-customer-question-list-wrapper {
    position: relative;
    height: calc(100vh - 208px);
    overflow: auto;

    .help-center-customer-question-list-header {
        margin-top: 24px;
        font-size: 14px;
        line-height: 20px;
        color: #7a8794;

        &__title {
            display: inline-block;
            font-weight: bold;
            color: #000;
            line-height: 24px;
            font-size: 20px;
            margin-right: 16px;
        }
    }

    .help-center-customer-question-list {
        margin-bottom: 24px;

        &-item {
            padding: 16px 0;
            border-bottom: 1px solid #e6eaee;
            cursor: pointer;

            &__title-wrapper {
                display: flex;
                justify-content: space-between;
            }

            &__title {
                flex: 1;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                font-size: 16px;
                color: #000;
                line-height: 24px;
                transition: all .2s;

                &:hover {
                    color: #0090ff;
                }
            }

            &__view-count {
                max-width: 80px;
                min-width: 80px;
                user-select: none;
                line-height: 24px;
                height: 24px;
                cursor: pointer;
                color: #7a8794;
                margin-left: 12px;
            }
        }

        &.is-search {
            .help-center-customer-question-list-item {
                &__content {
                    @include text-ellipsis(2);

                    align-items: flex-start;
                    -webkit-box-align: start;
                    margin-top: 8px;
                    font-size: 14px;
                    line-height: 20px;
                    color: #7a8794;
                    white-space: pre-wrap !important;
                }
            }
        }

        .search-empty {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding-top: 72px;
            color: #aab4bf;
            padding-bottom: 72px;
        }

        .help-center-customer-question-list__pagination {
            display: flex;
            align-items: center;
            margin: 32px 0;
            color: #7a9794;

            .total {
                color: #000;
                font-weight: bold;
            }
        }
    }

    .bottom {
        margin-bottom: 24px;
    }
}
</style>