<script lang="ts" setup>
import { computed, ref } from 'vue';
import { HelpCenterApi } from '@/api/help-center-api';
import { ElMessage } from 'element-plus/es';

const props = defineProps({
    visible: {
        type: Boolean,
        default: false,
    },
    type: {
        type: String,
        default: 'add',
    },
    wordsData: {
        type: Object,
        default: () => ({}),
    },
    questionId: {
        type: [Number, String],
        default: null,
    },
});
const isReply = computed(() => props.type === 'reply');
const getTitle = computed(() => (!isReply.value ? '新增留言' : '回复留言'));
const emit = defineEmits(['update:visible', 'refresh']);
const dialogVisible = computed({
    get() {
        return props.visible;
    },
    set(val) {
        emit('update:visible', val);
    },
});

const form = ref({
    name: '',
    time: '',
    content: isReply.value ? props.wordsData?.content || '' : '',
    replyContent: '',
    clinicName: isReply.value ? 'ABC小管家' : '',
});

const onHandClose = () => {
    form.value = {
        name: '',
        time: '',
        content: '',
        replyContent: '',
        clinicName: isReply.value ? 'ABC小管家' : '',
    };
    dialogVisible.value = false;
};
const onHandSubmit = async () => {
    const postData = {
        content: isReply.value ? form.value.replyContent : form.value.content,
        type: 1,
        associationMessageId: isReply.value ? props.wordsData.id : null,
        questionId: +props.questionId,
        messageType: isReply.value ? 1 : 0,
        clinicName: form.value.clinicName,
        created: form.value.time,
    };
    let res: any = {};
    try {
        res = await HelpCenterApi.postApiLowCodeMessage(postData);
    } catch (e: any) {
        ElMessage.error(e.message || e);
    }
    console.log(res);
    emit('refresh');
    onHandClose();
};
</script>
<template>
    <el-dialog
        v-model="dialogVisible"
        :title="getTitle"
        width="500px"
        @closed="onHandClose"
    >
        <el-form :model="form" label-width="100px">
            <el-form-item v-if="isReply" label="留言内容">
                <span>{{ form.content }}</span>
            </el-form-item>
            <el-form-item :label="!isReply? '留言诊所' : '回复诊所'">
                <el-input
                    v-model="form.clinicName"
                    style="width: 100%;"
                    :placeholder="!isReply? '请选择留言诊所' : '请选择回复诊所'"
                ></el-input>
            </el-form-item>
            <el-form-item v-if="!isReply" label="留言时间">
                <el-date-picker
                    v-model="form.time"
                    style="width: 100%;"
                    type="datetime"
                    placeholder="请选择留言时间"
                    value-format="YYYY-MM-DD HH:mm:ss"
                ></el-date-picker>
            </el-form-item>
            <el-form-item v-if="!isReply" label="留言内容">
                <el-input
                    v-model="form.content"
                    type="textarea"
                    placeholder="请输入留言内容"
                    :autosize="{ minRows: 2, maxRows: 4 }"
                />
            </el-form-item>
            <el-form-item v-if="isReply" label="回复内容">
                <el-input
                    v-model="form.replyContent"
                    type="textarea"
                    placeholder="请输入回复内容"
                    :autosize="{ minRows: 2, maxRows: 4 }"
                />
            </el-form-item>
        </el-form>
        <template #footer class="dialog-footer">
            <el-button type="primary" @click="onHandSubmit">确 定</el-button>
            <el-button @click="onHandClose">取 消</el-button>
        </template>
    </el-dialog>
</template>