import { WordsTableColumn } from '@/views/help-center/model';

export const STATUS_OPTIONS: any = {
    0: '未处理',
    1: '已处理',
    2: '无效留言',
};

export const WORDS_TABLE_COLUMNS: WordsTableColumn[] = [
    {
        dataIndex: 'questionTitle',
        title: '留言位置',
        width: 240,
    },
    {
        dataIndex: 'content',
        title: '留言内容',
        width: 180,
    },
    {
        dataIndex: 'hisType',
        title: '产品类型',
        width: 180,
    },
    {
        dataIndex: 'editionId',
        title: '版本',
        width: 100,
    },
    {
        dataIndex: 'nodeType',
        title: '连锁',
        width: 50,
    },
    {
        dataIndex: 'clinicName',
        title: '门店名称',
        width: 180,
    },
    {
        dataIndex: 'cityName',
        title: '城市',
        width: 140,
    },
    {
        dataIndex: 'employeeId',
        title: '账号ID',
        width: 140,
    },
    {
        dataIndex: 'roleName',
        title: '角色',
        width: 140,
    },
    {
        dataIndex: 'mobile',
        title: '联系方式',
        width: 140,
    },
    {
        dataIndex: 'replyContent',
        title: '回复内容',
        width: 180,
    },
    {
        dataIndex: 'operation',
        title: '操作',
    },
];

export const ARTICLE_WORDS_TABLE_COLUMNS: WordsTableColumn[] = [
    ...WORDS_TABLE_COLUMNS.filter((item) => item.dataIndex !== 'questionTitle'),
];