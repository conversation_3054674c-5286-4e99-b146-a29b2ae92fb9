export interface InspectionData {
   id?: string;
   name?: string;
   enName?: string;
   alias?: string;
   type?: number;
   parentCategoryId?: string;
   secondCategoryId?: string;
   list?: {
      id?: string;
      InspectionId?: string;
      content?: string;
      category?: string;
      sort?: number;
      tagId?: string;
      tagName?: string;
      isAdd?: number
   }[]
}

export interface InspectionTag {
   id?: string
   label?: string
   value?: string
}

export interface Tree {
   id: string;
   category: string;
   type: number;
   sort?: number;
   parentCategoryId?: string;
   isLeaf?: number;
   children?: Tree[]
}

export interface InspectionDetailOptions {
   title: any,
   list: {
      title: string
      key: keyof InspectionData
      slot?:string
      required?: boolean
   }[]
}