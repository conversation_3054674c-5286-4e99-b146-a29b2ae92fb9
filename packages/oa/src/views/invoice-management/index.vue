<script setup lang="ts">
import { onMounted, reactive, ref, watch } from 'vue';
import {
    INVOICE_TYPE_OPTIONS,
    ORDER_BILLING_STATUS_OPTIONS,
    ORDER_BILLING_STATUS_ENUM,
    INVOICE_ORDER_CATEGORY,
    INVOICE_ORDER_CATEGORY_ENUM,
    REGIIN_ENUM,
    REGIIN_OPTIONS,
    RECEIVE_ACCOUNT_ID_OPTIONS,
} from '@/utils/invoice';
import { InvoiceOptions } from '@/views/invoice-management/model';
import { formatMoney, formatCacheTime } from '@/utils/format';
import { ElMessage, ElMessageBox } from 'element-plus';
import { InvoiceAPI } from '@/api/invoice-api';
import _ from 'lodash';
import dayjs from 'dayjs';

const invoiceStatusOptions: InvoiceOptions[] = ORDER_BILLING_STATUS_OPTIONS;
const invoiceTypeOptions: InvoiceOptions[] = INVOICE_TYPE_OPTIONS;
const invoiceOrderCategoryOptions: InvoiceOptions[] = INVOICE_ORDER_CATEGORY;
const RegionOptions: InvoiceOptions[] = REGIIN_OPTIONS;
const ReceiveAccountOptions = RECEIVE_ACCOUNT_ID_OPTIONS;
const loading = ref<Boolean>(false);

const filterParams = ref<any>({
    range: [
        dayjs((new Date(new Date().getTime() - 3600 * 1000 * 24 * 30))).format('YYYY-MM-DD'),
        dayjs((new Date())).format('YYYY-MM-DD'),
    ],
    invoiceOrderCategory: INVOICE_ORDER_CATEGORY_ENUM.SYSTEM_ANNUAL_FEE,
    regionId: REGIIN_ENUM.SHANG_HAI,
});
const tableData = ref<any[]>([]);
const paginationProps = reactive({
    page: 1,
    pageSize: 10,
    totalCount: 0,
});
const getInvoiceType = (type: number) => invoiceTypeOptions.find((item: InvoiceOptions) => item.value === type)?.label || '';
const getInvoiceStatus = (status: number) => invoiceStatusOptions.find((item: InvoiceOptions) => item.value === status)?.label || '';
const getReceiveAccount = (accountId: string) => ReceiveAccountOptions.find((item) => item.value === accountId)?.label || '';

onMounted(() => {
    onSearch();
});
const createPostData = () => {
    const queryObj: AbcAPI.QueryClinicInvoiceApplyForQwReq = {
        orderInvoiceStatus: filterParams.value.orderInvoiceStatus,
        endDate: filterParams.value.range?.[1],
        invoiceType: filterParams.value.invoiceType,
        keyword: filterParams.value.keyword,
        limit: paginationProps.pageSize,
        offset: (paginationProps.page - 1) * paginationProps.pageSize,
        startDate: filterParams.value.range?.[0],
        invoiceOrderCategory: filterParams.value.invoiceOrderCategory,
    };
    if (queryObj.invoiceOrderCategory === INVOICE_ORDER_CATEGORY_ENUM.MESSAGE_FEE) {
        queryObj.regionId = filterParams.value.regionId;
    }
    return queryObj;
};
const onSearch = async () => {
    let res: any = {};
    loading.value = true;
    try {
        res = await InvoiceAPI.pageQueryClinicInvoiceApplyUsingPOST(
            createPostData(),
        );
    } catch (e: any) {
        ElMessage.error(e.message || e);
        loading.value = false;
    }
    if (res) {
        tableData.value = res.rows || [];
        paginationProps.totalCount = res.total || 0;
    }
    loading.value = false;
};
const _debounceSearch = _.debounce(onSearch, 200);
watch(filterParams.value, () => {
    _debounceSearch();
});
const disabledStartDateHandler = (time: Date) => time.getTime() > new Date().getTime();
const exportGoodsSkuHref = async () => {
    let res: any = {};
    try {
        res = await InvoiceAPI.exportClinicInvoiceApplyUsingPOST(
            createPostData(),
        );
    } catch (e: any) {
        ElMessage.error(e.message || e);
    }
    let binaryData = [];
    binaryData.push(res);
    const downloadUrl = window.URL.createObjectURL(new Blob(binaryData));
    const anchor = document.createElement('a');
    anchor.href = downloadUrl;
    anchor.download = '发票列表.xlsx'; // 表格名称.文件类型
    anchor.click();
    window.URL.revokeObjectURL(res); // 消除请求接口返回的list数据
};
const showCloseBtn = (row: any) => row?.invoiceStatus === ORDER_BILLING_STATUS_ENUM.NOT_APPLIED;
const handleCloseInvoice = (row: any) => {
    ElMessageBox.confirm(
        '关闭发票后，客户将无法申请发票',
        '关闭发票提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        },
    )
                    .then(async () => {
                        let res: any = {};
                        let postData: any = {
                            invoiceOrderId: row?.invoiceOrderId,
                            invoiceOrderCategory: row?.invoiceOrderCategory,
                            regionId: row?.regionId,
                        };
                        if (row?.invoiceOrderCategory === INVOICE_ORDER_CATEGORY_ENUM.MESSAGE_FEE) {
                            postData.regionId = filterParams.value.regionId;
                        }
                        try {
                            res = await InvoiceAPI.disableSubmitInvoiceApplyUsingPOST(postData);
                        } catch (e: any) {
                            ElMessage.error(e.message || e);
                        }
                        if (res === 'success') {
                            ElMessage.success('发票关闭成功！');
                            await onSearch();
                        }
                    })
                    .catch(() => {
                        ElMessage({
                            type: 'info',
                            message: '取消关闭',
                        });
                    });
};
</script>
<template>
    <div class="invoice-management-wrapper">
        <el-card :style="{ marginBottom: '24px' }" shadow="never">
            <div class="header">
                <el-space class="left">
                    <el-date-picker
                        v-model="filterParams.range"
                        type="daterange"
                        :disabled-date="disabledStartDateHandler"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                    />
                    <el-input
                        v-model="filterParams.keyword"
                        style="width: 300px;"
                        prefix-icon="search"
                        placeholder="诊所名称/id/开票抬头/销售人员"
                    >
                    </el-input>
                    <el-select v-model="filterParams.orderInvoiceStatus" placeholder="全部状态" clearable>
                        <el-option
                            v-for="(d,i) in invoiceStatusOptions"
                            :key="i"
                            :label="d.label"
                            :value="d.value"
                        />
                    </el-select>
                    <el-select v-model="filterParams.invoiceOrderCategory" placeholder="全部类型">
                        <el-option
                            v-for="(d,i) in invoiceOrderCategoryOptions"
                            :key="i"
                            :label="d.label"
                            :value="d.value"
                        />
                    </el-select>
                    <el-select
                        v-if="filterParams.invoiceOrderCategory === INVOICE_ORDER_CATEGORY_ENUM.MESSAGE_FEE"
                        v-model="filterParams.regionId"
                        placeholder="全部类型"
                    >
                        <el-option
                            v-for="(d,i) in RegionOptions"
                            :key="i"
                            :label="d.label"
                            :value="d.value"
                        />
                    </el-select>
                </el-space>
                <el-space class="right">
                    <el-button @click="exportGoodsSkuHref"><el-icon><Upload /></el-icon>导出</el-button>
                </el-space>
            </div>
        </el-card>
        <el-card shadow="never">
            <el-table
                v-loading="loading"
                :data="tableData"
                style="width: 100%;"
            >
                <el-table-column prop="clinicName" label="诊所名称">
                    <template #default="{ row }">{{ row?.clinicName || '' }}</template>
                </el-table-column>
                <el-table-column prop="clinicId" label="诊所ID">
                    <template #default="{ row }">{{ row?.clinicId || '' }}</template>
                </el-table-column>
                <el-table-column prop="sellerName" label="销售人员">
                    <template #default="{ row }">{{ row?.sellerName || '' }}</template>
                </el-table-column>
                <el-table-column prop="name" label="订单内容">
                    <template #default="{ row }">{{ row?.invoiceOrderName || '' }}</template>
                </el-table-column>
                <el-table-column prop="name" label="订单付款时间">
                    <template #default="{ row }">{{ formatCacheTime(row?.invoiceOrderTime, '', true) }}</template>
                </el-table-column>
                <el-table-column prop="name" label="开票抬头">
                    <template #default="{ row }">{{ row.invoiceTitle || '' }}</template>
                </el-table-column>
                <el-table-column prop="name" label="发票申请时间">
                    <template #default="{ row }">{{ row?.invoiceApplyCreated ? formatCacheTime(row?.invoiceApplyCreated, '', true) : '' }}</template>
                </el-table-column>
                <el-table-column prop="name" label="发票类型">
                    <template #default="{ row }">{{ getInvoiceType(row.invoiceType) }}</template>
                </el-table-column>
                <el-table-column prop="name" label="发票金额">
                    <template #default="{ row }">{{ formatMoney(row?.invoiceTotalPrice || 0, '￥') }}</template>
                </el-table-column>
                <el-table-column prop="name" label="客户联系信息">
                    <template #default="{ row }">
                        {{ row.contactInfo }}
                    </template>
                </el-table-column>
                <el-table-column prop="receiveAccountId" label="收款账户">
                    <template #default="{ row }">
                        {{ getReceiveAccount(row.receiveAccountId) }}
                    </template>
                </el-table-column>
                <el-table-column prop="name" label="开票状态">
                    <template #default="{ row }">
                        <el-space>
                            <a
                                v-if="row?.invoiceStatus === ORDER_BILLING_STATUS_ENUM.ALREADY_INVOICED"
                                style="color: #005ed9;"
                                :href="row.invoiceUrl"
                                target="_blank"
                            >查看发票</a>
                            <span v-else>{{ getInvoiceStatus(row?.invoiceStatus ) }}</span>
                            <el-link v-if="showCloseBtn(row)" type="warning" @click="handleCloseInvoice(row)">关闭发票</el-link>
                        </el-space>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    v-model:current-page="paginationProps.page"
                    v-model:page-size="paginationProps.pageSize"
                    class="deployment-pagination"
                    background
                    layout="prev, pager, next, sizes, total "
                    :total="paginationProps.totalCount"
                    @current-change="_debounceSearch"
                    @size-change="_debounceSearch"
                >
                    <template #total>
                        <span>共 {{ paginationProps.totalCount }} 条</span>
                    </template>
                </el-pagination>
            </div>
        </el-card>
    </div>
</template>

<style scoped lang="scss">
.invoice-management-wrapper {
    :deep(.el-card__body) {
        padding: 24px;
    }
}

.header {
    display: flex;
    align-items: center;
    justify-content: center;

    .left {
        display: flex;
        flex: 1;
    }

    .right {
        margin-left: 24px;
    }
}

.opt {
    margin-bottom: 24px;
}

.pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
}
</style>
