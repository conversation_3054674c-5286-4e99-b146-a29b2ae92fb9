# ASR 语音标注功能

## 功能概述

ASR语音标注功能是一个专为语音识别结果标注而设计的平台，帮助标注人员高效地对ASR识别结果进行校对和修正。

## 主要功能

### ASR语音标注界面 (`/label/asr-annotation`)

- **紧凑网格布局**: 使用响应式网格布局，每页显示6-8个任务卡片
- **状态可视化**:
  - 支持数字状态码映射（100=已确认, 200=已删除, 300=混淆）
  - 不同状态使用不同颜色的左边框和图标标识
  - 已处理任务使用半透明效果区分
- **音频播放器**:
  - 每个任务独立的音频播放控制
  - 支持播放/暂停、快进/快退
  - 可调节播放速度（0.5x - 2.0x）
  - 音量控制和进度条拖拽定位
  - 智能播放管理（播放一个时自动停止其他）
- **文本标注**:
  - 自动填充原始ASR文本到标注字段
  - 支持文本编辑和重置功能
  - 实时保存标注内容
- **任务操作**:
  - 确认标注（需要标注文本）
  - 标记困惑（需要人工审核）
  - 删除任务（带确认对话框）
- **分页和筛选**: 支持分页浏览和状态筛选

## 技术实现

### 文件结构

```
asr-annotation/
├── index.vue              # 主标注界面（紧凑网格布局）
├── api.ts                 # API接口定义
├── types.ts               # TypeScript类型定义
├── constants.ts           # 状态映射和常量定义
├── mock-data.ts           # 模拟数据（开发用）
├── components/
│   └── audio-player.vue   # 音频播放器组件
├── asr.postman_collection.json  # API接口文档
└── README.md              # 说明文档
```

### 数据结构

#### 任务数据 (AsrTask)
```typescript
interface AsrTask {
  id: string;                    // 任务ID
  name: string;                  // 任务名称
  audioUrl: string;              // 音频文件URL
  duration: number;              // 音频时长（秒）
  asrResults: AsrResult[];       // ASR结果列表
  status: TaskStatus;            // 任务状态
  progress: number;              // 完成进度（0-100）
  assignee: string;              // 标注人员
  createdAt: string;             // 创建时间
  updatedAt: string;             // 更新时间
  totalSegments: number;         // 总音频段数
  completedSegments: number;     // 已完成段数
}
```

#### ASR结果数据 (AsrResult)
```typescript
interface AsrResult {
  id: string;                    // 结果ID
  startTime: number;             // 开始时间（秒）
  endTime: number;               // 结束时间（秒）
  originalText: string;          // 原始ASR文本
  correctedText?: string;        // 修正后的文本
  status: AsrResultStatus;       // 标注状态
}
```

### API接口

#### 标注相关接口（label前缀）
- `GET /api/warehouse/asr/task/lable-tasks` - 获取用户标注任务列表
- `GET /api/warehouse/asr/task/corpus` - 获取任务详情和语料
- `PUT /api/warehouse/asr/task/confirm` - 确认标注结果
- `PUT /api/warehouse/asr/task/confusion` - 标记任务为困惑状态
- `PUT /api/warehouse/asr/task/destroy` - 删除/销毁任务
- `PUT /api/warehouse/asr/task/oss-address` - 更新OSS音频地址

#### 兼容接口（向后兼容）
- `GET /api/asr-annotation/statistics` - 获取任务统计信息
- `POST /api/asr-annotation/submit` - 提交标注结果
- `POST /api/asr-annotation/save-progress` - 保存标注进度

## 使用说明

### 标注流程

1. **选择任务**: 在任务列表页面选择需要标注的任务
2. **播放音频**: 使用音频播放器播放原始音频
3. **检查识别结果**: 查看ASR原始识别文本
4. **进行标注**:
   - 如果识别正确，点击"正确"按钮
   - 如果识别错误，在修正文本框中输入正确文本，点击"已修正"
   - 如果音频听不清或无法判断，点击"未知"
5. **保存进度**: 系统会自动保存，也可手动点击保存
6. **提交任务**: 完成所有片段标注后，点击"保存并下一个"

### 快捷键（计划中）

- `Space`: 播放/暂停
- `←/→`: 快退/快进10秒
- `↑/↓`: 上一个/下一个音频片段
- `Ctrl+S`: 保存进度

## 开发说明

### 模拟数据

当前版本使用模拟数据进行开发测试，在 `api.ts` 中通过 `USE_MOCK_DATA` 常量控制：

```typescript
// 开发环境使用模拟数据
const USE_MOCK_DATA = true;
```

生产环境部署时需要将此值设为 `false` 并确保后端API接口可用。

### 组件库

- **UI框架**: Element Plus + Vant（移动端）
- **图标**: Element Plus Icons
- **样式**: SCSS

### 响应式设计

支持PC端和移动端自适应，在移动端会自动调整布局和交互方式。

## 部署说明

1. 确保后端API接口已部署并可访问
2. 修改 `api.ts` 中的 `USE_MOCK_DATA` 为 `false`
3. 配置正确的API基础URL
4. 构建并部署前端应用

## 后续优化

- [ ] 添加键盘快捷键支持
- [ ] 支持批量标注操作
- [ ] 添加标注质量统计
- [ ] 支持音频波形显示
- [ ] 添加标注历史记录
- [ ] 支持多人协作标注
- [ ] 添加标注指南和帮助文档
