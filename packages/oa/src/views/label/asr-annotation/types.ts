/**
 * ASR标注相关的类型定义
 */

// 标注任务状态（支持数字状态码）
export type LabelTaskStatus = 'pending' | 'labeled' | 'confused' | 'destroyed' | '100' | '200' | '300';

// 音频播放器状态
export interface AudioPlayerState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  playbackRate: number;
}

// 标注任务查询参数
export interface LabelTaskQuery {
  page: number;
  pageSize: number;
  filters?: {
    status?: LabelTaskStatus;
    assignee?: string;
    keyword?: string;
  };
}

// 标注任务响应
export interface LabelTaskResponse {
  total: number;
  rows: LabelTask[];
  data: LabelTask[]; // 兼容不同的响应格式
}

// 标注任务数据结构
export interface LabelTask {
  id: string;
  taskId: string;
  audioUrl?: string;
  ossAddress?: string;
  originalText?: string;
  lableText?: string; // 注意：API使用的是 lableText 而不是 labelText
  status: LabelTaskStatus;
  userId?: string;
  createdAt: string;
  updatedAt: string;
}
