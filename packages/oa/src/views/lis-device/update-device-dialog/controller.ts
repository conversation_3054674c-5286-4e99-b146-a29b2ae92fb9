import { Model } from './model';
import type { UploadRequestOptions } from 'element-plus';
import { Service } from './service';
import { uniqWith, isEqual } from 'lodash';

export const useController = (model: Model, service: Service) => {
    async function handleUploadIcon(options: UploadRequestOptions) {
        const { file } = options;
        if (!file) {
            return;
        }
        const url = await service.uploadIcon(file);
        model.formData.iconUrl = url;
    }

    function handleBeforeAvatarUpload() {

    }

    async function handleDevicePictureUpload(options: UploadRequestOptions) {
        const { file } = options;
        if (!file) {
            return;
        }
        const url = await service.uploadIcon(file);
        model.formData.deviceImg.push(url);
    }

    function removeIcon() {
        model.formData.iconUrl = '';
    }

    function removeDeiviceReferenceImg(url:string) {
        model.formData.deviceImg = model.formData.deviceImg.filter(u => u !== url);
    }

    function transformFormData(data: typeof model.formData) {
        return {
            name: data.name,
            model: data.model,
            manufacture: data.manufacture,
            deviceUuid: data.deviceUuid,
            usageType: data.usageType,
            buyUrl: data.buyUrl,
            iconUrl: data.iconUrl,
            connectStatus: data.connectStatus,
            extendInfos: {
                reagent: data.reagent,
                testItem: data.testItem,
                examSpeed: data.examSpeed,
                operatingPrinciple: data.operatingPrinciple,
                measurementPattern: data.measurementPattern,
                sample: data.sample, // 样本
                advantages: data.advantages,
                connectDescription: data.connectDescription,
                connectMethod: data.connectMethod,
                examModel: data.examModel,
                connectMode: data.connectMode,
                refImages: data.deviceImg,
                dataGetWay: data.dataGetWay,
                develops: data.develops.map((d: any) => ({
                    name: d.name,
                    id: d.id,
                })),
            },
            remarks: data.remarks,
            goodsType: 3, // 检验仪器
            goodsSubType: 1,
            deviceModelStatus: data.deviceModelStatus,
            deviceType: data.deviceType,
            sampleNoRule: {
                format: data.sampleNoRule.format || undefined,
                length: data.sampleNoRule.length,
                prefix: data.sampleNoRule.prefix || undefined,
            },
            supplierId: data.supplierId,
        };
    }

    function handleConfirm(id: string, callback: Function) {
        model.formRef.value?.validate(async (valid) => {
            if (valid) {
                model.loading.btn = true;
                const postData = transformFormData(model.formData);
                if (id) {
                    await service.updateDevice(id, postData);
                } else {
                    await service.createDevice(postData);
                }
                model.loading.btn = false;
                callback();
            }
        });
    }

    async function handleMounted(id: string) {
        const deviceInfo = await service.getDeviceInfo(id as any);
        model.formData.buyUrl = deviceInfo?.buyUrl || '';
        model.formData.advantages = deviceInfo?.extendInfos?.advantages || '';
        model.formData.remarks = deviceInfo?.remarks || '';
        model.formData.connectDescription = deviceInfo?.extendInfos?.connectDescription || '';
        model.formData.connectMethod = deviceInfo?.extendInfos?.connectMethod || '';
        model.formData.connectMode = deviceInfo?.extendInfos?.connectMode || '';
        model.formData.connectStatus = deviceInfo?.connectStatus as number;
        model.formData.deviceImg = deviceInfo?.extendInfos?.refImages || [];
        model.formData.usageType = deviceInfo?.usageType === undefined ? '' : deviceInfo?.usageType as unknown as string;
        model.formData.deviceUuid = deviceInfo?.deviceUuid || '';
        model.formData.examModel = deviceInfo?.extendInfos?.examModel || '';
        model.formData.examSpeed = deviceInfo?.extendInfos?.examSpeed || '';
        model.formData.iconUrl = deviceInfo?.iconUrl || '';
        model.formData.manufacture = deviceInfo?.manufacture || '';
        model.formData.measurementPattern = deviceInfo?.extendInfos?.measurementPattern || '';
        model.formData.model = deviceInfo?.model || '';
        model.formData.name = deviceInfo?.name || '';
        model.formData.operatingPrinciple = deviceInfo?.extendInfos?.operatingPrinciple || '';
        model.formData.reagent = deviceInfo?.extendInfos?.reagent || '';
        model.formData.sample = deviceInfo?.extendInfos?.sample as unknown as string[] || [];
        model.formData.testItem = deviceInfo?.extendInfos?.testItem || '';
        model.formData.deviceModelStatus = deviceInfo?.deviceModelStatus === undefined ? 99 : deviceInfo.deviceModelStatus;
        model.formData.deviceType = deviceInfo?.deviceType === 0 ? '' : deviceInfo?.deviceType as unknown as string;
        model.formData.develops = deviceInfo?.extendInfos?.develops || [];
        model.formData.sampleNoRule = {
            prefix: deviceInfo?.sampleNoRule?.prefix || '',
            format: deviceInfo?.sampleNoRule?.format || '',
            length: deviceInfo?.sampleNoRule?.length || 0,
        };
        model.formData.dataGetWay = deviceInfo?.extendInfos?.dataGetWay || '';
        model.formData.supplierId = deviceInfo?.supplierId || 0;
    }

    async function handleSelectDevelopEmployee() {
        model.loading.addDevelopEmployee = true;
        // 没选或者无响应，最多5s取消loading
        let timer = setTimeout(() => {
            model.loading.addDevelopEmployee = false;
            clearTimeout(timer);
        }, 5000);
        const selectDevelopEmployees = await service.getCropEmployee();
        model.formData.develops = uniqWith([
            ...model.formData.develops,
            ...selectDevelopEmployees,
        ], isEqual);
        model.loading.addDevelopEmployee = false;   
    }

    function handleRemoveDevelopEmployee(name: string) {
        model.formData.develops = model.formData.develops.filter(u => u.name !== name);
    }

    return {
        handleConfirm,
        handleUploadIcon,
        handleBeforeAvatarUpload,
        handleDevicePictureUpload,
        removeIcon,
        removeDeiviceReferenceImg,
        handleMounted,
        handleSelectDevelopEmployee,
        handleRemoveDevelopEmployee,
    };
};