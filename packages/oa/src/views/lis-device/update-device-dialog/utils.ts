export function validateSampleNoRuleLength(rule:any, val:any, callback: any) {
    if (!val.length) {
        callback(new Error('实验编号规则长度不能为空'));
    } else {
        callback();
    }
}

export function formatSampleNoRuleDateReg(date:Date, format: string) {
    if (!(date instanceof Date)) {
        console.error('请传入日期对象');
    }
    let year = (date.getFullYear() + '').split(''),
        _mon = (date.getMonth() + 1),
        mon = ((_mon > 10 ? '' : '0') + _mon).split(''),
        _day = date.getDate(),
        day = ((_day > 10 ? '' : '0') + _day).split('');
    let res = [];
    for (const c of format) {
        let _c = c.toUpperCase();
        if (_c === 'Y') {
            res.push(year.shift() || '');
        }
        if (_c === 'M') {
            res.push(mon.shift() || '');
        }
        if (_c === 'D') {
            res.push(day.shift() || '');
        }
    }

    return res.join('');
}

export function validateSampleNoRulePrefix(rule:any, val:any, callback: any) {
    if (!val) {
        return callback();
    }
    const reg = /^[A-Za-z0-9]{1,2}$/;

    const matched = reg.test(val);

    if (!matched) {
        return callback(new Error('只能输入a-Z的字母或0-9的数字，最多2位'));
    }
    return callback();
}