<script setup lang="ts">
import { useUserStore } from '@/store/user';
import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const userStore = useUserStore();
const route = useRoute();
const router = useRouter();

const error = ref('');

onMounted(async () => {
    const { code, state, appid, from, ...otherQueryParams } = route.query;

    if (code) {
        console.log('callback query: ', { code, state, appid, from, otherQueryParams });

        if (state === 'debug') {
            window.location.href = `http://localhost:3000/wework-callback?from=${from}&code=${code}&appid=${appid}`;
            return;
        }

        if (from) {
            let fromDecoded = decodeURIComponent(<string>from);
            if (fromDecoded.includes('oa-auth')) {
                window.location.href = `${from}&code=${code}&appid=${appid}`;
                return;
            }
        }

        const loginResponse = await userStore.loginByCode(<string>code);
        if (loginResponse.status === false) {
            error.value = loginResponse.message;
        } else {
            const otherParamsUrl = Object.keys(otherQueryParams)
                            .map((key) => `${key}=${otherQueryParams[key]}`)
                            .filter((item) => item)
                            .join('&');
            const redirectUrl = [from, otherParamsUrl].filter((item) => item).join('&');
            console.log({ redirectUrl });

            await router.replace((!redirectUrl || redirectUrl === '/') ? { path: '/' } : redirectUrl);
        }
    } else {
        error.value = '登录失败';
    }
});

</script>
<template>
    <el-container v-if="error">
        {{ error }}
    </el-container>
</template>

<style scoped lang="scss">
</style>
