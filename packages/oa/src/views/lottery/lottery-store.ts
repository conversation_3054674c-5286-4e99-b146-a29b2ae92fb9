import { people } from '@/views/lottery/people';
import { defineStore } from 'pinia';
import { keys } from '@/utils/utils';

const KEY_PEOPLE = '__lottery_people_2024__';
const KEY_RESULT = '__lottery_result_2024__';

export interface LuckyItem {
    name: string;
    type: number;
}

const DEFAULT_RESULT = {
    1: <LuckyItem[]>[],
    2: <LuckyItem[]>[],
    3: <LuckyItem[]>[],
    4: <LuckyItem[]>[],
    5: <LuckyItem[]>[],
    6: <LuckyItem[]>[],
};

function getPeopleList() {
    let peopleList;
    try {
        const cache = localStorage.getItem(KEY_PEOPLE);
        if (cache) {
            peopleList = JSON.parse(cache);
        }
    } catch (e) {
    }
    if (!peopleList) {
        peopleList = people;
        localStorage.setItem(KEY_PEOPLE, JSON.stringify(peopleList));
    }
    return peopleList;
}

function getResult() {
    let result;
    try {
        const lastResult = localStorage.getItem(KEY_RESULT);
        if (lastResult) {
            result = JSON.parse(lastResult);
        }
    } catch (e) {
    }
    if (!result) {
        result = DEFAULT_RESULT;
        localStorage.setItem(KEY_RESULT, JSON.stringify(result));
    }
    return result;
}

// Fisher–Yates shuffle
function shuffle(arr: LuckyItem[]) {
    let m = arr.length;
    while (m > 1) {
        let index = Math.floor(Math.random() * m--);
        [arr[m], arr[index]] = [arr[index], arr[m]];
    }
    return arr;
}

function testShuffle(shuffleFn: Function) {
    // 乱序次数
    let n = 100000;
    let countObj = <Record<string, number[]>>{};
    people.reduce((acc, cur) => {
        // @ts-ignore
        acc[cur.name] = Array.from({ length: people.length }).fill(0);
        return acc;
    }, countObj);
    for (let i = 0; i < n; i++) {
        let arr = people.map(item => item.name);
        shuffleFn(arr);
        people.forEach(item => {
            // @ts-ignore
            countObj[item.name][arr.indexOf(item.name)]++;
        });
    }
    console.table(countObj);
    return countObj;
}

export const useLotteryStore = defineStore('lottery', {
    state: () => ({
        result: DEFAULT_RESULT,
        peopleList: <LuckyItem[]>[],
    }),

    getters: {
        awardedPeopleList(state): Set<string> {
            let awarded: Array<LuckyItem> = [];
            type PropType = keyof typeof state.result;
            awarded = keys(state.result).reduce((acc, cur: PropType) => {
                acc = acc.concat(state.result[cur]);
                return acc;
            }, awarded);
            return new Set(awarded.map(p => p.name));
        },
        restPeopleList(state): Array<LuckyItem> {
            return state.peopleList.filter((p) => !this.awardedPeopleList.has(p.name));
        },
    },

    actions: {
        initPeopleList() {
            this.peopleList = getPeopleList();
            this.result = getResult();
        },

        /**
         * 抽奖
         * @param award 奖项
         * @param count 数量
         */
        lottery(award: number, count: number): LuckyItem[] {
            let luckyArr: LuckyItem[] = this.restPeopleList
                            .filter(
                                item => (
                                    award !== 5 && item.type === 1)
                                    || award === 5
                                    || award === 6
                                    || (award === 4 && (item.type === 1 || item.type === 2)
                                    ),
                            );

            luckyArr = shuffle(luckyArr).slice(0, count);
            this.setResult(award, luckyArr);
            return luckyArr;
        },

        setResult(award: number, lucky: LuckyItem[]) {
            // @ts-ignore
            this.result[award] = this.result[award].concat(lucky);
            localStorage.setItem(KEY_RESULT, JSON.stringify(this.result));
        },

        /**
         * 测试乱序函数
         */
        testShuffle() {
            return testShuffle(shuffle);
        },
    },
});
