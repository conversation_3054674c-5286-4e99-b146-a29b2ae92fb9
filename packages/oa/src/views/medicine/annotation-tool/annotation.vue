<script setup lang="ts">
import {
    reactive,
    ref,
    onMounted,
    onUnmounted,
    computed,
    watch,
    nextTick,
} from 'vue';
import _ from 'lodash';
import { createTextGroup, resetTextGroup, svgXValue, svgYValue } from './tools/text-operate';
import {
    setNewLine,
    createLineGroup,
    getAllContent,
    newLineValue,
    deleteLineFun,
    randomColor,
    deleteLineValue,
    resetLineGroup,
} from '@/views/medicine/annotation-tool/tools/line-operate';
import { ElMessage } from 'element-plus/es';
import { MedicineLabelTagApi } from '@/api/medicine-label-tag-api';
import { MedicineManualApi } from '@/api/medicine-manual-api';

const props = defineProps({
    tagId: { 
        type: String,
        default: '',
    },
    manualId: {
        type: String,
        default: '',
    },
    visible: {
        type: Boolean,
        default: false,
    },
});
const emit = defineEmits(['update:visible']);
const dialogVisible = computed({
    get() {
        return props.visible;
    },
    set(val) {
        emit('update:visible', val);
    },
});

const dialogTop = computed(
    () => {
        const top: number = (window.innerHeight - svgYValue.value) / 2 - 200;
        return `${ top && !Number.isNaN(top) && top < 400 ? Math.abs(top) : 400 }px`;
    },
);

watch(() => props.visible, async (val) => {
    if (val) {
        await getApiWarehouseMedicineManualContent();
        await nextTick();
        await getApiWarehouseLabelTag();
        await setSvgSize();
        await getAllLabelTagAndContent();
    } else {
        resetDialog();
    }
});
onMounted(() => {
    // 窗口宽度监听功能
    window.addEventListener('resize', () => {
        setSvgSize();
    });
});
onUnmounted(() => {
    window.removeEventListener('resize', () => {
        setSvgSize();
    });
    timer.value && clearTimeout(timer.value);
});
const loadMoreSelect = ref<any>(null);
let textGroup: any = null;
interface AnnotationData {
    tagList: any[];
    text: string;
    contentId: string;
    title: string;
}
const annotationData = reactive<AnnotationData>({
    tagList: [],
    text: '',
    contentId: '',
    title: '说明书',
});
const annotationTag = ref('');
const annotationColor = ref('');
// popover提示框出现操作时的用途
const setLineType = ref('add');
let selectionObj = reactive<any>({});
let selectLoading = ref(false);
let pagination = reactive<any>({
    page: 1,
    limit: 10,
    count: 0,
});
const popoverOffset = reactive({
    visible: false,
    left: 0,
    top: 0,
});
const dialogLoading = ref(false);
let textLineList = reactive<any>([]);
let timer = ref(<any>null);

/**
 * 获取标注内容
 */
const getApiWarehouseMedicineManualContent = async () => {
    let res = <any>{};
    dialogLoading.value = true;
    try {
        res = await MedicineManualApi.getApiWarehouseMedicineManualContent(props.tagId, props.manualId);
    } catch (e: any) {
        dialogLoading.value = false;
        ElMessage({
            type: 'error',
            message: e.message || e,
        });
        return false;
    }
    if (res) {
        annotationData.text = res.content;
        annotationData.contentId = res.id;
        annotationData.title = res.tagName;
    }
    dialogLoading.value = false;
};
/**
 * 获取所有已标注内容
 */
const getAllLabelTagAndContent = async () => {
    let res = <any>{};
    try {
        res = await MedicineLabelTagApi.getApiWarehouseLabelTagBindContentList(annotationData.contentId);
    } catch (e: any) {
        ElMessage({
            type: 'error',
            message: e.message || e,
        });
        return false;
    }
    await setSvgSize();
    getAllContent(res, textLineList, drewAllLine);
};
/**
 * 获取tagList
 * @param keyword
 */
const getApiWarehouseLabelTag = async (keyword?: string) => {
    let res = <any>{};
    const offset = pagination.page * pagination.limit - pagination.limit;
    const limit = pagination.limit;
    if (pagination.page * limit - pagination.count > limit) {
        return false;
    }
    try {
        res = await MedicineLabelTagApi.getApiWarehouseLabelTag(props.tagId, keyword || '', offset.toString(), limit.toString());
    } catch (e: any) {
        ElMessage({
            type: 'error',
            message: e.message || e,
        });
        return false;
    }
    pagination.count = res.count;
    if (res.rows && res.rows.length) {
        res.rows = res.rows.map((item: any) => ({
            value: item.id,
            label: item.name,
            color: item.color,
        }));
        annotationData.tagList = keyword || pagination.page === 1 ? res.rows : [...annotationData.tagList, ...res.rows];
    } else {
        keyword && await postApiWarehouseLabelTag(keyword);
    }
};
const _debounceSearch = _.debounce(getApiWarehouseLabelTag, 1000);
/**
 * 新建标签
 * @param labelTagName
 */
const postApiWarehouseLabelTag = async (labelTagName: string) => {
    let res = <any>{};
    selectLoading.value = true;
    const params = <{
        labelTagName: string;
        labelTagColor: string;
        tagId: string;
    }>{
        labelTagName,
        labelTagColor: annotationColor.value || randomColor(),
        tagId: props.tagId,
    };
    try {
        res = await MedicineLabelTagApi.postApiWarehouseLabelTag(params);
    } catch (e: any) {
        selectLoading.value = false;
        ElMessage({
            type: 'error',
            message: e.message || e,
        });
    }
    if (res && res.id) {
        pagination.page = 1;
        await getApiWarehouseLabelTag(labelTagName);
    }
    selectLoading.value = false;
};

/**
 * 新建标注内容绑定
 * @param line
 */
const postApiWarehouseLabelTagBindContent = async (line: any) => {
    let res = <any>{};
    selectLoading.value = true;
    if (line.startIndex > line.endIndex) {
        [line.startIndex, line.endIndex] = [line.endIndex, line.startIndex];
    }
    const params = {
        labelContent: line.content,
        startIndex: line.startIndex,
        endIndex: line.endIndex,
        contentId: annotationData.contentId,
        labelTagId: line.tagId,
    };
    try {
        res = await MedicineLabelTagApi.postApiWarehouseLabelTagBindContent(params);
    } catch (e: any) {
        selectLoading.value = false;
        ElMessage({
            type: 'error',
            message: e.message || e,
        });
    }
    if (res && res.id) {
        ElMessage({
            type: 'success',
            message: '标注成功',
        });
        await getAllLabelTagAndContent();
    }
    selectLoading.value = false;
};

/**
 * 打开标注编辑框
 * @param x
 * @param y
 */
const openPopover = (x: number, y: number) => {
    popoverOffset.visible = true;
    popoverOffset.left = x;
    popoverOffset.top = y;
};

/**
 * 鼠标抬起绘制线
 * @param e
 */
const handleMouseSelect = (e: any) => {
    e.preventDefault();
    popoverOffset.visible = false;
    selectionObj = window.getSelection();
    // const selectRange = selectionObj && selectionObj.getRangeAt(0);
    const mouseSelectText = selectionObj && selectionObj.toString() || '';
    // 创建新的线
    if (mouseSelectText) {
        newLineValue.value = setNewLine(selectionObj);
        openPopover(e.clientX || e.changedTouches[0].clientX, e.clientY || e.changedTouches[0].clientY);
        setLineType.value = 'add';
    }
};

/**
 * 标注tag下拉选择切换
 * @param value 切换的tagId
 */
const onAnnotationTagChange = async (value: any) => {
    if (!value) {
        setLineType.value = 'delete';
        await deleteApiWarehouseLabelTagBindContent();
    }
    setLineType.value === 'edit' && deleteLineFun();
    let tag: any = annotationData.tagList.find((item: any) => item.value === value);
    const color = tag && tag.color ? tag.color : annotationColor.value || randomColor();
    if (!tag && value) {
        await postApiWarehouseLabelTag(value);
    } else if (value) {
        const newTagId = new Date().getTime();
        newLineValue.value.color = color;
        newLineValue.value.tagId = tag && tag.value ? tag.value : newTagId;
        newLineValue.value.tagText = tag && tag.label ? tag.label : value;
        // 添加标注内容与labelTag绑定
        await postApiWarehouseLabelTagBindContent(newLineValue.value);
    }
};

/**
 * 删除标注内容
 */
const deleteApiWarehouseLabelTagBindContent = async () => {
    let res = <any>{};
    selectLoading.value = true;
    const params = {
        labelContent: deleteLineValue.value.content,
        startIndex: deleteLineValue.value.startIndex,
        endIndex: deleteLineValue.value.endIndex,
        contentId: annotationData.contentId,
        labelTagId: deleteLineValue.value.tagId,
    };
    try {
        res = await MedicineLabelTagApi.deleteApiWarehouseLabelTagBindContent(params);
    } catch (e: any) {
        selectLoading.value = false;
        ElMessage({
            type: 'error',
            message: e.message || e,
        });
    }
    if (res && res.message) {
        ElMessage({
            type: 'success',
            message: '删除成功',
        });
        popoverOffset.visible = false;
        await getAllLabelTagAndContent();
    }
    selectLoading.value = false;
};

/**
 * 重新画线
 */
const drewAllLine = () => {
    createLineGroup('#annotationSvg', clickTag);
};

// 点击标注tag
const clickTag = (e: any, lineOffset: any, item: any) => {
    openPopover(e.clientX, e.clientY);
    annotationTag.value = lineOffset.tagId;
    annotationColor.value = item.color;
    setLineType.value = 'edit';
};
// 失去焦点关闭popover弹窗
const onAnnotationTagBlur = () => {
    timer.value = setTimeout(() => {
        popoverOffset.visible = false;
        pagination.page = 1;
        _debounceSearch();
    }, 200);
};
watch(() => popoverOffset.visible, (value: any) => {
    if (!value) {
        annotationTag.value = '';
        annotationColor.value = '';
    }
});

const resetDialog = () => {
    annotationData.tagList = [];
    annotationTag.value = '';
    annotationColor.value = '';
    textLineList.splice(0, textLineList.length);
    resetTextGroup();
    resetLineGroup();
};
/**
 * 设置svg幕布
 */
const setSvgSize = async () => {
    if (dialogVisible.value) {
        const res = await createTextGroup('#annotationSvg', annotationData.text);
        textGroup = res.textGroup;
        textLineList = res.textLineList;
        textGroup?.mouseup((e: any) => {
            handleMouseSelect(e);
        });
    }
};
/**
 * 颜色修改
 * @param color
 */
const onAnnotationColorChange = async (color:string) => {
    let res = <any>{};
    let tag: any = annotationData.tagList.find((item: any) => item.value === annotationTag.value);
    const params = {
        labelTagId: annotationTag.value,
        labelTagColor: color,
        labelTagName: tag.label,
        tagId: props.tagId,
    };
    try {
        res = await MedicineLabelTagApi.putApiWarehouseLabelTag(params);
    } catch (e: any) {
        ElMessage({
            type: 'error',
            message: e.message || e,
        });
    }
    if (res && res.id) {
        ElMessage({
            type: 'success',
            message: '标签修改成功',
        });
    }
    popoverOffset.visible = false;
    await getAllLabelTagAndContent();
};
/**
 * 下拉滚动加载更多
 * @param val 弹出层是否显示
 */
const loadMore = (val: boolean) => {
    if (val) {
        setTimeout(() => {
            const el = loadMoreSelect.value.scrollbar?.$el?.querySelector('.el-scrollbar__wrap') || false;
            if (el) {
                el.addEventListener('scroll', () => {
                    const oldScrollHeight = el.oldScrollHeight || 0;
                    if (el.scrollHeight !== oldScrollHeight && el.scrollHeight - el.scrollTop - el.clientHeight < 200) {
                        el.oldScrollHeight = el.scrollHeight;
                        pagination.page += 1;
                        getApiWarehouseLabelTag();
                    }
                });
            }
        }, 0);
    }
};
/**
 * 关闭弹窗
 */
const onHandClose = () => {
    resetDialog();
    svgXValue.value = 600;
    popoverOffset.visible = false;
};
/**
 * 关闭标注弹框
 */
const closePopover = () => {
    popoverOffset.visible = false;
};
</script>
<template>
    <el-dialog
        v-model="dialogVisible"
        :close-on-click-modal="false"
        :width="svgXValue + 100"
        :top="dialogTop"
        :title="`${annotationData.title || '说明书'}标注理解`"
        custom-class="annotation-tool"
        @closed="onHandClose"
    >
        <div v-loading="dialogLoading" class="annotation-card">
            <div id="svgDemo"></div>
            <svg
                id="annotationSvg"
                xmlns="http://www.w3.org/2000/svg"
                :width="svgXValue"
                :height="svgYValue"
            >
            </svg>
            <div
                v-if="popoverOffset.visible"
                class="popover-wrapper"
                :style="{left: `${popoverOffset.left}px`, top: `${popoverOffset.top}px` }"
            >
                <p class="popover-title">
                    设置标注标签
                    <el-icon class="popover-close-icon" @click="closePopover"><Close /></el-icon>
                </p>
                <div class="popover-content">
                    <el-select
                        ref="loadMoreSelect"
                        v-model="annotationTag"
                        filterable
                        remote
                        clearable
                        default-first-option
                        :reserve-keyword="false"
                        :loading="selectLoading"
                        placeholder="选择标注标签"
                        :remote-method="_debounceSearch"
                        @visible-change="loadMore"
                        @change="onAnnotationTagChange"
                        @blur="onAnnotationTagBlur"
                    >
                        <el-option
                            v-for="item in annotationData.tagList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                    <div class="color-select">
                        <p class="color-select-label">标签颜色选择</p>
                        <el-color-picker v-model="annotationColor" show-alpha @change="onAnnotationColorChange" />
                    </div>
                </div>
            </div>
        </div>
    </el-dialog>
</template>
<style lang="scss">
.annotation-tool {
    .el-dialog__header {
        background-color: #fffbd9;
        margin-right: 0;
    }

    .el-dialog__body {
        overflow-y: hidden;
        //height: 620px;
        background-color: #fffdec;
    }

    .annotation-card {
        .annotation-tag {
            display: inline-block;
            vertical-align: top;
            cursor: pointer;
        }

        .popover-wrapper {
            position: fixed;
            background-color: #fff;
            border-radius: 4px;
            padding: 12px;
            box-shadow: 0 0 4px rgba(0, 0, 0, .12), 0 4px 4px rgba(0, 0, 0, .24);

            .popover-title {
                margin-bottom: 12px;
                position: relative;

                .popover-close-icon {
                    position: absolute;
                    right: 4px;
                    top: 4px;
                    cursor: pointer;
                }
            }

            .popover-content {
                min-height: 120px;

                .color-select {
                    margin-top: 12px;

                    .color-select-label {
                        margin-bottom: 12px;
                    }
                }
            }
        }
    }
}
</style>
