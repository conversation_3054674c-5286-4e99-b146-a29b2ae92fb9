import {computed, reactive, ref} from "vue";
import {SVG} from "@svgdotjs/svg.js";
import {svgXValue, svgYValue} from "@/views/medicine/annotation-tool/tools/text-operate";
import _ from "lodash";
let lineGroup: any = null;
const lineOffsetList: any[] = [];
let selectionObj = {};
let newLine = {
    content: '',
    startX: 0,
    startY: 0,
    endX: 0,
    endY: 0,
    startChunkIndex: 0,
    endChunkIndex: 0,
    color: '',
    tagId: '',
    tagText: '',
    startIndex: 0,
    endIndex: 0,
};
let deleteLine = {};
const lineHeight = 36;
let chunkIndexTransformOptions: any = {};
export const newLineValue: any = computed({
    get() {
        return newLine;
    },
    set(value) {
        Object.assign(newLine, value);
    }
});
export const deleteLineValue: any = computed({
    get() {
        return deleteLine;
    },
    set(value) {
        Object.assign(deleteLine, value);
    }
});
export const  resetLineGroup = () => {
    lineGroup && lineGroup.clear();
    lineGroup = null;
}
// 判断线不相交
export const getLineNoIntersect = (line: any, comparedLine: any) => {
    // （——旧线1——）（———新线———）（———旧线2———）
    if (comparedLine.startChunkIndex === line.startChunkIndex
        && comparedLine.endChunkIndex === line.endChunkIndex
        && comparedLine.startChunkIndex === comparedLine.endChunkIndex) {
        if (comparedLine.endX < line.startX || line.endX < comparedLine.startX) {
            return true;
        }
    }
    // （——旧线——） （————————新——————
    //   ———线————）
    if (comparedLine.startChunkIndex === line.startChunkIndex
        && comparedLine.endChunkIndex === line.startChunkIndex
        && comparedLine.endChunkIndex < line.endChunkIndex) {
        if (comparedLine.endX < line.startX) {
            return true;
        }
    }
    //  （————————新——————
    //   ———线————） （——旧线——）
    if (comparedLine.startChunkIndex === line.endChunkIndex
        && comparedLine.endChunkIndex === line.endChunkIndex
        && comparedLine.startChunkIndex > line.startChunkIndex) {
        if (comparedLine.startX > line.endX) {
            return true;
        }
    }
    //  （————————新线——————）（——旧——
    //   ————线————）
    if (line.startChunkIndex === line.endChunkIndex
        && comparedLine.startChunkIndex === line.startChunkIndex
        && comparedLine.endChunkIndex > line.endChunkIndex) {
        if (comparedLine.startX > line.endX) {
            return true;
        }
    }
    //  （————————旧——————
    //  ———线———）（————————新——————）
    if (line.startChunkIndex === line.endChunkIndex
        && comparedLine.endChunkIndex === line.endChunkIndex
        && comparedLine.startChunkIndex < line.startChunkIndex) {
        if (comparedLine.endX < line.startX) {
            return true;
        }
    }
    //  （————————旧线——————） ｜（————————新线1——————）
    //  （————————新线——————） ｜（————————旧线1——————）
    if (line.startChunkIndex > comparedLine.endChunkIndex || line.endChunkIndex < comparedLine.startChunkIndex) {
        return true;
    }
};

// 获取选区dom信息
export const getSelectDomInfo = (selectionObj: any, nodeType: string) => {
    const node = selectionObj && selectionObj[nodeType].parentElement;
    const parentNode = node && node.parentElement;
    const styles = parentNode && parentNode.getAttribute('style');
    const translateYList = styles && styles.match(/\d+/g);
    const X = parseInt(node.getAttribute('x')) + (nodeType === 'focusNode' ? 15 : 0);
    const Y = parseInt(node.getAttribute('y')) + 10;
    const translateY = translateYList && translateYList.length ? parseInt(translateYList[0]) : 0;
    const chunkIndex = parseInt(node.getAttribute('data-chunkIndex'));
    const textIndex = parseInt(node.getAttribute('data-offset'));
    return {
        X,
        Y,
        translateY,
        chunkIndex,
        textIndex
    };
};
// 设置新的线
export const setNewLine = (selectionObj: any) => {
    let {
        X: startX,
        Y: startY,
        translateY: startTranslateY,
        chunkIndex: startChunkIndex,
        textIndex: startIndex
    } = getSelectDomInfo(selectionObj, 'anchorNode');
    let {
        X: endX,
        Y: endY,
        translateY: endTranslateY,
        chunkIndex: endChunkIndex,
        textIndex: endIndex
    } = getSelectDomInfo(selectionObj, 'focusNode');
    if (startChunkIndex > endChunkIndex || (endX < startX && startY === endY)) {
        [startY, endY] = [endY, startY];
        [startX, endX] = [endX, startX];
        [startChunkIndex, endChunkIndex] = [endChunkIndex, startChunkIndex];
        [startTranslateY, endTranslateY] = [endTranslateY, startTranslateY];
        [startIndex, endIndex] = [endIndex, startIndex];
        startX = startX - 15;
        endX = endX + 15;
    }
    return {
        content: selectionObj && selectionObj.toString() || '',
        startX,
        startY: startY + startTranslateY,
        endX,
        endY: endY + endTranslateY,
        startChunkIndex,
        endChunkIndex,
        startIndex,
        endIndex,
        color: '',
        tagId: '',
        tagText: '',
    };
};

// 绘制线
export const drewLine = (lineOffset: any, lineGroup: any, svgX:number, callback: Function) => {
    if (lineOffset.endY < lineOffset.startY) {
        return false;
    }
    if (lineOffset.startY < lineOffset.endY) {
        for (let i = lineOffset.startChunkIndex; i <= lineOffset.endChunkIndex; i++) {
            lineGroup.line(
                i === lineOffset.startChunkIndex ? lineOffset.startX : 0,
                lineOffset.startY
                + (lineOffset.endY - lineOffset.startY) / (lineOffset.endChunkIndex - lineOffset.startChunkIndex) * (i - lineOffset.startChunkIndex),
                i === lineOffset.endChunkIndex ? lineOffset.endX : svgX,
                lineOffset.startY
                + (lineOffset.endY - lineOffset.startY) / (lineOffset.endChunkIndex - lineOffset.startChunkIndex) * (i - lineOffset.startChunkIndex),
            ).stroke({
                color: lineOffset.color || 'red',
                width: 2,
            });
        }
    } else {
        lineGroup.line(lineOffset.startX, lineOffset.startY, lineOffset.endX, lineOffset.endY).stroke({
            color: lineOffset.color || 'red',
            width: 2,
        });
    }
    const text = lineGroup.text(lineOffset.tagText).attr('x', lineOffset.startX).attr('y', lineOffset.startY + 20).attr('fill', `${lineOffset.color || 'red'}`).attr('class', 'annotation-tag');
    text.node.addEventListener('click', (e: any) => {
        callback(e, lineOffset);
    });
};

// 根据索引选中区域
export const setRangeByIndex = (startChunkIndex: number, endChunkIndex: number) => {
    if(startChunkIndex > endChunkIndex) {
        [startChunkIndex, endChunkIndex] = [endChunkIndex, startChunkIndex];
    }
    const selectionObj: any = window.getSelection();
    const range = document.createRange();
    const startDom: any = document.querySelector(`[data-offset="${startChunkIndex}"]`)?.firstChild;
    const endDom: any = document.querySelector(`[data-offset="${endChunkIndex}"]`)?.lastChild;
    range.setStart(startDom, 0);
    range.setEnd(endDom, 1);
    selectionObj.removeAllRanges();
    selectionObj.addRange(range);
    return {selectionObj, newLineData: setNewLine(selectionObj)};
};

/**
 * 获取所有已标注内容
 * @param res
 * @param callback
 */
export const getAllContent = (res: any[], textLineList: any[], callback: Function) => {
    lineOffsetList.splice(0, lineOffsetList.length);
    chunkIndexTransformOptions = {};
    resetLineGroup();
    res.forEach((item: any) => {
        const rangeData: any = setRangeByIndex(item.startIndex, item.endIndex);
        Object.assign(selectionObj, rangeData.selectionObj);
        newLineValue.value = { ...rangeData.newLineData, tagId: item.labelTagId, tagText: item.labelTagName, color: item.labelTagColor };
        const offset = lineOffsetList.find((item: any) => !getLineNoIntersect(newLineValue.value, item));
        transformLineY(offset, newLineValue.value, textLineList);
        lineOffsetList.push(_.cloneDeep(newLineValue.value));
    });
    callback();
    const selectionData = window.getSelection();
    if (selectionData) {
        selectionData.removeAllRanges();
    }
}

/**
 * 删除选中的线
 */
export const deleteLineFun = () => {
    const findIndex = lineOffsetList.findIndex((item: any) => item.content === deleteLineValue.value.content
        && item.startX === deleteLineValue.value.startX && item.startY === deleteLineValue.value.startY
        && item.endX === deleteLineValue.value.endX && item.endY === deleteLineValue.value.endY);
    findIndex > -1 && lineOffsetList.splice(findIndex, 1);
}

/**
 * 设置文字往下移动
 * @param newLine
 * @param textLineList
 */
const setLineTextTransform = (newLine: any, textLineList: any[]) => {
    for (let i = newLine.startChunkIndex + 1; i < textLineList.length; i++) {
        const nextLine = textLineList[i];
        chunkIndexTransformOptions[i] = chunkIndexTransformOptions[i] || {isTransformY: false};

            lineOffsetList.forEach((downLine: any) => {
                if (downLine.startChunkIndex === i) {
                    downLine.startY += chunkIndexTransformOptions[i].isTransformY ? lineHeight : 12;
                    downLine.endY += chunkIndexTransformOptions[i].isTransformY ? lineHeight : 12;
                }
            });
            if (nextLine && nextLine.node) {
                const translateYList = nextLine.node.getAttribute('style').match(/\d+/g);
                translateYList && translateYList.length > 0
                && nextLine.attr('style', `transform: translateY(${parseInt(translateYList[0]) + (chunkIndexTransformOptions[i].isTransformY ? lineHeight : 12)}px)`);
            }
            chunkIndexTransformOptions[i].isTransformY = true;


    }
}

/**
 * 比较重合线并下移
 * @param textLineList
 */
export const transformLineY = (offset: any, newLine: any, textLineList: any[]) => {
    if(offset) {
        const index = (offset.startChunkIndex || 0) + 1

        // 如果本条线中相交的线中没有互相相交的线，退出
        const childOffset = offset.childLineList?.find((item: any) =>
        {
          return  !getLineNoIntersect(newLine, item) && item.content !== newLine.content;
        });
        if (childOffset) {
            transformLineY(childOffset, newLine, textLineList);
        }
        !offset.childLineList && (offset.childLineList = []);
        offset.childLineList.push(_.cloneDeep(newLine));
        newLine.endY += lineHeight;
        newLine.startChunkIndex === offset.startChunkIndex && (newLine.startY += lineHeight);
        !childOffset && setLineTextTransform(newLine, textLineList);
        svgYValue.value += 12;
    } else {
        return false;
    }
}


// 获取随机颜色
export const randomColor = (): String => {
    const R = Math.floor(Math.random() * 256);
    const G = Math.floor(Math.random() * 256);
    const B = Math.floor(Math.random() * 256);
    if (255 - R < 10 || R < 10 || 255 - G < 10 || G < 10 || 255 - B < 10 || B < 10) {
        return randomColor();
    }
    return `rgba(${R}, ${G} , ${B}, 1)`;
};

// 创建下划线SVG
export const createLineGroup = (selector: string, callback: Function) => {
    !lineGroup && (lineGroup = SVG().addTo(selector).size(svgXValue.value, svgYValue.value));
    lineGroup.clear();

    lineOffsetList.forEach((item: any) => {
        drewLine(item, lineGroup, svgXValue.value, (e: any, lineOffset: any) => {
            deleteLineValue.value = lineOffset;
            const rangeData: any = setRangeByIndex(lineOffset.startIndex, lineOffset.endIndex);
            Object.assign(selectionObj, rangeData.selectionObj);
            newLineValue.value =  rangeData.newLineData;
            callback(e, lineOffset, item);
        });
    });
    return lineGroup;
}