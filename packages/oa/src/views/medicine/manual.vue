<script lang="ts" setup>
import { MedicineCategoryApi } from '@/api/medicine-category-api';
import { MedicineManualApi } from '@/api/medicine-manual-api';
import { MedicineApi } from '@/api/medicine-api';
import _ from 'lodash';
import {
    nextTick,
    onMounted,
    reactive,
    ref,
} from 'vue';

import ManualEdit from './components/manual-edit.vue';
import MedicineCategory from './components/medicine-category.vue';
import MedicineHeader from './components/medicine-header.vue';

import { MEDICINE_INTRODUCTION_OPTIONS } from './configs/common';
import { ElMessage, ElMessageBox, ElScrollbar } from 'element-plus';
import { Tree } from '@/views/medicine/model';

let loading = ref(false);
let manualLoading = ref(false);
let category = ref<Tree[]>();
let showModal = ref(false);
let medicineId = ref('');
let medicineName = ref('');
let showPagination = ref(false);
let currentIngredientId = ref('');
let editSwitch = ref(false);
let showAddCategoryNodeVisible = ref(false);
let addCategoryLevel = ref('一级');
const manualDialogType = ref('add');
const categoryId = ref('');
const activeData = ref<any>({});
let basicTagList = reactive([]);
const manualIntroduction = ref({});
const expandedKeys = ref<string[]>([]);
const currentNodeKeyId = ref<any>('');
const scrollbarRef = ref<InstanceType<typeof ElScrollbar>>();
let query = reactive({
    filterIngredient: undefined,
    isClean: undefined,
    type: '1',
    medicineName: undefined,
    manufacturer: undefined,
});

interface Breif {
    hits: any[];
    total: number;
    page: number;
    pageSize: number;
}

const breif = reactive<Breif>({
    hits: [],
    total: 0,
    page: 1,
    pageSize: 10,
});
let breifHitsSource = reactive<any>([]);

onMounted(
    () => {
        getCategoryTree();
    },
);

/**
 * 获取药品分类树
 */
const getCategoryTree = async () => {
    loading.value = true;
    let res = <any>[];
    try {
        res = await MedicineCategoryApi.getApiWarehouseMedicineCategoryAbc(query.type);
    } catch (e: any) {
        loading.value = false;
        ElMessage({
            type: 'error',
            message: e.message || e,
        });
        return false;
    }
    if (res && res.length) {
        category.value = res || [];
    }
    loading.value = false;
    showPagination.value = false;
};

/**
 * 获取说明书简介tag
 */
const getBasicTags = async () => {
    manualLoading.value = true;
    let res = <any>[];
    try {
        res = await MedicineManualApi.getApiWarehouseMedicineManualBasicTag();
    } catch (e: any) {
        manualLoading.value = false;
        ElMessage({
            type: 'error',
            message: e.message || e,
        });
        return false;
    }
    if (res && res.length) {
        basicTagList = res || [];
    }
    manualLoading.value = false;
};
/**
 * 获取药品简介
 * @param data
 */
const onCategoryClick = async (data: any) => {
    activeData.value = data;
    if (data.isLeaf == 1 || (!data.isLeaf && data.isLeaf !== 0)) {
        manualLoading.value = true;
        let res = <any>{};
        const offset = ((breif.page || 1) - 1) * 10;
        try {
            res = await MedicineApi.getApiWarehouseMedicine(
                offset.toString(),
                breif.pageSize.toString(),
                data.id || activeData.value.id,
                query.type,
                query.medicineName,
                query.isClean,
            );
        } catch (e: any) {
            ElMessage({
                type: 'error',
                message: e.message || e,
            });
            manualLoading.value = false;
            return false;
        }
        await getBasicTags();

        breif.hits = res.rows;
        breifHitsSource = _.cloneDeep(res.rows);
        breif.total = res.total;
        showPagination.value = res.total > 10;
        currentIngredientId.value = data.id;
        manualLoading.value = false;
    }
};

/**
 * 查看说明书
 * @param data
 */
const onClickBreif = async (data: any) => {
    manualDialogType.value = 'view';
    Object.assign(manualIntroduction.value, data);
    await nextTick();
    showModal.value = true;
    medicineId.value = data.id;
    medicineName.value = data.name;
};

/**
 * 开启编辑药品
 * @param item
 * @param data
 */
const beforeEditMedicine = (item: any, data: any) => {
    item.isEdit = true;
    data.isEdit = true;
};

/**
 * 编辑药品信息
 * @param item
 * @param medicine
 */
const editMedicine = async (item: any, medicine: any) => {
    let res = <any>{};
    try {
        res = await MedicineApi.putApiWarehouseMedicine({
            medicineId: item.id,
            name: item.name,
            manufacturer: item.manufacturer,
            spec: item.spec,
            dosage: item.dosage,
            shebaoCode: item.shebaoCode,
        });
    } catch (e: any) {
        ElMessage({
            type: 'error',
            message: e.message || e,
        });
        return false;
    }
    if (res && res.id) {
        ElMessage.success('药品信息修改成功');
        await onCategoryClick(activeData.value);
        item.isEdit = false;
        medicine.isEdit = false;
    }
};

/**
 * 取消编辑药品信息
 * @param item
 * @param medicine
 */
const cancelEditMedicine = (item: any, medicine: any) => {
    const dataSource = breifHitsSource.find((i: any) => i.id == item.id);
    item[medicine.key] = dataSource && dataSource[medicine.key];
    item.isEdit = false;
    medicine.isEdit = false;
};

/**
 * 删除药品信息
 * @param item
 */
const deleteMedicine = async (item: any) => {
    ElMessageBox.confirm(
        '确定删除药品: ' + (item.name || '') + '?',
        '注意',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        },
    )
                    .then(async () => {
                        let res = <any>{};
                        try {
                            res = await MedicineApi.deleteApiWarehouseMedicine(item.id);
                        } catch (e: any) {
                            ElMessage({
                                type: 'error',
                                message: e.message || e,
                            });
                            return false;
                        }
                        if (res && res.id) {
                            await onCategoryClick(activeData.value);
                            ElMessage({
                                type: 'success',
                                message: '删除完毕',
                            });
                        }
                    })
                    .catch(() => {
                        ElMessage({
                            type: 'info',
                            message: '删除取消',
                        });
                    });
};

/**
 * 关闭说明书弹窗
 */
const onCloseModal = async () => {
    showModal.value = false;
    manualDialogType.value = 'view';
    await onCategoryClick(activeData.value);
};

/**
 * 药品资料翻页
 * @param current
 */
const onPageChange = async (current: any) => {
    let res = <any>{};
    const offset = ((current || 1) - 1) * 10;
    manualLoading.value = true;
    try {
        res = await MedicineApi.getApiWarehouseMedicine(
            offset.toString(), breif.pageSize.toString(), activeData.value.id, query.type, query.medicineName, query.isClean,
            query.manufacturer,
        );
    } catch (e: any) {
        ElMessage({
            type: 'error',
            message: e.message || e,
        });
        manualLoading.value = false;
        return false;
    }
    breif.hits = res.rows;
    breif.total = res.total;
    showPagination.value = res.total > 10;
    scrollbarRef.value!.setScrollTop(0);
    manualLoading.value = false;
};

/**
 * 打开新增一级分类弹窗
 */
const onPlusLevel1 = () => {
    addCategoryLevel.value = '一级';
    showAddCategoryNodeVisible.value = true;
};

/**
 * 药品分类变化
 * @param type 分类类型
 */
const onChangeType = (type: string) => {
    query.type = type;
    getCategoryTree();
};

/**
 * 筛选条件变化
 * @param newQueryData 新的筛选条件
 */
const onQueryChange = async (newQueryData: any) => {
    Object.assign(query, newQueryData);
    _debounceSearch();
};

/**
 * 筛选药品分类树
 * @param newFilterIngredient 分类树筛选条件
 */
const onFilterIngredientChange = (newFilterIngredient: any) => {
    query.filterIngredient = newFilterIngredient;
};

/**
 * 编辑分类名称
 * @param data 当前节点数据
 */
const onCategoryEdit = async (data: any) => {
    let res = <any>{};
    try {
        res = await MedicineCategoryApi.putApiWarehouseMedicineCategory({ categoryId: data.id, name: data.category });
    } catch (e: any) {
        ElMessage({
            type: 'error',
            message: e.message || e,
        });
        return false;
    }
    if (res && res.id) {
        ElMessage({
            type: 'success',
            message: '修改成功',
        });
        expandedKeys.value = [res.id];
        currentNodeKeyId.value = res.id;
    }
};

/**
 * 删除分类
 * @param data 当前节点数据
 */
const onCategoryDelete = async (data: any) => {
    await onCategoryClick(data);
    if (breif.hits.length > 0) {
        ElMessage({
            type: 'error',
            message: '请先删除分类下的药品',
        });
        return false;
    }
    let res = <any>{};
    try {
        res = await MedicineCategoryApi.deleteApiWarehouseMedicineCategory(data.id);
    } catch (e: any) {
        ElMessage({
            type: 'error',
            message: e.message || e,
        });
        return false;
    }
    if (res && res.id) {
        ElMessage({
            type: 'success',
            message: '删除完毕',
        });
        await getCategoryTree();
        breif.hits = [];
        activeData.value = {};
        expandedKeys.value = [res.parentCategoryId];
        currentNodeKeyId.value = res.parentCategoryId;
    }
};

/**
 * 新增分类
 * @param categoryName 分类名称
 * @param addNodeData 当前节点数据
 * @param categoryLevel 当前节点级别
 */
const onAddCategoryNodeSubmit = async (categoryName: string, addNodeData: any, categoryLevel: string) => {
    let { node, data }: { node: any, data: any } = addNodeData;
    let postData = {
        id: '',
        isLeaf: categoryLevel === '一级' ? false : data.parentCategoryId !== '',
        type: query.type,
        category: categoryName,
        children: [],
        parentCategoryId: categoryLevel === '一级' ? '' : data.id,
        label: categoryName,
    };
    let res = <any>{};
    try {
        res = await MedicineCategoryApi.postApiWarehouseMedicineCategory(postData);
    } catch (e: any) {
        ElMessage({
            type: 'error',
            message: e.message || e,
        });
        return false;
    }
    if (res && res.id) {
        postData.id = res.id;
        if (categoryLevel === '一级') {
            category.value && category.value.push(postData);
        } else {
            data.children && Array.isArray(data.children) ? data.children.push(postData) : data.children = [postData];
            node && (node.expanded = true);
        }
        showAddCategoryNodeVisible.value = false;
        ElMessage({
            message: '新增分类成功！',
            type: 'success',
        });
        try {
            categoryLevel === '一级' && await getCategoryTree();
        } catch (e: any) {
            ElMessage({
                type: 'error',
                message: e.message || e,
            });
            return false;
        }
        expandedKeys.value = [res.id];
        currentNodeKeyId.value = res.id;
        activeData.value = res;
    } else {
        ElMessage({
            message: res.message || '新增分类失败！',
            type: 'error',
        });
    }
};

/**
 * 新增子节点下级数据
 * @param node 当前节点
 * @param data 当前节点数据
 */
const onPlusOtherLeafLevel = async (node: any, data: any) => {
    categoryId.value = data.id;
    medicineId.value = '';
    manualDialogType.value = 'add';
    manualIntroduction.value = {};
    await nextTick();
    showModal.value = true;
};

let selectOptions = ref(<any>[]);
/**
 * 获取药品信息
 */
const remoteMethod = async () => {
    loading.value = true;
    if (query.medicineName) {
        let res = <any>{};
        const offset = ((breif.page || 1) - 1) * 10;
        try {
            res = await MedicineApi.getApiWarehouseMedicine(
                offset.toString(),
                breif.pageSize.toString(),
                '',
                query.type,
                query.medicineName,
                query.isClean,
                query.manufacturer,
            );
        } catch (e: any) {
            ElMessage({
                type: 'error',
                message: e.message || e,
            });
            return false;
        }
        if (res) {
            breif.hits = res.rows;
            breif.total = res.total;
            showPagination.value = res.total > 10;
        }
    } else {
        breif.hits = [];
    }
    loading.value = false;
};
const _debounceSearch = _.debounce(remoteMethod, 300);

</script>
<template>
    <div class="manual-wrapper">
        <el-container>
            <el-header class="medicine-header-wrapper">
                <MedicineHeader
                    v-model:edit-switch="editSwitch"
                    v-model:currentData="query"
                    @onChangeType="onChangeType"
                    @onQueryChange="onQueryChange"
                ></MedicineHeader>
            </el-header>
            <el-container>
                <el-aside class="manual-wrapper-aside">
                    <MedicineCategory
                        v-model:visible="showAddCategoryNodeVisible"
                        v-model:add-category-level="addCategoryLevel"
                        :filter-ingredient="query.filterIngredient"
                        :category-tree="category"
                        :current-node-key-id="currentNodeKeyId"
                        :expanded-keys="expandedKeys"
                        :loading="loading"
                        :edit-switch="editSwitch"
                        @onCategoryEdit="onCategoryEdit"
                        @onCategoryDelete="onCategoryDelete"
                        @onCategoryClick="onCategoryClick"
                        @onPlusOtherLeafLevel="onPlusOtherLeafLevel"
                        @onAddCategoryNodeSubmit="onAddCategoryNodeSubmit"
                    ></MedicineCategory>
                </el-aside>
                <el-main class="main manual-medicine-wrapper-main bg-purple-light">
                    <el-scrollbar
                        ref="scrollbarRef"
                        v-loading="manualLoading"
                        height="100%"
                    >
                        <el-row
                            v-for="item in breif.hits"
                            :key="item"
                            class="desc"
                        >
                            <el-col :span="24">
                                <div class="desc-item bg-desc-item">
                                    <el-descriptions
                                        :title="item.name"
                                        :column="2"
                                        border
                                    >
                                        <template #extra>
                                            <el-button type="primary" size="small" @click="onClickBreif(item)">
                                                说明书
                                            </el-button>
                                            <el-button type="danger" size="small" @click="deleteMedicine(item)">
                                                删除药品
                                            </el-button>
                                        </template>
                                        <el-descriptions-item
                                            v-for="medicine in MEDICINE_INTRODUCTION_OPTIONS"
                                            :key="medicine.key"
                                        >
                                            <template #label>
                                                <div class="cell-item">
                                                    {{ medicine.label }}
                                                </div>
                                            </template>
                                            <span v-if="!item.isEdit || !medicine.isEdit">
                                                <el-tag
                                                    class="cell-item-tag"
                                                    size="small"
                                                    @click="beforeEditMedicine(item,medicine)"
                                                    v-html="item[medicine.key] || ''"
                                                ></el-tag>
                                            </span>
                                            <div v-else class="edit-medicine-wrapper">
                                                <el-input
                                                    v-model="item[medicine.key]"
                                                    size="small"
                                                    autofocus
                                                    style="width: 60%;"
                                                    @keydown.enter="editMedicine(item, medicine)"
                                                ></el-input>
                                                <div class="edit-medicine-btn-wrapper">
                                                    <el-button
                                                        type="primary"
                                                        size="small"
                                                        @click="editMedicine(item,medicine)"
                                                    >
                                                        保存
                                                    </el-button>
                                                    <el-button size="small" @click="cancelEditMedicine(item,medicine)">
                                                        取消
                                                    </el-button>
                                                </div>
                                            </div>
                                        </el-descriptions-item>
                                    </el-descriptions>
                                </div>
                            </el-col>
                        </el-row>
                        <el-empty
                            v-if="!breif.hits || !breif.hits.length"
                            class="medicine-empty"
                            description="暂无对应药品资料"
                        />
                    </el-scrollbar>
                    <div v-if="showPagination" class="pagination">
                        <el-pagination
                            v-model:current-page="breif.page"
                            :page-size="breif.pageSize"
                            background
                            layout="total, prev, pager, next"
                            :total="breif.total"
                            @current-change="onPageChange"
                        >
                        </el-pagination>
                    </div>
                </el-main>
            </el-container>
        </el-container>
    </div>

    <Teleport to="body">
        <ManualEdit
            v-model:visible="showModal"
            :title="medicineName"
            :category-id="categoryId"
            :medicine-type="query.type"
            :manual-introduction="manualIntroduction"
            :manual-dialog-type="manualDialogType"
            :medicine-id="medicineId"
            :medicine-options="MEDICINE_INTRODUCTION_OPTIONS"
            @submit="onCloseModal"
            @cancel="onCloseModal"
        >
        </ManualEdit>
    </Teleport>
</template>

<style lang="scss">
.manual-wrapper {
    height: 100%;

    .medicine-header-wrapper {
        height: auto;
        padding: 0;
        margin-bottom: 12px;
    }

    .manual-wrapper-aside {
        height: 80vh;
        overflow-y: auto;
        padding: 12px 0;
        background: #fff;

        &-toolbar {
            padding: 12px;
        }
    }

    .manual-medicine-wrapper-main {
        height: 80vh;

        .el-scrollbar {
            height: 92%;
        }

        .pagination {
            margin-top: 30px;
            display: flex;
            justify-content: flex-end;
        }
    }

    .desc + .desc {
        margin-top: 12px;
    }

    .desc {
        cursor: pointer;

        .el-descriptions__header {
            padding: 12px;
            margin-bottom: 0;
        }

        .el-descriptions__label {
            width: 20%;
        }

        .el-descriptions__content {
            width: 30%;
        }

        .edit-medicine-wrapper {
            display: flex;
            align-items: center;

            .edit-medicine-btn-wrapper {
                margin-left: 12px;
            }
        }
    }

    .medicine-empty {
        height: 60%;
    }

    .bg-purple-light {
        background: #e5e9f2;
        border-radius: 4px;
        margin-left: 12px;
    }

    .bg-purple {
        background: #d3dce6;
        border-radius: 4px;
        margin-right: 2px;
    }

    .bg-desc {
        background: #e5e9f2;
        border-radius: 4px;
        margin-top: 10px;
    }

    .bg-desc-item {
        background: #7fa4cf;
        border-radius: 4px;

        .el-descriptions__title {
            max-width: 600px;
            white-space: normal;
        }

        .cell-item-tag {
            max-width: 600px;
            height: auto;
            white-space: normal;
            min-height: 24px;
        }
    }

    .bg-purple-dark {
        background: #99a9bf;
        border-radius: 4px;
        margin-left: 2px;
    }
}
</style>
