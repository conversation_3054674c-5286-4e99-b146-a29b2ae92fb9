import type { TableColumnCtx } from 'element-plus/es/components/table/src/table-column/defaults';

export interface Medicine {
    name: string,
    id: string,
    manufacturer: string,
    spec: string,
    dosage: string,
    shebaoCode: string,
}

export interface MedicineOptions {
    key: keyof Medicine;
    label: string;
    isEdit?: boolean;
}

export interface Tree {
    id: string
    category: string
    isLeaf: boolean
    parentCategoryId: string
    children?: Tree[]
    label: string
}

export interface TableData {
    id?: string
    medicineIngredientsIdOne: string
    medicineIngredientsIdTwo: string
    medicineIngredientsNameOne: string
    medicineIngredientsNameTwo: string
    influenceDetail: string
    level: number
    measures: string
}

export interface SpanMethodProps {
    row: TableData
    column: TableColumnCtx<TableData>
    rowIndex: number
    columnIndex: number
}

export interface SpanMethodByBusinessProps {
    business: string
    row: TableData
    column: TableColumnCtx<TableData>
    rowIndex: number
    columnIndex: number
}

export interface InteractionQuery {
    medicineIngredientsIdOne: any,
    medicineIngredientsIdTwo: string,
    primaryClassification: string,
    secondaryClassification: string,
    level: any,
    measures: string,
    isClean: any,
}

interface PropOptions {
    labelKey: string,
    labelIdKey: string,
    contentKey: string,
    childrenKey: string,
}

export interface ListSchema {
    propOptions: PropOptions,
    operateButtons: string[]
    tagList: {label?: any, value?: any, [key: string]: any}[]
}

export interface listDialogOption {
    tagId?: string | number,
    content?: string
}
