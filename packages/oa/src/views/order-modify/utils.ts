import type { OrderStatus } from './types';

export const formatOrderStatus = (status: OrderStatus) => {
    if (status === 0) {
        return '待支付';
    } if (status === 10) {
        return '已支付';
    } if (status === 20) {
        return '已退款';
    } if (status === 90) {
        return '已关闭';
    }
    return '未知状态';
};

export const formatUnit = (unit: string) => {
    if (unit === 'year') {
        return '年';
    }
    return unit;
};
