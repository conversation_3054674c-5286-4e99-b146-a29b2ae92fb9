<script setup lang="ts">
import failMarkImg from '@/assets/fail-mark.png';
import successMarkImg from '@/assets/success-mark.png';
import { OaCard } from '@abc-oa/components';
import OaCellGroup from '@/components/oa-cell-group.vue';
import OaCell from '@/components/oa-cell.vue';
import { formatMoney } from '@/utils/format';
import { OrderStatus } from '@/views/order/model/model';
import { computed } from 'vue';
import { getChargeItemTypeLabel } from '@/views/order/model/schema/buy-examination-device';

const props = defineProps({
    orderInfo: {
        type: Object,
        default: null,
    },
});
const getClinicDetail = (orderInfo: any) => `${orderInfo.clinicName} ${
    orderInfo.employeeName ? '/ ' + orderInfo.employeeName : ''} ${
    orderInfo.mobile ? '/ ' + orderInfo.mobile : ''}`;
// 已支付
const isPaid = computed(() => props.orderInfo?.status === OrderStatus.DONE);
// 已取消
const isCanceled = computed(() => props.orderInfo?.status === OrderStatus.CANCELED);

const statusMarkImg = computed(() => {
    if (isPaid.value) {
        return successMarkImg;
    } if (isCanceled.value) {
        return failMarkImg;
    }
    return null;
});
</script>
<template>
    <oa-card class="order-buy-examination-device-card">
        <oa-card class="order-desc-card">
            <img class="logo" src="@/assets/logo-cloud.png" alt="abc_logo">
            <div class="order-desc">
                <template v-if="isPaid">
                    <p>您已成功支付订单，现已可登陆系统正常使用。如有其它问题，可联系您的销售经理 <span class="seller-name">{{ orderInfo.createdByName }}</span></p>
                </template>
                <template v-else>
                    <p>您好，ABC数字医疗云的销售经理 <span class="seller-name">{{ orderInfo.createdByName }}</span></p>
                    <p>向您发起了一笔待支付订单，请确认后完成支付</p>
                </template>
            </div>
        </oa-card>

        <oa-cell-group label-width="90px" class="order-info-cell">
            <oa-cell label="订单类型">{{ getChargeItemTypeLabel(orderInfo) }}</oa-cell>
            <oa-cell label="购买诊所">
                <span>{{ getClinicDetail(orderInfo) }}</span>
            </oa-cell>
            <oa-cell label="订单金额">
                <span class="money">{{ formatMoney(orderInfo.totalPrice, '￥') }}</span>
            </oa-cell>
            <oa-cell label="应付金额">
                <span class="money">{{ formatMoney(orderInfo.receivableFee, '￥') }}</span>
            </oa-cell>
            <oa-cell label="订单编号">{{ orderInfo.id }}</oa-cell>
        </oa-cell-group>
        <img v-if="statusMarkImg" class="img-status-mark" :src="statusMarkImg">
    </oa-card>
</template>

<style lang="scss">
.order-buy-examination-device-card {
    padding-bottom: 0;
    position: relative;

    .order-desc-card {
        padding: var(--oa-padding-16);
        background-color: #f8f8fa;

        img.logo {
            width: 110px;
            margin-top: 4px;
        }

        .order-desc {
            margin-top: var(--oa-padding-12);

            span.seller-name {
                color: var(--oa-primary-color);
                font-weight: 500;
            }

            p {
                color: var(--oa-text-color-3);
                line-height: 18px;
            }
        }
    }

    .order-info-cell {
        font-size: var(--oa-font-size-14);
        padding: 0 6px;
    }

    .money {
        color: var(--oa-orange);
    }

    .img-status-mark {
        position: absolute;
        right: 0;
        top: 0;
        width: 74px;
    }
}
</style>
