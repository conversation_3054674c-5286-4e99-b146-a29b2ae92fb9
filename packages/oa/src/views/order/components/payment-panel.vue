<script setup lang="ts">
import { computed, reactive } from 'vue';
import { OaCard } from '@abc-oa/components';
import OaCellGroup from '@/components/oa-cell-group.vue';
import OaCell from '@/components/oa-cell.vue';
import wechatIcon from '@/assets/wechat-pay.png';
import publicIcon from '@/assets/public-transfer-pay.png';

const emit = defineEmits(['change', 'click']);
const props = defineProps({
    paymentIdList: {
        type: Array,
        default: () => [1, 3],
    },
    checkedColor: {
        type: String,
        default: '#1989fa',
    },
});
const paymentList = computed(() => {
    const options = [
        { name: '微信支付', icon: wechatIcon, value: 1 },
        // { name: '支付宝', icon: aliIcon, value: 2 },
        { name: '对公转账', icon: publicIcon, value: 3 },
    ];
    if (props.paymentIdList) {
        return options.filter((item) => props.paymentIdList.includes(item.value));
    }
    return options;
});

const vmData = reactive({
    currentPayment: 1,
});

function handlePaymentChange(val: number) {
    emit('change', val);
}

function handlePaymentClick(val: number) {
    vmData.currentPayment = val;
    emit('click', val);
}

</script>
<template>
    <oa-card class="payment-panel-wrapper">
        <van-radio-group v-model="vmData.currentPayment" @change="handlePaymentChange">
            <oa-cell-group>
                <oa-cell v-for="payment in paymentList" @click="handlePaymentClick(payment.value)">
                    <template #label>
                        <img :src="payment.icon" />{{ payment.name }}
                    </template>
                    <van-radio :name="payment.value" :checked-color="checkedColor"></van-radio>
                </oa-cell>
            </oa-cell-group>
        </van-radio-group>
    </oa-card>
</template>

<style lang="scss">
    .payment-panel-wrapper {
        padding-top: 0;
        padding-bottom: 0;

        .oa-cell__label-wrapper {
            display: flex;
            align-items: center;

            img {
                width: 20px;
                margin-right: 8px;
            }
        }

        .oa-cell__value-wrapper {
            margin-left: auto;
        }
    }
</style>
