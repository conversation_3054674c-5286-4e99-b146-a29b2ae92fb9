<script lang="ts" setup>
import SignaturePad from 'signature_pad';

import OSSUtil from '@/utils/oss';
import { nextTick, onMounted, ref, watch } from 'vue';
import { dataURLtoFile } from '@/utils/utils';
import { Toast } from 'vant';

const props = defineProps({
    visible: {
        type: Boolean,
        default: false,
    },
});
const emit = defineEmits(['handleClientSubmit', 'handleCancelClick']);
watch(() => props.visible, async () => {
    await nextTick();
    await createCanvas();
});
const imgLoading = ref(false);
const canvas = ref<HTMLCanvasElement>();
const _signaturePad = ref<any>(null);
onMounted(async () => {
    await nextTick();
    await createCanvas();
});
const createCanvas = async () => {
    document.title = '设置用户签名';

    let $signature: any = document.querySelector('.signature-pad-body');
    if (!canvas.value || !$signature) {
        return;
    }
    _signaturePad.value = new SignaturePad(canvas.value, {
        minWidth: 3.5,
        maxWidth: 8.5,
    });

    (function (doc, win) {
        let docEl = doc.documentElement,
            resizeEvt = 'orientationchange' in window ? 'orientationchange' : 'resize',
            resizeCanvas = function () {
                let width = docEl.clientWidth, height = docEl.clientHeight;
                let $sign: any = document.querySelector('.signature-wrapper');
                // Handling high DPI screens
                let ratio = Math.max(window.devicePixelRatio || 1, 1);

                if (!canvas.value) {
                    return;
                }
                let context: any = canvas.value?.getContext('2d');
                if (height >= width) {
                    // 竖屏
                    $signature.style.width = height - 32 + 'px';
                    $signature.style.height = width - 114 + 'px';
                    canvas.value.width = (height - 32) * ratio;
                    canvas.value.height = (width - 114) * ratio;

                    $sign.style.width = height + 'px';
                    $sign.style.height = width + 'px';
                    $sign.style.top = (height - width) / 2 + 'px';
                    $sign.style.left = (width - height) / 2 + 'px';
                    $sign.style.transform = 'rotate(90deg)';

                    context.rotate(1.5 * Math.PI); // 顺时针旋转90°
                    context.translate(-(canvas.value.height), 0);
                } else {
                    // 横屏
                    $signature.style.width = width - 32 + 'px';
                    $signature.style.height = height - 114 + 'px';
                    canvas.value.width = (width - 32) * ratio;
                    canvas.value.height = (height - 114) * ratio;

                    $sign.style.width = width + 'px';
                    $sign.style.height = height + 'px';
                    $sign.style.top = 0;
                    $sign.style.left = 0;
                    $sign.style.transform = 'none';
                }
                context.scale(ratio, ratio);
                _signaturePad.value.clear(); // otherwise isEmpty() might return incorrect value
            };
        resizeCanvas();
        win.addEventListener(resizeEvt, resizeCanvas, false);
    }(document, window));
};
const clearImg = () => {
    _signaturePad.value.clear();
};

/**
 * @desc 提交自己服务器前，先将文件提交至oss 获取其oss file url
 * <AUTHOR>
 * @date 2018/08/29 15:23:26
 */
const saveSign = async () => {
    const fileUrl = _signaturePad.value.toDataURL();
    const file = dataURLtoFile(fileUrl, '客户签名.png');
    try {
        imgLoading.value = true;

        // 获取服务器oss验证token
        const { url } = await OSSUtil.upload({
            bucket: import.meta.env.VITE_APP_OSS_BUCKET,
            region: import.meta.env.VITE_APP_OSS_REGION,
            rootDir: 'oa/sop/client-sign',
        }, file);

        imgLoading.value = false;

        if (url) {
            emit('handleClientSubmit', url);
            clearImg();
        }
    } catch (err: any) {
        imgLoading.value = false;
        Toast.fail(err.message || err);
    }
};
const handleCancelClick = () => {
    clearImg();
    emit('handleCancelClick');
};
</script>
<template>
    <div class="signature-wrapper">
        <div v-if="imgLoading" class="loading-wrapper">
            <div class="loading-cover"></div>
            <van-loading
                class="loading"
                type="spinner"
                size="48px"
            />
        </div>

        <div id="signature-pad" class="signature-pad">
            <div class="signature-pad-title">
                请绘制清晰可辨的签名，并保存
            </div>

            <div class="signature-pad-body">
                <canvas id="canvas" ref="canvas"></canvas>
            </div>

            <div class="signature-pad-footer">
                <button class="clear" @click="handleCancelClick">返回</button>
                <button class="clear" @click="clearImg">重写</button>
                <button @click="saveSign">保存</button>
            </div>
        </div>
    </div>
</template>

<style rel="stylesheet/scss" lang="scss">
.signature-wrapper {
    width: 100%;
    height: 100%;
    user-select: none;
    margin: 0;
    background-color: #fff;
    transform-origin: "50% 50%";

    .loading-wrapper {
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        position: fixed;
        overflow: auto;
        margin: 0;
        z-index: 1992;

        .loading-cover {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            opacity: .4;
            background: #333;
            z-index: 1992;
        }

        .loading {
            position: absolute;
            width: 48px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1994;
            color: #ececec;
        }
    }
}

.signature-pad {
    position: relative;
    width: 100%;
    height: 100%;
}

.signature-pad-title {
    height: 50px;
    line-height: 50px;
    font-size: 18px;
    color: #2e3439;
    text-align: center;
}

.signature-pad-body {
    position: relative;
    margin: 0 auto;

    canvas {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        border-radius: 4px;
        border: 1px solid #e6eaee;
        box-shadow: 0 0 5px rgba(0, 0, 0, .02) inset;
    }
}

.signature-pad-footer {
    width: 95%;
    margin: 0 auto;
    height: 64px;
    line-height: 64px;
    text-align: right;

    button {
        width: 80px;
        height: 32px;
        line-height: 28px;
        background-color: #1976d2;
        color: #fff;
        font-size: 14px;
        border-radius: 2px;
        border: 1px solid rgba(0, 0, 0, .1);
    }

    .clear {
        color: #2e3439;
        background: -webkit-linear-gradient(top, rgba(242, 244, 247, 1), rgba(255, 255, 255, 1)); /* Safari 5.1 - 6.0 */
        background: -o-linear-gradient(top, rgba(242, 244, 247, 1), rgba(255, 255, 255, 1)); /* Opera 11.1 - 12.0 */
        background: -moz-linear-gradient(top, rgba(242, 244, 247, 1), rgba(255, 255, 255, 1)); /* Firefox 3.6 - 15 */
        background: linear-gradient(to top, rgba(242, 244, 247, 1), rgba(255, 255, 255, 1)); /* 标准的语法 */
    }

    button + button {
        margin-left: 8px;
    }
}

@media screen and (orientation: portrait) {
    html,
    body {
        width: 100%;
        height: 100%;
        overflow: hidden;
    }

    .signature-wrapper {
        position: absolute;
    }
}

@media screen and (orientation: landscape) {
    html,
    body {
        width: 100%;
        height: 100%;
    }

    .signature-wrapper {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }
}

</style>
