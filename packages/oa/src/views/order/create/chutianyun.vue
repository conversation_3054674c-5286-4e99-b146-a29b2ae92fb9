<script lang="ts" setup>
import { ApprovalTicketAPI } from '@/api/approval-ticket-api';
import { sleep } from '@/utils/utils';
import { H5Form } from '@/vendor/x-form';
import { Form } from '@/vendor/x-form/core/form';
import { useOrderStore } from '@/views/order/store';
import { onBeforeUnmount } from 'vue';
import { OrderType } from '../model/model';
import { Toast } from 'vant';
import { useRouter } from 'vue-router';
import { chutianyunFormSchema, createPostDataForChutianyun } from '@/views/order/model/schema';

let formControl: Form;

const router = useRouter();

const orderStore = useOrderStore();

async function handleAction({ action, formData, valid }: any) {
    if (action.name === 'submit') {
        console.log('submit', formData, valid);
        if (!valid) {
            Toast.fail('请完善表单内容');
            return;
        }
        action.loading = true;

        Toast.loading('正在创建单据');

        let createResponse = null;
        let orderType = OrderType.CHUTIANYUN;
        // 新开子店
        try {
            createResponse = await ApprovalTicketAPI.createApprovalTicketCtyClinicUsingPOST(createPostDataForChutianyun(formData));
        } catch (e: any) {
            Toast.fail('创建订单失败-' + e.message);
            action.loading = false;
        }

        if (createResponse) {
            await sleep(2000);
            Toast.success('创建单据成功');
            action.loading = false;
            await router.replace({
                name: '@order/detail',
                params: {
                    id: createResponse.id,
                },
                query: {
                    orderType,
                },
            });
        }
    }
}

onBeforeUnmount(() => {
    if (orderStore.currentFormData) {
        orderStore.clearEditFormData();
    }
});

function handlePrepared(form: Form) {
    formControl = form;
}

function handleChange({ formData }: any) {
    console.log('handleChange', formData);
}
</script>
<template>
    <h5-form
        :schema="chutianyunFormSchema"
        :data="orderStore.currentFormData"
        @action="handleAction"
        @prepared="handlePrepared"
        @change="handleChange"
    ></h5-form>
</template>

<style>

</style>
