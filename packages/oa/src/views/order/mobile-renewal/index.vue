<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue';
import dayjs from 'dayjs';
import { formatEdition, formatMoney } from '@/utils/format';
import { YearOption } from '@/views/order/mobile-renewal/model';
import { Toast } from 'vant';
import { isWx } from '@/utils/ua';
import { useRoute } from 'vue-router';
import { ClinicCustomerRenewAPI } from '@/api/clinic-customer-renew-api';
import ClinicEditionOrderCalculateRspPromotion = AbcAPI.ClinicEditionOrderCalculateRspPromotion;
import ClinicCustomerEditionOrderView = AbcAPI.ClinicCustomerEditionOrderView;
import ClinicEditionOrderCreateReq = AbcAPI.ClinicEditionOrderCreateReq;
import ClinicEditionOrderCalculateReq = AbcAPI.ClinicEditionOrderCalculateReq;
import { editionLabelOptions } from '@/views/order/model/options';

const route = useRoute();
const { chainId = '', clinicId = '', employeeId = '', isApp = 0 } = route.query;

// const isChuTianYun = ref(false);
onMounted(async () => {
    await fetchOrderDetail();
});
const yearOptions = ref<YearOption[]>([
    {
        count: 3,
        discount: 8,
        disabled: false,
    },
    {
        count: 2,
        discount: 9,
        disabled: false,
    },
    {
        count: 1,
        discount: 0,
        disabled: false,
    },
]);
// 格式化产品价格
const formatProductPrice = computed(() => `¥${formData.value.unitPrice || 0}/门店/年`);
// 格式化续费金额
const formatRenewalAmount = computed(() => `¥${formData.value.minReceivableFee || 0} (¥${
    formData.value.unitPrice || 0}每年×${formData.value.unitCount || 0}年${
    formData.value.maxAdjustment?.discount ? `×${formData.value.maxAdjustment.discount}` : ''})`);
// 格式化增购账号
const formatAdditionalPurchaseAccount = computed(() => `${formData.value.accountOrderFeeInfo?.count || 0}个`);
// 格式化增购金额
const formatAdditionalPurchaseAmount = computed(
    () => `¥${formData.value.accountOrderFeeInfo?.totalPrice || 0} (¥${formData.value.accountOrderFeeInfo?.unitPrice || 0}×${
        formData.value.accountOrderFeeInfo?.count || 0}账号×${formData.value.unitCount || 0}年)`,
);
const priceItemOptions = ref({
    productPrice: {
        label: '产品价格',
        value: formatProductPrice,
    },
    renewalAmount: {
        label: '续费金额',
        value: formatRenewalAmount,
    },
    additionalPurchaseAccount: {
        label: '增购账号',
        value: formatAdditionalPurchaseAccount,
    },
    additionalPurchaseAmount: {
        label: '增购金额',
        value: formatAdditionalPurchaseAmount,
    },
});
const redEnvelopOptions = ref<ClinicEditionOrderCalculateRspPromotion[]>([]);

const formData = ref<any>({
    clinicName: undefined,
    editionId: undefined,
    beginDate: undefined,
    endDate: undefined,
    leftDay: undefined,
    unitCount: 3,
    promotionIds: [],
});
const calculateData = ref<AbcAPI.ClinicCustomerEditionOrderCalculateRsp>({
    id: '',
    maxAdjustmentFee: 0,
    maxAdjustment: undefined, // 折扣信息
    prePaidFee: 0,
    type: 0,
    unit: '',
    availablePromotions: undefined, // 可选优惠券id
    editionId: '', // 版本id
    unitPrice: 0, // 单价
    unitCount: 0, // 年数
    totalPrice: 0, // 原始总金额
    deductionFee: 0, // 抵扣金额
    discountPrice: 0, // 折扣金额
    receivableFee: 0, // 应收金额
    minReceivableFee: 0, // 折扣后应收金额
    endDate: '', // 到期时间
    beginDate: '', // 开始时间
    accountOrderFeeInfo: undefined, // 账号费用信息
    editionOrderFeeInfo: undefined, // 版本费用信息
    basicEmployeeCount: 0, // 版本基础用户数
    accountDeductionFee: 0, // 账号抵扣信息
    isCanReBuyWithHistoryPrice: 0, // 是否可以按历史价格续费 2023年2月1日前购买的门店并且未使用一次原价续费机会
    // 版本费用抵扣金额
    editionDeductionFee: 0,
    editionOrderTotalPrice: 0, // 版本总费用
    // 赠送天数
    giftDays: 0,
    maxBeginDate: '',
    maxEndDate: '',
});
/**
 * @description: 创建数据结构
 * @date: 2023-03-14 17:22:41
 * @author: Horace
 * @param null:
 * @return
*/
const createPostData = () => {
    const postData = {
        // 开始时间
        beginDate: formData.value.beginDate,
        // 新建门店算费时，门店属性，PC端无效
        bindClinic: {},
        // 子店自己续费时可不填，总部续费子店时需要填子店id
        bindClinicId: '',
        buyNewStaffSopSheet: {},
        // 购买店版本id
        editionId: formData.value.editionId,
        // 结束时间
        endDate: formData.value.endDate,
        // 订单id
        id: '',
        isSopRenewPurchase: 0,
        // 是否试用，PC端无效
        isTrial: 0,
        // 使用优惠券id列表
        promotionIds: formData.value.promotionIds,
        // 来源
        source: isWx ? 6 : isApp ? 5 : 7,
        // 数量
        unitCount: formData.value.unitCount,
    };

    return postData;
};
const handleCalculateResult = (data: any) => {
    const {
        id,
        availablePromotions, // 可选优惠券id
        editionId, // 版本id
        unitPrice, // 单价
        unitCount, // 年数
        totalPrice, // 原始总金额
        receivableFee, // 应收金额
        minReceivableFee, // 折扣后应收金额
        endDate, // 到期时间
        beginDate, // 开始时间
        accountOrderFeeInfo, // 账号费用信息
        maxAdjustment, // 折扣信息
    } = data;

    formData.value.leftDay = dayjs(beginDate).diff(dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss'), 'day');
    formData.value.id = id;
    // 检查版本
    formData.value.clinicName = calculateData.value!.clinic!.name;
    formData.value.editionId = editionId;
    formData.value.endDate = endDate;
    formData.value.beginDate = beginDate;
    // 检查优惠券
    formData.value.promotionIds = [];
    availablePromotions?.forEach((item: any) => {
        if (item.isUsed === 1) {
            formData.value.promotionIds.push(item.id);
        }
    });
    redEnvelopOptions.value = availablePromotions || [];
    // 金额信息相关
    formData.value.unitPrice = unitPrice;
    formData.value.unitCount = unitCount;
    formData.value.totalPrice = totalPrice;
    formData.value.receivableFee = receivableFee;
    formData.value.minReceivableFee = minReceivableFee || receivableFee;
    formData.value.accountOrderFeeInfo = accountOrderFeeInfo;
    formData.value.maxAdjustment = maxAdjustment;
};
/**
 * @description: 获取续费诊所详情
 * @date: 2023-03-09 16:54:27
 * @author: Horace
 * @param null:
 * @return
*/
const fetchOrderDetail = async () => {
    Toast.loading({
        message: '加载中...',
        forbidClick: true,
    });
    const reqBody = createPostData();
    let res: any = {};
    try {
        res = await ClinicCustomerRenewAPI.calculateClinicEditionOrderUsingPOST_1(
            <string>chainId, <string>clinicId, <string>employeeId, <ClinicEditionOrderCalculateReq>reqBody,
        );
    } catch (e: any) {
        Toast.fail(e.message || e);
    }
    calculateData.value = res;
    // isChuTianYun.value = !!res.clinic?.isChuTianYunOrgan;
    handleCalculateResult(calculateData.value);
    Toast.clear();
};
/**
 * @description: 创建续费支付订单
 * @date: 2023-03-15 10:38:58
 * @author: Horace
 * @param null:
 * @return
*/
const createClinicEditionOrder = async () => {
    let res: any = {};
    const reqBody = waitingPaidOrder.value || createPostData();
    try {
        res = await ClinicCustomerRenewAPI.createClinicEditionOrderUsingPOST_1(
            <string>chainId, <string>clinicId, <string>employeeId, <ClinicEditionOrderCreateReq>reqBody,
        );
    } catch (e: any) {
        Toast.fail(e.message || e);
    }
    return res;
};
/**
 * @description: 立即支付按钮点击
 * @date: 2023-03-09 16:59:21
 * @author: Horace
 * @param null:
 * @return
*/
const handleFormSubmit = async () => {
    // 没有待支付订单信息查询是否有待支付订单
    if (!waitingPaidOrder.value) {
        await getWaitingPaidOrder();
    }
    // 有待支付订单，展开弹窗
    if (hasWaitingPaidOrder.value) {
        return;
    }
    await handlePay();
};
const handlePay = async () => {
    const orderInfo = waitingPaidOrder.value || await createClinicEditionOrder();
    if (orderInfo.id) {
        const shareLink = `${import.meta.env.VITE_APP_SHARE_DOMAIN}/share/order-mobile-renewal-detail/${
            orderInfo.id}?chainId=${chainId}&clinicId=${clinicId}&employeeId=${employeeId}`;
        window.location.href = shareLink;
    }
};
const hasWaitingPaidOrder = ref(false);
const waitingPaidOrder = ref<ClinicCustomerEditionOrderView | unknown>(null);
/**
 * @description: 查询当前诊所是否有待支付订单
 * @date: 2023-03-15 10:18:11
 * @author: Horace
 * @param null:
 * @return
*/
const getWaitingPaidOrder = async () => {
    let res: any = {};
    try {
        res = await ClinicCustomerRenewAPI.getWaitingPaidClinicEditionOrderUsingGET(<string>chainId, <string>clinicId);
    } catch (e: any) {
        Toast.fail(e.message || e);
    }
    waitingPaidOrder.value = res.id ? res : null;
    hasWaitingPaidOrder.value = !!res.id;
    hasWaitingPaidOrder.value && handleCalculateResult(res);
};
const handleWaitPayView = () => {
    hasWaitingPaidOrder.value = false;
    handlePay();
};
/**
 * @description: 续费年数卡片选择
 * @date: 2023-03-09 16:55:21
 * @author: Horace
 * @param {YearOption} option: 选择的年数配置
 * @return
*/
const handleCardClick = (option: YearOption) => {
    formData.value.unitCount = option.disabled ? formData.value.unitCount : option.count;
    fetchOrderDetail();
};
const showVoucherPopover = ref(false);
/**
 * @description: 确认红包选择
 * @date: 2023-03-15 15:53:09
 * @author: Horace
 * @param null:
 * @return
*/
const handleRedEnvelopSubmit = () => {
    showVoucherPopover.value = false;
    fetchOrderDetail();
};
/**
 * @description: 红包选择
 * @date: 2023-03-15 15:53:02
 * @author: Horace
 * @param option: 选择的红包
 * @return
*/
const handleRedEnvelopClick = (option: any) => {
    formData.value.promotionIds = formData.value.promotionIds || [];
    if (formData.value.promotionIds.includes(option.id)) {
        formData.value.promotionIds = formData.value.promotionIds.filter((item: string) => item !== option.id);
    } else {
        formData.value.promotionIds.push(option.id);
    }
};
// 当前选择的红包总金额
const currentRedEnvelopPrice = computed(() => {
    let count = 0;
    formData.value.promotionIds?.forEach((redEnvelop: string) => {
        count += redEnvelopOptions.value.find((item) => item.id === redEnvelop)?.amount || 0;
    });
    return -count;
});
const editionTagClass = (editionId: string) => {
    if (editionLabelOptions.basicVersion.values.includes(editionId)) {
        return 'basic-version';
    }
    if (editionLabelOptions.professionalEdition.values.includes(editionId)) {
        return 'professional-version';
    }
    if (editionLabelOptions.standardEdition.values.includes(editionId)) {
        return 'standard-version';
    }
    if (editionLabelOptions.ultimate.values.includes(editionId)) {
        return 'ultimate-version';
    }
    if (editionLabelOptions.bigCustomerVersion.values.includes(editionId)) {
        return 'bigCustomer-version';
    }
    return '';
};

</script>
<template>
    <!--    <div v-if="isChuTianYun" class="order-mobile-renewal-chu-tian-yun-wrapper">-->
    <!--        <van-icon size="54" color="#ff9a33" name="warning" />-->
    <!--        <p>请联系客户经理</p>-->
    <!--        <p> 或拨打 ************ 转 1 完成续费</p>-->
    <!--    </div>-->
    <!--    <template>-->
    <div class="order-mobile-renewal-share-wrapper">
        <div class="order-mobile-renewal-share-content">
            <div class="content-item__wrapper">
                <span class="content-item__title">续费门店</span>
                <div class="content-item__content">
                    <span class="content-item__content-text"> {{ formData.clinicName }}</span>
                    <span
                        class="content-item__content-tag clinic-edition-tag"
                        :class="editionTagClass(formData.editionId)"
                    > {{ formatEdition(formData.editionId) }}</span>
                </div>
            </div>
            <div class="content-item__wrapper">
                <span class="content-item__title">到期时间</span>
                <div class="content-item__content">
                    <span class="content-item__content-text"> {{ dayjs(formData.beginDate).format('YYYY-MM-DD') }}</span>
                    <span class="content-item__content-text clinic-left-day"> {{ `（剩余${formData.leftDay || 0}天）` }}</span>
                </div>
            </div>
            <div class="content-item__wrapper">
                <span class="content-item__title">续费年数</span>
                <div class="content-item__content content-item__content__card-wrapper">
                    <div
                        v-for="option in yearOptions"
                        :key="option.count"
                        class="content-item__card"
                        :class="{'is-active': formData.unitCount === option.count, 'is-disabled': option.disabled}"
                        @click="handleCardClick(option)"
                    >
                        <span v-if="option.discount" class="content-item__card-icon">限时{{ option.discount }}折</span>
                        {{ option.count }} 年
                    </div>
                </div>
            </div>
            <div class="content-item__wrapper">
                <span class="content-item__title">续费后到期时间</span>
                <div class="content-item__content">{{ formData.endDate }}</div>
            </div>
        </div>
        <div class="order-mobile-renewal-share-price-list">
            <p
                v-for="(option) in priceItemOptions"
                :key="option.value"
                class="price-list-item"
            >
                <span class="price-list-item__label">{{ option.label }}</span>
                <span class="price-list-item__content">{{ option.value }}</span>
            </p>
            <p class="price-list-item" @click="showVoucherPopover = !!redEnvelopOptions?.length">
                <span class="price-list-item__label">代金券</span>
                <span v-if="!!redEnvelopOptions?.length && currentRedEnvelopPrice" class="price-list-item__content red-envelop-price">
                    -¥{{ currentRedEnvelopPrice }}
                    <van-icon size="14" color="#AAABB3" name="arrow" />
                </span>
                <span v-else-if="!!redEnvelopOptions?.length" class="price-list-item__content red-envelop-price is-disabled">
                    未选择
                    <van-icon size="14" color="#AAABB3" name="arrow" />
                </span>
                <span v-else class="price-list-item__content red-envelop-price is-disabled">
                    无可用优惠券
                </span>
            </p>
            <p class="price-list-item amounts-payable">
                <span class="price-list-item__label">应付金额</span>
                <span class="price-list-item__content">
                    <span class="price-list-item__content-icon">¥</span>
                    <span class="price-list-item__content-price">{{ formatMoney(formData.minReceivableFee) }}</span>
                </span>
            </p>
        </div>
        <div class="order-mobile-renewal-share-footer abc-share-footer">
            <span>
                应付金额：
                <span class="abc-share-footer-icon">¥</span>
                <span class="abc-share-footer-price">{{ formatMoney(formData.minReceivableFee) }}</span>
            </span>
            <van-button class="abc-share-footer-button" @click="handleFormSubmit">立即支付</van-button>
        </div>
    </div>
    <van-action-sheet v-model:show="showVoucherPopover" class="voucher-popover-wrapper" title="代金券">
        <div class="voucher-popover-content">
            <p class="voucher-popover-content__title">可使用红包</p>
            <van-checkbox-group v-model="formData.promotionIds">
                <div
                    v-for="option in redEnvelopOptions"
                    :key="option.id"
                    class="voucher-popover-content__item"
                    @click="handleRedEnvelopClick(option)"
                >
                    <div class="voucher-popover-content__item-red-envelop">
                        <p><span class="item-red-envelop-icon">¥</span><span class="item-red-envelop-price">{{ - option.amount }}</span></p>
                        <span class="item-red-envelop-time">
                            {{ `有效期截止到 ${ dayjs(option.expiredTime).format('YYYY-MM-DD')}` }}
                        </span>
                    </div>
                    <van-checkbox :name="option.id" checked-color="#08BB88" @click.stop></van-checkbox>
                </div>
            </van-checkbox-group>
        </div>
        <div class="voucher-popover-footer abc-share-footer">
            <span>
                已选{{ formData.promotionIds?.length || 0 }}张， 可减
                <span class="abc-share-footer-icon">¥</span>
                <span class="abc-share-footer-price">{{ currentRedEnvelopPrice }}</span>
            </span>
            <van-button class="abc-share-footer-button" @click="handleRedEnvelopSubmit">确定</van-button>
        </div>
    </van-action-sheet>
    <van-popup v-model:show="hasWaitingPaidOrder" class="waiting-paid-order-popup-wrapper">
        <div class="waiting-paid-order-popup-content">
            <img src="@/assets/<EMAIL>" alt="订单提示" />
            <p class="waiting-paid-order-popup-title">订单提示</p>
            <p class="waiting-paid-order-popup-text">您已有待支付订单，请优先处理</p>
        </div>
        <div class="waiting-paid-order-popup-footer" @click="handleWaitPayView">
            <span>查看订单</span>
        </div>
    </van-popup>
<!--    </template>-->
</template>
<style lang="scss">
.order-mobile-renewal-chu-tian-yun-wrapper {
    text-align: center;
    margin-top: 24vh;

    & > .van-icon {
        margin-bottom: 24px;
    }

    & > p {
        font-weight: bold;
        font-size: 18px;
        color: #333;
        text-align: center;
        line-height: 24px;
    }
}

.order-mobile-renewal-share-wrapper {
    padding: 14px 14px 15vh;
    height: 100vh;
    overflow-y: auto;

    .order-mobile-renewal-share-content {
        width: 100%;
        background: #fff;
        border-radius: 6px;
        padding: 0 16px;
        margin-bottom: 18px;

        .content-item__wrapper {
            padding: 16px 0;

            .content-item__title {
                font-size: 16px;
                color: #aaabb3;
            }

            .content-item__content {
                margin-top: 4px;
                font-size: 16px;
                color: #333;

                .clinic-edition-tag {
                    margin-left: 4px;
                    background-image: linear-gradient(180deg, #67de8d 0%, #13b97a 100%);
                    border-radius: 12px;
                    color: #fff;
                    font-size: 11px;
                    padding: 4px;
                    font-weight: 500;

                    &.basic-version {
                        background-image: linear-gradient(180deg, #67de8d 0%, #13b97a 100%);
                    }

                    &.professional-version {
                        background-image: linear-gradient(180deg, #ffdd76 0%, #ffaf02 100%);
                    }

                    &.standard-version {
                        background-image: linear-gradient(180deg, #62b9ff 0%, #296def 100%);
                    }

                    &.ultimate-version {
                        background-image: linear-gradient(180deg, #9f86ff 0%, #5029ef 100%);
                    }

                    &.bigCustomer-version {
                        background-image: linear-gradient(180deg, #ff7c90 0%, #f30505 100%);
                    }
                }

                .clinic-left-day {
                    color: #08bb88;
                }
            }

            .content-item__content__card-wrapper {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .content-item__card + .content-item__card {
                    margin-left: 8px;
                }

                .content-item__card {
                    flex: 0 0 calc((100% - 16px) / 3);
                    height: 58px;
                    background: #fff;
                    border: 1px solid #f0f0f0;
                    border-radius: 6px;
                    position: relative;
                    text-align: center;
                    line-height: 58px;

                    &.is-active {
                        background: rgba(8, 187, 136, .08);
                        border: 1px solid #08bb88;
                        border-radius: 6px;
                        color: #08bb88;
                    }

                    &.is-disabled {
                        background: rgba(116, 128, 128, .08);
                        border: 1px solid 	#748080;
                        border-radius: 6px;
                        cursor: no-drop;
                        color: #748080;
                    }

                    .content-item__card-icon {
                        display: block;
                        position: absolute;
                        top: -4px;
                        right: -10px;
                        padding: 4px;
                        font-size: 12px;
                        transform: scale(.8);
                        line-height: 10px;
                        color: #fff;
                        background-image: linear-gradient(90deg, #f55 0%, #ff7c3f 50%);
                        border-radius: 100px 50px 0 100px;

                        &::after {
                            content: "";
                            position: absolute;
                            bottom: -6px;
                            right: 0;
                            background: #fff;
                            width: 0;
                            height: 0;
                            border-top: #cf6330 solid;
                            border-right: transparent solid;
                            border-width: 6px;
                        }
                    }
                }
            }
        }

        .content-item__wrapper + .content-item__wrapper {
            border-top: 1px solid #f0f0f0;
        }
    }

    .order-mobile-renewal-share-price-list {
        width: 100%;
        background: #fff;
        border-radius: 6px;
        padding: 16px 16px 0;

        .price-list-item + .price-list-item {
            padding-top: 16px;
        }

        .price-list-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 16px;
            color: #777;

            .red-envelop-price {
                color: #f33;
                font-size: 16px;
                line-height: 22px;

                &.is-disabled {
                    color: #ccc;
                }
            }
        }

        .price-list-item.amounts-payable {
            padding: 24px 0;
            margin-top: 16px;
            border-top: 1px solid #f0f0f0;

            .price-list-item__label,
            .price-list-item__content {
                font-weight: bold;
                font-size: 18px;
                line-height: 18px;
                color: #333;
            }

            .price-list-item__content {
                .price-list-item__content-icon {
                    font-size: 14px;
                }

                .price-list-item__content-price {
                    font-size: 20px;
                }
            }
        }
    }

    .order-mobile-renewal-share-footer.abc-share-footer {
        position: fixed;
        bottom: 0;
        left: 0;
        box-shadow: 0 0 0 0 #f0f0f0;

        .abc-share-footer-icon {
            line-height: 20px;
        }

        .abc-share-footer-price {
            font-size: 18px;
            line-height: 20px;
        }
    }
}

.abc-share-footer {
    width: 100%;
    padding: 16px 12px;
    background: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &-icon {
        font-size: 12px;
        font-weight: bold;
        line-height: 16px;
        color: #08bb88;
    }

    &-price {
        font-size: 16px;
        font-weight: bold;
        line-height: 16px;
        color: #08bb88;
    }

    &-button.van-button {
        width: 98px;
        height: 44px;
        font-size: 16px;
        background: #08bb88;
        border-radius: 4px;
        color: #fff;
        border: none;
    }
}

.voucher-popover-wrapper {
    .voucher-popover-content {
        padding: 16px 20px 32px;
        border-top: 1px solid #f0f0f0;

        .voucher-popover-content__title {
            color: #333;
        }

        .voucher-popover-content__item {
            display: flex;
            justify-content: space-between;
            margin-top: 12px;

            .voucher-popover-content__item-red-envelop {
                width: 301px;
                height: 77px;
                border-radius: 5px;
                color: #ea5d59;
                background: #ffefef url('assets/<EMAIL>') no-repeat;
                background-size: 50px 50px;
                padding: 16px;

                .item-red-envelop-icon {
                    font-size: 14px;
                    line-height: 26px;
                }

                .item-red-envelop-price {
                    font-size: 26px;
                    line-height: 26px;
                }
            }

            .van-checkbox__icon--checked {
                .van-icon {
                    color: #fff !important;
                    border: none;
                }
            }
        }

        .voucher-popover-content__item + .voucher-popover-content__item {
            margin-top: 12px;
        }
    }

    .voucher-popover-footer.abc-share-footer {
        border-top: 1px solid #f0f0f0;

        .abc-share-footer-icon {
            line-height: 16px;
        }

        .abc-share-footer-price {
            font-size: 16px;
            line-height: 16px;
        }

        .abc-share-footer-button {
            border: none;
        }
    }
}

.waiting-paid-order-popup-wrapper {
    width: calc(100% - 48px);
    border-radius: 4px;

    .waiting-paid-order-popup-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 32px 0;

        > img {
            width: 42px;
        }

        .waiting-paid-order-popup-title {
            font-weight: 500;
            font-size: 18px;
            color: #000;
            line-height: 28px;
            margin: 16px 0;
        }

        .waiting-paid-order-popup-text {
            font-size: 16px;
            color: #777;
            line-height: 24px;
        }
    }

    .waiting-paid-order-popup-footer {
        width: 100%;
        height: 44px;
        background: #fff;
        border-radius: 0 0 4px 4px;
        text-align: center;
        line-height: 44px;
        border-top: 1px solid #e6eaee;

        span {
            font-weight: 500;
            font-size: 16px;
            color: #08bb88;
            line-height: 16px;
        }
    }
}
</style>
