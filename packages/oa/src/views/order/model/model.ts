// 订单类型
import { ClinicNodeType, HisType } from '@/utils/clinic';
import { editionLabelOptions } from '@/views/order/model/options';

export enum OrderType {
    // 新建连锁总部
    CREATE_CHAIN = 1,
    // 新建子店
    CREATE_CLINIC = 2,
    // 续费
    RENEWAL = 3,
    // 升级
    UPGRADE = 4,
    // 其他收费
    OTHER = 5,
    // 修改版本时长
    MODIFY_PERIOD = 6,
    // 楚天云新购
    CHUTIANYUN = 7,
    // 发放优惠券
    COUPON = 8,
    // 连锁子店转单店
    CHAIN_SUB_TO_SINGLE = 9,
    // 版本降级
    DOWNGRADE = 10,
    // 账号增购
    ADDITIONAL_ACCOUNT = 11,
    // 检验设备采购
    BUY_EXAMINATION_DEVICE = 12,
    // 试用单店
    APPLY_TRIAL = 13,
    // 试用单店转正
    TRIAL_TO_NORMALIZE = 14,
    // ABC云检版订单
    CLOUD_EXAM = 25,
}
// 医保对接费用
export const DOCKING_PRICE = 1000;

// 订单状态
export enum OrderStatus {
    // 待付款
    WAITING_PAY = 11,
    // 待审批
    WAITING_AUDIT = 12,
    // 执行中
    EXECUTING = 13,
    // 已完成
    DONE = 90,
    // 已取消
    CANCELED = 99
}

// 支付方式
export enum PayMode {
    // 微信扫码
    WECHAT_SCAN = 1,
    // 支付宝扫码
    ALIPAY_SCAN = 2,
    // 微信内支付
    WECHAT_PAY = 3,
    // 微信 H5 支付
    WECHAT_H5 = 4,
    // 支付宝 H5 支付
    ALIPAY_H5 = 5,
    // 对公转账
    PUBLIC_TRANSFER = 100
}

// 其他收费单类型
export enum OtherOrderType {
    CUSTOMIZE = -1, // 废弃
    // 医保代办
    SOCIAL_SECURITY = 1,
    // 自助服务机
    SELF_SERVICE_TERMINAL = 2,
    // 开放平台接口费
    OPEN_PLATFORM = 3,
    // 专网前置机
    ROUTER = 4,
    // 医院项目费用
    HOSPITAL = 5,
    // 医保对接费用
    DOCKING= 6,
    // 药诊合作诊所增购
    MEDICAL_DIAGNOSIS = 7,
    // LIS 设备对接费
    LIS = 8,
    // PACS 设备对接费
    PACS = 9,
    // 数据迁移费用
    DATA_MIGRATION = 10,
    // 短信充值费用
    SMS = 11,
    // 追溯码高拍仪
    RETRIEVAL_SCANNER = 12,
    // 其他费用
    OTHER = 13,
}

// 版本状态
export enum EditionStatus {
    UN_PURCHASED = 0, // 未购买
    UN_EFFEVTIVE = 10, // 购买未到启用时间，未生效
    IN_EFFEVTIVE = 20, // 有效期内
    EXPIRED = 30, // 已过期
}

// sop订单状态
export enum SopOrderStatus {
    NORMAL = 0, // 初始状态
    SUBMITTED = 1, // 已提及
    SAVED = 2, // 已保存
}

export interface ClinicOrderSummary {
    id: string;
    chainId: string;
    clinicName: string;
    statusName: string;
    status: number;
    editionId: string;
    unitCount: number;
    created: string;
    createdByName: string;
    receivableFee: number;
    title: string;
    orderType: OrderType.CREATE_CLINIC;
}

export interface ChainOrderSummary {
    id: string;
    clinicName: string;
    crmOrganId?: string;
    statusName: string;
    status: number;
    editionId: string;
    unitCount: number;
    created: string;
    createdByName: string;
    receivableFee: number;
    title: string;
    share?: string;
    orderType: OrderType.CREATE_CHAIN;
}

export interface ChutianyunOrderSummary {
    id: string;
    clinicName: string;
    statusName: string;
    status: number;
    editionId: string;
    unitCount: number;
    created: string;
    createdByName: string;
    receivableFee: number;
    title: string;
    orderType: OrderType.CHUTIANYUN;
}

export interface ModifyPeriodOrderSummary {
    id: string;
    title: string;
    statusName: string;
    status: number;
    created: string;
    createdByName: string;
    beginDate: string;
    endDate: string;
    orderType: OrderType.MODIFY_PERIOD;
}

export interface OtherOrderSummary {
    id: string;
    clinicName: string;
    title: string;
    statusName: string;
    status: number;
    editionId: string;
    unitCount: number;
    created: string;
    createdByName: string;
    receivableFee: number;
    type: OtherOrderType;
    name: string;
    orderType: OrderType.OTHER;
}

export interface RenewalOrderSummary {
    id: string;
    clinicName: string;
    title: string;
    statusName: string;
    status: number;
    editionId: string;
    unitCount: number;
    created: string;
    createdByName: string;
    receivableFee: number;
    orderType: OrderType.RENEWAL;
}

export interface UpgradeOrderSummary {
    id: string;
    clinicName: string;
    title: string;
    statusName: string;
    status: number;
    editionId: string;
    unitCount: number;
    created: string;
    createdByName: string;
    receivableFee: number;
    orderType: OrderType.UPGRADE;
}

export interface DowngradeOrderSummary {
    id: string;
    clinicName: string;
    title: string;
    statusName: string;
    status: number;
    editionId: string;
    unitCount: number;
    created: string;
    createdByName: string;
    receivableFee: number;
    orderType: OrderType.DOWNGRADE;
}

export interface CouponOrderSummary {
    id: string;
    clinicName: string;
    title: string;
    statusName: string;
    status: number;
    created: string;
    createdByName: string;
    promotionAmount: number;
    orderType: OrderType.COUPON,
}

export interface ChainSubToSingleSummary {
    id: string;
    clinicName: string;
    title: string;
    statusName: string;
    status: number;
    created: string;
    createdByName: string;
    orderType: OrderType.CHAIN_SUB_TO_SINGLE;
    remark: string;
}

export interface AdditionalAccountSummary {
    id: string;
    clinicName: string;
    title: string;
    statusName: string;
    status: number;
    editionId: string;
    count: number;
    created: string;
    createdByName: string;
    receivableFee: number;
    orderType: OrderType.ADDITIONAL_ACCOUNT;
}
export interface TrialToNormalizeSummary {
    id: string;
    chainId: string;
    clinicName: string;
    statusName: string;
    status: number;
    editionId: string;
    unitCount: number;
    created: string;
    createdByName: string;
    receivableFee: number;
    title: string;
    orderType: OrderType.TRIAL_TO_NORMALIZE;
}

export interface BuyExaminationDeviceSummary {
    id: string;
    clinicName: string;
    title: string;
    statusName: string;
    status: number;
    editionId: string;
    count: number;
    created: string;
    createdByName: string;
    // 使用abc系统的下单诊所id
    clinicId?:string
    // 下单诊所名称
    // 采购设备详情:收费项目为自选设备时填写
    deviceDetail?:string
    // 下单人id: 非必填
    employeeId?:string
    // 下单人姓名
    employeeName?:string
    // 是否使用abc系统: 0不使用 1使用
    isUseAbc?:number
    // 下单人手机号
    mobile?:string
    // 应收金额: 预提交时前端可以不填
    receivableFee?:number
    // 收款账户id (字节流 1, 字节星球 2)
    receiveAccountId?:number
    // 备注
    remark?:string
    // 合计费用
    totalPrice?:number
    // 10基础套餐20专业套餐30旗舰套餐40自选设备
    type?:number
    orderType: OrderType.BUY_EXAMINATION_DEVICE;
}

export interface ApplyTrialSummary {
    id: string;
    chainId: string;
    clinicName: string;
    statusName: string;
    status: number;
    editionId: string;
    unitCount: number;
    created: string;
    createdByName: string;
    receivableFee: number;
    title: string;
    orderType: OrderType.APPLY_TRIAL;
}

export interface SopQuery {
    keyword: string,
    status: number,
    page: number,
    pageSize: number,
}

export const showDocking = (formData: any, needDocking = false) => {
    const isNotChain = formData.clinicType !== ClinicNodeType.CHAIN_ADMIN;
    const isEditionBasic = ![
        ...editionLabelOptions.bigCustomerVersion.values,
    ].includes(formData.editionId);
    const isOphthalmologyUltimate = formData.hisType === HisType.OPHTHALMOLOGY
        && editionLabelOptions.ultimate.values.includes(formData.editionId);
    const isUnitCountOne = formData.unitCount === 1;
    const isPharmacy = formData.hisType === HisType.PHARMACY;
    const isDocking = formData.docking === 1;
    const result = isNotChain && isEditionBasic && isUnitCountOne && !isPharmacy && !isOphthalmologyUltimate;
    return false;
    // return needDocking ? isDocking && result : result;
};

export enum OrderErrorCodeEnum {
    // 对公转账失败
    PUBLIC_TRANSFER_FAIL = 10658,
}