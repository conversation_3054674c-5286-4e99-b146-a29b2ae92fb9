import { ApprovalTicketAPI } from '@/api/approval-ticket-api';
import { HisType, ClinicNodeType, getHisTypeDisplayName, PayAccount } from '@/utils/clinic';
import { formatEdition } from '@/utils/format';
import { validateEndDate, validateMobilePhone } from '@/utils/validate';
import { Field } from '@/vendor/x-form/core/field';
import { Form } from '@/vendor/x-form/core/form';
import { IValidateRule, NodeTypeFilter } from '@/vendor/x-form/types/model';
import { editionLabelOptions, getEditionOptions } from '@/views/order/model/options';
import { DOCKING_PRICE, EditionStatus, OrderStatus, OtherOrderType, showDocking } from '@/views/order/model/model';
import { useOrderStore } from '@/views/order/store';
import dayjs from 'dayjs';
import { generateFeeSchema } from '@/views/order/model/schema/common';
import { SopSheetStaffAPI } from '@/api/sop-sheet-staff-api';
import { EditionAPI } from '@/api/edition-api';
import { CrmOrganAPI } from '@/api/crm-organ-api';
import { ElMessage } from 'element-plus/es';
import MinUpgradeYearsFunction from '@/vendor/cloud-function/functions/min-upgrade-years';
import SupportItem = AbcAPI.SupportItem;
import { Toast } from 'vant';
import { chargeItemType } from '@/utils/examination-device';
import { createPostDataForPreSubmit } from '@/views/order/model/schema/buy-examination-device';

export function createPostDataForChain(formData: any): AbcAPI.CreateChainContentRequest {
    return {
        hisType: formData.hisType,
        name: formData.chainName,
        addressRegion: formData.chainAddress,
        admin: {
            name: formData.adminName,
            mobile: formData.adminMobile,
        },
        remark: formData.remark,
    };
}

export function createPostDataForSingle(formData: any): AbcAPI.CreateClinicContentRequest {
    const supportItems:SupportItem[] = [];
    // formData.docking === 1 && supportItems.push({
    supportItems.push({
        key: 'shebao',
        name: 'shebao',
    });
    return {
        hisType: formData.hisType,
        name: formData.clinicName,
        addressRegion: formData.clinicAddress,
        admin: {
            name: formData.adminName,
            mobile: formData.adminMobile,
        },
        editionId: formData.editionId,
        // 生效开始时间 (yyyy-MM-dd)
        beginDate: formData.beginDate,
        // 生效结束时间 (yyyy-MM-dd)
        endDate: formData.endDate,
        // 合计费用
        totalPrice: formData.totalPrice,
        // 应收费用
        receivableFee: formData.receivableFee,
        // 收款账户id (字节流 1, 字节星球 2)
        receiveAccountId: 1,

        // 单位数量 (N年)
        unitCount: formData.unitCount,
        remark: formData.remark,
        crmOrganId: formData.crmOrganId,
        crmOrganName: formData.crmOrganName,
        payAccount: formData.payAccount,
        supportItems,
        // share: formData.share,
    };
}

export function createPostDataForSub(formData: any): AbcAPI.CreateClinicContentRequest {
    const supportItems:SupportItem[] = [];
    // formData.docking === 1 && supportItems.push({
    supportItems.push({
        key: 'shebao',
        name: 'shebao',
    });
    return {
        hisType: formData.hisType,
        name: formData.clinicName,
        addressRegion: formData.clinicAddress,
        admin: {
            name: formData.adminName,
            mobile: formData.adminMobile,
        },
        editionId: formData.editionId,
        // 生效开始时间 (yyyy-MM-dd)
        beginDate: formData.beginDate,
        // 生效结束时间 (yyyy-MM-dd)
        endDate: formData.endDate,
        // 连锁id
        chainId: formData.chainId,
        // 合计费用
        totalPrice: formData.totalPrice,
        // 应收费用
        receivableFee: formData.receivableFee,
        // 收款账户id (字节流 1, 字节星球 2)
        receiveAccountId: 1,

        // 单位数量 (N年)
        unitCount: formData.unitCount,
        remark: formData.remark,
        crmOrganId: formData.crmOrganId,
        crmOrganName: formData.crmOrganName,
        payAccount: formData.payAccount,
        supportItems,
    };
}
export function createPostDataForTrialToNormalize(formData: any): AbcAPI.RenewClinicContentRequest {
    const supportItems:SupportItem[] = [];
    // formData.docking === 1 && supportItems.push({
    supportItems.push({
        key: 'shebao',
        name: 'shebao',
    });
    return {
        editionId: formData.editionId,
        // 生效开始时间 (yyyy-MM-dd)
        beginDate: formData.beginDate,
        // 生效结束时间 (yyyy-MM-dd)
        endDate: formData.endDate,
        // 门店id
        clinicId: formData.clinicId,
        // 合计费用
        totalPrice: formData.totalPrice,
        // 应收费用
        receivableFee: formData.receivableFee,
        // 诊所类型
        hisType: formData.hisType,
        // 收款账户id (字节流 1, 字节星球 2)
        receiveAccountId: 1,

        // 单位数量 (N年)
        unitCount: formData.unitCount,
        remark: formData.remark,
        crmOrganId: formData.crmOrganId,
        crmOrganName: formData.crmOrganName,
        supportItems,
        // share: formData.share,
    };
}

export function createPostDataForChutianyun(formData: any): AbcAPI.CtyClinicContentRequest {
    const supportItems:SupportItem[] = [];
    // formData.docking === 1 && supportItems.push({
    supportItems.push({
        key: 'shebao',
        name: 'shebao',
    });
    return {
        editionId: formData.editionId,
        // 生效开始时间 (yyyy-MM-dd)
        beginDate: formData.beginDate,
        // 生效结束时间 (yyyy-MM-dd)
        endDate: formData.endDate,
        // 连锁id
        clinicId: formData.clinicId,
        // 合计费用
        totalPrice: formData.totalPrice,
        // 应收费用
        receivableFee: formData.receivableFee,

        // 单位数量 (N年)
        unitCount: formData.unitCount,
        // 备注
        remark: formData.remark,
        // 付款截图
        attachments: formData.attachments || [],
        crmOrganId: formData.crmOrganId,
        crmOrganName: formData.crmOrganName,
        payAccount: formData.payAccount,
        supportItems,
        // share: formData.share,
    };
}

/**
 * 续费提交参数
 * @param formData
 */
export function createRenewalPostData(formData: any, form: Form, isSopRenewPurchase = 0): AbcAPI.RenewClinicContentRequest {
    const promotions = convertPromotionsFromBundle(form.getField('promotions').getBundle());
    const organInfo = form.getField('clinicId').getBundle();
    return {
        clinicId: formData.clinicId,
        editionId: organInfo.editionId,
        // 生效开始时间 (yyyy-MM-dd)
        beginDate: formData.beginDate,
        // 生效结束时间 (yyyy-MM-dd)
        endDate: formData.endDate,
        // 合计费用
        totalPrice: formData.totalPrice,
        // SOP合计费用
        editionOrderTotalPrice: formData.editionOrderTotalPrice,
        // 应收费用
        receivableFee: formData.receivableFee,
        // 收款账户id (字节流 1, 字节星球 2)
        receiveAccountId: 1,

        // 单位数量 (N年)
        unitCount: formData.unitCount,
        sopRenewPurchaseType: formData.sopRenewPurchaseType,
        remark: formData.remark,
        payAccount: formData.payAccount,
        promotions,
        isSopRenewPurchase,
    };
}

/**
 * 升级提交参数
 * @param formData
 */
export function createUpgradePostData(formData: any, form: Form): AbcAPI.RenewClinicContentRequest {
    return {
        clinicId: formData.clinicId,
        editionId: Number.isNaN(+formData.editionId) ? '' : formData.editionId,
        // 生效开始时间 (yyyy-MM-dd)
        beginDate: formData.beginDate,
        // 生效结束时间 (yyyy-MM-dd)
        endDate: formData.endDate,
        // 合计费用
        totalPrice: formData.totalPrice,
        // 应收费用
        receivableFee: formData.receivableFee,
        // 收款账户id (字节流 1, 字节星球 2)
        receiveAccountId: 1,

        // 单位数量 (N年)
        unitCount: formData.unitCount,
        remark: formData.remark,
        payAccount: formData.payAccount,
        crmOrganId: formData.crmOrganId,
        crmOrganName: formData.crmOrganName,
    };
}

/**
 * 其他订单提交参数
 * @param formData
 */
export function createOtherPostData(formData: any): AbcAPI.OtherChargeContentRequest {
    return {
        // 门店id
        clinicId: formData.clinicId || formData.pharmacyClinicId,
        // 定制项目名称
        name: formData.type === OtherOrderType.CUSTOMIZE ? formData.name : '',
        // 合计费用
        totalPrice: formData.totalPrice,
        // 应收费用
        receivableFee: formData.receivableFee || formData.pharmacyReceivableFee,
        // 收款账户id (字节流 1, 字节星球 2)
        receiveAccountId: 1,
        // 备注
        remark: formData.remark,
        // 收费类型 (1 医保代办; 2 自助服务机; 3 定制费用)
        type: formData.type,
        cooperationClinicAccount: formData.type === OtherOrderType.MEDICAL_DIAGNOSIS ? {
            clinicId: formData.pharmacyClinicId,
            count: formData.addCount,
        } : undefined,
    };
}

export function createPostDataForOtherChargeCoClinicSubmit(formData: any): AbcAPI.CooperationClinicAccountOrderCalculateReq {
    return {
        clinicId: formData.pharmacyClinicId,
        count: formData.addCount,
        type: formData.type,
    };
}

/**
 * 修改版本时长提交参数
 * @param formData
 */
export function createModifyPeriodPostData(formData: any): AbcAPI.ChangeClinicEditionPeriodContentRequest {
    return {
        createdDate: formData.createdDate,
        // 门店id
        clinicId: formData.clinicId,
        // 生效开始时间 (yyyy-MM-dd)
        beginDate: formData.beginDate,
        // 生效结束时间 (yyyy-MM-dd)
        endDate: formData.endDate,
        // 备注截图
        attachments: formData.attachments,
        // 备注
        remark: formData.remark,
    };
}

export function convertPromotionsFromBundle(bundle: any) {
    if (bundle) {
        bundle = bundle.map((item: any) => ({
            id: item.value,
            name: item.label,
            amount: item.amount,
        }));
    }
    return bundle || [];
}

// 新购
export const formSchema = {
    type: 'object',
    component: 'SubmitForm',
    labelPosition: 'top',
    actions: {
        submit: {
            type: 'submit',
            component: 'Button',
            loading: false,
            componentProps: {
                text: '创建',
                type: 'primary',
            },
        },
    },
    properties: {
        crmOrganId: {
            label: '添加CRM信息',
            type: 'string',
            component: 'CrmOrganPicker',
            componentProps: {
                style: 'margin-bottom: 12px',
                options: [],
            },
            rules: [
                { required: true, message: '必填' },
            ],
            visible(formData: any) {
                return formData.clinicType !== ClinicNodeType.CHAIN_ADMIN;
            },
        },
        crmOrganName: {
            label: '添加CRM信息',
            type: 'string',
            component: 'Input',
            componentProps: {
                style: 'margin-bottom: 12px',
            },
            watch: {
                sources: ['crmOrganId'],
                async handler(newValues: Array<any>, oldValues:Array<any>, field: Field) {
                    const crmOrgan = field.form.getField('crmOrganId');
                    const crmOrganBundle = crmOrgan?.getBundle() || {};
                    field.modelValue = crmOrganBundle.name || field.modelValue;
                },
                immediate: false,
            },
            visible: () => false,
        },
        clinicType: {
            label: '新购类型',
            type: 'string',
            defaultValue: ClinicNodeType.SINGLE,
            component: 'Radio',
            componentProps: {
                options: [
                    { label: '新开单店', value: ClinicNodeType.SINGLE },
                    { label: '新开连锁', value: ClinicNodeType.CHAIN_ADMIN },
                    { label: '新开子店', value: ClinicNodeType.CHAIN_SUB },
                    { label: '试用门店转正', value: ClinicNodeType.TRIAL_TO_NORMALIZE },
                ],
            },
        },

        clinicId: {
            label: '选择试用转正的门店',
            type: 'string',
            component: 'OrganPicker',
            componentProps: {
                nodeTypeFilter: NodeTypeFilter.ALL_SUB,
                isTrial: 1,
                searchKeyword: '',
            },
            rules: [
                { required: true, message: '必填' },
            ],
            visible(formData: any) {
                return formData.clinicType === ClinicNodeType.TRIAL_TO_NORMALIZE;
            },
        },
        hisType: {
            label: '新购产品',
            type: 'string',
            defaultValue: '',
            visible(formData: any) {
                return formData.clinicType !== ClinicNodeType.CHAIN_SUB
                    && formData.clinicType !== ClinicNodeType.TRIAL_TO_NORMALIZE;
            },
            component: 'Radio',
            componentProps: {
                options: [
                    { label: '诊所管家', value: HisType.NORMAL },
                    { label: '口腔管家', value: HisType.DENTISTRY },
                    { label: '眼视光管家', value: HisType.OPHTHALMOLOGY },
                    { label: '药店管家', value: HisType.PHARMACY },
                ],
            },
            rules: [
                { required: true, message: '必填' },
            ],
        },
        hisTypeName: {
            label: '产品类型',
            type: 'string',
            defaultValue: '',
            visible(formData: any) {
                return formData.clinicType === ClinicNodeType.TRIAL_TO_NORMALIZE;
            },
            component: 'Input',
            rules: [
                { required: true, message: '必填' },
            ],
            disable: true,
            watch: {
                sources: ['clinicId'],
                async handler(newValues: Array<any>, oldValues:Array<any>, field: Field) {
                    const clinic = field.form.getField('clinicId');
                    const clinicBundle = clinic?.getBundle() || {};
                    field.form.formData.hisType = clinicBundle.hisType;
                    field.modelValue = getHisTypeDisplayName({ hisType: clinicBundle.hisType }) || field.modelValue;
                },
                immediate: false,
            },
        },

        chainName: {
            label: '连锁名称',
            type: 'string',
            component: 'Input',
            rules: [
                { required: true, message: '必填' },
            ],
            visible(formData: any) {
                return formData.clinicType === ClinicNodeType.CHAIN_ADMIN;
            },
        },

        chainAddress: {
            label: '连锁地区',
            type: 'object',
            component: 'AreaPicker',
            rules: [
                { required: true, message: '必填' },
            ],
            defaultValue() {
                return useOrderStore().lastAddressRegion;
            },
            visible(formData: any) {
                return formData.clinicType === ClinicNodeType.CHAIN_ADMIN;
            },
        },

        chainId: {
            label: '选择新增子店的连锁',
            type: 'string',
            component: 'OrganPicker',
            componentProps: {
                nodeType: ClinicNodeType.CHAIN_ADMIN,
            },
            rules: [
                { required: true, message: '必填' },
            ],
            visible(formData: any) {
                return formData.clinicType === ClinicNodeType.CHAIN_SUB;
            },
        },

        adminName: {
            label: '管理员姓名',
            type: 'string',
            component: 'Input',
            rules: [
                { required: true, message: '必填' },
            ],
            visible(formData: any) {
                return formData.clinicType !== ClinicNodeType.TRIAL_TO_NORMALIZE;
            },
        },

        adminMobile: {
            label: '管理员手机',
            type: 'string',
            component: 'Input',
            componentProps: {
                type: 'tel',
            },
            rules: [
                { required: true, message: '必填' },
                { trigger: 'onChange', validator(mobile: any) { return validateMobilePhone(mobile); }, message: '手机号无效' },
            ],
            visible(formData: any) {
                return formData.clinicType !== ClinicNodeType.TRIAL_TO_NORMALIZE;
            },
        },

        clinicName: {
            label: '门店名称',
            type: 'string',
            component: 'Input',
            rules: [
                { required: true, message: '必填' },
            ],
            visible(formData: any) {
                return formData.clinicType !== ClinicNodeType.CHAIN_ADMIN
                  && formData.clinicType !== ClinicNodeType.TRIAL_TO_NORMALIZE;
            },
        },

        clinicAddress: {
            label: '门店地区',
            type: 'object',
            component: 'AreaPicker',
            rules: [
                { required: true, message: '必填' },
            ],
            defaultValue() {
                return useOrderStore().lastAddressRegion;
            },
            visible(formData: any) {
                return formData.clinicType !== ClinicNodeType.CHAIN_ADMIN
                    && formData.clinicType !== ClinicNodeType.TRIAL_TO_NORMALIZE;
            },
        },

        editionId: {
            label: '版本',
            type: 'string',
            component: 'Radio',
            componentProps: {
                options(field: Field) {
                    if (field.form.formData.clinicType === ClinicNodeType.TRIAL_TO_NORMALIZE) {
                        const organInfo = field.form.getField('clinicId').getBundle();
                        if (!organInfo && !field.form.getFieldValue('hisType')) {
                            return getEditionOptions(HisType.NORMAL);
                        }
                        if (!organInfo) {
                            // 展示升级订单
                            const hisType = field.form.getFieldValue('hisType');
                            const editionId = field.form.getFieldValue('editionId');
                            const options = getEditionOptions(hisType);
                            const index = options.findIndex((option: any) => option.value === editionId);
                            return options.slice(index);
                        }
                        // 升级订单
                        const options = getEditionOptions(organInfo.hisType);
                        const index = options.findIndex((option: any) => option.value === organInfo.editionId);
                        return options.slice(index);
                    }
                    if (field.form.formData.clinicType === ClinicNodeType.CHAIN_SUB) {
                        const hisType = field.form.getField('chainId')?.getBundle()?.hisType || field.form.formData.hisType;
                        return getEditionOptions(hisType, true);
                    }
                    const hisType = field.form.formData.hisType;
                    return getEditionOptions(hisType);
                },
            },
            rules: [
                { required: true, message: '必填' },
            ],
            watch: {
                sources: ['hisType', 'clinicId'],
                async handler(newValues: Array<any>, oldValues:Array<any>, field: Field) {
                    if (newValues[0] !== oldValues[0]) {
                        field.modelValue = '';
                    }
                    if (field.form.formData.clinicType === ClinicNodeType.TRIAL_TO_NORMALIZE) {
                        field.modelValue = field.form.getField('clinicId')?.getBundle()?.editionId || field.form.formData.editionId;
                    }
                },
                immediate: false,
            },
            visible(formData: any) {
                return formData.clinicType !== ClinicNodeType.CHAIN_ADMIN;
            },
        },

        unitCount: {
            label: '购买年数',
            type: 'string',
            component: 'Radio',
            defaultValue: 1,
            componentProps: {
                options: [
                    { label: '1 年', value: 1 },
                    { label: '2 年', value: 2 },
                    { label: '3 年', value: 3 },
                ],
            },
            rules: [
                { required: true, message: '必填' },
            ],
            visible(formData: any) {
                return formData.clinicType !== ClinicNodeType.CHAIN_ADMIN;
            },
        },
        docking: {
            label: '医保对接',
            type: 'number',
            component: 'Radio',
            defaultValue: 1,
            componentProps: {
                options: [
                    { label: '需要', value: 1 },
                    { label: '不需要', value: 0 },
                ],
            },
            rules: [
                { required: true, message: '必填' },
            ],
            visible(formData: any) {
                return showDocking(formData);
            },
            watch: {
                sources: ['editionId', 'unitCount', 'hisType'],
                handler(newValues: Array<any>, oldValues:Array<any>, field: Field) {
                    const formData: any = {
                        editionId: newValues[0],
                        unitCount: newValues[1],
                        hisType: newValues[2],
                    };
                    if (showDocking(formData)) {
                        field.modelValue = 1;
                    } else {
                        field.modelValue = 2;
                    }
                },
            },
        },
        payAccount: {
            label: '支付给',
            type: 'string',
            component: 'Radio',
            rules: [
                { required: true, message: '必填' },
            ],
            componentProps: {
                options: [
                    { label: 'ABC', value: PayAccount.ABC },
                    { label: '楚天云', value: PayAccount.CHU_TIAN_YUN },
                ],
            },
            visible(formData: any, field: Field, form: Form) {
                return formData.clinicAddress?.city?.id === '420100' || formData.payAccount || formData.payAccount === 0;
            },
            watch: {
                sources: ['clinicAddress'],
                handler(newValues: Array<any>, oldValues: Array<any>, field: Field) {
                    const clinicAddress = newValues[0];
                    if (clinicAddress?.city?.id !== '420100') {
                        field.modelValue = undefined;
                    }
                },
            },
        },
        // 开始时间默认是今天，可以往后选最多两个月
        beginDate: {
            label: '开始时间',
            type: 'date',
            component: 'DatePicker',
            defaultValue: '{{today}}',
            componentProps: {
                valueFormat: 'YYYY-MM-DD',
                minDate: dayjs().toDate(), // 最小时间
                maxDate(formData: any, field: Field, form: Form) {
                    const { maxBeginDate } = form.getField('totalPrice')?.getBundle() || {};
                    if (!maxBeginDate) {
                        return dayjs().add(2, 'month').toDate();
                    }
                    return dayjs(maxBeginDate).toDate();
                }, // 最大时间
            },
            rules: [
                { required: true, message: '必填' },
            ],
            visible(formData: any) {
                return formData.clinicType !== ClinicNodeType.CHAIN_ADMIN;
            },
        },

        // 默认值为 beginDate + unitCount 年，可以往后增加最多两个月
        endDate: {
            label: '结束时间',
            type: 'date',
            component: 'DatePicker',
            defaultValue(formData: any) {
                return dayjs(formData.beginDate)
                                .add(formData.unitCount, 'year')
                                .subtract(1, 'day')
                                .format('YYYY-MM-DD');
            },
            componentProps: {
                valueFormat: 'YYYY-MM-DD',
                minDate(formData: any) {
                    return dayjs(formData.beginDate).add(formData.unitCount, 'year').toDate();
                }, // 最小时间
                maxDate(formData: any, field: Field, form: Form) {
                    const { maxEndDate } = form.getField('totalPrice')?.getBundle() || {};
                    if (!maxEndDate) {
                        dayjs(formData.beginDate).add(formData.unitCount, 'year').add(1, 'month').toDate();
                    }
                    return dayjs(maxEndDate).toDate();
                }, // 最大时间
            },
            rules: [
                { required: true, message: '必填' },
                {
                    validator(modelValue: string, rule: any, field: Field, form: Form) {
                        const beginDate = form.getField('beginDate').modelValue;
                        return validateEndDate(beginDate, modelValue);
                    },
                    message: '结束时间不能小于开始时间',
                },
            ],
            disable: true,
            visible(formData: any) {
                return formData.clinicType !== ClinicNodeType.CHAIN_ADMIN;
            },
        },

        totalPrice: {
            label: '版本金额',
            type: 'string',
            component: 'Input',
            readonly: true,
            visible(formData: any) {
                return formData.clinicType !== ClinicNodeType.CHAIN_ADMIN;
            },
            watch: {
                sources: ['editionId', 'unitCount', 'beginDate', 'endDate', 'clinicType', 'receivableFee', 'docking', 'crmOrganId'],
                async handler(newValues: Array<any>, oldValues:Array<any>, field: Field) {
                    if (!newValues[0]) {
                        // 没有选择版本，直接 return
                        field.modelValue = '';
                        return;
                    }
                    if (field.form.formData.clinicType === ClinicNodeType.CHAIN_SUB && newValues[0] === '10') {
                        // 连锁子店，基础版
                        field.modelValue = '';
                        return;
                    }

                    if (field.form.formData.clinicType === ClinicNodeType.CHAIN_SUB) {
                        field.form.formData.hisType = field.form.getField('chainId')?.getBundle()?.hisType;
                    }
                    const postData = field.form.formData.clinicType === ClinicNodeType.TRIAL_TO_NORMALIZE
                        ? createPostDataForTrialToNormalize(field.form.formData)
                        : createPostDataForSub(field.form.formData);
                    let calculateRsp: any = {};

                    try {
                        if (field.form.formData.clinicType === ClinicNodeType.TRIAL_TO_NORMALIZE) {
                            calculateRsp = await ApprovalTicketAPI.preSubmitApprovalTicketTransTrialClinicUsingPOST({
                                ...postData,
                                hisType: field.form.formData.hisType,
                                clinicId: field.form.formData.clinicSourceId || field.form.formData.clinicId,
                            });
                        } else {
                            calculateRsp = await ApprovalTicketAPI.preSubmitApprovalTicketCreateClinicUsingPOST(postData);
                        }
                    } catch (e: any) {
                        Toast.fail(e.message || e);
                    }
                    field.modelValue = calculateRsp.editionOrderFeeInfo?.totalPrice;
                    // 填写最小收款金额
                    field.form.getField('minReceivableFee').modelValue = `应收金额${calculateRsp.receivableFee}, 收款金额不可低于${calculateRsp.minReceivableFee}`;
                    field.form.getField('adjustmentPrice').modelValue = calculateRsp.adjustmentPrice || 0;
                    field.form.getField('dockingPrice').modelValue = calculateRsp?.supportItems?.find((item: any) => item.key === 'shebao')?.totalPrice;

                    field.setBundle(calculateRsp);
                    if (field.form.actions) {
                        field.form.actions.submit.loading = false;
                    }
                },
            },
        },

        // 差异折扣金额，= 收款金额 - receivableFee
        adjustmentPrice: {
            label: '折扣金额',
            type: 'string',
            component: 'Input',
            readonly: true,
            visible(formData: any) {
                return formData.clinicType !== ClinicNodeType.CHAIN_ADMIN;
            },
            watch: {
                sources: ['clinicType', 'hisType'],
                handler(newValues: Array<any>, oldValues:Array<any>, field: Field) {
                    field.modelValue = '';
                },
            },
        },
        dockingPrice: {
            label: '医保对接费用',
            type: 'number',
            component: 'Input',
            defaultValue: DOCKING_PRICE,
            readonly: true,
            componentProps: {
                type: 'number',
            },
            visible(formData: any) {
                return showDocking(formData, true);
            },

        },

        receivableFee: {
            label: '收款金额',
            type: 'string',
            component: 'Input',
            componentProps: {
                type: 'number',
            },
            visible(formData: any) {
                return formData.clinicType !== ClinicNodeType.CHAIN_ADMIN;
            },
            rules: [
                { required: true, message: '必填' },
                {
                    trigger: 'onChange',
                    validator(fee: any, rule: IValidateRule, field: Field, form: Form) {
                        const { minReceivableFee = 0 } = form.getField('totalPrice').getBundle() || {};
                        return parseFloat(fee) >= parseFloat(minReceivableFee);
                    },
                    message(fee: any, rule: IValidateRule, field: Field, form: Form) {
                        const { minReceivableFee = 0 } = form.getField('totalPrice').getBundle() || {};
                        return `低于最低折扣：${minReceivableFee}`;
                    },
                },
            ],
            watch: {
                sources: ['clinicType', 'hisType'],
                handler(newValues: Array<any>, oldValues:Array<any>, field: Field) {
                    field.modelValue = '';
                },
                immediate: false,
            },
        },

        minReceivableFee: {
            type: 'string',
            component: 'Remark',
            visible(formData: any) {
                return formData.clinicType !== ClinicNodeType.CHAIN_ADMIN;
            },
            watch: {
                sources: ['clinicType', 'hisType'],
                handler(newValues: Array<any>, oldValues:Array<any>, field: Field) {
                    field.modelValue = '';
                },
            },
        },

        remark: {
            label: '备注',
            type: 'string',
            component: 'Textarea',
            componentProps: {
                placeholder: '内部审批时可见',
                maxLength: 50,
            },
        },

        // 本期先不要
        // share: {
        //     label: '成单分享',
        //     type: 'string',
        //     component: 'Textarea',
        //     componentProps: {
        //         placeholder: '仅ABC内部同事可见，客户不可见',
        //     },
        //     visible(formData: any) {
        //         return formData.clinicType !== ClinicNodeType.CHAIN_ADMIN;
        //     },
        // },
    },
};

// 新购-楚天云
export const chutianyunFormSchema = {
    type: 'object',
    component: 'SubmitForm',
    labelPosition: 'top',
    actions: {
        submit: {
            type: 'submit',
            component: 'Button',
            loading: false,
            componentProps: {
                text: '创建',
                type: 'primary',
            },
        },
    },
    properties: {
        crmOrganId: {
            label: '添加CRM信息',
            type: 'string',
            component: 'CrmOrganPicker',
            rules: [
                { required: true, message: '必填' },
            ],
            componentProps: {
                style: 'margin-bottom: 12px',
            },
        },
        crmOrganName: {
            label: '添加CRM信息',
            type: 'string',
            component: 'Input',
            componentProps: {
                style: 'margin-bottom: 12px',
            },
            watch: {
                sources: ['crmOrganId'],
                async handler(newValues: Array<any>, oldValues:Array<any>, field: Field) {
                    const crmOrgan = field.form.getField('crmOrganId');
                    const crmOrganBundle = crmOrgan?.getBundle() || {};
                    field.modelValue = crmOrganBundle.name || field.modelValue;
                },
                immediate: false,
            },
            visible: () => false,
        },
        clinicId: {
            label: '门店名',
            type: 'string',
            component: 'OrganPicker',
            componentProps: {
                businessType: 'chutianyun',
            },
            rules: [
                { required: true, message: '必填' },
            ],
        },

        editionId: {
            label: '版本',
            type: 'string',
            component: 'Radio',
            componentProps: {
                options(field: Field) {
                    return getEditionOptions(HisType.NORMAL, field.form.formData.clinicType === ClinicNodeType.CHAIN_SUB);
                },
            },
            rules: [
                { required: true, message: '必填' },
            ],
        },

        unitCount: {
            label: '购买年数',
            type: 'string',
            component: 'Radio',
            defaultValue: 1,
            componentProps: {
                options: [
                    { label: '1 年', value: 1 },
                    { label: '2 年', value: 2 },
                    { label: '3 年', value: 3 },
                ],
            },
            rules: [
                { required: true, message: '必填' },
            ],
        },
        docking: {
            label: '医保对接',
            type: 'number',
            component: 'Radio',
            defaultValue: 1,
            componentProps: {
                options: [
                    { label: '需要', value: 1 },
                    { label: '不需要', value: 0 },
                ],
            },
            rules: [
                { required: true, message: '必填' },
            ],
            visible(formData: any) {
                return showDocking(formData);
            },
            watch: {
                sources: ['editionId', 'unitCount', 'hisType'],
                handler(newValues: Array<any>, oldValues:Array<any>, field: Field) {
                    const formData: any = {
                        editionId: newValues[0],
                        unitCount: newValues[1],
                        hisType: newValues[2],
                    };

                    if (showDocking(formData)) {
                        field.modelValue = 1;
                    } else {
                        field.modelValue = 2;
                    }
                },
            },
        },
        payAccount: {
            label: '支付给',
            type: 'string',
            component: 'Radio',
            rules: [
                { required: true, message: '必填' },
            ],
            componentProps: {
                options: [
                    { label: '楚天云', value: PayAccount.CHU_TIAN_YUN },
                    { label: 'ABC', value: PayAccount.ABC },
                ],
            },
            visible(formData: any, field: Field, form: Form) {
                const organInfo = form.getField('clinicId').getBundle();
                console.log({ organInfo, formData });
                return organInfo?.addressCityId === '420100' || formData.payAccount || formData.payAccount === 0;
            },
        },

        // 开始时间默认是今天，可以往后选最多两个月
        beginDate: {
            label: '开始时间',
            type: 'date',
            component: 'DatePicker',
            defaultValue: '{{today}}',
            componentProps: {
                valueFormat: 'YYYY-MM-DD',
                minDate: dayjs().toDate(), // 最小时间
                maxDate(formData: any, field: Field, form: Form) {
                    const { maxBeginDate } = form.getField('totalPrice')?.getBundle() || {};
                    if (!maxBeginDate) {
                        return dayjs().add(2, 'month').toDate();
                    }
                    return dayjs(maxBeginDate).toDate();
                }, // 最大时间
            },
            rules: [
                { required: true, message: '必填' },
            ],
        },

        // 默认值为 beginDate + unitCount 年，可以往后增加最多1个月
        endDate: {
            label: '结束时间',
            type: 'date',
            component: 'DatePicker',
            defaultValue(formData: any) {
                return dayjs(formData.beginDate).add(formData.unitCount, 'year').format('YYYY-MM-DD');
            },
            componentProps: {
                valueFormat: 'YYYY-MM-DD',
                minDate(formData: any) {
                    return dayjs(formData.beginDate).add(formData.unitCount, 'year').toDate();
                }, // 最小时间
                maxDate(formData: any, field: Field, form: Form) {
                    const { maxEndDate } = form.getField('totalPrice')?.getBundle() || {};
                    if (!maxEndDate) {
                        dayjs(formData.beginDate).add(formData.unitCount, 'year').add(1, 'month').toDate();
                    }
                    return dayjs(maxEndDate).toDate();
                }, // 最大时间
            },
            disable: true,
            rules: [
                { required: true, message: '必填' },
                {
                    validator(modelValue: string, rule: any, field: Field, form: Form) {
                        const beginDate = form.getField('beginDate').modelValue;
                        return validateEndDate(beginDate, modelValue);
                    },
                    message: '结束时间不能小于开始时间',
                },
            ],
        },

        totalPrice: {
            label: '版本金额',
            type: 'string',
            component: 'Input',
            readonly: true,
            watch: {
                sources: ['clinicId', 'editionId', 'unitCount', 'beginDate', 'endDate', 'receivableFee', 'docking'],
                async handler(newValues: Array<any>, oldValues:Array<any>, field: Field) {
                    if (!newValues[0] || !newValues[1]) {
                        // 没有选择门店、版本，直接 return
                        return;
                    }
                    const postData = createPostDataForChutianyun(field.form.formData);
                    const calculateRsp = await ApprovalTicketAPI.preSubmitApprovalTicketCtyClinicUsingPOST(postData);
                    field.modelValue = calculateRsp.totalPrice;
                    // 填写最小收款金额
                    field.form.getField('adjustmentPrice').modelValue = calculateRsp.adjustmentPrice || 0;
                    field.form.getField('dockingPrice').modelValue = calculateRsp?.supportItems?.find((item: any) => item.key === 'shebao')?.totalPrice;

                    field.setBundle(calculateRsp);
                    if (field.form.actions) {
                        field.form.actions.submit.loading = false;
                    }
                },
            },
        },
        dockingPrice: {
            label: '医保对接费用',
            type: 'number',
            component: 'Input',
            defaultValue: DOCKING_PRICE,
            readonly: true,
            componentProps: {
                type: 'number',
            },
            visible(formData: any) {
                return showDocking(formData, true);
            },
        },

        ...generateFeeSchema(false, false),

        receivableFee: {
            label: '收款金额',
            type: 'string',
            component: 'Input',
            componentProps: {
                type: 'number',
            },
            rules: [
                { required: true, message: '必填' },
            ],
        },

        remark: {
            label: '备注',
            type: 'string',
            component: 'Textarea',
            componentProps: {
                placeholder: '内部审批时可见',
                maxLength: 50,
            },
        },

        attachments: {
            label: '付款截图',
            type: 'array',
            defaultValue: [],
            component: 'Images',
            componentProps: {
                placeholder: '上传截图',
                // 上传目录
                rootDir: 'oa/order',
            },
            rules: [
                { required: true, message: '必填' },
            ],
        },

        // 本期先不要
        // share: {
        //     label: '成单分享',
        //     type: 'string',
        //     component: 'Textarea',
        //     componentProps: {
        //         placeholder: '仅ABC内部同事可见，客户不可见',
        //     },
        //     visible(formData: any) {
        //         return formData.clinicType !== ClinicNodeType.CHAIN_ADMIN;
        //     },
        // },
    },
};

// 续费
export const renewalFormSchema = {
    type: 'object',
    component: 'SubmitForm',
    labelPosition: 'top',
    actions: {
        submit: {
            type: 'submit',
            component: 'Button',
            loading: false,
            componentProps: {
                text: '创建',
                type: 'primary',
            },
        },
    },
    properties: {
        clinicId: {
            label: '选择续费诊所',
            type: 'string',
            component: 'OrganPicker',
            componentProps: {
                nodeType: ClinicNodeType.CHAIN_SUB,
                isTrial: 0,
            },
            rules: [
                { required: true, message: '必填' },
            ],
        },

        editionId: {
            label: '当前版本',
            type: 'string',
            component: 'Input',
            readonly: true,
            defaultValue(formData: any, field: Field, form: Form) {
                const organInfo = form.getField('clinicId').getBundle();
                if (organInfo) {
                    return formatEdition(organInfo.editionId);
                }
                return '';
            },
            visible(formData: any) {
                return !!formData.clinicId;
            },
        },

        unitCount: {
            label: '购买年数',
            type: 'string',
            component: 'Radio',
            defaultValue: 1,
            componentProps: {
                options: [
                    { label: '1 年', value: 1 },
                    { label: '2 年', value: 2 },
                    { label: '3 年', value: 3 },
                ],
            },
            rules: [
                { required: true, message: '必填' },
            ],
            watch: {
                sources: ['sopRenewPurchaseType', 'isSopRenewPurchase'],
                handler(newValues: Array<any>, oldValues:Array<any>, field: Field) {
                    const isSopRenew = !!newValues[1];
                    if (isSopRenew) {
                        // SOP续费设置实际续费年数
                        const sopRenewPurchaseTypeOptions = field.form.getQuerySchemaOptions('sopRenewPurchaseType');
                        const sopRenewPurchaseType = field.form.getField('sopRenewPurchaseType').modelValue;
                        field.modelValue = sopRenewPurchaseTypeOptions.find(
                            (item: {label: string, value: number, disabled?: boolean, realUnitCount?: number}) => item.value === sopRenewPurchaseType,
                        )?.realUnitCount || 1;
                    }
                },
            },
            visible(formData: any) {
                return !formData.isSopRenewPurchase;
            },
        },

        sopRenewPurchaseType: {
            label: '购买年数',
            type: 'string',
            component: 'Radio',
            defaultValue: 0,
            componentProps: {
                options: [],
            },
            rules: [
                { required: true, message: '必填' },
            ],
            visible(formData: any) {
                return !!formData.isSopRenewPurchase;
            },
        },
        payAccount: {
            label: '支付给',
            type: 'string',
            component: 'Radio',
            rules: [
                { required: true, message: '必填' },
            ],
            componentProps: {
                options: [
                    { label: '楚天云', value: PayAccount.CHU_TIAN_YUN },
                    { label: 'ABC', value: PayAccount.ABC },
                ],
            },
            visible(formData: any, field: Field, form: Form) {
                const organInfo = form.getField('clinicId').getBundle();
                console.log({ organInfo, formData });
                return organInfo?.addressCityId === '420100' || formData.payAccount || formData.payAccount === 0;
            },
        },

        // 开始时间默认是今天，可以往后选最多两个月
        beginDate: {
            label: '开始时间',
            type: 'date',
            component: 'DatePicker',
            disable: true,
            defaultValue(formData: any, field: Field, form: Form) {
                const today = dayjs(new Date());
                const { beginDate } = form.getField('totalPrice')?.getBundle() || {};
                if (!beginDate) {
                    return today.format('YYYY-MM-DD');
                }
                return dayjs(beginDate).format('YYYY-MM-DD');
            },
            componentProps: {
                valueFormat: 'YYYY-MM-DD',
            },
            rules: [
                { required: true, message: '必填' },
            ],
        },

        // 默认值为 beginDate + unitCount 年，有后台计算值后使用后台计算值
        endDate: {
            label: '结束时间',
            type: 'date',
            component: 'DatePicker',
            disable: true,
            defaultValue(formData: any, field: Field, form: Form) {
                const { endDate } = form.getField('totalPrice')?.getBundle() || {};
                if (!endDate) {
                    return dayjs(formData.beginDate)
                                    .add(formData.unitCount, 'year')
                                    .subtract(1, 'day')
                                    .format('YYYY-MM-DD');
                }
                return dayjs(endDate).format('YYYY-MM-DD');
            },
            componentProps: {
                valueFormat: 'YYYY-MM-DD',
            },
            rules: [
                { required: true, message: '必填' },
                {
                    validator(modelValue: string, rule: any, field: Field, form: Form) {
                        const beginDate = form.getField('beginDate').modelValue;
                        return validateEndDate(beginDate, modelValue);
                    },
                    message: '结束时间不能小于开始时间',
                },
            ],
        },

        totalPrice: {
            label: '版本金额',
            type: 'string',
            component: 'Input',
            readonly: true,
            watch: {
                sources: ['editionId', 'unitCount', 'clinicId', 'promotions', 'receivableFee', 'sopRenewPurchaseType'],
                async handler(newValues: Array<any>, oldValues:Array<any>, field: Field) {
                    // 诊所变化查询是否为sop阶段性续费
                    if (newValues[2] !== oldValues[2]) {
                        if (!newValues[2]) {
                            return;
                        }
                        let res: any = {};
                        const clinicSourceId = field.form.formData?.clinicSourceId;
                        try {
                            res = await EditionAPI.supportSopRenewPurchaseUsingGET(newValues[2]);
                        } catch (e: any) {
                            ElMessage.error(e.message || e);
                        }
                        !clinicSourceId && (field.form.getField('isSopRenewPurchase').modelValue = res?.isSupport);
                        const sopRenewPurchaseTypeOptions:
                            {label: string, value: number, disabled?: boolean, realUnitCount?: number}[] = field.form.getField('isSopRenewPurchase').modelValue
                                ? [
                                    {
                                        label: '改为 2 年',
                                        value: 0,
                                        disabled: res.supportSopRenewPurchases?.length === 1,
                                        realUnitCount: res.supportSopRenewPurchases?.[0]?.realUnitCount,
                                    },
                                    {
                                        label: '改为 3 年',
                                        value: res.supportSopRenewPurchases?.[1]?.type || res.supportSopRenewPurchases?.[0]?.type || 2,
                                        realUnitCount: res.supportSopRenewPurchases?.[1]?.realUnitCount,
                                    },
                                ]
                                : [];
                        field.form.setQuerySchemaOptions('sopRenewPurchaseType', sopRenewPurchaseTypeOptions);
                        const sopRenewPurchaseType = field.form.getField('sopRenewPurchaseType').modelValue;
                        const disabled = sopRenewPurchaseTypeOptions.find(
                            (item: {label: string, value: number, disabled?: boolean, realUnitCount?: number}) => item.value === sopRenewPurchaseType,
                        )?.disabled;
                        if (disabled) {
                            field.form.getField('sopRenewPurchaseType').modelValue = undefined;
                        }
                    }
                    const isSopRenew = field.form.getField('isSopRenewPurchase').modelValue;
                    // SOP续费拦截监听收款金额变化请求
                    if (isSopRenew && newValues[4] !== oldValues[4]) {
                        return;
                    }
                    if (!newValues[2]) {
                        // 没有选择门店，直接 return
                        return;
                    }
                    if (field.form.formData.clinicType === ClinicNodeType.CHAIN_SUB && newValues[0] === '10') {
                        // 连锁子店，基础版
                        field.modelValue = '';
                        return;
                    }
                    const postData = createRenewalPostData(field.form.formData, field.form, isSopRenew);
                    const calculateRsp = await ApprovalTicketAPI.preSubmitApprovalTicketRenewClinicUsingPOST(postData);
                    field.modelValue = isSopRenew ? calculateRsp.editionOrderTotalPrice : calculateRsp.editionOrderFeeInfo?.totalPrice;
                    // 填写优惠券折扣金额
                    field.form.getField('discountPrice').modelValue = calculateRsp.discountPrice;
                    // SOP续费填写应收金额
                    if (isSopRenew) {
                        field.form.getField('receivableFee').modelValue = calculateRsp.minReceivableFee;
                        field.form.getField('prePaidFee').modelValue = calculateRsp.prePaidFee;
                    }
                    // 填写最小收款金额
                    field.form.getField('minReceivableFee').modelValue = `应收金额${calculateRsp.receivableFee}, 收款金额不可低于${calculateRsp.minReceivableFee}`;
                    field.form.getField('adjustmentPrice').modelValue = calculateRsp.adjustmentPrice || 0;
                    field.form.getField('additionalAccountPrice').modelValue = '';
                    if (calculateRsp.accountOrderFeeInfo) {
                        const {
                            count,
                            years,
                            totalPrice,
                            unitPrice,
                        } = calculateRsp.accountOrderFeeInfo;
                        if (count > 0) {
                            field.form.getField('additionalAccountPrice').modelValue = `${totalPrice}：${unitPrice}(年费) × ${count}(增购数) × ${years}(年数)`;
                        }
                    }
                    if (calculateRsp.cooperationAccountOrderFeeInfo) {
                        const {
                            count,
                            years,
                            totalPrice,
                            unitPrice,
                        } = calculateRsp.cooperationAccountOrderFeeInfo;
                        if (count > 0) {
                            field.form.getField('cooperationAccountOrderFee').modelValue = `${totalPrice}：${unitPrice}(年费) × ${count}(增购数) × ${years}(年数)`;
                        }
                    }

                    field.setBundle(calculateRsp);
                    if (field.form.actions) {
                        field.form.actions.submit.loading = false;
                    }
                },
            },
        },

        additionalAccountPrice: {
            label: '增购金额',
            type: 'string',
            component: 'Input',
            readonly: true,
        },

        cooperationAccountOrderFee: {
            label: '增购合作诊所',
            type: 'string',
            component: 'Input',
            readonly: true,
        },

        prePaidFee: {
            label: '已付金额',
            type: 'string',
            component: 'Input',
            readonly: true,
            visible(formData: any) {
                return !!formData.isSopRenewPurchase;
            },
        },

        isSopRenewPurchase: {
            label: '是否SOP订单',
            type: 'string',
            component: 'Input',
            readonly: true,
            visible: () => false,
        },

        // 优惠券
        promotions: {
            label: '优惠券',
            type: 'array',
            component: 'Checkbox',
            componentProps: {
                options(field: Field, form: Form) {
                    const bundle = form.getField('totalPrice').getBundle();
                    const {
                        availablePromotions = [],
                    } = bundle || {};
                    return availablePromotions.map((promotion: any) => ({
                        label: promotion.name,
                        value: promotion.id,
                        amount: promotion.amount,
                    }));
                },
            },
            visible(formData: any) {
                return !formData.isSopRenewPurchase;
            },
        },
        // 优惠券折扣金额
        discountPrice: {
            label: '优惠券折扣金额',
            type: 'string',
            component: 'Input',
            readonly: true,
            visible(formData: any) {
                return !formData.isSopRenewPurchase;
            },
        },
        adjustmentPrice: {
            label: '折扣金额',
            type: 'string',
            component: 'Input',
            readonly: true,
        },
        receivableFee: {
            label: '收款金额',
            type: 'string',
            component: 'Input',
            componentProps: {
                type: 'number',
            },
            readonly(formData: any) {
                return !!formData.isSopRenewPurchase;
            },
            rules: [
                { required: true, message: '必填' },
                {
                    trigger: 'onChange',
                    validator(fee: any, rule: IValidateRule, field: Field, form: Form) {
                        const { minReceivableFee = 0 } = form.getField('totalPrice').getBundle() || {};
                        return parseFloat(fee) >= parseFloat(minReceivableFee);
                    },
                    message(fee: any, rule: IValidateRule, field: Field, form: Form) {
                        const { minReceivableFee = 0 } = form.getField('totalPrice').getBundle() || {};
                        return `低于最低折扣：${minReceivableFee}`;
                    },
                },
            ],
        },
        minReceivableFee: {
            type: 'string',
            component: 'Remark',
            visible(formData: any) {
                return !formData.isSopRenewPurchase;
            },
        },

        remark: {
            label: '备注',
            type: 'string',
            component: 'Textarea',
            componentProps: {
                placeholder: '内部审批时可见',
                maxLength: 50,
            },
        },
    },
};

// 版本升级
export const upgradeFormSchema = {
    type: 'object',
    component: 'SubmitForm',
    labelPosition: 'top',
    actions: {
        submit: {
            type: 'submit',
            component: 'Button',
            loading: false,
            componentProps: {
                text: '创建',
                type: 'primary',
            },
        },
    },
    properties: {
        crmOrganId: {
            label: '添加CRM信息',
            type: 'string',
            component: 'CrmOrganPicker',
            componentProps: {
                style: 'margin-bottom: 12px',
                options: [],
            },
            rules: [
                { required: true, message: '必填' },
            ],
            watch: {
                sources: ['clinicId'],
                async handler(newValues: Array<any>, oldValues:Array<any>, field: Field) {
                    if (newValues[0] && oldValues[0] !== newValues[0]) {
                        const response = await CrmOrganAPI.getByClinicIdUsingGET(newValues[0]);
                        field.form.setFieldValue('crmOrganId', response.id);
                        field.form.setFieldValue('crmOrganName', response.name);
                        field.form.setFieldValue('hasCrmOrgan', !response.id);
                    }
                },
                immediate: false,
            },
            visible(formData: any) {
                return formData.hasCrmOrgan;
            },
        },
        clinicId: {
            label: '选择升级诊所',
            type: 'string',
            component: 'OrganPicker',
            componentProps: {
                nodeType: ClinicNodeType.CHAIN_SUB,
                isTrial: 0,
            },
            rules: [
                { required: true, message: '必填' },
            ],
        },

        currentEdition: {
            label: '当前版本',
            type: 'string',
            component: 'Input',
            readonly: true,
            defaultValue(formData: any, field: Field, form: Form) {
                const organInfo = form.getField('clinicId').getBundle();
                if (organInfo) {
                    return formatEdition(organInfo.editionId);
                }
                return '';
            },
            visible(formData: any) {
                return !!formData.clinicId;
            },
        },

        editionId: {
            label: '升级版本',
            type: 'string',
            component: 'Radio',
            componentProps: {
                options(field: Field, form: Form) {
                    const organInfo = form.getField('clinicId').getBundle();
                    if (!organInfo && !form.getFieldValue('hisType')) {
                        return getEditionOptions(HisType.NORMAL);
                    }
                    if (!organInfo) {
                        // 展示升级订单
                        const hisType = form.getFieldValue('hisType');
                        const editionId = form.getFieldValue('oldEditionId');
                        const options = getEditionOptions(hisType);
                        const index = options.findIndex((option: any) => option.value === editionId);
                        return options.slice(index + 1);
                    }
                    // 升级订单
                    const options = getEditionOptions(organInfo.hisType);
                    const index = options.findIndex((option: any) => option.value === organInfo.editionId);
                    return options.slice(index + 1);
                },
            },
            rules: [
                { required: true, message: '必填' },
            ],
        },

        unitCount: {
            label: '购买年数',
            type: 'string',
            component: 'Radio',
            defaultValue: 1,
            componentProps: {
                options: [
                    { label: '1 年', value: 1 },
                    { label: '2 年', value: 2 },
                    { label: '3 年', value: 3 },
                ],
            },
            rules: [
                { required: true, message: '必填' },
            ],
            watch: {
                sources: ['clinicId'],
                async handler(newValues: Array<any>, oldValues:Array<any>, field: Field) {
                    if (newValues[0]) {
                        const minYearsResponse = await MinUpgradeYearsFunction.exec({
                            clinicId: field.form?.formData?.clinicSourceId || newValues[0],
                        });
                        let { minYears = 1 } = minYearsResponse.data;
                        const options: { label: string, value: number }[] = [
                            { label: '1 年', value: 1 },
                            { label: '2 年', value: 2 },
                            { label: '3 年', value: 3 },
                        ];
                        field.componentProps = field.componentProps || {};
                        field.componentProps.options = options.filter((item) => item.value >= (minYears > 3 ? 3 : minYears)) || [];
                        field.modelValue = field.componentProps.options[0].value;
                    }
                },
            },
        },
        payAccount: {
            label: '支付给',
            type: 'string',
            component: 'Radio',
            rules: [
                { required: true, message: '必填' },
            ],
            componentProps: {
                options: [
                    { label: '楚天云', value: PayAccount.CHU_TIAN_YUN },
                    { label: 'ABC', value: PayAccount.ABC },
                ],
            },
            visible(formData: any, field: Field, form: Form) {
                const organInfo = form.getField('clinicId').getBundle();
                return organInfo?.addressCityId === '420100' || formData.payAccount || formData.payAccount === 0;
            },
        },

        // 开始时间默认是今天
        beginDate: {
            label: '开始时间',
            type: 'date',
            component: 'DatePicker',
            disable: true,
            defaultValue(formData: any, field: Field, form: Form) {
                const { beginDate } = form.getField('totalPrice')?.getBundle() || {};
                if (!beginDate) {
                    return dayjs(new Date()).format('YYYY-MM-DD');
                }
                return dayjs(beginDate).format('YYYY-MM-DD');
            },
            componentProps: {
                valueFormat: 'YYYY-MM-DD',
            },
            rules: [
                { required: true, message: '必填' },
            ],
        },

        // 默认值为 beginDate + unitCount 年，算费后使用后台返回结果
        endDate: {
            label: '结束时间',
            type: 'date',
            component: 'DatePicker',
            disable: true,
            defaultValue(formData: any, field: Field, form: Form) {
                const { endDate } = form.getField('totalPrice')?.getBundle() || {};
                if (!endDate) {
                    return dayjs(formData.beginDate)
                                    .add(formData.unitCount, 'year')
                                    .subtract(1, 'day')
                                    .format('YYYY-MM-DD');
                }
                return dayjs(endDate).format('YYYY-MM-DD');
            },
            componentProps: {
                valueFormat: 'YYYY-MM-DD',
            },
            rules: [
                { required: true, message: '必填' },
                {
                    validator(modelValue: string, rule: any, field: Field, form: Form) {
                        const beginDate = form.getField('beginDate').modelValue;
                        return validateEndDate(beginDate, modelValue);
                    },
                    message: '结束时间不能小于开始时间',
                },
            ],
        },

        totalPrice: {
            label: '版本金额',
            type: 'string',
            component: 'Input',
            readonly: true,
            visible(formData: any) {
                return formData.clinicType !== ClinicNodeType.CHAIN_ADMIN;
            },
            watch: {
                sources: ['editionId', 'unitCount', 'beginDate', 'endDate', 'clinicId', 'promotions', 'receivableFee'],
                async handler(newValues: Array<any>, oldValues:Array<any>, field: Field) {
                    if (!newValues[4]) {
                        // 没有选择门店，直接 return
                        return;
                    }
                    if (field.form.formData.clinicType === ClinicNodeType.CHAIN_SUB && newValues[0] === '10') {
                        // 连锁子店，基础版
                        field.modelValue = '';
                        return;
                    }
                    const postData = createUpgradePostData(field.form.formData, field.form);
                    const calculateRsp = await ApprovalTicketAPI.preSubmitApprovalTicketUpgradeClinicUsingPOST(postData);
                    field.modelValue = calculateRsp.editionOrderFeeInfo?.totalPrice;
                    // 填写抵扣金额
                    field.form.getField('deductionFee').modelValue = calculateRsp.deductionFee;
                    // 填写最小收款金额
                    field.form.getField('minReceivableFee').modelValue = `应收金额${calculateRsp.receivableFee}, 收款金额不可低于${calculateRsp.minReceivableFee}`;
                    field.form.getField('adjustmentPrice').modelValue = calculateRsp.adjustmentPrice || 0;
                    // 抵扣金额需要展示版本和账号的抵扣金额
                    if (calculateRsp.accountDeductionFee > 0) {
                        // eslint-disable-next-line max-len
                        field.form.getField('deductionFee').modelValue = `${calculateRsp.deductionFee}：含版本抵扣${calculateRsp.editionDeductionFee} + 增购账号抵扣${calculateRsp.accountDeductionFee}`;
                    }
                    field.setBundle(calculateRsp);
                    if (field.form.actions) {
                        field.form.actions.submit.loading = false;
                    }
                },
            },
        },

        ...generateFeeSchema(false),

        remark: {
            label: '备注',
            type: 'string',
            component: 'Textarea',
            componentProps: {
                placeholder: '内部审批时可见',
                maxLength: 50,
            },
        },
    },
};

// 其他收费
export const otherFormSchema = {
    type: 'object',
    component: 'SubmitForm',
    labelPosition: 'top',
    actions: {
        submit: {
            type: 'submit',
            component: 'Button',
            loading: false,
            componentProps: {
                text: '创建',
                type: 'primary',
            },
        },
    },
    properties: {
        type: {
            label: '收费项目',
            type: 'number',
            defaultValue: OtherOrderType.DOCKING,
            component: 'Radio',
            componentProps: {
                options: [
                    // { label: '专网前置机', value: OtherOrderType.ROUTER },
                    { label: '医保对接费用', value: OtherOrderType.DOCKING },
                    { label: '自助服务机', value: OtherOrderType.SELF_SERVICE_TERMINAL },
                    { label: 'LIS设备对接费', value: OtherOrderType.LIS },
                    { label: 'PACS设备对接费', value: OtherOrderType.PACS },
                    { label: '数据迁移费用', value: OtherOrderType.DATA_MIGRATION },
                    { label: '短信充值费用', value: OtherOrderType.SMS },
                    { label: '开放平台接口费', value: OtherOrderType.OPEN_PLATFORM },
                    { label: '药诊合作诊所增购', value: OtherOrderType.MEDICAL_DIAGNOSIS },
                    { label: '医院项目费用', value: OtherOrderType.HOSPITAL },
                    { label: '追溯码高拍仪', value: OtherOrderType.RETRIEVAL_SCANNER },
                    { label: '其他费用', value: OtherOrderType.OTHER },
                ],
            },
        },

        name: {
            label: '定制项目名称',
            type: 'string',
            component: 'Input',
            rules: [
                { required: true, message: '必填' },
            ],
            visible(formData: any) {
                // 定制项目需要填写
                return formData.type === OtherOrderType.CUSTOMIZE;
            },
        },

        clinicId: {
            label: '收款诊所名称',
            type: 'string',
            component: 'OrganPicker',
            componentProps: {
                nodeType: ClinicNodeType.CHAIN_SUB,
                isTrial: 0,
            },
            rules: [
                { required: true, message: '必填' },
            ],
            visible(formData: any) {
                // 药诊合作诊所增购排除
                return formData.type !== OtherOrderType.MEDICAL_DIAGNOSIS;
            },
        },

        receivableFee: {
            label: '收款金额',
            type: 'number',
            component: 'Input',
            componentProps: {
                type: 'number',
            },
            readonly: (formData: any) => formData.type === OtherOrderType.DOCKING,
            rules: [
                { trigger: 'onChange', validator(fee: any) { return parseFloat(fee) >= 0; }, message: '收款金额无效' },
            ],
            visible(formData: any) {
                return formData.clinicType !== ClinicNodeType.CHAIN_ADMIN && formData.type !== OtherOrderType.MEDICAL_DIAGNOSIS;
            },
            watch: {
                sources: ['type', 'clinicId'],
                handler(newValues: Array<any>, oldValues:Array<any>, field: Field) {
                    const isDocking = newValues[0] === OtherOrderType.DOCKING;
                    const clinicInfo = field.form.getField('clinicId').getBundle();
                    if (!newValues[1] || !isDocking) {
                        field.modelValue = undefined;
                        return;
                    }
                    field.modelValue = isDocking && clinicInfo.needPayForYiBao ? DOCKING_PRICE : 0;
                },
            },
        },

        pharmacyClinicId: {
            label: '增购药店',
            type: 'string',
            component: 'OrganPicker',
            componentProps: {
                nodeType: ClinicNodeType.CHAIN_SUB,
                isTrial: 0,
                hisType: HisType.PHARMACY,
            },
            rules: [
                { required: true, message: '必填' },
            ],
            visible(formData: any) {
                // 药诊合作诊所增购
                return formData.type === OtherOrderType.MEDICAL_DIAGNOSIS;
            },
        },

        editionId: {
            label: '当前版本',
            type: 'string',
            component: 'Input',
            readonly: true,
            visible(formData: any) {
                // 药诊合作诊所增购
                return formData.type === OtherOrderType.MEDICAL_DIAGNOSIS;
            },
            rules: [
                { required: true, message: '必填' },
            ],
            watch: {
                sources: ['pharmacyClinicId'],
                async handler(newValues: Array<any>, oldValues:Array<any>, field: Field) {
                    if (newValues[0]) {
                        const organInfo = field.form.getField('pharmacyClinicId').getBundle();
                        if (editionLabelOptions.basicVersion.values.includes(organInfo.editionId)) {
                            Toast.fail('专业版及以上的门店才可使用药诊互通功能');
                            field.modelValue = undefined;
                            return;
                        }
                        field.modelValue = formatEdition(organInfo.editionId);
                    }
                },
            },
        },
        clinicCount: {
            label: '当前合作诊所数量',
            type: 'string',
            component: 'Input',
            readonly: true,
            rules: [
                { required: true, message: '必填' },
            ],
            visible(formData: any) {
                // 药诊合作诊所增购
                return formData.type === OtherOrderType.MEDICAL_DIAGNOSIS;
            },
        },
        addCount: {
            label: '本次增购合作诊所数量',
            type: 'number',
            component: 'InputNumber',
            componentProps: {
                type: 'number',
            },
            rules: [
                { required: true, message: '必填' },
                { trigger: 'onChange', validator(fee: any) { return parseFloat(fee) > 0; }, message: '增购数量无效' },
            ],
            visible(formData: any) {
                // 药诊合作诊所增购
                return formData.type === OtherOrderType.MEDICAL_DIAGNOSIS;
            },
        },
        beginDate: {
            label: '开始时间',
            type: 'date',
            component: 'DatePicker',
            defaultValue(formData: any, field: Field, form: Form) {
                return dayjs().format('YYYY-MM-DD');
            },
            readonly: true,
            rules: [
                { required: true, message: '必填' },
            ],
            visible(formData: any) {
                // 药诊合作诊所增购
                return formData.type === OtherOrderType.MEDICAL_DIAGNOSIS;
            },
        },
        endDate: {
            label: '结束时间',
            type: 'date',
            component: 'DatePicker',
            readonly: true,
            defaultValue(formData: any, field: Field, form: Form) {
                const organInfo = form.getField('pharmacyClinicId').getBundle();
                if (organInfo && organInfo.editionEndDate) {
                    const { editionEndDate } = organInfo;
                    return dayjs(editionEndDate).format('YYYY-MM-DD');
                }
                return '';
            },
            rules: [
                { required: true, message: '必填' },
                {
                    validator(modelValue: string, rule: any, field: Field, form: Form) {
                        const beginDate = form.getField('beginDate').modelValue;
                        return validateEndDate(beginDate, modelValue);
                    },
                    message: '结束时间不能小于开始时间',
                },
            ],
            visible(formData: any) {
                // 药诊合作诊所增购
                return formData.type === OtherOrderType.MEDICAL_DIAGNOSIS;
            },
        },
        pharmacyReceivableFee: {
            label: '应收金额',
            type: 'number',
            component: 'Input',
            readonly: true,
            visible(formData: any) {
                // 药诊合作诊所增购
                return formData.type === OtherOrderType.MEDICAL_DIAGNOSIS;
            },
            watch: {
                sources: ['pharmacyClinicId', 'addCount'],
                async handler(newValues: Array<any>, oldValues:Array<any>, field: Field) {
                    if (!newValues[0] || !field.form.formData.editionId || field.form.formData.payModeName) {
                        return;
                    }
                    const postData = createPostDataForOtherChargeCoClinicSubmit(field.form.formData);
                    const calculateRsp = await ApprovalTicketAPI.preSubmitApprovalTicketOtherChargeCoClinicUsingPOST(postData);
                    field.modelValue = calculateRsp.receivableFee;
                    const clinicCount = `${calculateRsp.cooperationClinicCount || 0}个名额，已绑定${calculateRsp.bindCount || 0}家诊所`;
                    field.form.getField('clinicCount').modelValue = clinicCount;
                },
            },
        },
        remark: {
            label: '备注',
            type: 'string',
            component: 'Textarea',
            componentProps: {
                placeholder: '内部审批时可见',
                maxLength: '50',
            },
        },
    },
};

// 修改版本时长
export const modifyPeriodFormSchema = {
    type: 'object',
    component: 'SubmitForm',
    labelPosition: 'top',
    actions: {
        submit: {
            type: 'submit',
            component: 'Button',
            loading: false,
            componentProps: {
                text: '创建',
                type: 'primary',
            },
        },
    },
    properties: {
        clinicId: {
            label: '选择修改时长的诊所',
            type: 'string',
            component: 'OrganPicker',
            componentProps: {
                nodeType: ClinicNodeType.CHAIN_SUB,
            },
            rules: [
                { required: true, message: '必填' },
            ],
        },

        sopStatus: {
            label: '实施SOP完成状态',
            type: 'string',
            component: 'Input',
            componentProps: {
                placeholder: '请选择实施SOP完成状态',
            },
            watch: {
                sources: ['clinicId'],
                async handler(newValues: Array<any>, oldValues:Array<any>, field: Field) {
                    const selectOptions: any = {
                        0: '未提交',
                        1: '已提交',
                        2: '未提交',
                    };
                    let res: any = {};
                    try {
                        res = await SopSheetStaffAPI.getStaffSopSheetByClinicIdAndTypeUsingGET(newValues[0], 0);
                    } catch (e: any) {
                        console.error(e.message || e);
                    }
                    field.modelValue = selectOptions[res.status] || '无SOP记录';
                },
                immediate: false,
            },
            readonly: true,
        },

        currentPeriod: {
            label: '当前有效时间',
            type: 'string',
            component: 'Input',
            readonly: true,
            defaultValue(formData: any, field: Field, form: Form) {
                const organInfo = form.getField('clinicId').getBundle();
                if (organInfo) {
                    const { editionStatus, editionBeginDate, editionEndDate } = organInfo;
                    if (editionStatus === EditionStatus.EXPIRED) {
                        return '已过期';
                    }
                    // 未生效，展示生效时间
                    if (editionStatus === EditionStatus.UN_EFFEVTIVE) {
                        return dayjs(editionBeginDate).format('YYYY-MM-DD') + '~' + dayjs(editionEndDate).format('YYYY-MM-DD');
                    }
                    // 生效期内，展示今天 ~ 截止
                    if (editionStatus === EditionStatus.IN_EFFEVTIVE) {
                        return '今天~' + dayjs(editionEndDate).format('YYYY-MM-DD');
                    }
                }
                return '';
            },
        },
        trialBeginDate: {
            label: '试用开始时间',
            type: 'string',
            component: 'Input',
            readonly: true,
            defaultValue(formData: any, field: Field, form: Form) {
                const organInfo = form.getField('clinicId').getBundle();
                if (organInfo) {
                    const { trialBeginDate } = organInfo;
                    if (trialBeginDate) {
                        return dayjs(trialBeginDate).format('YYYY-MM-DD');
                    }
                }
                return '';
            },
            visible(formData: any) {
                return formData.trialBeginDate;
            },
        },

        createdDate: {
            label: '创建时间',
            type: 'string',
            component: 'Input',
            readonly: true,
            rules: [
                { required: true, message: '必填' },
            ],
            watch: {
                sources: ['clinicId'],
                async handler(newValues: Array<any>, oldValues:Array<any>, field: Field) {
                    const organInfo = field.form.getField('clinicId').getBundle();
                    if (organInfo && organInfo.editionBeginDate) {
                        const { createdDate } = organInfo;
                        field.modelValue = dayjs(createdDate).format('YYYY-MM-DD');
                    }
                },
                immediate: false,
            },
        },

        // 开始时间默认是今天，可以往后选最多2年，首购订单不做限制
        beginDate: {
            label: '开始时间',
            type: 'date',
            component: 'DatePicker',
            defaultValue(formData: any, field: Field, form: Form) {
                const organInfo = form.getField('clinicId').getBundle();
                if (organInfo && organInfo.editionBeginDate) {
                    const { editionStatus, editionBeginDate } = organInfo;
                    // 已经失效，不带入 默认值
                    if (editionStatus === EditionStatus.EXPIRED) {
                        return '';
                    }
                    return dayjs(editionBeginDate).format('YYYY-MM-DD');
                }
                return '';
            },
            componentProps: {
                valueFormat: 'YYYY-MM-DD',
                minDate(formData: any, field: Field, form: Form) {
                    const organInfo = form.getField('clinicId').getBundle();
                    // 如果是首购订单，修改时长不做任何限制，因参数必填且有默认值，设置10年上下
                    if (organInfo && organInfo.isFirstPurchase) {
                        return dayjs().subtract(10, 'year').toDate();
                    }
                    return dayjs().toDate();
                }, // 最小时间
                maxDate(formData: any, field: Field, form: Form) {
                    const organInfo = form.getField('clinicId').getBundle();
                    // 如果是首购订单，修改时长不做任何限制，因参数必填且有默认值，设置10年上下
                    if (organInfo && organInfo.isFirstPurchase) {
                        return dayjs().add(10, 'year').toDate();
                    }
                    if (organInfo && organInfo.editionBeginDate) {
                        const { editionBeginDate } = organInfo;
                        return dayjs(editionBeginDate).add(1, 'year').toDate();
                    }
                    return dayjs().add(1, 'year').toDate();
                }, // 最大时间
            },
            rules: [
                { required: true, message: '必填' },
            ],
        },

        // 默认值为 beginDate + unitCount 年，可以往后增加最多两年，首购订单不做限制
        endDate: {
            label: '结束时间',
            type: 'date',
            component: 'DatePicker',
            defaultValue(formData: any, field: Field, form: Form) {
                const organInfo = form.getField('clinicId').getBundle();
                if (organInfo && organInfo.editionEndDate) {
                    const { editionStatus, editionEndDate } = organInfo;
                    // 过期了，不带入默认值
                    if (editionStatus === EditionStatus.EXPIRED) {
                        return '';
                    }
                    return dayjs(editionEndDate).format('YYYY-MM-DD');
                }
                return '';
            },
            componentProps: {
                valueFormat: 'YYYY-MM-DD',
                maxDate(formData: any, field: Field, form: Form) {
                    const organInfo = form.getField('clinicId').getBundle();
                    // 如果是首购订单，修改时长不做任何限制，因参数必填且有默认值，设置10年上下
                    if (organInfo && organInfo.isFirstPurchase) {
                        return dayjs().add(10, 'year').toDate();
                    }
                    if (organInfo && organInfo.editionEndDate) {
                        const { editionEndDate } = organInfo;
                        return dayjs(editionEndDate).add(2, 'year').toDate();
                    }
                    return dayjs().add(2, 'year').toDate();
                }, // 最大时间
                minDate(formData: any, field: Field, form: Form) {
                    const organInfo = form.getField('clinicId').getBundle();
                    // 如果是首购订单，修改时长不做任何限制，因参数必填且有默认值，设置10年上下
                    if (organInfo && organInfo.isFirstPurchase) {
                        return dayjs().subtract(10, 'year').toDate();
                    }
                    return new Date();
                }, // 最小时间,
            },
            rules: [
                { required: true, message: '必填' },
                {
                    validator(modelValue: string, rule: any, field: Field, form: Form) {
                        const beginDate = form.getField('beginDate').modelValue;
                        return validateEndDate(beginDate, modelValue);
                    },
                    message: '结束时间不能小于开始时间',
                },
            ],
        },

        attachments: {
            label: '截图',
            type: 'array',
            defaultValue: [],
            component: 'Images',
            componentProps: {
                placeholder: '上传截图',
                // 上传目录
                rootDir: 'oa/order',
            },
        },

        remark: {
            label: '修改原因',
            type: 'string',
            component: 'Textarea',
            componentProps: {
                placeholder: '内部审批时可见',
            },
            rules: [
                { required: true, message: '必填' },
            ],
        },
    },
};

export const cloudExamFormSchema = {
    type: 'object',
    component: 'SubmitForm',
    labelPosition: 'top',
    actions: {
        submit: {
            type: 'submit',
            component: 'Button',
            loading: false,
            componentProps: {
                text: '创建',
                type: 'primary',
            },
        },
    },
    properties: {
        clinicType: {
            label: '新购类型',
            type: 'string',
            component: 'Input',
            readonly: true,
            defaultValue: '新开单店',
        },
        hisType: {
            label: '新购产品',
            type: 'string',
            component: 'Input',
            readonly: true,
            defaultValue: '诊所管家',
        },
        editionId: {
            label: '版本',
            type: 'string',
            component: 'Input',
            readonly: true,
            defaultValue: 'ABC云检版',
        },
        unitCount: {
            label: '购买年数',
            type: 'string',
            component: 'Input',
            readonly: true,
            defaultValue: '永久',
        },
        adminName: {
            label: '管理员姓名',
            type: 'string',
            component: 'Input',
            rules: [
                { required: true, message: '必填' },
            ],
        },
        adminMobile: {
            label: '管理员手机',
            type: 'string',
            component: 'Input',
            componentProps: {
                type: 'tel',
            },
            rules: [
                { required: true, message: '必填' },
                { trigger: 'onChange', validator(mobile: any) { return validateMobilePhone(mobile); }, message: '手机号无效' },
            ],
        },
        clinicName: {
            label: '门店名称',
            type: 'string',
            component: 'Input',
            rules: [
                { required: true, message: '必填' },
            ],
        },
        clinicAddress: {
            label: '门店地区',
            type: 'object',
            component: 'AreaPicker',
            rules: [
                { required: true, message: '必填' },
            ],
        },
        // 开始时间默认是今天，可以往后选最多两个月
        beginDate: {
            label: '开始时间',
            type: 'date',
            component: 'DatePicker',
            defaultValue: '{{today}}',
            componentProps: {
                valueFormat: 'YYYY-MM-DD',
                minDate: dayjs().toDate(), // 最小时间
                maxDate(formData: any, field: Field, form: Form) {
                    const { maxBeginDate } = form.getField('totalPrice')?.getBundle() || {};
                    if (!maxBeginDate) {
                        return dayjs().add(2, 'month').toDate();
                    }
                    return dayjs(maxBeginDate).toDate();
                }, // 最大时间
            },
            rules: [
                { required: true, message: '必填' },
            ],
        },
        totalPrice: {
            label: '版本金额',
            type: 'string',
            component: 'Input',
            readonly: true,
            defaultValue: '0',
        },
        receivableFee: {
            label: '收款金额',
            type: 'string',
            component: 'Input',
            readonly: true,
            defaultValue: '0',
        },
        remark: {
            label: '备注',
            type: 'string',
            component: 'Textarea',
            componentProps: {
                placeholder: '内部审批时可见',
                maxLength: 50,
            },
        },
    },
};
