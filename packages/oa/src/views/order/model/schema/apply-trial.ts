import { ClinicNodeType, HisType, PayAccount } from '@/utils/clinic';
import { useOrderStore } from '@/views/order/store';
import { validateMobilePhone } from '@/utils/validate';
import { Field } from '@/vendor/x-form/core/field';
import { getEditionOptions } from '@/views/order/model/options';
import dayjs from 'dayjs';

export const formSchema = {
    type: 'object',
    component: 'SubmitForm',
    labelPosition: 'top',
    actions: {
        submit: {
            type: 'submit',
            component: 'Button',
            loading: false,
            componentProps: {
                text: '创建',
                type: 'primary',
            },
        },
    },
    properties: {
        clinicType: {
            label: '试用类型',
            type: 'string',
            defaultValue: '单店',
            component: 'Input',
            disable: true,
        },
        hisType: {
            label: '产品类型',
            type: 'string',
            defaultValue: '',
            visible(formData: any) {
                return formData.clinicType !== ClinicNodeType.CHAIN_SUB;
            },
            component: 'Radio',
            componentProps: {
                options: [
                    { label: '诊所管家', value: HisType.NORMAL },
                    { label: '口腔管家', value: HisType.DENTISTRY },
                    { label: '眼视光管家', value: HisType.OPHTHALMOLOGY },
                    { label: '药店管家', value: HisType.PHARMACY },
                ],
            },
            rules: [
                { required: true, message: '必填' },
            ],
        },

        adminName: {
            label: '管理员姓名',
            type: 'string',
            component: 'Input',
            rules: [
                { required: true, message: '必填' },
            ],
        },

        adminMobile: {
            label: '管理员手机',
            type: 'string',
            component: 'Input',
            componentProps: {
                type: 'tel',
            },
            rules: [
                { required: true, message: '必填' },
                { trigger: 'onChange', validator(mobile: any) { return validateMobilePhone(mobile); }, message: '手机号无效' },
            ],
        },

        clinicName: {
            label: '门店名称',
            type: 'string',
            component: 'Input',
            rules: [
                { required: true, message: '必填' },
            ],
            visible(formData: any) {
                return formData.clinicType !== ClinicNodeType.CHAIN_ADMIN;
            },
        },

        clinicAddress: {
            label: '门店地区',
            type: 'object',
            component: 'AreaPicker',
            rules: [
                { required: true, message: '必填' },
            ],
            defaultValue() {
                return useOrderStore().lastAddressRegion;
            },
            visible(formData: any) {
                return formData.clinicType !== ClinicNodeType.CHAIN_ADMIN;
            },
        },
        payAccount: {
            label: '支付给',
            type: 'string',
            component: 'Radio',
            rules: [
                { required: true, message: '必填' },
            ],
            componentProps: {
                options: [
                    { label: '楚天云', value: PayAccount.CHU_TIAN_YUN },
                    { label: 'ABC', value: PayAccount.ABC },
                ],
            },
            visible(formData: any, field: Field, form: Form) {
                return formData.clinicAddress?.city?.id === '420100' || formData.payAccount || formData.payAccount === 0;
            },
            watch: {
                sources: ['clinicAddress'],
                handler(newValues: Array<any>, oldValues: Array<any>, field: Field) {
                    const clinicAddress = newValues[0];
                    if (clinicAddress?.city?.id !== '420100') {
                        field.modelValue = undefined;
                    }
                },
            },
        },

        editionId: {
            label: '版本',
            type: 'string',
            component: 'Radio',
            componentProps: {
                options(field: Field) {
                    if (field.form.formData.clinicType === ClinicNodeType.CHAIN_SUB) {
                        const hisType = field.form.getField('chainId')?.getBundle()?.hisType || field.form.formData.hisType;
                        return getEditionOptions(hisType, true);
                    }
                    const hisType = field.form.formData.hisType;
                    return getEditionOptions(hisType);
                },
            },
            rules: [
                { required: true, message: '必填' },
            ],
            watch: {
                sources: ['hisType'],
                async handler(newValues: Array<any>, oldValues:Array<any>, field: Field) {
                    if (newValues[0] !== oldValues[0]) {
                        field.modelValue = '';
                    }
                },
                immediate: false,
            },
            visible(formData: any) {
                return formData.clinicType !== ClinicNodeType.CHAIN_ADMIN;
            },
        },

        // 开始时间默认是今天，可以往后选最多两个月
        beginDate: {
            label: '开始时间',
            type: 'date',
            component: 'DatePicker',
            defaultValue: '{{today}}',
            componentProps: {
                valueFormat: 'YYYY-MM-DD',
            },
            rules: [
                { required: true, message: '必填' },
            ],
            visible(formData: any) {
                return formData.clinicType !== ClinicNodeType.CHAIN_ADMIN;
            },
            disable: true,
        },

        // 默认值为 beginDate + unitCount 年，可以往后增加最多两个月
        endDate: {
            label: '结束时间',
            type: 'date',
            component: 'DatePicker',
            defaultValue(formData: any) {
                return dayjs(formData.beginDate).add(7, 'day').format('YYYY-MM-DD');
            },
            componentProps: {
                valueFormat: 'YYYY-MM-DD',
            },
            rules: [
                { required: true, message: '必填' },
            ],
            disable: true,
            visible(formData: any) {
                return formData.clinicType !== ClinicNodeType.CHAIN_ADMIN;
            },
        },

        remark: {
            label: '备注',
            type: 'string',
            component: 'Textarea',
            componentProps: {
                placeholder: '内部审批时可见',
                maxLength: 50,
            },
        },
    },
};
