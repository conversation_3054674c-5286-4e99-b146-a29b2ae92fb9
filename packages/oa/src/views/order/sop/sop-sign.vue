<script lang="ts" setup>

import { Dialog, Toast } from 'vant';
import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { SopSheetStaffAPI } from '@/api/sop-sheet-staff-api';
import { useOnMenuShareAppMessage } from '@/composables/wx';
import Sign from '@/views/order/components/sign.vue';

const route = useRoute();
const sopDetailData = ref<any>({});
onMounted(async () => {
    const sopId: any = route.query?.sopId || '';
    sopId && await getSopDetailById(sopId);
    const shareLink = `${import.meta.env.VITE_APP_HELP_CENTER_DOMAIN}/m/unpack-service-sign?staffSopSheetId=${sopDetailData.value.id}`;
    await useOnMenuShareAppMessage({
        title: 'ABC产品服务报告', // 分享标题
        desc: '您有一份产品服务报告待确认签字', // 分享描述
        link: shareLink, // 分享链接；在微信上分享时，该链接的域名必须与企业某个应用的可信域名一致
        imgUrl: 'https://static-common-cdn.abcyun.cn/img/<EMAIL>', // 分享图标
    });
});
const getSopDetailById = async (id: string) => {
    let res: any = {};
    try {
        res = await SopSheetStaffAPI.getByIdUsingGET(id);
    } catch (e: any) {
        Toast.fail(e.message || e);
    }
    sopDetailData.value = res;
    sopDetailData.value.customerSign = sopDetailData.value.customerSign || localStorage.getItem(`customerSign__${sopDetailData.value.id}`) || '';
};
const showSignDialog = ref(false);
const onHandleClientSign = (url: string) => {
    sopDetailData.value.customerSign = url;
    localStorage.setItem(`customerSign__${sopDetailData.value.id}`, sopDetailData.value.customerSign);
    showSignDialog.value = false;
};
const onHandleSignCancelClick = () => {
    showSignDialog.value = false;
};

function handleShare() {
    const shareLink = `${import.meta.env.VITE_APP_HELP_CENTER_DOMAIN}/m/unpack-service-sign?staffSopSheetId=${sopDetailData.value.id}`;
    // eslint-disable-next-line no-undef
    wx.invoke(
        'shareWechatMessage', {
            title: 'ABC产品服务报告', // 分享标题
            desc: '您有一份产品服务报告待确认签字', // 分享描述
            link: shareLink, // 分享链接；在微信上分享时，该链接的域名必须与企业某个应用的可信域名一致
            imgUrl: 'https://static-common-cdn.abcyun.cn/img/<EMAIL>', // 分享图标
        }, (res: any) => {
            if (res.err_msg == 'shareWechatMessage:ok') {
                console.log();
            } else {
                Toast({
                    type: 'fail',
                    message: '分享失败',
                });
            }
        },
    );
}
const router = useRouter();
const viewClientDetail = () => {
    router.push({
        name: '@order/sop-detail',
        query: {
            sopId: sopDetailData.value.id,
        },
    });
};
const handleSubmitClick = async () => {
    if (!sopDetailData.value.customerSign) {
        Toast.fail('请签字确认！');
        return;
    }
    let res: any = {};
    try {
        res = await SopSheetStaffAPI.submitStaffSopSheetUsingPOST(sopDetailData.value, sopDetailData.value.id);
    } catch (e: any) {
        Toast.fail(e.message || e);
        return;
    }
    if (res && res.id) {
        Dialog.alert({
            title: '提交成功',
            message: '服务报告将推送给客户的管理员账号！',
        }).then(() => {
            localStorage.removeItem(`customerSign__${sopDetailData.value.id}`);
            router.push({
                name: '@order/sop',
                query: {
                    type: 1,
                },
            });
        });
    } else {
        Toast.fail(res.message || res);
    }
};
</script>
<template>
    <div class="sop-sign-wrapper">
        <div class="client-sign-wrapper">
            <p>
                <span>{{ `请管理员${sopDetailData.clinicAdmin?.name}签字` }}</span>
                <span class="client-sign-online" @click="handleShare">线上邀请客户签字</span>
            </p>
            <div class="client-sign-show-box" @click="showSignDialog = true">
                <img v-if="sopDetailData.customerSign" :src="sopDetailData.customerSign" alt="客户签字">
                <span v-else>点击进行签字</span>
            </div>
            <van-popup v-model:show="showSignDialog" overlay-class="client-sign-popup">
                <sign :visible="showSignDialog" @handleClientSubmit="onHandleClientSign" @handleCancelClick="onHandleSignCancelClick"></sign>
            </van-popup>
        </div>

        <div v-if="sopDetailData.status !== 1" class="sop-detail-footer">
            <van-button @click="viewClientDetail">查看服务项</van-button>
            <van-button type="primary" @click="handleSubmitClick">提交</van-button>
        </div>
        <div v-else class="sop-detail-footer">
            <van-button type="primary" disabled>已提交</van-button>
        </div>
    </div>
</template>
<style lang="scss">
.sop-sign-wrapper {
    background: #fff;
    height: calc(100vh - 58px);
    position: relative;

    .client-sign-wrapper {
        padding: var(--oa-padding-12);
        border-top: 1px solid #f5f5f7;

        .client-sign-online {
            color: #52bc98;
        }

        > p {
            padding: var(--oa-padding-12) 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
    }

    .client-sign-show-box {
        width: 100%;
        height: 150px;
        line-height: 150px;
        text-align: center;
        background: #9c9d9f;

        >img {
            width: 100%;
            height: 100%;
        }

        >span {
            color: #fff;
            font-size: 16px;
        }
    }

    .van-popup.van-popup--center {
        width: 100%;
        height: 100%;
    }

    .sop-detail-footer {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 24px;
        background: #fff;

        > button {
            width: 120px;
        }

        button + button {
            margin-left: 12px;
        }
    }
}
</style>