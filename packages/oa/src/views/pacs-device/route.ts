import { RouteRecordRaw } from 'vue-router';
import PacsDevice from './index.vue';
import { PermissionLisDevice } from './permission';

export default [
    {
        path: 'pacs-device',
        name: '@pacsDevice',
        component: PacsDevice,
        meta: {
            roles: PermissionLisDevice,
            isEntry: true,
            name: 'pacs设备管理',
            icon: 'Help',
        },
    },
] as RouteRecordRaw[];