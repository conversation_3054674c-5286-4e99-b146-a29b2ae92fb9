<script setup lang="ts">

import KvCard from '@/components/kv-card.vue';
import { reactive } from 'vue';

const kvObj = reactive({
    id: 10007,
    userId: 'bubble',
    name: '袁列诚',
    position: '前端开发组组长',
    mobile: '15608000252',
    gender: '1',
    email: '<EMAIL>',
    avatar: 'http://wework.qpic.cn/bizmail/Rm8Ou4FH2jk6uRh14AxiaAlOgKXw6YMliaepEIt8BKRqy5v5pKKeJWwg/0',
    telephone: '',
    qrCode: 'https://open.work.weixin.qq.com/wwopen/userQRCode?vcode=vcbd137126bcca9a7e',
    alias: 'Bubble',
    address: '',
    thumbAvatar: 'http://wework.qpic.cn/bizmail/Rm8Ou4FH2jk6uRh14AxiaAlOgKXw6YMliaepEIt8BKRqy5v5pKKeJWwg/100',
    departments: [
        {
            id: 11,
            name: '前端开发组',
        },
        {
            id: 2,
            name: '技术研发中心',
        },
        {
            id: 1,
            name: '成都字节流科技有限公司',
            members: [
                {
                    name: '牛逼',
                },
                {
                    name: '牛逼',
                },
            ],
        },
    ],
    tags: [
        {
            id: 1,
            name: '运营告警组',
        },
        {
            id: 2,
            name: '开发组',
        },
        {
            id: 7,
            name: '活跃同步',
        },
        {
            id: 8,
            name: '事故通报',
        },
        {
            id: 9,
            name: '用户规模同步',
        },
    ],
});
</script>
<template>
    <kv-card :info="kvObj">
    </kv-card>
</template>

<style scoped>

</style>
