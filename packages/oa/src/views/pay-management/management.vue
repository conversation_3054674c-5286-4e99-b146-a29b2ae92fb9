<script lang="ts" setup>
import { useRoute, useRouter } from 'vue-router';
import { computed, onMounted, onUnmounted, ref } from 'vue';
import { RegionMap, RegionOptions } from '@/utils/clinic';
import {
    TabEnum,
    UpdatePayConfigDetailType,
    PayManageTabs,
    DeliveryStatusOptions,
    TrainingStatusOptions,
    TrainingStatusEnum,
    DeliveryStatusEnum,
    ActiveStatusOptions,
    LossStatusOptions,
    formatActiveStatus,
    formatLossStatus,
} from '@/views/pay-management/model';
import { formatDate } from '@abc-oa/utils';
import { ElMessage, ElMessageBox, ElInput } from 'element-plus';
import { usePayManage } from '@/views/pay-management/hook/pay-manage';
import { usePayTable } from '@/views/pay-management/hook/pay-table';
import { usePayHook } from '@/views/pay-management/hook/pay';
import { WechatPayAPI } from '@/api/wechat-pay-api';
import { useUserStore } from '@/store/user';
import { PermissionPayManagementOpen } from '@/views/pay-management/permission';
import EditCustomTagsDialog from './components/edit-custom-tags-dialog.vue';

const userStore = useUserStore();
const {
    query,
    activeTab,
    isAbcPay,
    isWechatPay,
    updateActiveTab,
    reset,
} = usePayHook();
const {
    handlePayClose,
    reset: resetPayManage,
} = usePayManage();

const {
    tableData,
    tableColumn,
    loading,
    pagination,
    handlePageChange,
    fetchPayList,
    reset: resetPayTable,
} = usePayTable();
const route = useRoute();
const tab = route.query.tab as string;
onMounted(() => {
    handleTabClick({
        props: {
            name: tab || PayManageTabs[0].name,
        },
    });
    getCustomTagOptions();
});
const isToDetail = ref(false);
const router = useRouter();
const hasOpenStorePermission = computed(() => {
    const roles = userStore.roles;
    return PermissionPayManagementOpen.some((role: string) => roles.includes(role));
});
onUnmounted(() => {
    const isNavigatingToChild = router.currentRoute.value.name?.toString().startsWith('@pay-management');
    if (!isNavigatingToChild) {
        reset();
        resetPayTable();
        resetPayManage();
    }
});

function handleCloseClick(row: any) {
    ElMessageBox.confirm(`<p>确认关闭？<span style="color: red">关闭后该门店将无法使用【${
        isAbcPay.value ? 'ABC支付' : isWechatPay.value ? '微信支付' : '医保微信移动支付'
    }】！</span></p>门店：${row.clinicName}，门店ID：${row.id}`, '', {
        confirmButtonText: '取消',
        cancelButtonText: '确定',
        dangerouslyUseHTMLString: true,
        cancelButtonClass: 'el-button--primary',
        confirmButtonClass: 'el-message-confirm-button',
    }).catch(() => {
        handlePayClose(row.id, row.clinicId);
    });
}

const placeholder = computed(() => (isAbcPay.value ? '通联支付商户号' : isWechatPay.value ? '微信支付商户号' : '医保移动支付商户号'));

function handleOpenClick(row?: any) {
    isToDetail.value = true;
    router.push({ name: '@pay-management/clinic-open', query: { tab: activeTab.value, id: row?.id, clinicId: row?.clinicId } });
}
// 备注弹窗相关状态
const showRemarkDialog = ref(false);
const currentRow = ref<any>({});
const editingRemark = ref('');

const handleEditRemark = (row: any) => {
    // 设置当前行和备注值
    currentRow.value = row;
    editingRemark.value = row.remark || '';
    // 打开对话框
    showRemarkDialog.value = true;
};

// 处理备注提交
const handleRemarkSubmit = async () => {
    try {
        const remark = editingRemark.value.trim();
        
        // 调用API保存备注
        const response = await WechatPayAPI.updateWechatPayConfigDetailUsingPUT(
            currentRow.value.id, 
            {
                clinicId: currentRow.value.clinicId,
                remark,
                type: UpdatePayConfigDetailType.REMARK,
            },
        );
        if (response?.id) {
            ElMessage.success('备注更新成功');
            // 更新行数据中的备注
            currentRow.value.remark = remark;
            // 关闭对话框
            showRemarkDialog.value = false;
        } else {
            ElMessage.error('备注更新失败');
        }
    } catch (error) {
        console.error('更新备注出错:', error);
        ElMessage.error('备注更新失败');
    }
};

// 取消备注编辑
const handleRemarkEditCancel = () => {
    showRemarkDialog.value = false;
    editingRemark.value = '';
};

// 编辑标签弹窗相关状态
const showEditTagsDialog = ref(false);

// 处理编辑标签
const handleEditCustomTags = () => {
    showEditTagsDialog.value = true;
};

// 刷新标签选项和表格数据
const handleTagsRefresh = () => {
    getCustomTagOptions();
    fetchPayList();
};
function handleTabClick(tab: any) {
    updateActiveTab(tab.props.name);
    router.replace({ name: '@pay-management', query: { tab: tab.props.name } });
    fetchPayList();
}
function showCloseBtn(row: any) {
    return !isWechatPay.value || row.chainId === row.clinicId;
}

// 处理培训状态变更
async function handleTrainingStatusChange(row: any) {
    try {
        const newStatus = row.trainingStatus === TrainingStatusEnum.PENDING ? TrainingStatusEnum.PENDING : TrainingStatusEnum.COMPLETED;
        const statusText = newStatus === TrainingStatusEnum.PENDING ? '待培训' : '已培训';

        ElMessageBox.confirm(`确认将培训状态修改为"${statusText}"？`).then(async () => {
            await WechatPayAPI.updateTrainedStatusUsingPUT(row.clinicId, row.id, newStatus);
            ElMessage.success('培训状态修改成功');
            fetchPayList();
        }).catch(() => {
            row.trainingStatus = row.trainingStatus === TrainingStatusEnum.PENDING ? TrainingStatusEnum.COMPLETED : TrainingStatusEnum.PENDING;
            return false;
        });
    } catch (error: any) {
        ElMessage.error(error.message || '培训状态修改失败');
    }
}

// 处理发货状态变更
async function handleDeliveryStatusChange(row: any) {
    try {
        const newStatus = row.deliveryStatus === DeliveryStatusEnum.PENDING ? DeliveryStatusEnum.PENDING : DeliveryStatusEnum.COMPLETED;
        const statusText = newStatus === DeliveryStatusEnum.PENDING ? '待发货' : '已发货';

        ElMessageBox.confirm(`确认将发货状态修改为"${statusText}"？`).then(async () => {
            await WechatPayAPI.updateDeliveryStatusUsingPUT(row.clinicId, newStatus, row.id);
            ElMessage.success('发货状态修改成功');
            fetchPayList();
        }).catch(() => {
            row.deliveryStatus = row.deliveryStatus === DeliveryStatusEnum.PENDING ? DeliveryStatusEnum.COMPLETED : DeliveryStatusEnum.PENDING;
            return false;
        });
    } catch (error: any) {
        ElMessage.error(error.message || '发货状态修改失败');
    }
}
const CustomTagOptions = ref<any>([]);
async function getCustomTagOptions() {
    let res: any = {};
    try {
        res = await WechatPayAPI.listCustomTagUsingGET();
    } catch (e: any) {
        ElMessage.error(e.message || e);
    }
    console.log(res.rows);
    if (res && res.rows) {
        CustomTagOptions.value = res.rows.map((item: any) => ({
            ...item,
            sourceTagId: item.id,
            value: item.id,
            label: item.name,
        }));
    }
}
async function updateCustomTag(row: any) {
    if (row.sourceTagId === row.customTagId) {
        return;
    }
    try {
        await WechatPayAPI.updateWechatPayConfigDetailUsingPUT(row.id, {
            tagIds: [row.customTagId].filter(Boolean),
            clinicId: row.clinicId,
            type: UpdatePayConfigDetailType.TAG,
        });
        ElMessage.success('自定义标签修改成功');
        fetchPayList();
    } catch (error: any) {
        ElMessage.error(error.message || '自定义标签修改失败');
    }
}

// 显示发货状态的条件：仅通联商户号数据显示
function showDeliveryStatus(row: any) {
    return !row.businessScene;
}
</script>
<template>
    <div class="pay-management-container">
        <el-card class="pay-management-wrapper">
            <el-tabs v-model="activeTab" class="demo-tabs" @tab-click="handleTabClick">
                <el-tab-pane
                    v-for="item in PayManageTabs"
                    :key="item.name"
                    :label="item.label"
                    :name="item.name"
                >
                </el-tab-pane>
            </el-tabs>
            <el-form class="flex-between pay-management-toolbar" :model="query">
                <el-space wrap>
                    <el-form-item label="数据分区">
                        <el-select
                            v-model="query.regionId"
                            style="width: 120px;"
                            @change="fetchPayList"
                        >
                            <el-option
                                v-for="region in RegionOptions"
                                :key="region.value"
                                :label="region.label"
                                :value="region.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="开通时间">
                        <el-date-picker
                            v-model="query.openedRange"
                            style="width: 240px;"
                            placeholder="请选择"
                            clearable
                            range-separator="至"
                            start-placeholder="开始时间"
                            end-placeholder="截止时间"
                            type="daterange"
                            format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD"
                            @change="fetchPayList"
                        ></el-date-picker>
                    </el-form-item>
                    <el-form-item label="流失时间">
                        <el-date-picker
                            v-model="query.lossRange"
                            style="width: 240px;"
                            placeholder="请选择"
                            clearable
                            type="daterange"
                            format="YYYY-MM-DD"
                            range-separator="至"
                            start-placeholder="开始时间"
                            end-placeholder="截止时间"
                            value-format="YYYY-MM-DD"
                            @change="fetchPayList"
                        ></el-date-picker>
                    </el-form-item>
                    <el-form-item>
                        <el-select
                            ref="customTagSelect"
                            v-model="query.customTagId"
                            style="width: 100%;"
                            filterable
                            clearable
                            placeholder="自定义标签"
                            @change="fetchPayList"
                        >
                            <el-option
                                v-for="item in CustomTagOptions"
                                :key="item.value"
                                :value="item.value"
                                :label="item.label"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-input
                            v-model="query.keyword"
                            style="width: 260px;"
                            placeholder="开通门店、ID"
                            clearable
                            @change="fetchPayList"
                        ></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-input
                            v-model="query.subMchId"
                            style="width: 260px;"
                            :placeholder="placeholder"
                            clearable
                            @change="fetchPayList"
                        ></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-select
                            v-model="query.trainingStatus"
                            style="width: 120px;"
                            placeholder="培训状态"
                            clearable
                            @change="fetchPayList"
                        >
                            <el-option
                                v-for="option in TrainingStatusOptions"
                                :key="option.value"
                                :label="option.label"
                                :value="option.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-select
                            v-model="query.deliveryStatus"
                            style="width: 120px;"
                            placeholder="发货状态"
                            clearable
                            @change="fetchPayList"
                        >
                            <el-option
                                v-for="option in DeliveryStatusOptions"
                                :key="option.value"
                                :label="option.label"
                                :value="option.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-select
                            v-model="query.activeStatus"
                            style="width: 120px;"
                            placeholder="激活状态"
                            clearable
                            @change="fetchPayList"
                        >
                            <el-option
                                v-for="option in ActiveStatusOptions"
                                :key="option.value"
                                :label="option.label"
                                :value="option.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-select
                            v-model="query.lossStatus"
                            style="width: 120px;"
                            placeholder="流失状态"
                            clearable
                            @change="fetchPayList"
                        >
                            <el-option
                                v-for="option in LossStatusOptions"
                                :key="option.value"
                                :label="option.label"
                                :value="option.value"
                            />
                        </el-select>
                    </el-form-item>
                </el-space>
                <el-button v-if="hasOpenStorePermission" type="success" @click="handleOpenClick()">
                    <el-icon><Plus /></el-icon>开通门店
                </el-button>
                <el-button type="primary" @click="handleEditCustomTags">
                    编辑标签
                </el-button>
            </el-form>
            <el-table
                v-loading="loading"
                :data="tableData"
                scrollbar-always-on
            >
                <el-table-column
                    v-for="column in tableColumn"
                    :key="column.dataIndex"
                    :prop="column.dataIndex"
                    :label="column.title"
                    :width="column.width"
                    :fixed="column.fixed"
                >
                    <template #default="{ row }">
                        <el-space v-if="column.slots === 'operation'">
                            <el-link v-if="activeTab === TabEnum.abc" type="primary" @click="handleEditRemark(row)">备注</el-link>
                            <el-link v-if="hasOpenStorePermission" type="primary" @click="handleOpenClick(row)">修改</el-link>
                            <el-link v-if="showCloseBtn(row)" type="danger" @click="handleCloseClick(row)">关闭</el-link>
                        </el-space>
                        <template v-else-if="column.slots === 'trainingStatus'">
                            <el-select
                                v-if="!row.businessScene"
                                v-model="row.trainingStatus"
                                style="width: 90px;"
                                size="small"
                                @change="(val) => handleTrainingStatusChange(row)"
                            >
                                <el-option
                                    v-for="option in TrainingStatusOptions"
                                    :key="option.value"
                                    :label="option.label"
                                    :value="option.value"
                                />
                            </el-select>
                        </template>
                        <template v-else-if="column.slots === 'deliveryStatus'">
                            <el-select
                                v-if="showDeliveryStatus(row)"
                                v-model="row.deliveryStatus"
                                style="width: 90px;"
                                size="small"
                                @change="(val) => handleDeliveryStatusChange(row)"
                            >
                                <el-option
                                    v-for="option in DeliveryStatusOptions"
                                    :key="option.value"
                                    :label="option.label"
                                    :value="option.value"
                                />
                            </el-select>
                        </template>
                        <template v-else-if="column.slots === 'customTag'">
                            <el-select
                                v-if="showDeliveryStatus(row)"
                                v-model="row.customTagId"
                                size="small"
                                clearable
                                placeholder="请选择自定义标签"
                                @change="updateCustomTag(row)"
                            >
                                <el-option
                                    v-for="option in CustomTagOptions"
                                    :key="option.value"
                                    :label="option.label"
                                    :value="option.value"
                                />
                            </el-select>
                        </template>
                        <template v-else-if="column.slots === 'openStatus'">
                            已开通
                        </template>
                        <template v-else-if="column.slots === 'activeStatus'">
                            {{ formatActiveStatus(row.activeStatus) }}
                        </template>
                        <template v-else-if="column.slots === 'lossStatus'">
                            {{ formatLossStatus(row.lossStatus) }}
                        </template>
                        <template v-else-if="column.slots === 'time'">
                            {{ formatDate(row[column.dataIndex]) }}
                        </template>
                        <template v-else-if="column.slots === 'mchId'">{{ row.businessScene ? '-': row.subMchId }}</template>
                        <template v-else-if="column.slots === 'subMchId'">{{ row.businessScene ? row.subMchId: '-' }}</template>
                        <span v-else>{{ row[column.dataIndex] }}</span>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination
                    :current-page="pagination.page"
                    background
                    layout="prev, pager, next, total"
                    :total="pagination.total"
                    @update:current-page="handlePageChange"
                />
            </div>
        </el-card>
        
        <!-- 备注编辑对话框 -->
        <el-dialog
            v-model="showRemarkDialog"
            width="400px"
            title="编辑备注"
            custom-class="remark-dialog-wrapper"
            @closed="handleRemarkEditCancel"
        >
            <el-input
                v-model="editingRemark"
                placeholder="请输入备注"
                clearable
                :autosize="{ minRows: 3, maxRows: 6 }"
                type="textarea"
                size="small"
            ></el-input>
            <template #footer>
                <el-button type="primary" size="small" @click="handleRemarkSubmit">确定</el-button>
                <el-button size="small" @click="handleRemarkEditCancel">取消</el-button>
            </template>
        </el-dialog>

        <!-- 编辑标签弹窗 -->
        <EditCustomTagsDialog
            v-if="showEditTagsDialog"
            v-model:visible="showEditTagsDialog"
            @refresh="handleTagsRefresh"
        />
    </div>
</template>
<style lang="scss">
.pay-management-wrapper {
    height: calc(100% - 16px);

    .pagination {
        display: flex;
        justify-content: flex-end;
        margin-top: 24px;
    }
}

.el-button.el-message-confirm-button {
    --el-button-font-weight: var(--el-font-weight-primary);
    --el-button-border-color: var(--el-border-color);
    --el-button-bg-color: var(--el-fill-color-blank);
    --el-button-text-color: var(--el-text-color-regular);
    --el-button-disabled-text-color: var(--el-disabled-text-color);
    --el-button-disabled-bg-color: var(--el-fill-color-blank);
    --el-button-disabled-border-color: var(--el-border-color-light);
    --el-button-divide-border-color: rgba(255, 255, 255, .5);
    --el-button-hover-text-color: var(--el-color-primary);
    --el-button-hover-bg-color: var(--el-color-primary-light-9);
    --el-button-hover-border-color: var(--el-color-primary-light-7);
    --el-button-active-text-color: var(--el-button-hover-text-color);
    --el-button-active-border-color: var(--el-color-primary);
    --el-button-active-bg-color: var(--el-button-hover-bg-color);
    --el-button-outline-color: var(--el-color-primary-light-5);
    --el-button-hover-link-text-color: var(--el-color-info);
    --el-button-active-color: var(--el-text-color-primary);
}
</style>
