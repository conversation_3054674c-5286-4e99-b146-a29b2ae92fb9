<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { ElMessage, FormInstance } from 'element-plus';
import OSSUtil from '@/utils/oss';
import { handleDeleteOSSFile } from '@/views/print-config/print-driver/utils';
import { PrintConfigApi } from '@/api/print-config';
import { usePrintConfigStore } from '@/views/print-config/store';
import { useUserStore } from '@/store/user';
import { PermissionPrintConfigView } from '@/views/print-config/permission';

const props = defineProps({
    modelValue: {
        type: Boolean,
        required: true,
    },
    isEditDialog: {
        type: Boolean,
        required: true,
    },
    editRowItem: {
        type: Object,
        default: () => ({}),
    },
    driverId: {
        type: String,
        default: '',
    },
});

const emit = defineEmits(['update:modelValue', 'editBrandsOrModels', 'submitAddDriver']);

const driverData = ref<AbcAPI.PrintConfigAddDriver>({
    name: '', // 驱动名称
    brandId: '', // 品牌id
    modelIds: [], // 型号ids
    supportSystem: [], // 支持的操作系统
    remark: '', // 备注
    driverUrl: '', // 驱动OSS链接
    ossId: '', // 驱动文件名称，OSS存储的文件名
});
const ruleFormRef = ref<FormInstance>();
const formRules = ref({
    name: [{ required: true, message: '请输入打印机名称', trigger: 'blur' }],
    brandId: [{ required: true, message: '请选择打印机品牌', trigger: 'change' }],
    modelIds: [{ required: true, message: '请选择打印机型号', trigger: 'change' }],
    supportSystem: [{ required: true, message: '请选择支持的电脑操作系统', trigger: 'change' }],
    ossId: [{ validator: validateDriverName }],
});
const isSubmit = ref(false);
const modelsSelectorRef = ref();
const showConfigButton = ref(false);
const isUploadLoading = ref(false);
const cacheUploadOssIdList = ref<Array<string>>([]); // 暂存已上传的文件，如果最终不需要保存，则需要统一删除
const cacheOssId = ref(''); // 暂存修改时的驱动，如果更改了驱动，则需要删除，反之则不删除

const printConfigStore = usePrintConfigStore();
const { fetchModelOptions, changeSelectedBrandId } = printConfigStore;

const currentBrandOptions = computed(() => printConfigStore.currentBrandOptions);
const currentModelOptions = computed(() => printConfigStore.currentModelOptions);

const visible = computed({
    get() {
        return props.modelValue;
    },
    set(val) {
        emit('update:modelValue', val);
    },
});

onMounted(() => {
    const userStore = useUserStore();
    const roles = userStore?.roles;
    if (!roles || !roles.length) {
        showConfigButton.value = false;
    }
    showConfigButton.value = PermissionPrintConfigView.some((role: string) => roles.includes(role));
});

watch(currentBrandOptions, (val) => {
    if (!val.find(it => it.id === driverData.value.brandId)) {
        driverData.value.brandId = '';
        driverData.value.modelIds = [];
        changeSelectedBrandId('');
        fetchModelOptions('');
    }
});
watch(currentModelOptions, (val) => {
    const resModelIds: Array<string> = [];
    driverData.value.modelIds.forEach(modelId => {
        if (val.find(it => it.id === modelId)) {
            resModelIds.push(modelId);
        }
    });
    driverData.value.modelIds = resModelIds;
});

/**
 * 确定回调
 */
function handleSubmit(formEl: any) {
    if (!formEl) return;
    formEl.validate(async (valid: any) => {
        if (valid) {
            try {
                if (props.isEditDialog) {
                    await PrintConfigApi.editDriver(props.editRowItem.id, driverData.value);
                    ElMessage.success('修改成功');
                } else {
                    await PrintConfigApi.addDriver(driverData.value);
                    ElMessage.success('添加成功');
                }
                isSubmit.value = true;
            } catch (e: any) {
                console.error(e);
                isSubmit.value = false;
                ElMessage.error(`${props.isEditDialog ? '修改' : '添加'}失败，原因：` + e.message);
            } finally {
                emit('submitAddDriver');
                visible.value = false;
            }
        }
    });
}

/**
 * 关闭弹窗回调
 */
async function handleClose() {
    // 通过非保存关闭的弹窗，即取消时，需要删除已经上传的驱动
    let needDeleteList = [...cacheUploadOssIdList.value];
    if (isSubmit.value) {
        needDeleteList = needDeleteList.filter((it) => it !== driverData.value.ossId);

        if (props.isEditDialog && cacheOssId.value && cacheOssId.value !== driverData.value.ossId) {
            needDeleteList.push(cacheOssId.value);
        }
    }
    if (needDeleteList.length) {
        for (let i = 0; i < needDeleteList.length; i++) {
            handleDeleteOSSFile(needDeleteList[i]);
        }
    }

    driverData.value = {
        name: '', // 驱动名称
        brandId: '', // 品牌id
        modelIds: [], // 型号ids
        supportSystem: [], // 支持的操作系统
        remark: '', // 备注
        driverUrl: '', // 驱动OSS链接
        ossId: '', // 驱动文件名称，OSS存储的文件名
    };
    isSubmit.value = false;
    changeSelectedBrandId('');
    await fetchModelOptions('');
}

/**
 * 自定义校验上传文件
 */
function validateDriverName(rule: any, value: any, callback: any) {
    if (driverData.value.driverUrl === '') {
        callback(new Error('请上传驱动'));
    } else {
        callback();
    }
}

/**
 * 选择文件回调
 */
async function handleUploaderAfterRead(fileRead: any) {
    try {
        isUploadLoading.value = true;
        const { file = {} } = fileRead;

        const { url } = await OSSUtil.upload({
            bucket: 'abc-tool-files',
            region: import.meta.env.VITE_APP_OSS_REGION,
            rootDir: '打印机驱动程序',
            fileName: file.name || '',
        }, file);
        driverData.value.driverUrl = url || '';
        driverData.value.ossId = file.name || '';
        cacheUploadOssIdList.value.push(file.name);
    } catch (e) {
        console.error(e);
    } finally {
        isUploadLoading.value = false;
    }
}

/**
 * 根据品牌请求型号列表
 */
function fetchModelList(brandId: string) {
    driverData.value.modelIds = [];
    changeSelectedBrandId(brandId);
    fetchModelOptions(brandId);
}

/**
 * 弹窗打开回调
 */
function handleOpen() {
    cacheUploadOssIdList.value = [];
    cacheOssId.value = '';
    
    if (props.isEditDialog) {
        const { brandId = '', ossId = '' } = props.editRowItem;
        changeSelectedBrandId(brandId);
        fetchModelOptions(brandId);
        driverData.value = props.editRowItem as AbcAPI.PrintConfigAddDriver;
        cacheOssId.value = ossId;
    } else {
        fetchModelOptions('');
        changeSelectedBrandId('');
        driverData.value = {
            name: '', // 驱动名称
            brandId: '', // 品牌id
            modelIds: [], // 型号ids
            supportSystem: [], // 支持的操作系统
            remark: '', // 备注
            driverUrl: '', // 驱动OSS链接
            ossId: '', // 驱动文件名称，OSS存储的文件名
        };
    }

    isSubmit.value = false;
}

function handleBrandChange(val: string) {
    if (val === 'settings') {
        emit('editBrandsOrModels', 1);
        return;
    }
    driverData.value.brandId = val;
    fetchModelList(val);
}

function handleModelChange(val: Array<string>) {
    driverData.value.modelIds = val.filter(it => it !== 'settings');
    const settingsIndex = val.indexOf('settings');
    if (settingsIndex > -1) {
        // 多选不会关闭下拉框, 需要延迟后手动关闭, 不加延迟或者加 nextTick 都关不掉
        setTimeout(() => {
            modelsSelectorRef.value.blur();
        }, 0);
        emit('editBrandsOrModels', 2);
    }
}
</script>

<template>
    <el-dialog
        v-model="visible"
        title="新增驱动"
        append-to-body
        :width="700"
        @close="handleClose"
        @open="handleOpen"
    >
        <el-form
            ref="ruleFormRef"
            style="width: 100%;"
            :model="driverData"
            label-width="auto"
            label-position="left"
            :rules="formRules"
        >
            <el-form-item label="打印机名称" prop="name">
                <el-input v-model="driverData.name" placeholder="建议按照型号命名，方便查找" />
            </el-form-item>
            <el-form-item label="支持的打印机品牌" prop="brandId">
                <el-select
                    :model-value="driverData.brandId"
                    clearable
                    filterable
                    style="width: 100%;"
                    @change="handleBrandChange"
                >
                    <el-option
                        v-for="item in currentBrandOptions"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    />
                    <el-option v-if="showConfigButton" value="settings">
                        <div class="driver-dialog-selector-footer-wrapper">
                            <el-button
                                icon="Setting"
                                link
                            ></el-button>
                        </div>
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="支持的打印机型号" prop="modelIds">
                <el-select
                    ref="modelsSelectorRef"
                    :model-value="driverData.modelIds"
                    clearable
                    multiple
                    filterable
                    style="width: 100%;"
                    @change="handleModelChange"
                >
                    <el-option
                        v-for="item in currentModelOptions"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    />
                    <el-option v-if="showConfigButton" value="settings">
                        <div class="driver-dialog-selector-footer-wrapper">
                            <el-button
                                icon="Setting"
                                link
                                class="driver-dialog-selector-footer-btn"
                            ></el-button>
                        </div>
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="支持的电脑操作系统" prop="supportSystem">
                <el-checkbox-group v-model="driverData.supportSystem">
                    <el-checkbox :label="0">Win7</el-checkbox>
                    <el-checkbox :label="1">Win8</el-checkbox>
                    <el-checkbox :label="2">Win10及以上</el-checkbox>
                </el-checkbox-group>
            </el-form-item>
            <el-form-item label="备注" prop="remark">
                <el-input
                    v-model="driverData.remark"
                    :autosize="{ minRows: 3, maxRows: 6 }"
                    type="textarea"
                    placeholder="对驱动用法的备注说明"
                />
            </el-form-item>
            <el-form-item label="上传安装驱动包" prop="ossId">
                <span v-if="driverData.ossId" style="margin-right: 16px;">{{ driverData.ossId }}</span>
                <van-uploader
                    accept="*"
                    :show-upload="true"
                    :preview-image="false"
                    :after-read="handleUploaderAfterRead"
                >
                    <span v-if="driverData.ossId" class="driver-dialog-upload-text">重新上传</span>
                    <span v-else class="driver-dialog-upload-text">上传</span>
                </van-uploader>
            </el-form-item>
        </el-form>

        <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" :loading="isUploadLoading" @click="handleSubmit(ruleFormRef)">
                    确定
                </el-button>
                <el-button @click="visible = false">取消</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<style>
.driver-dialog-selector-footer-wrapper {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    width: 100%;
    height: 100%;
    position: relative;
}

.driver-dialog-selector-footer-btn {
    position: absolute;
    right: -16px;
}

.driver-dialog-upload-text {
    color: #409eff;
    cursor: pointer;
    display: inline-block;
    height: 100%;
}

.driver-dialog-upload-text + .van-uploader__input {
    cursor: default !important;
}
</style>
