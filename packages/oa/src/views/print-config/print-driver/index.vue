<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { Search } from '@element-plus/icons-vue';
import dayjs from 'dayjs';
import AddDriverDialog from '@/views/print-config/print-driver/components/add-driver-dialog.vue';
import { handleDeleteOSSFile } from '@/views/print-config/print-driver/utils';
import { PrintConfigApi } from '@/api/print-config';
import { Action, ElMessage, ElMessageBox } from 'element-plus';
import _ from 'lodash';
import { usePrintConfigStore } from '@/views/print-config/store';
import EditBrandDialog from '@/views/print-config/print-driver/components/edit-brand-dialog.vue';
import EditModelDialog from '@/views/print-config/print-driver/components/edit-model-dialog.vue';

const SupportSystemEnum = {
    0: 'Win7',
    1: 'Win8',
    2: 'Win10及以上',
};

const tableLoading = ref(false);
const addDriverDialogVisible = ref(false);
const editBrandDialogVisible = ref(false);
const editModelDialogVisible = ref(false);
const isEditDialog = ref(false);
const editRowItem = ref({});
const tablePagination = ref({ currentPage: 1, limit: 12, total: 0, brand: '', key: '' });
const tableDataList = ref<Array<AbcAPI.PrintConfigDriverVO>>([]);

const printConfigStore = usePrintConfigStore();
const { fetchBrandOptions } = printConfigStore;

const currentBrandOptions = computed(() => printConfigStore.currentBrandOptions);
const currentSelectedBrandId = computed(() => printConfigStore.currentSelectedBrandId);

onMounted(() => {
    fetchBrandOptions();
    fetchDriverListResult();
});

/**
 * 打开新增驱动弹窗
 */
function openAddDriverDialog() {
    editRowItem.value = {};
    isEditDialog.value = false;
    addDriverDialogVisible.value = true;
}

/**
 * 下载驱动
 */
async function handleDownload(item: any) {
    const { driverUrl, ossId } = item;
    if (!driverUrl) return;
    const aTag = document.createElement('a');
    aTag.style.display = 'none';
    aTag.href = driverUrl;
    aTag.download = ossId;
    document.body.appendChild(aTag);
    aTag.click();
    document.body.removeChild(aTag);
}

/**
 * 删除驱动
 */
function handleDelete(item: any) {
    ElMessageBox.alert('驱动删除后无法恢复，是否确认删除？', '删除驱动', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        showCancelButton: true,
        callback: async (action: Action) => {
            if (action === 'confirm') {
                const { id, ossId } = item;
                if (!ossId) {
                    ElMessage.error('删除失败, 没有 OSS 文件名');
                    return;
                }
                try {
                    await PrintConfigApi.deleteDriver(id);
                    const isDeleteSuccess = await handleDeleteOSSFile(ossId);
                    if (isDeleteSuccess) {
                        ElMessage.success('删除成功');
                        // 刷新表格, 页数回到第1页
                        tablePagination.value.currentPage = 1;
                    } else {
                        ElMessage.error('删除失败');
                    }
                } catch (e: any) {
                    console.error(e);
                    ElMessage.error('删除失败，原因：' + e.message);
                } finally {
                    // 刷新表格
                    await fetchDriverListResult();
                }
            }
        },
    });
}

/**
 * 请求驱动列表
 */
async function fetchDriverListResult() {
    try {
        tableLoading.value = true;
        const { currentPage, limit, key, brand } = tablePagination.value;
        const res = await PrintConfigApi.fetchDriverList(currentPage, limit, key, brand);
        const { rows, total } = res;
        tablePagination.value.total = total;
        tableDataList.value = rows || [];
    } catch (e: any) {
        console.error(e);
    } finally {
        tableLoading.value = false;
    }
}

/**
 * 打开编辑品牌或型号弹窗
 * @param type 1:品牌 2:型号
 */
function editBrandsOrModels(type: number) {
    switch (type) {
        case 1:
            editBrandDialogVisible.value = true;
            break;
        case 2:
            if (!currentSelectedBrandId.value) {
                ElMessage.warning('请先选择品牌');
                break;
            }
            editModelDialogVisible.value = true;
            break;
        default:
            break;
    }
}

function handleRowClick(row: any) {
    const { id = '', name = '', brandId = '', modelViewList = [], supportSystem = [], remark = '', driverUrl = '', ossId = '' } = row;
    const modelIds = (modelViewList || []).map((it: any) => it.id);
    editRowItem.value = {
        id, // 驱动id
        name, // 驱动名称
        brandId, // 品牌id
        modelIds, // 型号ids
        supportSystem, // 支持的操作系统
        remark, // 备注
        driverUrl, // 驱动OSS链接
        ossId, // 驱动文件名称，OSS存储的文件名
    };
    isEditDialog.value = true;
    addDriverDialogVisible.value = true;
}

/**
 * 请求驱动列表防抖
 */
const _debounceFetchDriverListResult = _.debounce(fetchDriverListResult, 300);
</script>

<template>
    <div class="print-driver-wrapper">
        <!-- filter-bar -->
        <div class="filter-bar-wrapper">
            <el-select
                v-model="tablePagination.brand"
                clearable
                filterable
                placeholder="品牌"
                class="input-width"
                @change="fetchDriverListResult"
            >
                <el-option
                    v-for="item in currentBrandOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                />
            </el-select>

            <el-input
                v-model="tablePagination.key"
                placeholder="名称、型号、品牌"
                clearable
                :prefix-icon="Search"
                class="input-width"
                @input="_debounceFetchDriverListResult"
            />
        </div>

        <!-- table -->
        <div class="table-wrapper">
            <el-button type="primary" style="width: 100px;" @click="openAddDriverDialog">新增驱动</el-button>
            
            <el-table
                v-loading="tableLoading"
                :data="tableDataList"
                style="width: 100%;"
                @row-click="handleRowClick"
            >
                <el-table-column label="打印机名称" width="200">
                    <template #default="scope">
                        {{ scope.row.name }}
                    </template>
                </el-table-column>
                <el-table-column label="品牌" width="150">
                    <template #default="scope">
                        {{ scope.row.brandName }}
                    </template>
                </el-table-column>
                <el-table-column label="型号" width="200">
                    <template #default="scope">
                        <div class="long-table-text" :title="scope.row.modelViewList.map((it: AbcAPI.PrintConfigModelVO) => it.name).join('、')">
                            {{ scope.row.modelViewList.map((it: AbcAPI.PrintConfigModelVO) => it.name).join('、') }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="支持的操作系统" width="200">
                    <template #default="scope">
                        <div
                            class="long-table-text"
                            :title="scope.row.supportSystem.map((it: AbcAPI.PrintConfigSupportSystemVO) => SupportSystemEnum[it]).join('、')"
                        >
                            {{ scope.row.supportSystem.map((it: AbcAPI.PrintConfigSupportSystemVO) => SupportSystemEnum[it]).join('、') }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="上传者" width="100">
                    <template #default="scope">
                        {{ scope.row.createdByName }}
                    </template>
                </el-table-column>
                <el-table-column label="上传时间" width="200">
                    <template #default="scope">
                        {{ scope.row.createdTime ? dayjs(scope.row.createdTime).format('YYYY.MM.DD HH.mm.ss') : '' }}
                    </template>
                </el-table-column>
                <el-table-column label="备注" min-width="100">
                    <template #default="scope">
                        <div class="long-table-text" :title="scope.row.remark">
                            {{ scope.row.remark }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="200">
                    <template #default="scope">
                        <div class="table-column-operate">
                            <el-button
                                text
                                bg
                                type="primary"
                                @click.stop.prevent="handleDownload(scope.row)"
                            >
                                下载
                            </el-button>
                            <el-button
                                text
                                bg
                                type="danger"
                                @click.stop.prevent="handleDelete(scope.row)"
                            >
                                删除
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>

            <div class="pagination-wrapper">
                <el-pagination
                    v-model:current-page="tablePagination.currentPage"
                    background
                    layout="prev, pager, next"
                    :page-size="tablePagination.limit"
                    :total="tablePagination.total"
                    @current-change="fetchDriverListResult"
                />
            </div>
        </div>
        
        <add-driver-dialog
            v-model="addDriverDialogVisible"
            :is-edit-dialog="isEditDialog"
            :edit-row-item="editRowItem"
            @editBrandsOrModels="editBrandsOrModels"
            @submitAddDriver="fetchDriverListResult"
        ></add-driver-dialog>
        
        <edit-brand-dialog v-model="editBrandDialogVisible"></edit-brand-dialog>
        
        <edit-model-dialog v-model="editModelDialogVisible"></edit-model-dialog>
    </div>
</template>

<style>
.print-driver-wrapper {
    display: flex;
    flex-direction: column;
    gap: 24px 0;

    .filter-bar-wrapper {
        display: flex;
        align-items: center;
        width: 100%;
        gap: 0 24px;
        background-color: #fff;
        padding: 24px;
        border-radius: 4px;
    }

    .input-width {
        width: 240px;
    }

    .table-wrapper {
        display: flex;
        flex-direction: column;
        width: 100%;
        gap: 24px 0;
        background-color: #fff;
        border-radius: 4px;
        padding: 24px;
    }

    .long-table-text {
        display: inline-block;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: middle;
    }

    .table-column-operate {
        display: flex;
        align-items: center;
        width: 100%;
        height: 100%;
    }

    .pagination-wrapper {
        display: flex;
        align-items: center;
        justify-content: flex-end;
    }
}
</style>
