/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import ProtocolApi from '@/api/protocol';
import { createProtocolStore } from '../../store';
import * as utils from '@/utils/utils';
import { isEqual } from 'lodash';
import { reactive, computed } from 'vue';

import createStepModel from '@/common/model/step';
import createLoadingModel from '@/common/model/loading';
import createDialogModel from '@/common/model/dialog';
import createSourceModel from '../../model/source';
import createExportModel from '../../model/export';

export const createExportInfoController = () => {
    const protocolStore = createProtocolStore();
    const stepModel = createStepModel();
    const loadingModelCreate = createLoadingModel();
    const loadingModelUpdate = createLoadingModel();
    const sourceModel = createSourceModel();
    const exportModel = createExportModel();
    const enumJsonModel = createDialogModel();
    const quickAddModel = createDialogModel();

    const searchData = reactive({
        keywordSource: '', // 关键词 - 来源搜索

        keyword: '', // 关键词
        tempKey: '', // 临时key
    });
    
    const toolsParams = reactive({
        keyword: '', // 搜索关键词
    });

    const editData = reactive({
        exportInfo: null,
    });

    // 是否更新了表单数据
    const isUpdatedFormData = computed(() => {
        const exportModelEdit = createExportModel();
        exportModelEdit.initExportInfo(editData.exportInfo);
        return !isEqual(exportModelEdit.createPostData(), exportModel.createPostData());
    });

    /**
     * 初始化输出数据
     * <AUTHOR>
     * @date 2024-05-07
     * @param {Object} exportInfo
     */
    const initExportInfo = (exportInfo: any) => {
        editData.exportInfo = exportInfo;
        exportModel.initExportInfo(exportInfo);
    };

    /**
     * 请求输出协议创建
     * <AUTHOR>
     * @date 2024-04-18
     * @returns {Promise<AbcResponse>}
     */
    const requestExportCreate = async () => {
        const fileName = exportModel.createFileName()
        const uploadResponse = await requestOssDataUpload(fileName);
        if (uploadResponse.status === false) {
            return uploadResponse;
        }
        const postData = exportModel.createPostData();
        postData.oss = uploadResponse.data.url;
        const response = await ProtocolApi.createExport(postData);
        if (response.status === false) {
            return response;
        }
        // 删除草稿
        await protocolStore.deleteExportDraft(exportModel.exportInfo.id);
        return response;
    };

    /**
     * 请求输出协议更新
     * <AUTHOR>
     * @date 2024-04-18
     * @returns {Promise<AbcResponse>}
     */
    const requestExportUpdate = async () => {
        const fileName = exportModel.getFileName()
        const uploadResponse = await requestOssDataUpload(fileName);
        if (uploadResponse.status === false) {
            return uploadResponse;
        }
        const postData = exportModel.createPostData();
        postData.oss = uploadResponse.data.url;
        const response = await ProtocolApi.updateExport(exportModel.exportInfo.id, postData);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    /**
     * 请求Oss数据上传
     * <AUTHOR>
     * @date 2024-05-08
     * @param {String} fileName
     * @returns {Promise<AbcResponse>}
     */
    const requestOssDataUpload = async (fileName: string) => {
        const ossData = exportModel.createOssData();
        const uploadResponse = await utils.uploadJsonDataToOss(ossData, fileName);
        if (uploadResponse.status === false) {
            return uploadResponse;
        }
        return uploadResponse;
    };

    return {
        protocolStore,
        stepModel,
        loadingModelCreate,
        loadingModelUpdate,
        sourceModel,
        exportModel,
        enumJsonModel,
        quickAddModel,
        searchData,
        toolsParams,
        isUpdatedFormData,
        initExportInfo,
        requestExportCreate,
        requestExportUpdate,
        requestOssDataUpload,
    };
};