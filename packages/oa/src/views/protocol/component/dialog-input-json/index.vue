<template>
    <el-dialog
        v-model="isShowDialogInputJson"
        :title="title"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        width="640px"
        custom-class="protocol-module__dialog-input-json"
    >
        <el-input
            v-model="editInfo.json"
            type="textarea"
            :rows="17"
            :placeholder="placeholder"
        ></el-input>
        <template #footer>
            <span class="dialog-footer">
                <el-button
                    type="primary"
                    :disabled="isDisabledConfirmBtn"
                    @click="onClickConfirm"
                >
                    确定
                </el-button>
                <el-button @click="isShowDialogInputJson = false">取消</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
    import { ElMessage } from 'element-plus';
    import { computed, reactive } from 'vue';

    const props = defineProps({
        modelValue: {
            type: Number,
            default: 0,
        },
        title: {
            type: String,
            default: '',
        },
        placeholder: {
            type: String,
            default: '',
        },
    });

    const $emit = defineEmits([
        'update:modelValue', // 更新显示状态
        'confirm', // 确认
    ]);

    const isShowDialogInputJson = computed({
        get() {
            return !!props.modelValue;
        },
        set(val:boolean) {
            $emit('update:modelValue', +val);
        },
    });

    const isDisabledConfirmBtn = computed(() => !editInfo.json);

    const editInfo = reactive({
        json: '',
    });

    /**
     * json解析
     * <AUTHOR>
     * @date 2024-05-15
     * @returns {Object}
     */
    const parse = () => {
        let data = null;
        try {
            data = JSON.parse(editInfo.json);
        } catch (error) {
            data = null;
        }
        if (data === null) {
            try {
                // eslint-disable-next-line no-eval
                data = eval(editInfo.json);
            } catch (error) {
                data = null;
            }
        }
        return data;
    };

    /**
     * 当点击确认
     * <AUTHOR>
     * @date 2024-05-15
     */
    const onClickConfirm = () => {
        const data = parse();
        if (!data) {
            return ElMessage.error('解析失败，json格式错误');
        }
        $emit('confirm', data);
    };
</script>

<style lang="scss">
    .protocol-module__dialog-input-json {
        .el-dialog__body {
            height: 360px !important;
            padding: 8px 20px 0;
        }
    }
</style>