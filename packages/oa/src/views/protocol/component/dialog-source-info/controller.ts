/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import ProtocolApi from '@/api/protocol';
import { isEqual } from 'lodash';
import { createProtocolStore } from '../../store';
import { reactive, computed } from 'vue';

import createStepModel from '@/common/model/step';
import createLoadingModel from '@/common/model/loading';
import createDialogModel from '@/common/model/dialog';
import createSourceModel from '../../model/source';

export const createSourceInfoController = () => {
    const protocolStore = createProtocolStore();
    const stepModel = createStepModel();
    const loadingModelCreate = createLoadingModel();
    const loadingModelUpdate = createLoadingModel();
    const sourceModel = createSourceModel();
    const enumJsonModel = createDialogModel();

    const toolsParams = reactive({
        keyword: '', // 搜索关键词
    });

    const editData = reactive({
        sourceInfo: null,
    });

    // 是否更新了表单数据
    const isUpdatedFormData = computed(() => {
        const sourceModelEdit = createSourceModel();
        sourceModelEdit.initSourceInfo(editData.sourceInfo);
        return !isEqual(sourceModelEdit.createPostData(), sourceModel.createPostData());
    });

    /**
     * 初始化来源数据
     * <AUTHOR>
     * @date 2024-05-07
     * @param {Object} sourceInfo
     */
    const initSourceInfo = (sourceInfo: any) => {
        editData.sourceInfo = sourceInfo;
        sourceModel.initSourceInfo(sourceInfo);
    };

    /**
     * 请求来源数据创建
     * <AUTHOR>
     * @date 2024-04-18
     * @returns {Promise<AbcResponse>}
     */
    const requestSourceCreate = async () => {
        const postData = sourceModel.createPostData();
        const response = await ProtocolApi.createSource(postData);
        if (response.status === false) {
            return response;
        }
        await protocolStore.deleteSourceDraft(sourceModel.sourceInfo.id);
        return response;
    };

    /**
     * 请求来源数据更新
     * <AUTHOR>
     * @date 2024-04-18
     * @returns {Promise<AbcResponse>}
     */
    const requestSourceUpdate = async () => {
        const postData = sourceModel.createPostData();
        const response = await ProtocolApi.updateSource(sourceModel.sourceInfo.id, postData);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    return {
        protocolStore,
        stepModel,
        loadingModelCreate,
        loadingModelUpdate,
        sourceModel,
        enumJsonModel,
        toolsParams,
        isUpdatedFormData,
        initSourceInfo,
        requestSourceCreate,
        requestSourceUpdate,
    };
};
