/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 10:25:15 
 */
import { cloneDeep } from 'lodash';
import { reactive } from 'vue';
import * as utils from '@/utils/utils'
import exportConfigItemType from '../type/exportConfigItem';
import exportEnumItemType from '../type/exportEnumItem';
import exportInfoInterface from '../type/exportInfo';
import sourceConfigItemType from '../type/sourceConfigItem';

const createExportModel = () => {
    const exportInfo = reactive<exportInfoInterface>({
        id: '', // 输出协议id
        type: '1', // 类型
        name: '', // 名称
        version: '', // 版本号
        remark: '', // 备注
        isDraft: false, // 是否草稿
        sourceId: '', // 来源ID
        oss: '', // oss地址
        configItems: [], // 配置列表
        lastModifiedName: '', // 修改人
        lastModifiedBy: '', // 修改人
        lastModified: '', // 修改时间
        createdName: '', // 创建人
        createdBy: '', // 创建人
        created: '', // 创建时间
    });

    const rules = {
        type: [
            { required: true, message: '请选择类型', trigger: 'blur' },
        ],
        name: [
            { required: true, message: '请输入名称', trigger: 'blur' },
        ],
        version: [
            { required: true, message: '请输入版本', trigger: 'blur' },
        ],
    };

    const initDraftInfo = () => {
        if (!exportInfo.id) {
            exportInfo.id = Date.now() + '';
        }
        exportInfo.lastModified = utils.createDateTimeFormat19();
    };

    /**
     * 初始化输出
     * <AUTHOR>
     * @date 2024-04-18
     * @param {Object} info
     */
    const initExportInfo = (info: any) => {
        utils.pick(exportInfo, cloneDeep(info));
        updateConfigItemIndex();
    };

    const addConfigItem = (item: any) => {
        const isExist = exportInfo.configItems.find((one: exportConfigItemType) => one.key === item.key);
        if (isExist) {
            return;
        }
        const configItem = <exportConfigItemType> {
            index: 0, // 索引
            isChecked: false, // 是否检查了
            fromKey: '', // 来源key
            key: item.key, // 对应key
            note: '', // 说明
            type: 'string', // 类型
            min: 0, // 最小值
            max: 10, // 最大值
            decimalNumber: 0, // 小数位数 - 数值的
            decimalMoney: 2, // 小数位数 - 金额的
            minLength: 0, // 最小长度
            maxLength: 32, // 最大长度
            dateFormat: 'YYYY-MM-DD HH:mm:ss', // 日期时间格式
            defaultValue: '', // 默认值
            isEnableEmpty: false, // 是否允许为空
            isEnumValue: false, // 是否枚举值
            isTruncation: false, // 是否截断
            truncationNum: -10, // 截取位数，整数即左边截取，负数为右边截取
            enumItems: <exportEnumItemType []> [], // 枚举项
            sourceConfigItem: null, // 来源字段配置
        };
        exportInfo.configItems.push(configItem);
        updateConfigItemIndex();
    };

    const updConfigItem = (index: number, sourceConfigItem: sourceConfigItemType) => {
        const target = exportInfo.configItems[index];
        if (!target) {
            return;
        }
        target.fromKey = sourceConfigItem.key; // 来源key
        target.type = sourceConfigItem.type; // 类型
        target.min = sourceConfigItem.min; // 最小值
        target.max = sourceConfigItem.max; // 最大值
        target.decimalNumber = sourceConfigItem.decimalNumber; // 小数位数 - 数值的
        target.decimalMoney = sourceConfigItem.decimalMoney; // 小数位数 - 金额的
        target.minLength = sourceConfigItem.minLength; // 最小长度
        target.maxLength = sourceConfigItem.maxLength; // 最大长度
        target.sourceConfigItem = sourceConfigItem; // 来源字段配置
        if (sourceConfigItem.isEnumValue && sourceConfigItem.enumItems.length !== 0) {
            target.isEnumValue = true;
            initEnumItems(target);
        }
    };

    const updateConfigItemIndex = () => {
        exportInfo.configItems.forEach((item: any, index: number) => {
            item.index = index;
        });
    };

    const initEnumItems = (configItem: any) => {
        configItem.enumItems = (configItem.sourceConfigItem?.enumItems || []).map((item: any) => ({
            fromValue: item.value, // 值
            fromLabel: item.label, // 名
            toValue: '', // 值
            toLabel: '', // 名
        })); // 枚举项
    };

    const delConfigItem = (index: number) => {
        exportInfo.configItems.splice(index, 1);
        updateConfigItemIndex();
    };

    const clearConfigItem = () => {
        exportInfo.configItems = [];
    };

    const getFileName = () => {
        // 当版本号没有变化时，复用oss里面的文件名
        // 当版本号有变化时，重新生成文件名
        let fileName = (exportInfo.oss || '').split('/').slice(-1)[0] || ''
        if (exportInfo.version !== getVersionByFileName(fileName)) {
            fileName = ''
        }
        return fileName || createFileName()
    }

    const createFileName = () => `${Date.now()}-${exportInfo.version}.json`;

    const getVersionByFileName = (fileName: string) => {
        const pattern = /-(.+?)\.json/i
        const arr = fileName.match(pattern)
        return arr ? arr[1] : ''
    }

    const createOssData = () => ({
        configItems: exportInfo.configItems.slice().filter((item) => item.isChecked),
    });

    const createPostData = () => {
        const postData = {
            type: '', // 类型
            name: '', // 名称
            version: '', // 版本号
            remark: '', // 备注
            sourceId: '', // 来源ID
            oss: '', // oss地址
            configItems: [], // 扩展信息
        };
        return utils.pick(postData, exportInfo);
    };
    
    return {
        exportInfo,
        rules,
        initDraftInfo,
        initExportInfo,
        addConfigItem,
        updConfigItem,
        updateConfigItemIndex,
        initEnumItems,
        delConfigItem,
        clearConfigItem,
        getFileName,
        createFileName,
        createOssData,
        createPostData,
    };
};

export default createExportModel;