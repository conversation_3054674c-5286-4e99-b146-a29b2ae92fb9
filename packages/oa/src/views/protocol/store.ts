/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 11:25:50 
 */
import ProtocolApi from '@/api/protocol';
import { defineStore } from 'pinia';
import { AbcResponse } from '@/common/AbcResponse';
import exportInfoInterface from './type/exportInfo';
import sourceInfoInterface from './type/sourceInfo';

export const createProtocolStore = defineStore('protocol', {
    state: () => ({
        sourceDataList: <sourceInfoInterface[]>[],
        exportDataList: <exportInfoInterface[]>[],
        sourceDraftList: <any[]>[],
        exportDraftList: <any[]>[],
    }),
    getters: {
        // 是否有来源草稿
        isHasSourceDraft() : boolean {
            return this.sourceDraftList.length !== 0;
        },
        // 是否有输出草稿
        isHasExportDraft() : boolean {
            return this.exportDraftList.length !== 0;
        },
    },
    actions: {
        /**
         * 拉取来源数据列表
         * <AUTHOR>
         * @date 2024-04-17
         * @returns {AbcResponse}
         */
        async fetchSourceList() {
            const fetchResponse: AbcResponse = await ProtocolApi.fetchSourceList();
            if (fetchResponse.status === false) {
                return fetchResponse;
            }
            this.sourceDataList = fetchResponse.data?.rows || [];
            this.sourceDataList.reverse();
            return fetchResponse;
        },
        /**
         * 拉取输出协议列表
         * <AUTHOR>
         * @date 2024-04-17
         * @returns {AbcResponse}
         */
        async fetchExportList() {
            const fetchResponse: AbcResponse = await ProtocolApi.fetchExportList();
            if (fetchResponse.status === false) {
                return fetchResponse;
            }
            this.exportDataList = fetchResponse.data?.rows || [];
            this.exportDataList.reverse();
            return fetchResponse;
        },
        /**
         * 存储来源数据草稿
         * <AUTHOR>
         * @date 2024-04-18
         */
        async storageSourceDraft() {
            localStorage.setItem('SOURCE_DRAFT', JSON.stringify(this.sourceDraftList));
        },
        /**
         * 刷新来源数据草稿
         * <AUTHOR>
         * @date 2024-04-18
         */
        async refreshSourceDraft() {
            const sourceDraft = localStorage.getItem('SOURCE_DRAFT') || '[]';
            const dataList = JSON.parse(sourceDraft);
            this.sourceDraftList = dataList || [];
        },
        /**
         * 创建来源数据草稿
         * <AUTHOR>
         * @date 2024-04-18
         * @param {Object} sourceDraft
         */
        async createWithUpdateSourceDraft(sourceDraft: any) {
            const index = this.sourceDraftList.findIndex((item) => item.id === sourceDraft.id);
            if (index === -1) {
                this.sourceDraftList.push(sourceDraft);
            } else {
                this.sourceDraftList[index] = sourceDraft;
            }
            this.storageSourceDraft();
        },
        /**
         * 删除来源数据草稿
         * <AUTHOR>
         * @date 2024-04-18
         * @param {String} id
         */
        async deleteSourceDraft(id: string) {
            this.sourceDraftList = this.sourceDraftList.filter((item: any) => item.id !== id);
            this.storageSourceDraft();
        },
        /**
         * 存储输出协议草稿
         * <AUTHOR>
         * @date 2024-04-18
         */
        async storageExportDraft() {
            localStorage.setItem('EXPORT_DRAFT', JSON.stringify(this.exportDraftList));
        },
        /**
         * 刷新输出协议草稿
         * <AUTHOR>
         * @date 2024-04-18
         */
        async refreshExportDraft() {
            const exportDraft = localStorage.getItem('EXPORT_DRAFT') || '[]';
            const dataList = JSON.parse(exportDraft);
            this.exportDraftList = dataList || [];
        },
        /**
         * 创建输出协议草稿
         * <AUTHOR>
         * @date 2024-04-18
         * @param {Object} exportDraft
         */
        async createWithUpdateExportDraft(exportDraft: any) {
            const index = this.exportDraftList.findIndex((item) => item.id === exportDraft.id);
            if (index === -1) {
                this.exportDraftList.push(exportDraft);
            } else {
                this.exportDraftList[index] = exportDraft;
            }
            this.storageExportDraft();
        },
        /**
         * 删除输出协议草稿
         * <AUTHOR>
         * @date 2024-04-18
         * @param {String} id
         */
        async deleteExportDraft(id: string) {
            this.exportDraftList = this.exportDraftList.filter((item: any) => item.id !== id);
            this.storageExportDraft();
        },
    },
});