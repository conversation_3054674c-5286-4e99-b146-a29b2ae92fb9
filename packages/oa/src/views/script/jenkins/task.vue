<script lang="ts">
import { <PERSON> } from '@/vendor/jenkins';
import { FreeStyleProject } from '@/vendor/jenkins/model';
import {
    JenkinsParameter,
    JenkinsParameterType,
    JenkinsType,
} from '@/vendor/jenkins/model/shape';
import WorkflowJob from '@/vendor/jenkins/model/workflow-job';
import { H5Form } from '@/vendor/x-form';
import dayjs from 'dayjs';
import { computed, onUnmounted, reactive, toRaw, watch } from 'vue';
import {
    NavigationGuardNext,
    RouteLocationNormalized,
    useRoute,
} from 'vue-router';
import BuildInfoCard from './build-info-card.vue';

const jenkins = new Jenkins();

function promiseQueryPath(to: RouteLocationNormalized, next: NavigationGuardNext) {
    let { uri = '' } = to.query;
    if (!uri) {
        uri = jenkins.getBaseUrl();
        next({
            name: <string>to.name,
            query: {
                uri: jenkins.encodeQueryPath(uri),
            },
            replace: true,
        });
    } else {
        next();
    }
}

export default {
    components: {
        BuildInfoCard,
        H5Form,
    },

    beforeRouteEnter(to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) {
        promiseQueryPath(to, next);
    },

    beforeRouteLeave(to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) {
        console.log('beforeRouteLeave', to, from);
        next();
    },

    beforeRouteUpdate(to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) {
        promiseQueryPath(to, next);
    },

    setup() {
        const route = useRoute();

        const data = reactive({
            isLoading: true,
            jobInfo: <WorkflowJob | FreeStyleProject>{},
            isInQueue: false,
            buildInfo: <any>null,
        });

        // 监听路由变化
        const unWatchRoute = watch(() => ({
            query: route.query,
            name: route.name,
        }), async (newVal, oldVal) => {
            console.log('watch', newVal, oldVal);
            if (newVal.name !== '@script/jenkins-task') {
                return;
            }
            const { uri } = newVal.query;
            if (!uri) {
                return;
            }
            data.isLoading = true;
            const fullUrl = jenkins.decodeQueryPath(<string>uri);
            const jenkinsResponse = <WorkflowJob | FreeStyleProject> await jenkins.getJobInfo(fullUrl);
            if (
                jenkinsResponse.type === JenkinsType.WorkflowJob
                || jenkinsResponse.type === JenkinsType.FreeStyleProject
            ) {
                data.jobInfo = jenkinsResponse;
            }
            data.isLoading = false;
        }, {
            immediate: true,
            deep: true,
        });

        onUnmounted(() => {
            unWatchRoute();
        });

        /**
         * Jenkins 类型转为表单协议
         * @param param
         */
        function jenkinsParam2FormParam(param: JenkinsParameter) {
            let label = param.name;
            let type = 'string';
            let component = 'Input';
            let properties;
            let componentProps: any = {};
            let flatten = false;
            if (param.type === JenkinsParameterType.WReadonlyStringParameterDefinition) {
                type = 'string';
                componentProps.readonly = true;
            } else if (param.type === JenkinsParameterType.ChoiceParameterDefinition) {
                component = 'Picker';
                componentProps.columns = param.choices;
            } else if (param.name === 'clinicId') {
                type = 'string';
                label = '门店';
                component = 'OrganPicker';
            } else if (param.name === 'employeeId') {
                type = 'string';
                label = '人员';
                component = 'EmployeePicker';
            } else if (param.name === 'clinicIdEmployeeId') {
                type = 'object';
                label = '门店-人员';
                component = 'OrganEmployeePicker';
                properties = {
                    clinicId: {
                        type: 'string',
                    },
                    employeeId: {
                        type: 'string',
                    },
                };
                flatten = true;
            }

            return {
                label,
                type,
                properties,
                flatten,
                component,
                componentProps,
                defaultValue: param.defaultValue,
            };
        }

        const formSchema = computed(() => {
            const properties: any = {};
            if (data.jobInfo.parameters) {
                data.jobInfo.parameters.reduce((acc, param) => {
                    // 同时存在 employeeId 和 clinicId，则转换为 clinicIdEmployeeId
                    if (param.name === 'employeeId' && acc.clinicId || param.name === 'clinicId' && acc.employeeId) {
                        delete acc.employeeId;
                        delete acc.clinicId;
                        param.name = 'clinicIdEmployeeId';
                    }
                    acc[param.name] = jenkinsParam2FormParam(param);
                    return acc;
                }, properties);
            }
            const formSchema = {
                type: 'object',
                name: '执行参数',
                actions: {
                    exec: {
                        type: 'void',
                        component: 'Button',
                        componentProps: {
                            text: '执行',
                            type: 'primary',
                            loading: data.isInQueue,
                        },
                    },
                },
                properties,
            };
            return formSchema;
        });

        async function handleAction(params: any) {
            const { action, formData } = params;
            if (action.name === 'exec') {
                // 脚本执行
                data.isInQueue = true;
                await jenkins.execJob(toRaw(data.jobInfo), toRaw(formData), {
                    onStart: async () => {
                        // 开始构建，可以拉取构建信息
                        const fullUrl = jenkins.decodeQueryPath(<string>route.query.uri);
                        const jenkinsResponse = <WorkflowJob | FreeStyleProject> await jenkins.getJobInfo(fullUrl);
                        if (
                            jenkinsResponse.type === JenkinsType.WorkflowJob
                            || jenkinsResponse.type === JenkinsType.FreeStyleProject
                        ) {
                            data.jobInfo.builds = jenkinsResponse.builds;
                        }
                        data.isInQueue = false;
                    },
                });
            }
        }

        return {
            data,
            formSchema,
            handleAction,
            dayjs,
        };
    },
};
</script>

<template>
    <div v-loading="data.isLoading" class="jenkins-task-wrapper">
        <h1>{{ data.jobInfo.fullDisplayName }}</h1>
        <h5-form v-if="!data.isLoading" :schema="formSchema" @action="handleAction"></h5-form>
        <div v-if="data.jobInfo && data.jobInfo.builds" class="build-history-wrapper">
            <h2 class="build-history-title">构建历史</h2>
            <transition-group name="list-anim">
                <div v-if="data.isInQueue" :key="-1" class="in-queue-build-item list-anim-item">
                    <van-loading type="spinner" size="16">
                        已加入队列，即将开始构建...
                    </van-loading>
                </div>
                <build-info-card
                    v-for="(build, index) in data.jobInfo.builds"
                    :key="build.number"
                    v-model:build="data.jobInfo.builds[index]"
                    class="list-anim-item"
                >
                </build-info-card>
            </transition-group>
        </div>
    </div>
</template>

<style lang="scss">
    @import "src/style/common/animation";

    .jenkins-task-wrapper {
        min-height: 60%;

        h1 {
            margin-bottom: 12px;
        }

        .build-info-wrapper {
            display: flex;
            justify-content: center;
            height: 64px;
        }

        .build-history-wrapper {
            h2.build-history-title {
                margin: 8px 0;
            }

            .in-queue-build-item {
                height: 64px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: white;
                border-radius: var(--oa-border-radius-4);
                margin-bottom: 8px;
            }
        }
    }
</style>
