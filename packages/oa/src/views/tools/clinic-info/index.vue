<script setup lang="ts">
import { querySchema, tableSchema } from './schema';
import QueryTable from '@/components/query-table.vue';
import { formatEdition, formatHisTypeName } from "@/utils/format";
import {Form} from "@/vendor/x-form/core/form";
import {useRoute} from "vue-router";
import ClinicDetail from '../components/clinic-detail.vue';
import { ref } from 'vue';
import dayjs from 'dayjs';

const hisDomain = import.meta.env.VITE_APP_HELP_CENTER_DOMAIN;

const route = useRoute();
const openId = route.query.openId as string;
const clinicId = route.query.clinicId as string;
const employeeId = route.query.employeeId as string;
const chainId = route.query.chainId as string;
const mobile = route.query.mobile as string;
const isAdmin = route.query.isAdmin as string;
const currentRow = ref({});
const showClinicDetailDialog = ref(false);

let formControl: Form;

function handlePrepared(form: Form) {
    formControl = form;
    if(formControl) {
        formControl.setFieldValue('chainId', chainId);
        formControl.setFieldValue('clinicId', clinicId);
        formControl.setFieldValue('employeeId', employeeId);
        formControl.setFieldValue('openId', openId);
        formControl.setFieldValue('mobile', mobile);
        formControl.setFieldValue('isAdmin', isAdmin ? Number(isAdmin) : '');
    }
}

const showClinicDetail = (row: any) => {
    showClinicDetailDialog.value = true;
    currentRow.value = row;
}
</script>

<template>
    <div class="clinic-info-list">
        <query-table
            :table-schema="tableSchema"
            :query-schema="querySchema"
            @prepared="handlePrepared"
        >
            <template #hisType="{row}">
                {{ formatHisTypeName(row.hisType) }}
            </template>
            <template #editionId="{row}">
                {{ formatEdition(row.editionId) }}
            </template>
            <template #employeeName="{row}">
                <el-tag
                    v-if="row.roleId === 1"
                    class="admin-tag"
                    effect="dark"
                    type="danger"
                    size="small"
                >
                    管
                </el-tag>
                <span>
                    {{ row.employeeName || '' }}
                    {{ row.employeeWechatName ? `(${row.employeeWechatName })` : '' }}
                </span>
            </template>
            <template #employeeOpenId="{ row }">
                <el-link type="primary" :href="`${hisDomain}/login/password?openId=${row.employeeOpenId}`"  target="_blank">{{row.employeeOpenId}}</el-link>
            </template>
            <template #employeeMobile="{ row }">
                <el-link type="primary" :href="`${hisDomain}/login/password?openId=${row.employeeMobile}`" target="_blank">{{row.employeeMobile}}</el-link>
            </template>
            <template #orderEditionCreatedDate="{ row }">
                <span>{{ row.orderEditionCreatedDate ? dayjs(row.orderEditionCreatedDate).format('YYYY-MM-DD') : '' }}</span>
            </template>
            <template #editionEndDate="{ row }">
                <span>{{ row.editionEndDate ? dayjs(row.editionEndDate).format('YYYY-MM-DD') : '' }}</span>
            </template>
            <template #organEnv="{ row }">
                <span>{{ row.regionId }}-{{ row.organEnv }}</span>
            </template>
            <template #isTrial="{ row }">
                <span>{{ row.isTrial === 1 ? '是' : row.isTrial === 0 ? '否' : '' }}</span>
            </template>
            <template #operate="{ row} ">
                <el-link type="primary" :underline="false" @click="showClinicDetail(row)">更多</el-link>
            </template>
        </query-table>
        <clinic-detail
            v-if="showClinicDetailDialog"
            v-model:visible="showClinicDetailDialog"
            :current-row="currentRow"
        ></clinic-detail>
    </div>
</template>

<style lang="scss">
.clinic-info-list {
    .admin-tag {
        transform: scale(.8);
    }
}
</style>
