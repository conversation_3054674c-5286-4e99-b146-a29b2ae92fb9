const DEFAULT_REQUEST_FILE = 'base-api';

class RequestFile {
    static requestPathMapToRquestFileName = {
        '/api/low-code-his/swagger-json': {
            '/api/help-center': 'help-center-base-api',
        },
        '/api/management/api-docs': {
            '/api-share/management': 'share-base-api',
        },
    }

    static getCurFileToRequestMap(api_doc_url){
        for(let key in this.requestPathMapToRquestFileName){
            if(api_doc_url.includes(key)){
                return this.requestPathMapToRquestFileName[key];
            }
        }
        return null;
    }

    // path:当前controller下的某一个请求的路径
    static getRequestFileName(api_doc_url,path){
        if(!path){
            return DEFAULT_REQUEST_FILE;
        }
        const fileToRequestMap = this.getCurFileToRequestMap(api_doc_url);

        if(!fileToRequestMap){
            return DEFAULT_REQUEST_FILE;
        }

        for(let key in fileToRequestMap){
            if(path.includes(key)){
                return fileToRequestMap[key]
            }
        }

        return DEFAULT_REQUEST_FILE;
    }
}

module.exports = RequestFile;