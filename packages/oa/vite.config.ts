import autoprefixer from 'autoprefixer';
import pxtorem from 'postcss-pxtorem';
import { resolve } from 'path';
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import { viteVConsole } from 'vite-plugin-vconsole';
const AbcBuildTool = require('abc-fed-build-tool');
const AbcViteOssPlugin = AbcBuildTool.AbcViteOSSPlugin;
// @ts-ignore
import history from 'vite-plugin-history';

import * as path from 'path';

const pathResolve = (dir: string) => resolve(__dirname, '.', dir);
const alias = {
    '@': pathResolve('./src/'),
    'assets': pathResolve('./src/assets'),
    '@social': pathResolve('../shared/social/src/'),
};

const tagSuffix = process.env.BUILD_TAG ? `/${process.env.BUILD_TAG}` : '';
const { bucket, region, secretId, secretKey, url } = AbcBuildTool.OSS.getOSSInfo(process.env.BUILD_ENV || 'dev', `oa${tagSuffix}`);
const remoteSDKOSSInfo = AbcBuildTool.OSS.getOSSInfo(process.env.BUILD_ENV || 'dev', 'abc-remote-assist');

const getRemoteSDKUrl = function () {
    return 'https:' + remoteSDKOSSInfo.url + 'remote-assist.js';
}

interface HtmlPluginOptions {
    injectData: Record<string, any>
}

const htmlPlugin = (options: HtmlPluginOptions) => {
    const injectData = options.injectData || {};
    return {
        name: "html-transform",
        transformIndexHtml(html: string) {
            return html.replace(/<%=(.*?)%>/g, function (match, p1) {
                return injectData[p1];
            });
        },
    };
};


// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
    base: mode && mode !== 'loc' ? url : '/',
    build: {
        rollupOptions: {
            input: {
                main: resolve(__dirname, 'index.html'),
                sharepage: resolve(__dirname, 'share-page.html'),
                helpcenter: resolve(__dirname, 'help-center.html'),
            },
        },
        emptyOutDir: true,
        outDir: 'dist'
    },
    server: {
        host: '0.0.0.0', // 监听所有地址
        port: 3000,
        open: true,
        hmr: {
            protocol: 'ws',
            host: '127.0.0.1'
        },
        proxy: {
            '/api/low-code': {
                target: 'https://dev-oa.abczs.cn',
                // target: 'http://localhost:3070',
                changeOrigin: true,
                // headers: {
                //     'user-mobile': '13980727980',
                //     'user-id': '10007',
                // },
                bypass: function (req, res, proxyOptions) {
                    req.headers['origin'] = 'https://dev-oa.abczs.cn';
                }
            },
            '/api/help-center': {
                target: 'https://dev.abczs.cn',
                // target: 'http://localhost:3070',
                changeOrigin: true,
                // headers: {
                //     'user-mobile': '13980727980',
                //     'user-id': '10007'
                // },
            },
            '/api/warehouse': {
                // target: 'http://localhost:3001',
                target: 'https://dev-oa.abczs.cn',
                changeOrigin: true,
                headers: {
                    'user-mobile': '13980727980',
                    'user-id': '10007'
                },
            },
            '/api/v3/cms': {
                target: 'https://dev.abczs.cn',
                changeOrigin: true,
            },
            '/api/v3/clinics': {
                target: 'https://dev.abczs.cn',
                changeOrigin: true,
            },
            '/api': {
                target: 'https://dev-oa.abczs.cn',
                changeOrigin: true,
                bypass: function (req, res, proxyOptions) {
                    req.headers['origin'] = 'https://dev-oa.abczs.cn';
                }
            },
        },
    },
    define: {
        'process.env': process.env,
    },
    resolve: {
        alias,
    },
    plugins: [
        vue(),
        vueJsx(),
        htmlPlugin({
            injectData: {
                remoteSDKUrl: getRemoteSDKUrl(),
            }
        }),
        // viteSentry(sentryConfig),
        mode === 'prod' ? null : viteVConsole({
            entry: path.resolve('src/main.ts'),
            localEnabled: true,
            enabled: true,
            config: {
                maxLogNumber: 1000,
                theme: 'dark',
            },
        }),
        history({
            rewrites: [
                { from: '/share', to: './share-page.html' },
                { from: '/help-center-customer', to: './help-center.html' },
            ],
        }),
        mode && mode !== 'loc' ? AbcViteOssPlugin({
            prefix: `oa${tagSuffix}`,
            ossType: 'ali',
            Bucket: bucket,
            Region: region,
            SecretId: secretId,
            SecretKey: secretKey
        }) : null,
        // vitePluginImp({
        //     // 组件按需导入
        //     libList: [
        //         {
        //             libName: 'vant',
        //             style(name) {
        //                 return `vant/es/${name}/style/index`;
        //             },
        //         },
        //     ],
        // }),
    ].filter(plugin => !!plugin),
    css: {
        postcss: {
            plugins: [
                {
                    postcssPlugin: 'internal:charset-removal',
                    AtRule: {
                        charset: (atRule) => {
                            if (atRule.name === 'charset') {
                                atRule.remove();
                            }
                        },
                    },
                },
                autoprefixer({
                    overrideBrowserslist: [
                        'Android 4.1',
                        'iOS 7.1',
                        'Chrome > 31',
                        'not ie <= 11', // 不考虑IE浏览器
                        'ff >= 30', // 仅新版本用“ff>=30
                        '> 1%', //  全球统计有超过1%的使用率使用“>1%”;
                        'last 2 versions', // 所有主流浏览器最近2个版本
                    ],
                }),
                pxtorem({
                    rootValue: 37.5,
                    propList: ['*'],
                    exclude: /element-plus|pc/i,
                }),
            ],
        },
    },
}));
