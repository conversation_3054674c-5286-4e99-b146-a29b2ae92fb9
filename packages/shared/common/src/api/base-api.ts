import { ApiService } from '../utils/request';

export default class BaseAPI {
    protected static get get() {
        return ApiService.getAxiosInstance().get;
    }

    protected static get post() {
        return ApiService.getAxiosInstance().post;
    }

    protected static get put() {
        return ApiService.getAxiosInstance().put;
    }

    protected static get del() {
        return ApiService.getAxiosInstance().delete;
    }

    protected static get head() {
        return ApiService.getAxiosInstance().delete;
    }

    protected static get options() {
        return ApiService.getAxiosInstance().options;
    }

    protected static get patch() {
        return ApiService.getAxiosInstance().patch;
    }
}
