import { AbcResponse } from '../../../utils/AbcResponse';
import { CloudFunctionAPI } from '../../../api/cloud-function-api';

export class BaseFunction {
    static NAME: string;

    public static async exec(params?: any): Promise<AbcResponse> {
        try {
            const { result } = await CloudFunctionAPI.executeUsingPOST({
                name: this.NAME,
                params,
            });
            if (result?.httpCode !== 200 || result?.error) {
                // @ts-ignore
                return AbcResponse.error(result?.error?.message || result?.error || '业务侧未知异常', result);
            }
            // @ts-ignore
            return AbcResponse.success(result.data?.data || result.data);
        } catch (e: any) {
            return AbcResponse.error(e || e.message || '代理侧异常', e);
        }
    }
}
