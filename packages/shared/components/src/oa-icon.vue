<!--支持 iconfiy 的图标，在这里搜索图标使用：https://icon-sets.iconify.design/-->
<template>
    <el-icon :size="size" :color="color">
        <Icon :icon="icon" />
    </el-icon>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue';
import { defineProps } from 'vue';

const props = defineProps({
    icon: {
        type: [Object, String],
        required: true,
    },
    size: {
        type: [String, Number],
        default: '16px',
    },
    color: {
        type: String,
        default: '',
    },
});
</script>
