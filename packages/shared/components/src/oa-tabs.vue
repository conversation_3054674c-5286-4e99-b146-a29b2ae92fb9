<script setup lang="ts">
import { computed, ref } from 'vue';

const props = defineProps({
    modelValue: {
        type: String,
        default: '',
    },
    options: {
        type: Array,
        default: () => [],
    },
    type: {
        type: String,
        default: '',
    },
});

const emit = defineEmits([
    'update:modelValue',
    'change',
]);

const activeTab = computed({
    get() {
        return props.modelValue;
    },
    set(value) {
        emit('update:modelValue', value);
    },
});

function handleTabChange(tab) {
    emit('change', tab);
}

</script>

<template>
    <div
        :class="[
            'oa-tabs',
            { [`oa-tabs--${props.type}`]: !!props.type },
        ]"
    >
        <el-tabs v-model="activeTab" :type="props.type" @tabChange="handleTabChange">
            <el-tab-pane
                v-for="tab in options"
                :key="tab.value"
                :label="tab.label"
                :name="tab.value"
            >
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<style lang="scss">
.oa-tabs {
    height: 56px;
    padding: 0 20px;
    display: flex;
    flex-direction: column;

    &.oa-tabs--border-card,
    &.oa-tabs--card {
        --el-tabs-header-height: 56px;

        padding: 0;

        .el-tabs--card > .el-tabs__header {
            border-bottom: none;
        }
    }

    &.oa-tabs--card {
        --el-tabs-header-height: 56px;

        .el-tabs--card > .el-tabs__header {
            border-bottom: none;
        }
    }

    .el-tabs__header {
        line-height: 56px;
        height: 56px;
        background-color: #fff;
        margin-bottom: 0;

        .el-tabs__nav-wrap::after {
            height: 0;
        }
    }

    .el-tabs__item {
        --el-tabs-header-height: 56px;
    }

    .el-tabs__content {
        display: none;
    }
}
</style>
