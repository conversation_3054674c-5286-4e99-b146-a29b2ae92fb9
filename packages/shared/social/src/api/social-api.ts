/*
 * <AUTHOR>
 * @DateTime 2023-11-07 15:03:38
 */
import BaseAPI from './base-api';
import AbcResponse from '../common/AbcResponse';
import * as utils from '../common/utils';
import {regionNameOptions} from "@social/common/options";

export default class SocialApi extends BaseAPI {
    /**
     * 查询阿里云日志
     * <AUTHOR>
     * @date 2023-11-20
     * @param {Object} data
     * @returns {Promise<AbcResponse>}
     */
    static fetchManagementLog(data: any): Promise<AbcResponse> {
        return this.fetchPack({
            url: '/api/management/logs',
            method: 'POST', // 可能查询入参过长，改为POST
            data,
        });
    }

    /**
     * 查询阿里云日志 - 通过参数查询
     * <AUTHOR>
     * @date 2023-11-20
     * @param {Object} data
     * @returns {Promise<AbcResponse>}
     */
    static async fetchManagementLogByCustomParams(data: any): Promise<AbcResponse> {
        const dataNew = utils.cloneDeep(data);
        const customParams = dataNew.customParams;
        delete dataNew.customParams;

        const logItems = <any []>[];
        // 轮询
        const checkWhile = async (data: any) => {
            const limit = 100;
            let offset = 0;
            let isContinue = false;
            do {
                const postData = utils.cloneDeep(data);
                postData.offset = offset;
                postData.limit = limit;
                const response = await SocialApi.fetchManagementLog(postData);
                if (response.status === false) {
                    return response;
                }
                const items = response.data.logItems || [];
                items.forEach((item: any) => {
                    item.region = data.region;
                    item.logStore = data.logStore;
                    logItems.push(item);
                });
                if (items.length < limit || logItems.length >= data.limit) {
                    isContinue = false;
                } else {
                    isContinue = true;
                    offset += limit;
                }
            } while (isContinue);
            return AbcResponse.success();
        };
        // 并发
        const checkBatch = async (data: any) => {
            const limit = 100;
            const lenth = Math.ceil(data.limit / limit);
            const list = new Array(lenth).fill('');
            const promiseList = list.map((item, index) => {
                const params = {
                    ...data,
                    offset: data.offset + index * 100,
                };
                delete params.limit;
                return SocialApi.fetchManagementLog(params);
            });
            const responseList = await Promise.all(promiseList);
            for (let index = 0; index < responseList.length; index++) {
                const response = responseList[index];
                if (response.status === false) {
                    return response;
                }
                (response.data.logItems || []).forEach((item: any) => {
                    item.region = data.region;
                    item.logStore = data.logStore;
                    logItems.push(item);
                });
            }
            return AbcResponse.success();
        };

        // 处理长短日志
        const checkStore = async (data: any) => {
            for (let index = 0; index < customParams.logStore.length; index++) {
                data.logStore = customParams.logStore[index];
                const response = await (customParams.isBatch ? checkBatch(data) : checkWhile(data));
                if (response.status === false) {
                    return response;
                }
            }
            return AbcResponse.success();
        };

        // 处理分区
        for (let index = 0; index < customParams.region.length; index++) {
            dataNew.region = customParams.region[index];
            const response = await checkStore(dataNew);
            if (response.status === false) {
                return response;
            }
        }

        return AbcResponse.success({
            logItems: utils.logItemsLimit(logItems, dataNew.limit),
        });
    }

    /**
     * 创建社保消息转义分类
     * <AUTHOR>
     * @date 2023-11-20
     * @param {Object} data
     * @returns {Promise<AbcResponse>}
     */
    static createCategory(data: any): Promise<AbcResponse> {
        return this.fetchPack({
            url: '/api/management/shebao/message-escape/category',
            method: 'POST',
            data,
        });
    }

    /**
     * 获取指定消息转义分类
     * <AUTHOR>
     * @date 2023-11-20
     * @param {String} id
     * @returns {Promise<AbcResponse>}
     */
    static fetchCategory(id: string): Promise<AbcResponse> {
        return this.fetchPack({
            url: `/api/management/shebao/message-escape/category/${id}`,
            method: 'GET',
        });
    }

    /**
     * 更新社保消息转义分类
     * <AUTHOR>
     * @date 2023-11-20
     * @param {String} id
     * @param {Object} data
     * @returns {Promise<AbcResponse>}
     */
    static updateCategory(id: string, data: any): Promise<AbcResponse> {
        return this.fetchPack({
            url: `/api/management/shebao/message-escape/category/${id}`,
            method: 'PUT',
            data,
        });
    }

    /**
     * 删除社保消息转义分类
     * <AUTHOR>
     * @date 2023-11-20
     * @param {String} id
     * @returns {Promise<AbcResponse>}
     */
    static deleteCategory(id: string): Promise<AbcResponse> {
        return this.fetchPack({
            url: `/api/management/shebao/message-escape/category/${id}`,
            method: 'DELETE',
        });
    }

    /**
     * 获取指定消息转义分类列表
     * <AUTHOR>
     * @date 2023-11-20
     * @returns {Promise<AbcResponse>}
     */
    static fetchCategoryList(): Promise<AbcResponse> {
        return this.fetchPack({
            url: '/api/management/shebao/message-escape/category/list',
            method: 'GET',
        });
    }

    /**
     * 创建社保消息转义
     * <AUTHOR>
     * @date 2023-11-20
     * @param {Object} data
     * @returns {Promise<AbcResponse>}
     */
    static createExplain(data: any): Promise<AbcResponse> {
        return this.fetchPack({
            url: '/api/management/shebao/message-escape/explain',
            method: 'POST',
            data,
        });
    }

    /**
     * 获取指定消息转义
     * <AUTHOR>
     * @date 2023-11-20
     * @param {String} id
     * @returns {Promise<AbcResponse>}
     */
    static fetchExplain(id: string): Promise<AbcResponse> {
        return this.fetchPack({
            url: `/api/management/shebao/message-escape/explain/${id}`,
            method: 'GET',
        });
    }

    /**
     * 更新社保消息转义
     * <AUTHOR>
     * @date 2023-11-20
     * @param {String} id
     * @param {Object} data
     * @returns {Promise<AbcResponse>}
     */
    static updateExplain(id: string, data: any): Promise<AbcResponse> {
        return this.fetchPack({
            url: `/api/management/shebao/message-escape/explain/${id}`,
            method: 'PUT',
            data,
        });
    }

    /**
     * 删除社保消息转义
     * <AUTHOR>
     * @date 2023-11-20
     * @param {String} id
     * @returns {Promise<AbcResponse>}
     */
    static deleteExplain(id: string): Promise<AbcResponse> {
        return this.fetchPack({
            url: `/api/management/shebao/message-escape/explain/${id}`,
            method: 'DELETE',
        });
    }

    /**
     * 获取消息转义列表
     * <AUTHOR>
     * @date 2023-11-20
     * @returns {Promise<AbcResponse>}
     */
    static fetchExplainList(): Promise<AbcResponse> {
        return this.fetchPack({
            url: '/api/management/shebao/message-escape/explain/list',
            method: 'GET',
        });
    }

    /**
     * 获取远程设备列表
     * <AUTHOR>
     * @date 2023-11-20
     * @returns {Promise<AbcResponse>}
     */
    static fetchRemoteListByClinics(params: any): Promise<AbcResponse> {
        return this.fetchPack({
            url: '/api/low-code/remotely',
            method: 'GET',
            params,
        });
    }

    /**
     * 获取门店列表
     * <AUTHOR>
     * @date 2023-11-20
     * @returns {Promise<AbcResponse>}
     */
    static fetchClinicListPage(params: any): Promise<AbcResponse> {
        return this.fetchPack({
            url: '/api/low-code/crm/organ/list/page',
            method: 'GET',
            params,
        });
    }

    /**
     * 获取门店信息
     * <AUTHOR>
     * @date 2023-11-20
     * @returns {Promise<AbcResponse>}
     */
    static fetchClinicInfo(clinicId: string): Promise<AbcResponse> {
        return this.fetchPack({
            url: `/api/low-code/crm/detail/${clinicId}`,
            method: 'GET',
        });
    }

    /**
     * 获取追溯码统计数据
     * <AUTHOR>
     * @date 2023-11-20
     * @param {Object} params
     * @returns {Promise<AbcResponse>}
     */
    static fetchTraceabilityCodeStatistics(params: any): Promise<AbcResponse> {
        return this.fetchPack({
            url: '/api/management/supervision/stock/stat/get-shebao-stock-stat-oa',
            method: 'GET',
            params,
        });
    }

    /**
     * 获取门店成员列表
     * <AUTHOR>
     * @date 2023-11-20
     * @param {Object} params
     * @returns {Promise<AbcResponse>}
     */
    static fetchCrmClinicInfo(params: any): Promise<AbcResponse> {
        return this.fetchPack({
            url: '/api/low-code/crm/clinic/info',
            method: 'GET',
            params,
        });
    }

    /**
     * 获取区域用量
     * <AUTHOR>
     * @date 2025-05-22
     * @returns {Promise<AbcResponse>}
     */
    static fetchRegionUserCount(): Promise<AbcResponse> {
        return this.fetchPack({
            url: '/api/management/shebao/region-clinic-stats',
            method: 'GET',
        });
    }

    /**
     * 获取结算统计状态
     * <AUTHOR>
     * @date 2025-07-29
     * @param {Object} data
     * @returns {Promise<AbcResponse>}
     */
    static fetchSettleStatStatus(data: any): Promise<AbcResponse> {
        return this.fetchPack({
            url: '/api/management/shebao/settle/stat/status',
            method: 'POST',
            data,
        });
    }
    /**
     * 创建推送计划
     * <AUTHOR>
     * @Date 2025-09-08
     * @param data
     */
    static createDllLibPushPlan(data: any): Promise<AbcResponse> {
        return this.fetchPack({
            url: '/api/management/shebao/exe-lib/push-plan',
            method: 'POST',
            data,
        });
    }
    /**
     * 获取动态库版本
     * <AUTHOR>
     * @Date 2025-09-08
     * @param data
     */
    static async fetchDllLibList(data: any): Promise<AbcResponse> {
        const response = await this.fetchPack({
            url: '/api/management/shebao/exe-lib/groups',
            method: 'POST',
            data,
        })
        if (response.status === false) {
            return response;
        }
        try {
            const entries = Object.entries(response.data.groups)
            const result = []
            for (const entry of entries) {
                result.push({
                    value: entry[0],
                    label: entry[1][0].title,
                    children: entry[1].map((item: any) => {
                        return {
                            ...item,
                            value: item.version,
                            label: item.version,
                        }
                    })
                })
            }
            return AbcResponse.success(result);
        } catch (e) {
            console.log(e)
            return AbcResponse.error(e.message);
        }
    }
    /**
     * 获取动态库安装情况
     * <AUTHOR>
     * @Date 2025-09-08
     * @param data
     */
    static fetchDllInstallList(data: any): Promise<AbcResponse> {
        return this.fetchPack({
            url: '/api/management/shebao/exe-lib/install/query',
            method: 'POST',
            data,
        })
    }
    /**
     * 获取推送记录列表
     * <AUTHOR>
     * @Date 2025-09-08
     * @param data
     */
    static async fetchPushRecordList(data: any): Promise<AbcResponse> {
        const response = await this.fetchPack({
            url: '/api/management/shebao/exe-lib/push-plans',
            method: 'POST',
            data,
        })
        if (response.status === false) {
            return response;
        }
        response.data.map((item: any) => {
            item.region = item.region.split(',').map((item: string) => {
                return {
                    region: item,
                    regionName: regionNameOptions.find((it: any) => it.region === item)?.regionShortName || '',
                }
            })
        })
        return response;
    }
    /**
     * 暂停推送
     * <AUTHOR>
     * @Date 2025-09-08
     * @param planId
     */
    static pausePushRecord(planId: string): Promise<AbcResponse> {
        return this.fetchPack({
            url: `/api/management/shebao/exe-lib/push-plan/${planId}/pause`,
            method: 'PUT',
        })
    }
    /**
     * 恢复推送
     * <AUTHOR>
     * @Date 2025-09-08
     * @param planId
     */
    static resumePushRecord(planId: string): Promise<AbcResponse> {
        return this.fetchPack({
            url: `/api/management/shebao/exe-lib/push-plan/${planId}/resume`,
            method: 'PUT',
        })
    }
    /**
     * 删除推送记录
     * <AUTHOR>
     * @Date 2025-09-08
     * @param planId
     */
    static deletePushRecord(planId: string): Promise<AbcResponse> {
        return this.fetchPack({
            url: `/api/management/shebao/exe-lib/push-plan/${planId}`,
            method: 'DELETE',
        })
    }
    /**
     * 查询可以推送的设备
     * <AUTHOR>
     * @Date 2025-09-08
     * @param data
     */
    static fetchPushableDeviceList(data: any): Promise<AbcResponse> {
        return this.fetchPack({
            url: '/api/management/shebao/exe-lib/install/query-not-in-versions',
            method: 'POST',
            data,
        })
    }
    /**
     * 锁定设备
     * <AUTHOR>
     * @Date 2025-09-08
     * @param data
     */
    static lockDevice(data: any): Promise<AbcResponse> {
        return this.fetchPack({
            url: '/api/management/shebao/exe-lib/device-exe-lib-version/lock',
            method: 'PUT',
            data,
        })
    }
    /**
     * 解锁设备
     * <AUTHOR>
     * @Date 2025-09-08
     * @param data
     */
    static unlockDevice(data: any): Promise<AbcResponse> {
        return this.fetchPack({
            url: '/api/management/shebao/exe-lib/device-exe-lib-version/unlock',
            method: 'PUT',
            data,
        })
    }
}
