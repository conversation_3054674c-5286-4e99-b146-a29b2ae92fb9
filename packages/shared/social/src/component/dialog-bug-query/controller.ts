/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import dayjs from 'dayjs';
import SocialApi from '../../api/social-api';
import AbcResponse from '../../common/AbcResponse';
import * as utils from '../../common/utils';
import { reactive, computed } from 'vue';

import createLoadingModel from '../../model/loading';
import createPageModel from '../../model/page';

export const createBugQueryController = () => {
    const loadingModel = createLoadingModel();
    const pageModel = createPageModel();

    const toolsParams = reactive({
        keywords: '',
    });

    const logInfo = reactive({
        searchResult: <any> null,
    });

    // 筛选的数据列表
    const mateDataList = computed(() => {
        const dataList = logInfo.searchResult?.dataList || [];
        pageModel.setTotal(dataList.length);
        pageModel.setPage(1);
        return dataList;
    });

    // 展示的数据列表
    const showDataList = computed(() => {
        const { sIndex, eIndex } = pageModel.createSliceParams();
        return mateDataList.value.slice(sIndex, eIndex);
    });

    /**
     * 创建Bug日志搜索参数
     * <AUTHOR>
     * @date 2024-10-08
     * @returns {Object}
     */
    const createLogSearchParams = () => {
        const sDate = dayjs().subtract(3, 'day').startOf('day');
        const eDate = dayjs().endOf('day');
        const params = {
            region: 'abc-fed-log',
            logStore: utils.createBugLogStoreOptions()[0].value,
            beginDate: utils.createDateTimeFormat19(sDate), // 开始日期
            endDate: utils.createDateTimeFormat19(eDate), // 结束日期
            offset: 0, // 偏移量
            limit: 100, // 每页条数
            // eslint-disable-next-line quotes
            query: `attribute.t: 'error' and attribute.ex.project: 'SOCIAL'`, // 查询条件
        };
        const keywords = toolsParams.keywords.trim();
        if (keywords !== '') {
            params.query += ` and ${keywords}`;
        }
        return params;
    };

    /**
     * 请求bug日志
     * <AUTHOR>
     * @date 2025-06-12
     * @param {Object} params
     * @returns {Promise<AbcResponse>}
     */
    const requestLogSearchResult = async (params: any) => {
        const response = await SocialApi.fetchManagementLog(params);
        if (response.status === false) {
            return response;
        }
        const dataList = (response.data.logItems || [])
                        .map((item: any) => createItemInfo(item))
                        .filter((item: any) => !!item);
        return AbcResponse.success({ dataList });
    };

    /**
     * 创建数据立碑
     * <AUTHOR>
     * @date 2024-10-09
     * @param {Object} item
     * @returns {Object}
     */
    const createItemInfo = (item: any) => {
        try {
            const attribute = JSON.parse(item.attribute);
            const logData = {
                id: attribute['ex.id'], // 错误ID 
                message: attribute['ex.message'], // 错误信息
                pageReferrer: attribute['page.referrer'], // 页面来源
                pageTarget: attribute['page.target'], // 页面目标
                province: `${attribute.province} / ${attribute.city}`, // 发生地区
                createdTime: utils.createDateTimeFormat19(item.start / 1000), // 发生时间
            };
            return logData;
        } catch (error) {
            console.log('createItemInfo error', error);
            return null;
        }
    };

    return {
        loadingModel,
        pageModel,
        toolsParams,
        logInfo,
        showDataList,
        createLogSearchParams,
        requestLogSearchResult,
    };
};