<template>
    <el-dialog
        v-model="isShowDialogBugQuery"
        title="BUG查询"
        :close-on-click-modal="false"
        custom-class="social-module__dialog-bug-query"
    >
        <div class="tools-wrapper">
            <el-input
                v-model="toolsParams.keywords"
                placeholder="门店ID"
            />
            <div class="track"></div>
            <el-button
                type="primary"
                style="margin-left: 8px;"
                @click="onClickQueryBtn"
            >
                查询
            </el-button>
        </div>
        <el-table
            v-loading="loadingModel.loading.value"
            :data="showDataList"
            :height="530"
            style="width: 100%; margin: 16px 0;"
        >
            <el-table-column
                prop="createdTime"
                label="发生时间"
                width="160"
            />
            <el-table-column
                prop="province"
                label="发生地区"
                width="160"
                show-overflow-tooltip
            />
            <el-table-column
                prop="message"
                label="错误信息"
                min-width="240"
                show-overflow-tooltip
            />
            <el-table-column
                prop="pageReferrer"
                label="页面来源"
                width="200"
                show-overflow-tooltip
            />
            <el-table-column
                prop="pageTarget"
                label="页面目标"
                width="200"
                show-overflow-tooltip
            />
            <el-table-column
                prop="handles"
                label="操作"
                width="100"
                fixed="right"
            >
                <template #default="scope">
                    <div style="display: flex; align-items: center;">
                        <el-button
                            type="primary"
                            plain
                            :icon="Promotion"
                            @click="onClickBugDetial(scope.row)"
                        >
                            定位
                        </el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            v-model:current-page="pageModel.params.page"
            :page-size="pageModel.params.pageSize"
            :total="pageModel.params.total"
            background
            layout="total, prev, pager, next"
        />
    </el-dialog>
</template>

<script lang="ts" setup>
import * as utils from '../../common/utils';
import { ElMessage } from 'element-plus';
import { Promotion } from '@element-plus/icons-vue';
import { createBugQueryController } from './controller';
import { computed } from 'vue';
import { openExternal } from '@abc-oa/utils/src/electron';

const props = defineProps({
    modelValue: {
        type: Number,
        default: 0,
    },
});

const $emit = defineEmits([
    'update:modelValue', // 更新显示状态
    'select', // 选择
]);

const isShowDialogBugQuery = computed({
    get() {
        return !!props.modelValue;
    },
    set(val:boolean) {
        $emit('update:modelValue', +val);
    },
});

const {
    loadingModel,
    pageModel,
    toolsParams,
    logInfo,
    showDataList,
    createLogSearchParams,
    requestLogSearchResult,
} = createBugQueryController();

/**
 * 当点击查询时
 * <AUTHOR>
 * @date 2025-06-12
 */
const onClickQueryBtn = async () => {
    loadingModel.setLoading(true);
    const params = createLogSearchParams();
    const response = await requestLogSearchResult(params);
    loadingModel.setLoading(false);
    if (!response || !utils.isEqual(params, createLogSearchParams())) {
        return response;
    }
    if (response.status === false) {
        return ElMessage.error('拉取数据失败: ' + response.message);
    }
    logInfo.searchResult = response.data;
    if (logInfo.searchResult.dataList.length >= params.limit) {
        ElMessage.warning('日志条数大于100条，建议更精准的查询');
    }
    return response;
};

/**
 * 当点击错误ID时
 * <AUTHOR>
 * @date 2025-06-12
 * @param {Object} row
 */
const onClickBugDetial = (row: any) => {
    const href = `${window.location.origin}/monitor/error?errorKey=${row.id}`;
    openExternal(href);
};
</script>

<style lang="scss">
    .social-module__dialog-bug-query {
        width: 1200px !important;

        .el-dialog__body {
            padding-top: 8px;
        }

        .tools-wrapper {
            display: flex;
            align-items: center;
        }

        .align-center {
            display: flex;
            align-items: center;
        }

        .el-pagination {
            justify-content: flex-end;
        }
    }
</style>