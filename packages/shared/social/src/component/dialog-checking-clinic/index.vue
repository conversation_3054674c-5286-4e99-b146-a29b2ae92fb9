<template>
    <el-dialog
        v-model="isShowDialogCheckingClinic"
        title="对账门店数据"
        :close-on-click-modal="false"
        custom-class="social-module__dialog-checking-clinic"
    >
        <div class="tools-wrapper">
            <el-select
                v-model="toolsParams.status"
                clearable
                placeholder="状态"
            >
                <el-option
                    v-for="item in statusOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
            </el-select>
            <el-input
                v-model="toolsParams.keyword"
                class="search-wrapper"
                :suffix-icon="Search"
                placeholder="机构代码 / 机构名称"
            />
            <div class="track"></div>
        </div>
        <el-table
            v-loading="loadingModel.loading.value"
            :data="showDataList"
            :height="530"
            style="width: 100%; margin: 16px 0;"
        >
            <el-table-column
                prop="hospitalCode"
                label="机构代码"
                width="160"
                show-overflow-tooltip
            />
            <el-table-column
                prop="hospitalName"
                label="机构名称"
                min-width="160"
                show-overflow-tooltip
            />
            <el-table-column
                prop="checkingStatus"
                label="对账状态"
                width="120"
            >
                <template #default="scope">
                    <el-tag :type="scope.row.checkingStatusType">
                        {{ scope.row.checkingStatusText }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column
                prop="remark"
                label="操作"
                width="220"
                fixed="right"
            >
                <template #default="scope">
                    <div style="display: flex; align-items: center;">
                        <el-button
                            type="text"
                            @click="onClickLoginClinic(scope.row)"
                        >
                            登录门店
                        </el-button>
                        <el-button
                            type="text"
                            @click="onClickRemoteExec(scope.row)"
                        >
                            远程执行
                        </el-button>
                        <el-button
                            type="text"
                            @click="onClickQueryLog(scope.row)"
                        >
                            日志查询
                        </el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            v-model:current-page="pageModel.params.page"
            :page-size="pageModel.params.pageSize"
            :total="pageModel.params.total"
            background
            layout="total, prev, pager, next"
        />
    </el-dialog>
</template>

<script lang="ts" setup>
import { Search } from '@element-plus/icons-vue';
import { createCheckingClinicController } from './controller';
import { computed } from 'vue';
import { ElMessage } from 'element-plus';
import { quickLoginAbc } from '@abc-oa/utils/src/utils';
import { openExternal } from '@abc-oa/utils/src/electron';

const props = defineProps({
    modelValue: {
        type: Number,
        default: 0,
    },
    region: {
        type: String,
        default: '',
    },
    status: {
        type: Number,
        default: 0,
    },
    clinicStatusList: {
        type: Array,
        default: () => [],
    }
});

const $emit = defineEmits([
    'update:modelValue', // 更新显示状态
    'select', // 选择
]);

const isShowDialogCheckingClinic = computed({
    get() {
        return !!props.modelValue;
    },
    set(val:boolean) {
        $emit('update:modelValue', +val);
    },
});

const {
    loadingModel,
    pageModel,
    toolsParams,
    statusOptions,
    showDataList,
    requestLoginClinicHref,
} = createCheckingClinicController(props);

/**
 * 当点击登录门店
 * <AUTHOR>
 * @date 2025-01-03
 * @param {Object} item
 */
const onClickLoginClinic = async (item: any) => {
    const response = await requestLoginClinicHref(item.clinicId);
    if (response.status === false) {
        return ElMessage.error(response.message);
    }
    quickLoginAbc(response.data);
};

/**
 * 当点击远程执行
 * <AUTHOR>
 * @date 2025-01-03
 * @param {Object} item
 */
const onClickRemoteExec = async (item: any) => {
    const href = `${window.location.origin}/cs/social/remote?clinicId=${item.clinicId}`;
    openExternal(href);
};

/**
 * 当点击日志查询
 * <AUTHOR>
 * @date 2025-01-03
 * @param {Object} item
 */
const onClickQueryLog = async (item: any) => {
    const href = `${window.location.origin}/cs/social/log?keywords=${item.clinicId}`;
    openExternal(href);
};

</script>

<style lang="scss">
    .social-module__dialog-checking-clinic {
        width: 960px !important;

        .el-dialog__body {
            padding-top: 8px;
        }

        .tools-wrapper {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding-right: 12px;

            .search-wrapper {
                width: 200px;
            }

            .el-select {
                width: 140px;
                margin-right: 8px;

                .el-input {
                    height: 32px;
                }
            }

            .track {
                flex: 1;
            }
        }

        .el-pagination {
            justify-content: flex-end;
        }
    }
</style>