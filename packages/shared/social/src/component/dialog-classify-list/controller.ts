/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import SocialApi from '../../api/social-api';
import AbcResponse from '../../common/AbcResponse';
import classifyStore from '../../store/classifyStore';
import * as utils from '../../common/utils';
import { reactive, computed } from 'vue';

import createLoadingModel from '../../model/loading';
import createPageModel from '../../model/page';
import createDialogModel from '../../model/dialog';
import createClassifyModel from '../../model/classify';

export const createClassifyListController = () => {
    const loadingModel = createLoadingModel();
    const pageModel = createPageModel();
    const dialogModelRemarkEdit = createDialogModel();
    const dialogModelExplain = createDialogModel();
    const classifyModel = createClassifyModel();

    const toolsParams = reactive({
        keyword: '',
    });

    // 当前编辑的数据
    const editData = reactive({
        classifyId: '',
    });
    
    // 当前配文的数据
    const explainData = reactive({
        classifyId: '',
        explainId: '',
    });

    const explainInfo = reactive({
        classifyIds: <string[]>[],
    });

    const deleteInfo = reactive({
        classifyIds: <string[]>[],
    });

    /**
     * 开始配文
     * <AUTHOR>
     * @date 2023-11-14
     * @param {String} classifyId
     */
    const startExplain = (classifyId: string) => {
        explainInfo.classifyIds.push(classifyId);
    };

    /**
     * 配文完成
     * <AUTHOR>
     * @date 2023-11-14
     * @param {String} classifyId
     */
    const finishExplain = (classifyId: string) => {
        explainInfo.classifyIds = explainInfo.classifyIds.filter((id) => id !== classifyId);
    };
    
    /**
     * 开始删除
     * <AUTHOR>
     * @date 2023-11-14
     * @param {String} classifyId
     */
    const startDelete = (classifyId: string) => {
        deleteInfo.classifyIds.push(classifyId);
    };

    /**
     * 删除完成
     * <AUTHOR>
     * @date 2023-11-14
     * @param {String} classifyId
     */
    const finishDelete = (classifyId: string) => {
        deleteInfo.classifyIds = deleteInfo.classifyIds.filter((id) => id !== classifyId);
    };

    // 筛选的数据列表
    const mateDataList = computed(() => {
        let dataList = classifyStore.classifyDataList.value;
        if (toolsParams.keyword) {
            // 通过关键字过滤
            dataList = dataList.filter((item) => (
                item.id.includes(toolsParams.keyword)
                || item.shortId.includes(toolsParams.keyword)
                || (item.includes || []).join('').includes(toolsParams.keyword)
                || (item.checkCode || '').includes(toolsParams.keyword)
                || (item.labels || []).join('').includes(toolsParams.keyword)
                || item.remark.includes(toolsParams.keyword)
            ));
        }
        pageModel.setTotal(dataList.length);
        pageModel.setPage(1);
        return dataList;
    });

    // 展示的数据列表
    const showDataList = computed(() => {
        const { sIndex, eIndex } = pageModel.createSliceParams();
        return mateDataList.value.slice(sIndex, eIndex).map((item) => {
            const itemInfo = {
                ...item,
                classifyWording: utils.getClassifyWording(item),
                labelWording: utils.getLabelWording(item),
                createTimeWording: utils.createDateTimeFormat19(item.created),
                isHasExplain: !!item.explainId,
                explainWording: '无',
                explainHtml: '',
                loadingExplain: explainInfo.classifyIds.includes(item.id),
                loadingDelete: deleteInfo.classifyIds.includes(item.id),
            };
            if (itemInfo.isHasExplain) {
                const target = classifyStore.explainDataList.value.find((item) => item.id === itemInfo.explainId);
                if (target) {
                    Object.assign(itemInfo, {
                        explainWording: target?.title,
                        explainHtml: `【${target?.title}】<br / >${target?.content.replace(/\n/g, '<br />') || ''}`,
                    });
                } else {
                    itemInfo.isHasExplain = false;
                }
            }
            return itemInfo;
        });
    });

    /**
     * 请求保存
     * 成功后更新store的classifyItem
     * <AUTHOR>
     * @date 2023-11-14
     * @param {Object} explainId
     * @returns {Promise<AbcResponse>}
     */
    const requestClassifyUpdate = async (explainId: string) => {
        if (explainData.explainId === explainId) {
            // 无需更新，视为更新成功
            return AbcResponse.success();
        }
        const classifyInfo = classifyStore.classifyDataList.value.find((item) => item.id === explainData.classifyId);
        if (!classifyInfo) {
            return AbcResponse.error('未找到归类信息');
        }
        classifyModel.setClassifyInfo(classifyInfo);
        classifyModel.setExplainId(explainId);
        const classifyPostData = classifyModel.createClassifyPostData();
        const updateResponse = await SocialApi.updateCategory(explainData.classifyId, classifyPostData);
        if (updateResponse.status === false) {
            return updateResponse;
        }
        classifyStore.updateClassifyItem(explainData.classifyId, updateResponse.data);
        return updateResponse;
    };

    /**
     * 请求删除
     * <AUTHOR>
     * @date 2023-11-14
     * @param {String} classifyId
     * @returns {Promise<AbcResponse>}
     */
    const requestDelete = async (classifyId: string): Promise<AbcResponse> => {
        const deleteResponse = await SocialApi.deleteCategory(classifyId);
        if (deleteResponse.status === false) {
            return deleteResponse;
        }
        classifyStore.delClassifyItem(classifyId);
        return deleteResponse;
    };

    return {
        classifyStore,
        loadingModel,
        pageModel,
        dialogModelRemarkEdit,
        dialogModelExplain,
        toolsParams,
        editData,
        explainData,
        showDataList,
        startExplain,
        finishExplain,
        startDelete,
        finishDelete,
        requestClassifyUpdate,
        requestDelete,
    };
};