<template>
    <div>
        <el-dialog
            v-model="isShowDialogControlCreate"
            title="新增放量控制"
            width="680px"
            append-to-body
            :close-on-click-modal="false"
            custom-class="social-module__dialog-control-create"
        >
            <el-form
                :model="formData"
                label-width="80px"
                ref="formRef"
                :rules="rules"
            >
                <el-form-item label="推送地区" prop="regions">
                    <el-cascader
                        v-model="formData.regions"
                        :options="regionCascaderOptions"
                        :props="{
                        multiple: true,
                    }"
                        collapse-tags
                        :show-all-levels="false"
                        :max-collapse-tags="1"
                        clearable
                        placeholder="选择地区"
                        @change="onChangeRegions"
                    ></el-cascader>
                </el-form-item>
                <el-form-item label="推送版本" prop="dllVersions">
                    <el-cascader
                        v-model="formData.dllVersions"
                        :options="dllVersionCascaderOptions"
                        collapse-tags
                        :show-all-levels="false"
                        :max-collapse-tags="1"
                        clearable
                        placeholder="选择动态库版本"
                    ></el-cascader>
                </el-form-item>
                <el-form-item label="推送标题" prop="title">
                    <el-input
                        v-model="formData.title"
                        placeholder="请输入推送标题"
                        style="width: 300px;"
                    ></el-input>
                </el-form-item>
                <el-form-item label="推送内容" prop="description">
                    <el-input
                        type="textarea"
                        v-model="formData.description"
                        placeholder="请输入内容"
                        rows="5"
                    ></el-input>
                </el-form-item>
                <el-form-item label="推送时机" prop="pushTiming">
                    <div style="display: flex; flex-direction: column; gap: 10px;">
                        <div style="display: flex; gap: 10px;">
                            <el-checkbox v-model="formData.onEnterSystem" label="进入系统时" :value="1" style="width: 80px;"/>
                            <el-form-item label="提醒频率" prop="onEnterSystem">
                                <el-radio-group v-model="formData.onEnterSystemConfig" :disabled="!formData.onEnterSystem">
                                    <el-radio :label="1">仅一次</el-radio>
                                    <el-radio :label="2">每日一次</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </div>
                        <div style="display: flex; gap: 10px;">
                            <el-checkbox v-model="formData.onReadCard" label="读卡时" :value="1"  style="width: 80px;"/>
                            <el-form-item label="提醒频率" prop="onEnterSystem">
                                <el-radio-group v-model="formData.onReadCardConfig" :disabled="!formData.onReadCard">
                                    <el-radio :label="1">仅一次</el-radio>
                                    <el-radio :label="2">每日一次</el-radio>
                                    <el-radio :label="3">强制提醒</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </div>
                    </div>
                </el-form-item>
                <el-form-item label="放量方式" prop="pushStrategy">
                    <el-radio-group v-model="formData.pushStrategy" @change="() => {
						formData.total = 0
						formData.deviceList = []
                }">
                        <el-radio :label="1">按百分比放量</el-radio>
                        <el-radio :label="0">按设备码放量</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="百分比值" v-if="isShowSlider" prop="total">
                    <div class="slider-wrapper">
                        <el-slider
                            v-model="formData.total"
                            :step="5"
                            size="small"
                            show-stops
                            :show-tooltip="false"
                        />
                        <span class="slider-value">{{ formData.total }}%</span>
                    </div>
                </el-form-item>
                <el-form-item label="已选设备" v-else prop="deviceList">
                    <el-button
                        type="primary"
                        link
                        @click="dialogModelSelectDeviceVisible = 1"
                    >
                        选择设备
                    </el-button>
                    <span class="select-count">{{ formData.deviceList.length }} / {{ limitCount }}</span>
                </el-form-item>
            </el-form>
            <template #footer>
            <span class="dialog-footer">
                <el-button
                    type="primary"
                    :disabled="isDisabledSaveBtn"
                    :loading="loading"
                    @click="onClickSaveBtn"
                >
                    保存
                </el-button>
                <el-button @click="isShowDialogExplainEdit = false">取消</el-button>
            </span>
            </template>

            <dialog-select-device
                v-if="dialogModelSelectDeviceVisible"
                v-model="dialogModelSelectDeviceVisible"
                :queryParams="formData"
                @select="handleSelectDevice"
            />
        </el-dialog>

        <dialog-control-record v-model="isShowRecordDialog" :record-list="recordList" v-if="isShowRecordDialog"/>
    </div>
</template>

<script lang="ts" setup>
import { ElMessage, ElLoading } from 'element-plus';
import { ref, computed, reactive } from 'vue';
import type { FormInstance, FormRules } from 'element-plus'
import _ from 'lodash';

import DialogSelectDevice from '../dialog-select-device/index.vue';
import DialogControlRecord from '../dialog-control-record/index.vue';
import * as utils from '@social/common/utils';
import { SocialApi } from "../../../index";

const props = defineProps({
    modelValue: {
        type: Number,
        default: 0,
    },
    regions: {
        type: Array,
        default: () => [],
    },
    baseDllVersionOptions: {
        type: Array,
        default: () => [],
    }
});

/*
* 表单相关
*/
interface FormData {
    regions: Array<string>,
    dllVersions: Array<string>,
    title: string,
    description: string,
    pushStrategy: string,
    total: number,
    deviceList: Array<string>,
    onEnterSystem: number,
    onEnterSystemConfig: number | null,
    onReadCard: number,
    onReadCardConfig: number | null,
    pushTiming: number,
}

const rules = reactive<FormRules<FormData>>({
    regions: [
        {
            required: true,
            message: '请选择地区',
            trigger: 'change',
        },
    ],
    dllVersions: [
        {
            required: true,
            message: '请选择动态库版本',
            trigger: 'change',
        }
    ],
    title: [
        {
            required: true,
            message: '请输入推送标题',
            trigger: 'blur',
        },
    ],
    description: [
        {
            required: true,
            message: '请输入推送内容',
            trigger: 'blur',
        }
    ],
    pushStrategy: [
        {
            required: true,
            message: '请选择放量方式',
            trigger: 'blur',
        }
    ],
    total: [
        {
            required: true,
            message: '请输入百分比值',
            trigger: 'change',
        },
        {
            type: 'number',
            min: 5,
            message: '放量值必须大于5%',
        }
    ],
    deviceList: [
        {
            required: true,
            message: '请选择设备',
            trigger: 'blur'
        }
    ],
    pushTiming: [
        {
            required: true,
            message: '请选择推送时机',
            validator: (rule, value, callback) => {
                if (formData.onEnterSystem || formData.onReadCard) {
                    if (formData.onEnterSystemConfig === null && formData.onReadCardConfig === null) {
                        callback(new Error('请选择提醒频率'))
                    } else {
                        callback()
                    }
                } else {
                    callback(new Error('请选择提醒频率'))
                }
            },
            trigger: 'submit'
        }
    ]
})

const formRef = ref<FormInstance>();

const createFormData = () => {
    return reactive<FormData>({
        regions: utils.cloneDeep(props.regions),
        dllVersions: [],
        title: '',
        description: '',
        pushStrategy: 1,
        total: 0,
        deviceList: [],
        onEnterSystem: 0,
        onEnterSystemConfig: null,
        onReadCard: 0,
        onReadCardConfig: null,
        pushTiming: 0,
    })
}

const limitCount = ref(100);

const formData = createFormData()

// 是否更新了表单数据
const isUpdatedFormData = computed(() => {
    const initFormData = createFormData();
    return !utils.isEqual(initFormData, formData);
});

// 按钮是否禁用
const isDisabledSaveBtn = computed(() => {
    return !isUpdatedFormData.value;
});

// 是否显示百分比滑块
const isShowSlider = computed(() => {
    return formData.pushStrategy=== 1;
});


/*
* 弹窗相关
*/
const dialogModelSelectDeviceVisible = ref(0);

const isShowRecordDialog = ref(0)

const loading = ref(false);

const $emit = defineEmits([
    'update:modelValue', // 更新显示状态
    'update', // 更新
]);

const isShowDialogControlCreate = computed({
    get() {
        return !!props.modelValue;
    },
    set(val:boolean) {
        $emit('update:modelValue', +val);
    },
});

const regionCascaderOptions = utils.getRegionCascaderOptions();

const dllVersionCascaderOptions = ref(utils.cloneDeep(props.baseDllVersionOptions));

/**
 * 省份名称变化
 * <AUTHOR>
 * @date 2025-06-10
 * @param originalRegions
 */
const onChangeRegions = (originalRegions: string[]) => {
    const regions = utils.cascaderSelectSingle(originalRegions)
    if (regions.length === 0) {
        formData.dllVersions = []
        dllVersionCascaderOptions.value = []
        return
    }
    formData.regions = regions
    fetchDllLibByRegion()
};

const regionList = computed(() => {
    return formData.regions.map((item) => item[item.length - 1])
        .reduce((arr, region) => {
            arr.push(region);
            return arr;
        }, []);
})

/**
 * 根据regions查询动态库
 * <AUTHOR>
 * @date 2025-09-08
 */
const fetchDllLibByRegion = async () => {
    const response = await SocialApi.fetchDllLibList({
        regionList: regionList.value,
    })
    if (response.status === false) {
        return response
    }
    dllVersionCascaderOptions.value = response.data
}

/**
 * 当点击保存时触发
 * <AUTHOR>
 * @date 2023-11-07
 */
const onClickSaveBtn = async () => {
    if (!formRef.value) {
        return
    }
    await formRef.value.validate( async (valid, fields) => {
        if (valid) {
            await checkCanCreate()
            if (recordList.value.length > 0) {
                isShowRecordDialog.value = 1
                return
            }
            await createDllLibPushPlan()
        } else {
            console.log('表单验证失败！', fields)
        }
    })
};


const createDllLibPushPlan = async () => {
    loading.value = true;
    const postData = {
        region: regionList.value.join(','),
        exeLibName: formData.dllVersions[0],
        exeLibVersion: formData.dllVersions[1],
        title: formData.title,
        description: formData.description,
        pushStrategy: formData.pushStrategy,
        pushStrategyConfig: (() => {
            if (formData.pushStrategy === 0) {
                return JSON.stringify({
                    devices: formData.deviceList.map((item) => {
                        return {
                            chainId: item.chainId,
                            clinicId: item.clinicId,
                            deviceId: item.deviceId,
                        }
                    }),
                    expContent: createExpContent(),
                })
            }
            return JSON.stringify({
                percentage: formData.total,
                exeLibName: formData.dllVersions[0],
                expContent: createExpContent(),
            })
        })()
    }
    const response = await SocialApi.createDllLibPushPlan(postData)
    loading.value = false
    if (response.status === false) {
        return ElMessage.error('创建失败: ' + response.message)
    }
    ElMessage.success('创建成功')
    isShowDialogControlCreate.value = false
    $emit('update')
}

const createExpContent = () => {
    const expContent = {
        pushTiming: (() => {
            const timing = []
            if (formData.onEnterSystem) {
                timing.push({
                    type: 1,
                    config: formData.onEnterSystemConfig,
                })
            }
            if (formData.onReadCard) {
                timing.push({
                    type: 2,
                    config: formData.onReadCardConfig,
                })
            }
            return timing
        })()
    }
    return expContent
}

const recordList = ref([])
const checkCanCreate = async () => {
    const loading = ElLoading.service({
        target: '.social-module__dialog-control-create',
        text: '正在查询历史推送记录，请稍后...',
    })
    try {
        const promiseArr= []
        const dataList = []
        for (const region of regionList.value) {
            const params =  creatParams(region)
            promiseArr.push(SocialApi.fetchPushRecordList(params))
        }
        const result = await Promise.all(promiseArr)
        for (const resultElement of result) {
            dataList.push(...resultElement.data)
        }
        recordList.value = _.uniqBy(dataList, 'id')
    } catch (e) {
        ElMessage.error(e.message)
    } finally {
        loading.close()
    }
}

const creatParams = (region: string) => {
    return {
        region: [ region ],
        status: 1,
    }
}

const handleSelectDevice = (deviceList: Array<string>) => {
    formData.deviceList = deviceList
}

</script>

<style lang="scss">
    .social-module__dialog-control-create {
        .el-cascader {
            width: 190px;
            margin-right: 8px;

            .el-input {
                height: 32px;
            }

            .el-select-tags-wrapper {
                height: 100%;
            }

            .el-select__tags {
                height: 100%;
                padding-top: 2px;
                box-sizing: border-box;
            }
        }

        .slider-wrapper {
            width: 100%;
            display: flex;
            align-items: center;
            padding-left: 10px;

            .el-slider {
                flex: 1;
            }

            .slider-value {
                margin-left: 8px;
                width: 50px;
                text-align: right;
            }
        }

        .select-count {
            font-size: 16px;
            margin-left: 8px;
        }
    }
</style>
