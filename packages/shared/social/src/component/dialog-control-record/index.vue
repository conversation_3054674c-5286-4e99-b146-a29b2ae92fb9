<template>
    <el-dialog
        v-model="isShowDialogControlRecord"
        title="放量控制记录"
        :close-on-click-modal="false"
        custom-class="social-module__dialog-control-record"
        :modal="!isFromCreate"
    >
        <div class="tools-wrapper">
            <el-form inline class="custom-inline-form">
                <el-form-item>
                    <el-cascader
                        v-model="toolsParams.regions"
                        :options="regionCascaderOptions"
                        :props="cascaderProps"
                        collapse-tags
                        :show-all-levels="false"
                        :max-collapse-tags="1"
                        clearable
                        placeholder="选择地区"
                        @change="onChangeRegions"
                    ></el-cascader>
                </el-form-item>
            </el-form>
            <el-input
                v-model="toolsParams.keywords"
                class="search-wrapper"
                :suffix-icon="Search"
                placeholder="标题 / 内容"
                clearable
                @change="handleSearch"
            />
            <el-select
                v-model="toolsParams.status"
                clearable
                placeholder="状态"
                @change="handleSearch"
            >
                <el-option
                    v-for="item in typeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
            </el-select>
            <div class="track"></div>
        </div>
        <el-table
            v-loading="loadingModel.loading.value"
            :data="showDataList"
            :height="530"
            style="width: 100%; margin: 16px 0;"
        >
            <el-table-column
                prop="title"
                label="标题"
                width="160"
                fixed
                show-overflow-tooltip
            />
            <el-table-column
                prop="description"
                label="内容"
                width="160"
                show-overflow-tooltip
            />
            <el-table-column
                prop="exeLibVersion"
                label="动态库版本"
                width="100"
                show-overflow-tooltip
            />
            <el-table-column
                prop="region"
                label="推送地区"
                width="100"
            >
                <template #default="scope">
                    <template v-if="scope.row.region.length > 1">
                        <el-popover
                            width="200"
                            placement="right"
                        >
                            <template #reference>
                                <div>
                                    <span style="cursor: pointer;">{{ scope.row.region[0].regionName }}</span>
                                    <span >...</span>
                                </div>
                            </template>
                            <el-table
                                :data="scope.row.region"
                                border
                            >
                                <el-table-column
                                    prop="region"
                                    label="region"
                                    width="160"
                                />
                                <el-table-column
                                    prop="regionName"
                                    label="地区"
                                    width="120"
                                />
                            </el-table>
                        </el-popover>
                    </template>
                    <template v-else>
                        <span>{{ scope.row.region[0].regionName }}</span>
                    </template>
                </template>
            </el-table-column>
            <el-table-column
                prop="pushStrategy"
                label="推送类型"
                width="120"
                show-overflow-tooltip
            >
                <template #default="scope">
                    <el-tag>
                        {{ scope.row.pushStrategy === 0 ? '按设备' : '按百分比' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column
                prop="pushStrategyConfig"
                label="推送场景"
                show-overflow-tooltip
                width="180"
            >
                <template #default="scope">
                    <div style="display: flex; flex-direction: column; gap: 4px;" v-if="scope.row.pushStrategyConfig?.expContent?.pushTiming">
                        <span
                            style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden;"
                            v-for="item in scope.row.pushStrategyConfig.expContent.pushTiming" :key="item.id">
                            {{ getPushTimingWord(item) }}
                        </span>
                    </div>
                    <span v-else>-</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="status"
                label="推送状态"
                show-overflow-tooltip
            >
                <template #default="scope">
                    <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
                        {{ scope.row.status === 1 ? '激活' : '暂停' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column
                prop="created"
                label="创建时间"
                show-overflow-tooltip
                width="160"
            >
                <template #default="scope">
                    {{ dayjs(scope.row.created).format('YYYY-MM-DD HH:mm:ss') }}
                </template>
            </el-table-column>
            <el-table-column
                prop="remark"
                label="操作"
                width="80"
                fixed="right"
            >
                <template #default="scope">
                    <div style="display: flex; align-items: center;"><el-button
                        v-if="scope.row.status === 1"
                        size="small"
                        type="primary"
                        plain
                        :loading="scope.row.loadingStatus"
                        @click="onClickOpreation(scope.row, 1)"
                    >
                        暂停
                    </el-button>
                    <el-button
                        v-else
                        size="small"
                        type="primary"
                        plain
                        :loading="scope.row.loadingStatus"
                        @click="onClickOpreation(scope.row, 2)"
                    >
                        激活
                    </el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            style="margin-top: 14px;"
            v-model:current-page="pageModel.params.page"
            :page-size="pageModel.params.pageSize"
            :total="pageModel.params.total"
            background
            layout="total, prev, pager, next"
            @current-change="handlePageChange"
        />
    </el-dialog>
</template>

<script lang="ts" setup>
import { Search } from '@element-plus/icons-vue';
import { createControlRecordController } from './controller';
import {computed, onMounted, withDefaults} from 'vue';
import { SocialApi } from "../../../index";
import { ElMessageBox, ElMessage } from "element-plus";
import * as utils from '@social/common/utils';
import dayjs from 'dayjs';
import type { Datum } from "@social/type/version";
import { AbcResponse } from "@abc-oa/common/src/utils/AbcResponse";

interface Props {
    modelValue: number;
    recordList: Datum[];
    regions: string[];
}

interface Emits {
    (e: 'update:modelValue', value: number): void;
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: 0,
    recordList: () => [],
    regions: () => [],
})

const emit = defineEmits<Emits>();

const regionCascaderOptions = utils.getRegionCascaderOptions();

const cascaderProps = {
    multiple: true,
}

/**
 * 省份名称变化
 * <AUTHOR>
 * @date 2025-06-10
 * @param {string[]} regions
 */
const onChangeRegions = (regions: string[]) => {
    toolsParams.regions = utils.cascaderSelectSingle(regions)
    fetchData(regionList.value)
};

/**
 * 传给后端的region
 * <AUTHOR>
 * @date 2025-09-17
 * @param {string[]} regions
 */
const regionList = computed(() => {
    return toolsParams.regions.map((item) => item[item.length - 1])
        .reduce((arr: string[], region) => {
            arr.push(region);
            return arr;
        }, []);
})

const isShowDialogControlRecord = computed({
    get() {
        return !!props.modelValue;
    },
    set(val:boolean) {
        emit('update:modelValue', +val);
    },
});

const {
    loadingModel,
    pageModel,
    toolsParams,
    showDataList,
    handleSearch,
    handlePageChange,
    fetchData,
} = createControlRecordController();

const typeOptions = [
    {
        value: 1,
        label: '激活',
    },
    {
        value: 2,
        label: '暂停',
    },
]

onMounted(async () => {
    if (props.regions.length) {
        toolsParams.regions = utils.cloneDeep(props.regions)
    }
    if (isFromCreate.value) {
        await fetchData(regionList.value, props.recordList);
        return ElMessage.error('当前地区已有推送记录，请先删除后再创建')
    }
    await fetchData(regionList.value);
});

const isFromCreate = computed(() => props.recordList.length)

/**
 * 当点击操作
 * @param item
 * @param type
 */
const onClickOpreation = async (item: any, type: number) => {
    item.loadingStatus = true
    let responseHandle
    switch (type) {
        case 1:
            responseHandle = SocialApi.pausePushRecord(item.id)
            break
        case 2:
            responseHandle = SocialApi.resumePushRecord(item.id)
            break
        case 3:
            try {
                await ElMessageBox.confirm('确定要删除吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                });
                responseHandle = SocialApi.deletePushRecord(item.id)
            } catch (error) {
                item.loadingStatus = false
                return
            }
            break
        default:
            responseHandle = AbcResponse.error('未知操作')
            break
    }
    const response = await responseHandle
    item.loadingStatus = false
    if (response.status === false && response.message.includes('恢复医保控件版本推送任务失败,有更新的推送计划')) {
        return ElMessage.error('存在更新的推送任务，不允许激活')
    }
    ElMessage.success('操作成功')
    item.status = item.status === 1 ? 2 : 1
}

const getPushTimingWord = (item: any) => {
    const pushType = item.type === 1 ? '登录系统时' : '读卡时'
    const pushTiming = ['仅一次', '每日一次', '强制提醒'][item.config - 1]
    return `推送方式：${pushType}；推送频率：${pushTiming}；`
}

</script>

<style lang="scss">
    .social-module__dialog-control-record {
        width: 1200px !important;

        .el-dialog__body {
            padding-top: 8px;
        }

        .tools-wrapper {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding-right: 12px;

            .search-wrapper {
                width: 200px;
            }

            .el-select {
                width: 140px;
                margin-left: 8px;

                .el-input {
                    height: 32px;
                }
            }

            .track {
                flex: 1;
            }
        }

        .el-pagination {
            justify-content: flex-end;
        }
    }
</style>
