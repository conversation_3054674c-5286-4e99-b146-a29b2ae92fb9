<template>
    <el-dialog
        v-model="isShowDialogExplainEdit"
        :title="title"
        width="560px"
        align-center
        append-to-body
        :close-on-click-modal="false"
        custom-class="social-module__dialog-explain-edit"
    >
        <el-form :model="explainModel.explainInfo" label-width="56px">
            <el-form-item label="标题">
                <el-input
                    v-model="explainModel.explainInfo.title"
                    placeholder="请输入标题"
                ></el-input>
            </el-form-item>
            <el-form-item label="内容">
                <el-input
                    v-model="explainModel.explainInfo.content"
                    type="textarea"
                    :rows="6"
                    placeholder="请输入内容"
                ></el-input>
            </el-form-item>
            <el-form-item label="链接">
                <el-tag
                    v-for="(item, index) in explainModel.explainInfo.linkList"
                    :key="index"
                    closable
                    @close="explainModel.delLinkItem(index)"
                >
                    {{ item.name }}
                </el-tag>
                <el-popover
                    v-model:visible="isShowPopover"
                    placement="top-start"
                    title="添加链接"
                    :width="320"
                    trigger="click"
                >
                    <template #reference>
                        <el-icon class="add-link" @click="onClickAddLink"><Plus /></el-icon>
                    </template>
                    <template #default>
                        <div class="subform-wrapper">
                            <el-form
                                ref="subFrom"
                                :model="linkModel.linkItem"
                                :rules="linkModel.rules"
                                label-width="56px"
                            >
                                <el-form-item label="类型">
                                    <el-radio-group v-model="linkModel.linkItem.type">
                                        <el-radio label="link">链接</el-radio>
                                        <el-radio label="code">二维码</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                                <el-form-item label="名称" prop="name">
                                    <el-input v-model="linkModel.linkItem.name" style="width: 140px;"></el-input>
                                </el-form-item>
                                <el-form-item label="链接" prop="link">
                                    <el-input v-model="linkModel.linkItem.link"></el-input>
                                </el-form-item>
                            </el-form>
                            <el-button type="primary" @click="onConfirmAddLink">确定</el-button>
                        </div>
                    </template>
                </el-popover>
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button
                    type="primary"
                    :disabled="isDisabledSaveBtn"
                    :loading="loadingModel.loading.value"
                    @click="onClickSaveBtn"
                >
                    保存
                </el-button>
                <el-button @click="isShowDialogExplainEdit = false">取消</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ElMessage, FormInstance } from 'element-plus';
import { createExplainEditController } from './controller';
import { ref, computed, onMounted } from 'vue';

const props = defineProps({
    modelValue: {
        type: Number,
        default: 0,
    },
    action: {
        type: String, //  String as PropType<'create' | 'update'>
        default: 'create',
    },
    explainId: {
        type: String,
        default: '',
    },
});

const $emit = defineEmits([
    'update:modelValue', // 更新显示状态
    'update', // 更新
]);

const isShowDialogExplainEdit = computed({
    get() {
        return !!props.modelValue;
    },
    set(val:boolean) {
        $emit('update:modelValue', +val);
    },
});

const {
    loadingModel,
    linkModel,
    explainModel,
    isDisabledSaveBtn,
    initExplainInfo,
    requestCreate,
    requestUpdate,
} = createExplainEditController(props.explainId);

const subFrom = ref<FormInstance>();
const isShowPopover = ref(false);
const title = computed(() => (props.action === 'create' ? '新增文案' : '编辑文案'));

onMounted(() => {
    initExplainInfo(explainModel);
});

/**
 * 当点击添加链接时触发
 * <AUTHOR>
 * @date 2023-11-27
 */
const onClickAddLink = () => {
    isShowPopover.value = true;
    linkModel.initLinkItem();
};

/**
 * 当确认添加链接时触发
 * <AUTHOR>
 * @date 2023-11-15
 */
const onConfirmAddLink = () => {
    if (!subFrom.value) {
        return;
    }
    subFrom.value.validate(async (valid: boolean) => {
        if (!valid) {
            return;
        }
        const linkItem = linkModel.createLinkPostData();
        explainModel.addLinkItem(linkItem);
        isShowPopover.value = false;
    });
};

/**
 * 当点击保存时触发
 * <AUTHOR>
 * @date 2023-11-07
 */
const onClickSaveBtn = async () => {
    if (loadingModel.loading.value === true) {
        return;
    }
    loadingModel.setLoading(true);
    const response = explainModel.isUpdate.value ? await requestUpdate() : await requestCreate();
    loadingModel.setLoading(false);
    if (response.status === false) {
        return ElMessage.error('保存失败: ' + response.message);
    }
    $emit('update');
    ElMessage.success('保存成功');
    isShowDialogExplainEdit.value = false;
};
</script>

<style lang="scss">
    .social-module__dialog-explain-edit {
        .add-link {
            width: 48px;
            height: 24px;
            line-height: 24px;
            border-radius: 16px;
            border: 1px solid #dcdfe6;
            cursor: pointer;
        }

        .el-tag {
            margin-right: 8px;
        }
    }
</style>