/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import SocialApi from '../../api/social-api';
import AbcResponse from '../../common/AbcResponse';
import classifyStore from '../../store/classifyStore';
import * as utils from '../../common/utils';
import { reactive, computed } from 'vue';

import createLoadingModel from '../../model/loading';
import createDialogModel from '../../model/dialog';
import createPageModel from '../../model/page';

export const createExplainListController = () => {
    const loadingModel = createLoadingModel();
    const dialogModel = createDialogModel();
    const pageModel = createPageModel();

    const toolsParams = reactive({
        keyword: '',
    });

    const explainEditProps = reactive({
        action: 'create',
        explainId: '',
    });

    const deleteInfo = reactive({
        explainIds: <string[]>[],
    });

    /**
     * 开始删除
     * <AUTHOR>
     * @date 2023-11-14
     * @param {String} explainId
     */
    const startDelete = (explainId: string) => {
        deleteInfo.explainIds.push(explainId);
    };

    /**
     * 删除完成
     * <AUTHOR>
     * @date 2023-11-14
     * @param {String} explainId
     */
    const finishDelete = (explainId: string) => {
        deleteInfo.explainIds = deleteInfo.explainIds.filter((item) => item !== explainId);
    };

    /**
     * 创建文案库数据
     * <AUTHOR>
     * @date 2023-11-14
     */
    const createExplainItem = () => {
        explainEditProps.action = 'create';
        explainEditProps.explainId = '';
    };

    /**
     * 更新文案库数据
     * <AUTHOR>
     * @date 2023-11-14
     */
    const updateExplainItem = (explainId: string) => {
        explainEditProps.action = 'update';
        explainEditProps.explainId = explainId;
    };

    // 筛选的数据列表
    const mateDataList = computed(() => {
        let dataList = classifyStore.explainDataList.value;
        if (toolsParams.keyword) {
            // 通过关键字过滤
            dataList = dataList.filter((item) => (
                item.id.includes(toolsParams.keyword)
                || item.title.includes(toolsParams.keyword)
                || item.content.includes(toolsParams.keyword)
            ));
        }
        return dataList;
    });

    // 展示的数据列表
    const showDataList = computed(() => {
        pageModel.setTotal(mateDataList.value.length);
        const { sIndex, eIndex } = pageModel.createSliceParams();
        return mateDataList.value.slice(sIndex, eIndex).map((item) => {
            const itemInfo = {
                ...item,
                linkListWording: (item.linkList || []).map((link) => link.name).join('、'),
                contentHtml: item.content.replace(/\n/g, '<br />'),
                createTimeWording: utils.createDateTimeFormat19(item.created),
                uesdCount: '0 次',
                isDisabledDelete: false,
                loadingDelete: deleteInfo.explainIds.includes(item.id),
            };
            const uesdCount = getUesdCount(item.id);
            if (uesdCount > 0) {
                itemInfo.uesdCount = `${uesdCount} 次`;
                itemInfo.isDisabledDelete = true;
            }
            return itemInfo;
        });
    });

    /**
     * 获取使用次数
     * <AUTHOR>
     * @date 2023-11-23
     * @param {String} explainId
     * @returns {Number}
     */
    const getUesdCount = (explainId: string) => classifyStore.classifyDataList.value.reduce((count, item) => {
        if (item.explainId === explainId) {
            count += 1;
        }
        return count;
    }, 0);

    /**
     * 请求删除
     * <AUTHOR>
     * @date 2023-11-14
     * @param {String} explainId
     * @returns {Promise<AbcResponse>}
     */
    const requestDelete = async (explainId: string): Promise<AbcResponse> => {
        const deleteResponse = await SocialApi.deleteExplain(explainId);
        if (deleteResponse.status === false) {
            return deleteResponse;
        }
        classifyStore.delExplainItem(explainId);
        return deleteResponse;
    };

    return {
        classifyStore,
        loadingModel,
        dialogModel,
        pageModel,
        explainEditProps,
        toolsParams,
        showDataList,
        startDelete,
        finishDelete,
        createExplainItem,
        updateExplainItem,
        requestDelete,
    };
};