<template>
    <el-dialog 
        v-model="isShowDialogFullScreenViewer" 
        fullscreen
        :show-close="false" 
        custom-class="social-module__dialog-full-screen-viewer"
    >
        <el-scrollbar class="json-wrapper">
            <viewer-ctrl
                v-model="numm"
                :show-full-screen-btn="false"
            />
            <el-button
                class="close-btn"
                :icon="Close"
                circle
                @click="isShowDialogFullScreenViewer = false"
            />
            <vue-json-viewer
                :key="numm"
                :value="logData"
                :expand-depth="numm"
            ></vue-json-viewer>
        </el-scrollbar>
    </el-dialog>  
</template>

<script lang="ts" setup>
import VueJsonViewer from 'vue-json-viewer';
import ViewerCtrl from '../viewer-ctrl/index.vue';
import { ref, computed } from 'vue';
import { Close } from '@element-plus/icons-vue';

const props = defineProps({
    modelValue: {
        type: Number,
        default: 0,
    },
    num: {
        type: Number,
        default: 5,
    },
    logData: {
        type: Object,
        default: () => ({}),
    },
});

const $emit = defineEmits([
    'update:modelValue', // 更新显示状态
]);

const numm = ref(props.num);

const isShowDialogFullScreenViewer = computed({
    get() {
        return !!props.modelValue;
    },
    set(val:boolean) {
        $emit('update:modelValue', +val);
    },
});
</script>

<style lang="scss">
    .social-module__dialog-full-screen-viewer {
        .el-dialog__header {
            display: none;
        }

        .el-dialog__body {
            padding: 0;
            height: 100%;

            .json-wrapper {
                position: relative;

                .social-module__viewer-ctrl {
                    position: absolute;
                    top: 8px;
                    right: 52px;
                }

                .close-btn {
                    position: absolute;
                    top: 12px;
                    right: 16px;
                    z-index: 99;
                }
            }

            .jv-container {
                padding: 16px;
            }
        }
    }
</style>