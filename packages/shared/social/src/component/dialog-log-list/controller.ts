/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import SocialApi from '../../api/social-api';
import AbcResponse from '../../common/AbcResponse';
import * as utils from '../../common/utils';
import { reactive, computed } from 'vue';

import createPageModel from '../../model/page';
import createLoadingModel from '../../model/loading';

export const createLogListController = (baseParams: any) => {
    const loadingModel = createLoadingModel();
    const pageModel = createPageModel();

    pageModel.setPage(1);
    pageModel.setTotal(parseInt(baseParams.total || 0));

    // 日志信息
    const logData = reactive({
        dataList: <any []>[],
    });

    // 展示的数据列表
    const showDataList = computed(() => logData.dataList.map((item: any) => {
        const itemInfo = {
            errorId: '', // 错误id
            regionWording: '', // 区域
            transTypeWording: '', // 交易类型
            originMessage: '', // 原始信息
            time: '', // 耗时
            processId: '', // 进程id
            processName: '', // 进程名称
            createTime: '', // 创建时间
            logData: null, // 日志数据
        };
        try {
            const {
                createTime,
                region, // 区域
                processId,
                processName,
                data,
            } = item;
            const {
                transType, // 交易类型
                transName, // 交易名称
                response,
                time,
            } = data;
            Object.assign(itemInfo, {
                errorId: response.errorId, // 错误id
                regionWording: utils.getRegionWording(region), // 区域
                transTypeWording: `${transName}（${transType}）`, // 交易类型
                originMessage: response.message, // 原始信息
                time, // 耗时
                processId, // 进程id
                processName, // 进程名称
                createTime, // 创建时间
                logData: data, // 日志数据
            });
        } catch (error) {
            console.log('logData.dataList error', error);
        }
        return itemInfo;
    }));

    /**
     * 设置日志数据列表
     * <AUTHOR>
     * @date 2023-11-21
     * @param {Array} logItems
     */
    const setLogDataList = (logItems: any[]) => {
        logData.dataList = logItems.map((item: any) => {
            try {
                const info = utils.parseMessage(item.message);
                info.createTime = item.time.slice(0, 19);
                return info;
            } catch (error) {
                console.log('setLogDataList error', error);
                return null;
            }
        }).filter((item: any) => item !== null);
    };

    /**
     * 创建日志查询参数
     * <AUTHOR>
     * @date 2023-11-15
     * @returns {Object}
     */
    const createFetchParams = () => {
        const {
            startDate, // 开始日期
            endDate, // 结束日期
            classifyId, // 分类id
        } = baseParams;
        const {
            offset, // 偏移量
            limit,
        } = pageModel.createFetchParams();
        const fetchParams = {
            logStore: utils.createLogStore(), // 日志存储位置
            beginDate: `${startDate} 00:00:00`, // 开始日期
            endDate: `${endDate} 23:59:59`, // 结束日期
            offset, // 偏移量
            limit, // 每页条数
            query: classifyId, // 查询条件
        };
        return fetchParams;
    };

    /**
     * 请求日志数据列表
     * <AUTHOR>
     * @date 2023-11-21
     * @param {Object} params
     * @returns {Promise<AbcResponse>}
     */
    const requestLogDataList = async (params: any): Promise<AbcResponse> => {
        const fetchResponse = await SocialApi.fetchManagementLog(params);
        if (fetchResponse.status === false) {
            return fetchResponse;
        }
        return fetchResponse;
    };

    return {
        loadingModel,
        pageModel,
        showDataList,
        setLogDataList,
        createFetchParams,
        requestLogDataList,
    };
};