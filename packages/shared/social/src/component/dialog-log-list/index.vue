<template>
    <el-dialog
        v-model="isShowDialogLogList"
        title="日志详情"
        :close-on-click-modal="false"
        custom-class="social-module__dialog-log-list"
    >
        <el-table
            v-loading="loadingModel.loading.value"
            :data="showDataList"
            :height="530"
            style="width: 100%; margin: 16px 0;"
        >
            <el-table-column
                prop="errorId"
                label="错误ID"
                min-width="140"
            />
            <el-table-column
                prop="regionWording"
                label="地区"
                min-width="200"
                show-overflow-tooltip
            />
            <el-table-column
                prop="transTypeWording"
                label="交易类型"
                width="200"
                show-overflow-tooltip
            />
            <el-table-column
                prop="originMessage"
                label="原始报错"
                min-width="300"
                show-overflow-tooltip
            />
            <el-table-column
                prop="time"
                label="耗时"
                min-width="100"
                show-overflow-tooltip
            />
            <el-table-column
                prop="processId"
                label="进程ID"
                min-width="140"
                show-overflow-tooltip
            />
            <el-table-column
                prop="processName"
                label="进程名称"
                min-width="140"
                show-overflow-tooltip
            />
            <el-table-column
                prop="createTime"
                label="交易时间"
                min-width="200"
                show-overflow-tooltip
            />
            <el-table-column
                prop="result"
                label="操作"
                width="100"
                fixed="right"
            >
                <template #default="scope">
                    <div style="display: flex; align-items: center;">
                        <el-popover
                            :width="460"
                            trigger="click"
                            placement="left"
                            popper-style="max-height: 560px; overflow-y: auto;"
                        >
                            <template #reference>
                                <el-button type="text" :disabled="!scope.row.logData">日志详情</el-button>
                            </template>
                            <template #default>
                                <vue-json-viewer :value="scope.row.logData" :expand-depth="5" />
                            </template>
                        </el-popover>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            v-model:current-page="pageModel.params.page"
            :page-size="pageModel.params.pageSize"
            :total="pageModel.params.total"
            background
            layout="total, prev, pager, next"
            @current-change="onChangePage"
        />
    </el-dialog>
</template>

<script lang="ts" setup>
import VueJsonViewer from 'vue-json-viewer';
import { ElMessage } from 'element-plus';
import { createLogListController } from './controller';
import { computed, onMounted } from 'vue';
import * as utils from '../../common/utils';

const props = defineProps({
    modelValue: {
        type: Number,
        default: 0,
    },
    baseParams: {
        type: Object,
        required: true,
    },
});

const $emit = defineEmits([
    'update:modelValue', // 更新显示状态
]);

const isShowDialogLogList = computed({
    get() {
        return !!props.modelValue;
    },
    set(val:boolean) {
        $emit('update:modelValue', +val);
    },
});

const {
    loadingModel,
    pageModel,
    showDataList,
    setLogDataList,
    createFetchParams,
    requestLogDataList,
} = createLogListController(props.baseParams);

onMounted(() => {
    onChangePage();
});

/**
 * 当点击分页时，触发的事件
 * <AUTHOR>
 * @date 2023-11-21
 */
const onChangePage = async () => {
    loadingModel.setLoading(true);
    const fetchParams = createFetchParams();
    const fetchResponse = await requestLogDataList(fetchParams);
    if (utils.isEqual(fetchParams, createFetchParams())) {
        loadingModel.setLoading(false);
        if (fetchResponse.status === false) {
            return ElMessage.error('拉取数据失败: ' + fetchResponse.message);
        }
        setLogDataList(fetchResponse.data?.logItems || []);
    }
};
</script>

<style lang="scss">
    .social-module__dialog-log-list {
        width: 1200px !important;

        .el-dialog__body {
            padding-top: 8px;
        }

        .el-pagination {
            justify-content: flex-end;
        }
    }
</style>