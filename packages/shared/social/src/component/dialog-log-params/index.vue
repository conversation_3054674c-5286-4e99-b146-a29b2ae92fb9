<template>
    <el-dialog
        v-model="isShowDialogLogParams"
        title="查询条件"
        width="30%"
        :close-on-click-modal="false"
        custom-class="social-module__dialog-log-params"
    >
        <el-form :model="formData" label-width="80px">
            <el-form-item label="查询语句">
                <el-input 
                    v-model="formData.query" 
                    type="textarea"
                    :rows="4"
                    placeholder="A and B ..."
                />
            </el-form-item>
            <el-form-item label="时间范围">
                <el-date-picker
                    v-model="formData.dataRange"
                    type="daterange"
                    value-format="YYYY-MM-DD"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="截止日期"
                />
            </el-form-item>
            <el-form-item label="数据条数">
                <el-input-number
                    v-model="formData.total"
                    :step="1000"
                    :min="1000"
                    :max="20000"
                ></el-input-number>
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button type="primary" @click="$emit('confirm', formData)">
                    确认
                </el-button>
                <el-button @click="$emit('update:modelValue', false)">取消</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { computed, reactive } from 'vue';

const props = defineProps({
    modelValue: {
        type: Number,
        default: 0,
    },
    id: {
        type: String,
        default: '',
    },
});

const $emit = defineEmits([
    'update:modelValue', // 更新显示状态
    'confirm', // 确认
]);

const isShowDialogLogParams = computed({
    get() {
        return !!props.modelValue;
    },
    set(val:boolean) {
        $emit('update:modelValue', +val);
    },
});

const formData = reactive({
    query: 'is_not_mate_escape', // 查询语句
    dataRange: [
        dayjs().format('YYYY-MM-DD'), // 开始日期
        dayjs().format('YYYY-MM-DD'), // 截止日期
    ],
    total: 3000, // 数据条数
});
</script>

<style lang="scss">
    .social-module__dialog-log-params {
        .el-date-editor {
            max-width: 260px;
        }

        .el-select {
            width: 120px;
        }
    }
</style>