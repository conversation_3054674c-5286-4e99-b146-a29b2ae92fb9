/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 15:17:46 
 */
import SocialApi from '../../api/social-api';
import AbcResponse from '../../common/AbcResponse';
import classifyStore from '../../store/classifyStore';
import * as utils from '../../common/utils';
import { computed } from 'vue';

import createLoadingModel from '../../model/loading';
import createClassifyModel from '../../model/classify';

export const createRemarkEditController = (classifyId: string) => {
    const loadingModel = createLoadingModel();
    const classifyModel = createClassifyModel();

    // 是否更新了表单数据
    const isUpdatedFormData = computed(() => {
        const classifyModelTmp = createClassifyModel();
        initClassifyInfo(classifyModelTmp);
        return (
            !utils.isEqual(classifyModelTmp.classifyInfo, classifyModel.classifyInfo)
            || !utils.isEqual(classifyModelTmp.flagForm, classifyModel.flagForm)
        );
    });

    // 是否禁用保存按钮
    const isDisabledSaveBtn = computed(() => (
        classifyModel.isDisabledPost.value
        || isUpdatedFormData.value === false
    ));

    /**
     * 初始化分类信息
     * <AUTHOR>
     * @date 2023-11-14
     * @param {Object} classifyModel
     */
    const initClassifyInfo = (classifyModel: any) => {
        const classifyInfo = classifyStore.classifyDataList.value.find((item) => item.id === classifyId);
        if (classifyInfo) {
            classifyModel.setClassifyInfo(classifyInfo);
        }
    };

    /**
     * 请求更新
     * 成功后更新store的classifyItem
     * <AUTHOR>
     * @date 2023-11-14
     * @returns {Promise<AbcResponse>}
     */
    const requestUpdate = async (): Promise<AbcResponse> => {
        const classifyId = classifyModel.classifyInfo.id;
        const postData = classifyModel.createClassifyPostData();
        const updateResponse = await SocialApi.updateCategory(classifyId, postData);
        if (updateResponse.status === false) {
            return updateResponse;
        }
        classifyStore.updateClassifyItem(classifyId, updateResponse.data);
        return updateResponse;
    };

    return {
        loadingModel,
        classifyModel,
        isDisabledSaveBtn,
        initClassifyInfo,
        requestUpdate,
    };
};