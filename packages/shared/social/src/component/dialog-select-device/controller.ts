/*
 * <AUTHOR>
 * @DateTime 2023-11-14 14:49:05
 */
import SocialApi from '../../api/social-api';
import * as utils from '../../common/utils';
import { reactive, ref } from 'vue';

import createLoadingModel from '../../model/loading';
import createPageModel from '../../model/page';
import type { InstallInfo } from "@social/type/version";
import { ElMessage } from "element-plus";

export const createSelectDeviceController = () => {
    const loadingModel = createLoadingModel();
    const pageModel = createPageModel();

    interface ToolsParams {
        keywords: string;
        clinicId: string;
        deviceId: string;
    }

    const toolsParams = reactive<ToolsParams>({
        keywords: '',
        clinicId: '',
        deviceId: '',
    });

    // 展示的数据列表
    const showDataList = ref<InstallInfo []>([])
    const originalList = ref<InstallInfo []>([])
    const searchData = ref<InstallInfo []>([])

    const createShowDataList = () => {
        const offset = pageModel.params.page - 1
        const limit = pageModel.params.pageSize
        return searchData.value.slice(offset * limit, (offset + 1) * limit)
    }

    const createSearchData = () => {
        return originalList.value.filter((item) => {
            return item.clinicName.includes(toolsParams.keywords) ||
                item.clinicId.includes(toolsParams.keywords) ||
                item.deviceId.includes(toolsParams.keywords)
        })
    }

    interface Params {
        dllVersions: string[],
        regionList: string[],
    }

    const createParams = (params: Params) => {
        const { dllVersions, regionList  } = params
        return {
            regions: regionList,
            deviceId: toolsParams.deviceId,
            clinic: toolsParams.keywords,
            exeLibName: (() => {
                if (dllVersions.length > 0) {
                    return dllVersions[0]
                }
                return ''
            })(),
            versions: (() => {
                if (dllVersions.length > 0) {
                    return [ dllVersions[1] ]
                }
                return []
            })(),
        }
    }

    const fetchData = async (baseParams: Params) => {
        loadingModel.setLoading(true)
        const params = createParams(baseParams);
        const response = await SocialApi.fetchPushableDeviceList(params);
        loadingModel.setLoading(false)
        if (response.status === false) {
            return ElMessage.error(response.message);
        }
        if (!utils.isEqual(params, createParams(baseParams))) {
            return response
        }
        originalList.value = response.data.installInfos
        searchData.value = createSearchData()
        showDataList.value = createShowDataList()
        pageModel.setTotal(searchData.value.length)
    }

    const handleSearch = () => {
        pageModel.setPage(1)
        searchData.value = createSearchData()
        showDataList.value = createShowDataList()
        pageModel.setTotal(searchData.value.length)
    }

    const handlePageChange = () => {
        showDataList.value = createShowDataList()
    }

    return {
        loadingModel,
        pageModel,
        toolsParams,
        showDataList,
        createShowDataList,
        createSearchData,
        fetchData,
        handleSearch,
        handlePageChange,
    };
};
