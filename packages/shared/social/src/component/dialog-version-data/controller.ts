/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import SocialApi from '../../api/social-api';
import AbcResponse from '../../common/AbcResponse';
import classifyStore from '../../store/classifyStore';
import * as utils from '../../common/utils';
import { reactive, computed } from 'vue';

import createLoadingModel from '../../model/loading';
import createDialogModel from '../../model/dialog';
import createPageModel from '../../model/page';

export const createVersionDataController = () => {
    const loadingModel = createLoadingModel();
    const dialogModel = createDialogModel();
    const pageModel = createPageModel();

    const toolsParams = reactive({
        regions: [],
    });

    // 展示的数据列表
    const showDataList = [];

    return {
        loadingModel,
        dialogModel,
        pageModel,
        toolsParams,
        showDataList,
    };
};