<template>
    <div class="social-module__view-log-item">
        <div class="row-wrapper row-1">
            <div class="item" style="width: 50%;">
                <span class="label">地区标识:</span>
                <span class="value">{{ itemWording.region }}</span>
            </div>
            <div class="item" style="width: 50%;">
                <span class="label">错误ID:</span>
                <span class="value">{{ itemWording.errorId }}</span>
            </div>
        </div>
        <div class="row-wrapper row-2">
            <div class="item" style="width: 50%;">
                <span class="label">交易名称:</span>
                <span class="value">{{ itemWording.transName }}</span>
            </div>
            <div class="item" style="width: 50%;">
                <span class="label">交易类型:</span>
                <span class="value">{{ itemWording.transType }}</span>
            </div>
        </div>
        <div class="row-3">
            <span class="label">错误详情:</span>
            <div class="value">{{ itemWording.message }}</div>
        </div>
        <div class="row-4">
            <el-popover
                v-if="!!itemWording.logData"
                :width="460"
                trigger="click"
                placement="right"
                popper-style="max-height: 560px; overflow-y: auto;"
            >
                <template #reference>
                    <el-button type="text">日志详情</el-button>
                </template>
                <template #default>
                    <vue-json-viewer :value="itemWording.logData" :expand-depth="5" />
                </template>
            </el-popover>
        </div>
    </div>
</template>

<script lang="ts" setup>
import VueJsonViewer from 'vue-json-viewer';
import { computed } from 'vue';

const props = defineProps({
    item: {
        type: Object,
        default: () => ({}),
    },
});

const itemWording = computed(() => {
    const info = {
        region: '', // 区域
        errorId: '', // 错误id
        transType: '', // 交易类型
        transName: '', // 交易名称
        message: '', // 错误信息
        logData: null, // 日志详情
    };
    try {   
        const {
            region, // 区域
            data,
        } = props.item;
        const {
            transType, // 交易类型
            transName, // 交易名称
            response,
        } = data;
        Object.assign(info, {
            region, // 区域
            errorId: response.errorId, // 错误id
            transType, // 交易类型
            transName, // 交易名称
            message: response.message, // 错误信息
            logData: data,
        });
    } catch (error) {
        console.log('itemWording error', error, props.item);
    }
    return info;
});
</script>

<style lang="scss">
    .social-module__view-log-item {
        padding: 8px;
        margin-top: 12px;
        background-color: #f6f6f6;
        border-radius: 4px;
        border: 1px solid #ebeef5;

        .row-wrapper {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            height: 24px;

            .item {
                display: flex;
                align-items: center;
                justify-content: flex-start;

                .label {
                    width: 60px;
                    margin-right: 8px;
                    color: #999;
                    display: inline-block;
                    min-width: 60px;
                    text-align: justify;
                    text-align-last: justify;
                }

                .value {
                    color: #333;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    word-break: keep-all;
                    white-space: nowrap;
                    padding-right: 8px;
                }
            }
        }

        .row-3 {
            display: flex;
            justify-content: flex-start;
            align-items: flex-start;

            .label {
                width: 60px;
                text-align: right;
                flex-shrink: 0;
                margin-right: 8px;
                color: #999;
            }

            .value {
                flex: 1;
                display: -webkit-box;
                overflow: hidden;
                text-overflow: ellipsis;
                word-break: break-word;
                word-wrap: break-word;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 10;
            }
        }
    }
</style>