<template>
    <div class="social-module__viewer-ctrl">
        <el-input-number
            :model-value="modelValue"
            :min="1"
            :max="10"
            style="width: 100px;"
            @update:model-value="handleInput"
        />
        <el-button
            v-if="showFullScreenBtn"
            :icon="FullScreen"
            circle
            style="margin-left: 8px;"
            @click="onClickFullScreen"
        />
    </div>
</template>

<script lang="ts" setup>
import { FullScreen } from '@element-plus/icons-vue';

defineProps({
    modelValue: {
        type: Number,
        required: true,
    },
    showFullScreenBtn: {
        type: Boolean,
        default: true,
    },
});

const emit = defineEmits([
    'update:modelValue',
    'full-screen',
]);

const handleInput = (value: number) => {
    emit('update:modelValue', value);
};

const onClickFullScreen = () => {
    emit('full-screen');
};
</script>

<style lang="scss">
    .social-module__viewer-ctrl {
        padding: 4px 6px;
        display: inline-block;
        border-radius: 6px;
        z-index: 99;
    }
</style>