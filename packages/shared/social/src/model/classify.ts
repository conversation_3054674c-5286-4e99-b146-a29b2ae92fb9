/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 10:25:15 
 */
import * as utils from '../common/utils';
import { reactive, computed } from 'vue';
import { flagOptions } from '../common/options';

import classifyInfoInterface from '../type/classifyInfo';

const createClassifyModel = () => {
    const classifyInfo = reactive<classifyInfoInterface>({
        id: '',
        includes: <string []>[],
        checkCode: '',
        config: {},
        labels: <string []>[],
        remark: '',
        explainId: '',
        createdName: '',
        created: '',
    });

    const flagForm = reactive(flagOptions.reduce((form: any, item) => {
        if (item.multiple) {
            form[item.key] = (item.defaultValue || []).slice();
        } else {
            form[item.key] = item.defaultValue;
        }
        return form;
    }, reactive({})));

    const isCreate = computed(() => classifyInfo.id === '');
    const isUpdate = computed(() => classifyInfo.id !== '');
    const isDisabledPost = computed(() => (
        (classifyInfo.includes || []).length === 0
        && (classifyInfo.checkCode || '') === ''
    ));

    const setClassifyInfo = (info:classifyInfoInterface) => {
        classifyInfo.id = info.id;
        classifyInfo.includes = info.includes.slice();
        classifyInfo.checkCode = info.checkCode;
        classifyInfo.config = info.config ? utils.cloneDeep(info.config) : {};
        classifyInfo.labels = info.labels.slice();
        classifyInfo.remark = info.remark;
        classifyInfo.explainId = info.explainId;
        classifyInfo.createdName = info.createdName;
        classifyInfo.created = info.created;
        setFlagForm(classifyInfo.config);
    };

    const addIncludeItem = (item:string) => {
        classifyInfo.includes.push(item);
    };

    const delIncludeItem = (index:number) => {
        classifyInfo.includes.splice(index, 1);
    };

    const setLables = (labels:string[]) => {
        classifyInfo.labels = labels;
    };

    const setExplainId = (id:string) => {
        classifyInfo.explainId = id;
    };

    const delExplainId = () => {
        classifyInfo.explainId = '';
    };

    const setPattern = (pattern: string) => {
        const checkCode = `(message) => {\n  const pattern = /${pattern}/i;\n  const arr = message.match(pattern)\n  return arr ? arr.slice(1) : false\n}`;
        setCheckCode(checkCode);
    };

    const setCheckCode = (checkCode:string) => {
        classifyInfo.checkCode = checkCode;
    };
    
    const delCheckCode = () => {
        classifyInfo.checkCode = '';
    };

    const setFlagForm = (config: any) => {
        Object.assign(flagForm, config);
    };

    const setConfig = (flagForm: any) => {
        for (const key in flagForm) {
            const value = flagForm[key];
            const target = flagOptions.find((item) => item.key === key);
            if (target?.multiple) {
                classifyInfo.config[key] = value;
                if (value.length === 0) {
                    delete classifyInfo.config[key];
                }
            } else {
                classifyInfo.config[key] = value;
                if (value === target?.defaultValue) {
                    delete classifyInfo.config[key];
                }
            }
        }
    };

    const initClassifyInfo = () => {
        classifyInfo.id = '';
        classifyInfo.includes = [];
        classifyInfo.checkCode = '';
        classifyInfo.config = {};
        classifyInfo.labels = [];
        classifyInfo.remark = '';
        classifyInfo.explainId = '';
        classifyInfo.createdName = '';
        classifyInfo.created = '';
        utils.pick(flagForm, classifyInfo.config);
    };

    const createClassifyPostData = () => {
        setConfig(flagForm);
        return {
            includes: classifyInfo.includes,
            checkCode: classifyInfo.checkCode,
            config: classifyInfo.config,
            labels: classifyInfo.labels,
            remark: classifyInfo.remark,
            explainId: classifyInfo.explainId,
        };
    };

    return {
        classifyInfo,
        flagForm,
        isCreate,
        isUpdate,
        isDisabledPost,
        flagOptions,
        addIncludeItem,
        delIncludeItem,
        setLables,
        setExplainId,
        delExplainId,
        setPattern,
        setCheckCode,
        delCheckCode,
        setFlagForm,
        setConfig,
        setClassifyInfo,
        initClassifyInfo,
        createClassifyPostData,
    };
};

export default createClassifyModel;