/*
 * <AUTHOR> 
 * @DateTime 2024-06-20 17:09:50 
 */
import { reactive, computed } from 'vue';

import linkItemInterface from '../type/linkItem';
import explainInfoInterface from '../type/explainInfo';

const createExplainModel = () => {
    const explainInfo = reactive<explainInfoInterface>({
        id: '',
        title: '',
        content: '',
        linkList: <linkItemInterface []>[],
        createdName: '',
        created: '',
    });

    const isCreate = computed(() => explainInfo.id === '');
    const isUpdate = computed(() => explainInfo.id !== '');
    const isDisabledPost = computed(() => (
        explainInfo.title === ''
        || explainInfo.content === ''
    ));

    const setExplainInfo = (info:explainInfoInterface) => {
        explainInfo.id = info.id;
        explainInfo.title = info.title;
        explainInfo.content = info.content;
        explainInfo.linkList = info.linkList.slice();
        explainInfo.createdName = info.createdName;
        explainInfo.created = info.created;
    };

    const addLinkItem = (linkItem:linkItemInterface) => {
        explainInfo.linkList.push(linkItem);
    };

    const delLinkItem = (index:number) => {
        explainInfo.linkList.splice(index, 1);
    };

    const initExplainInfo = () => {
        explainInfo.id = '';
        explainInfo.title = '';
        explainInfo.content = '';
        explainInfo.linkList = [];
        explainInfo.createdName = '';
        explainInfo.created = '';
    };

    const createExplainPostData = () => ({
        title: explainInfo.title,
        content: explainInfo.content,
        linkList: explainInfo.linkList,
    });

    return {
        explainInfo,
        isCreate,
        isUpdate,
        isDisabledPost,
        addLinkItem,
        delLinkItem,
        setExplainInfo,
        initExplainInfo,
        createExplainPostData,
    };
};

export default createExplainModel;