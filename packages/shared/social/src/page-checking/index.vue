<template>
    <div class="social-module__page-checking">
        <div class="tools-warpper">
            <el-date-picker
                v-model="toolsParams.dataRange"
                type="daterange"
                value-format="YYYY-MM-DD"
                :clearable="false"
                :disabled-date="disabledDateFunc"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="截止日期"
            ></el-date-picker>
            <el-cascader
                v-model="toolsParams.regions"
                :options="regionCascaderOptions"
                :props="{
                    multiple: true,
                }"
                collapse-tags
                :show-all-levels="false"
                :max-collapse-tags="1"
                clearable
                placeholder="选择地区"
            ></el-cascader>
            <el-button
                type="primary"
                :loading="loadingModelData.loading.value"
                :disabled="disabledQueryBtn"
                @click="onClickQueryBtn"
            >
                查询
            </el-button>
        </div>
        <div
            v-loading="loadingModelData.loading.value"
            class="content-wrapper"
        >
            <canvas ref="chartRef"></canvas>
            <span 
                v-if="!toolsParams.myChart"
                class="kong-wrapper"
            >
                请选择地区后查询
            </span>
        </div>

        <dialog-checking-clinic
            v-if="dialogModelCheckingClinic.visible.value"
            v-model="dialogModelCheckingClinic.visible.value"
            :region="resultData.region"
            :status="resultData.status"
            :clinicStatusList="resultData.clinicStatusList"
        />
    </div>
</template>

<script lang="ts" setup>
import Chart from 'chart.js/auto';
import * as utils from '../common/utils';
import { regionNameOptions } from '../common/options';
import { ElMessage } from 'element-plus';
import { ref } from 'vue';
import { createPageCheckingController } from './controller';
import DialogCheckingClinic from '../component/dialog-checking-clinic/index.vue';

const {
    loadingModelData,
    dialogModelCheckingClinic,
    toolsParams,
    resultData,
    disabledQueryBtn,
    disabledDateFunc,
    createParams,
    checkDateRange,
    requestSettleStatStatus,
} = createPageCheckingController();

const regionCascaderOptions = utils.getRegionCascaderOptions();

const chartRef = ref(null);

/**
 * 创建图表数据
 * <AUTHOR>
 * @date 2025-07-29
 * @param {Array} regionList
 * @param {Array} dataList
 * @returns {Object}
 */
const createChartData = (regionList: string[], dataList: any[]) => {
    const chartData = {
        labels: [] as string[],
        datasets: [
            {
                label: '未对账',
                backgroundColor: 'rgb(54, 162, 235, 0.5)',
                data: [] as number[],
                stack: 'checkingWill',
            },
            {
                label: '对账成功',
                backgroundColor: 'rgb(75, 192, 192, 0.5)',
                data: [] as number[],
                stack: 'checkingSuccess',
            },
            {
                label: '对账失败',
                backgroundColor: 'rgb(255, 159, 64, 0.5)',
                data: [] as number[],
                stack: 'checkingFail',
            },
        ],
    }
    chartData.labels = regionList.map((region: string) => {
        const target = regionNameOptions.find((item: any) => item.region === region);
        return target ? target.regionShortName : region;
    });
    chartData.datasets[0].data = regionList.map(() => 0);
    chartData.datasets[1].data = regionList.map(() => 0);
    chartData.datasets[2].data = regionList.map(() => 0);
    dataList.forEach((item: any) => {
        const checkingWill = item.checkingStatus === 0 ? 1 : 0
        const checkingSuccess = item.checkingStatus === 1 ? 1 : 0
        const checkingFail = item.checkingStatus === 2 ? 1 : 0
        const index = regionList.findIndex((label: string) => label === item.region);
        if (index !== -1) {
            chartData.datasets[0].data[index] += checkingWill;
            chartData.datasets[1].data[index] += checkingSuccess;
            chartData.datasets[2].data[index] += checkingFail;
        }
    })
    return chartData;
}

/**
 * 创建Y轴最大值
 * <AUTHOR>
 * @date 2025-07-31
 * @param {Array} datasets
 * @returns {Number}
 */
const createYAxisMax = (datasets: any[]) => {
    const dataList = datasets.reduce((list: any[], item: any) => {
        if (item.hidden !== true) {
            list.push(...item.data);
        }
        return list;
    }, []);
    const maxValue = Math.max(...dataList);
    return utils.roundUpToZero(maxValue);
}

/**
 * 创建图表
 * <AUTHOR>
 * @date 2025-07-29
 * @param {Array} regionList
 * @param {Array} dataList
 */
const createChartView = (regionList: string[], dataList: any[]) => {
    if (!chartRef.value) {
        return;
    }
    if (toolsParams.myChart) {
        toolsParams.myChart.destroy();
    }
    const chartData = createChartData(regionList, dataList);
    toolsParams.myChart = new Chart(chartRef.value, {
        type: 'bar',
        data: chartData,
        options: {
            onClick: (event, elements) => {
                if (
                    toolsParams.myChart
                    && elements.length > 0
                ) {
                    const index = elements[0].index;
                    const label = toolsParams.myChart.data.labels[index];
                    const target = regionNameOptions.find((item: any) => item.regionShortName === label);
                    if (!target) {
                        return;
                    }
                    const datasetIndex = elements[0].datasetIndex;
                    onClickShowDetail(target.region, datasetIndex);
                }
            },
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    text: '自动对账结果分析',
                },
                legend: {
                    position: 'top',
                    onClick: (e, legendItem, legend) => {
                        // 切换数据集可见性
                        const chart = legend.chart;
                        const index = legendItem.datasetIndex as number;
                        chart.data.datasets[index].hidden = !chart.data.datasets[index].hidden;
                        // 重新计算Y轴最大值
                        chart.options.scales.y.max = createYAxisMax(chart.data.datasets);
                        // 更新图表
                        chart.update();
                    }
                },
                tooltip: {
                    enabled: true,
                    callbacks: {
                        label(context) {
                            const dataset = context.dataset;
                            return `${dataset.label}: ${context.raw} 家`;
                        },
                    },
                },
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1,
                    },
                    max: createYAxisMax(chartData.datasets),
                },
            },
        },
    });
};

/**
 * 查询按钮点击
 * <AUTHOR>
 * @date 2025-07-29
 */
const onClickQueryBtn = async () => {
    const isEnable = checkDateRange();
    if (!isEnable) {
        return ElMessage.error('查询日期范围必须为同一个月');
    }
    const params = createParams()
    loadingModelData.setLoading(true);
    const response = await requestSettleStatStatus(params)
    if (!utils.isEqual(params, createParams())) {
        return response;
    }
    loadingModelData.setLoading(false);
    if (response.status === false) {
        return ElMessage.error('拉取数据失败: ' + response.message);
    }
    resultData.clinicStatusList = response.data.clinicStatusList;
    createChartView(params.regionList, response.data.clinicStatusList)
}

/**
 * 当点击显示详情数据
 * <AUTHOR>
 * @date 2025-07-29
 */
const onClickShowDetail = async (region: any, datasetIndex: number) => {
    resultData.region = region;
    resultData.status = datasetIndex;
    dialogModelCheckingClinic.show();
};

</script>

<style lang="scss">
    .social-module__page-checking {
        height: calc(100vh - 56px);
        background-color: #fff;

        .tools-warpper {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid #e0e5ee;

            .el-cascader {
                width: 180px;
                height: 32px;
                margin-left: 8px;

                .el-input {
                    height: 32px;
                }

                .el-select-tags-wrapper {
                    height: 100%;
                }

                .el-select__tags {
                    height: 100%;
                    padding-top: 2px;
                    box-sizing: border-box;
                }
            }

            .el-date-editor {
                max-width: 260px;
            }

            .el-button {
                margin-left: 8px;
            }
        }

        .content-wrapper {
            position: relative;
            height: calc(100vh - 122px);
            background-color: #fff;
            border-radius: 6px;
            padding: 16px;

            .kong-wrapper {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                color: #909399;
                font-size: 14px;
            }
        }
    }
</style>