/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import dayjs from 'dayjs';
import SocialApi from '../api/social-api';
import AbcResponse from '../common/AbcResponse';
import classifyStore from '../store/classifyStore';
import * as options from '../common/options';
import * as utils from '../common/utils';
import { reactive, computed } from 'vue';

import createLoadingModel from '../model/loading';
import createDialogModel from '../model/dialog';
import createPageModel from '../model/page';
import createClassifyModel from '../model/classify';

export const createPageExplainController = () => {
    const loadingModelData = createLoadingModel();
    const loadingModelExplain = createLoadingModel();
    const dialogModelExplain = createDialogModel();
    const dialogModelRemarkEdit = createDialogModel();
    const dialogModelLogList = createDialogModel();
    const pageModel = createPageModel();
    const classifyModel = createClassifyModel();

    // 工具栏参数
    const toolsParams = reactive({
        dataRange: [
            dayjs().format('YYYY-MM-DD'),
            dayjs().format('YYYY-MM-DD'),
        ],
        keyword: '',
        type: '',
        flag: [],
        tradeType: [],
        region: [],
        order: 'descending', // 排序
    });

    // 当前编辑的数据
    const editData = reactive({
        classifyId: '',
        explainId: '',
        baseParams: {},
    });

    // 统计数据，接口返回的原始数据
    const statisticsData = reactive({
        logItems: [],
        showErrorTotal: 0, // 展示数据的报错总条数
    });

    // 标记选项
    const flagOptions = computed(() => {
        const options = <any>[];
        classifyModel.flagOptions.forEach((item) => {
            item.options.forEach((one) => {
                options.push({
                    value: `${item.key}_${one.value}`,
                    label: `${item.name}（${one.label}）`,
                });
            });
        });
        return options;
    });

    // 地区选项
    const regionOptions = computed(() => {
        const map = statisticsData.logItems.reduce((map, item: any) => {
            const {
                region,
            } = item;
            if (!map.has(region)) {
                const value = {
                    value: region,
                    label: region,
                };
                const target = options.regionNameOptions.find((item: any) => item.region === region);
                if (target) {
                    value.label = `${target.provinceShortName}-${target.regionShortName}（${region}）`;
                }
                map.set(region, value);
            }
            return map;
        }, new Map());
        return Array.from(map.values());
    });

    // 交易类型选项
    const tradeTypeOptions = computed(() => {
        const map = statisticsData.logItems.reduce((map, item: any) => {
            if (!map.has(item.transType)) {
                map.set(item.transType, {
                    value: item.transType,
                    label: `${item.transName}（${item.transType}）`,
                });
            }
            return map;
        }, new Map());
        return Array.from(map.values());
    });

    // 检查是否标记匹配
    const checkIsMateFlag = (flagStr: string, item: any) => {
        const key = flagStr.split('_')[0];
        const val = Number(flagStr.split('_')[1]);
        const value = item.config[key];
        if (Array.isArray(value)) {
            return value.includes(val);
        } 
        return value === val;
    };

    // 数据列表
    const handDataList = computed(() => {
        const defaultConfig = classifyModel.flagOptions.reduce((map: any, item) => {
            map[item.key] = item.defaultValue;
            return map;
        }, {});
        const map = statisticsData.logItems.reduce((map, item: any) => {
            const {
                classifyId,
                count,
                region,
                transType,
            } = item;
            if (!map.has(classifyId)) {
                const value = {
                    id: classifyId, // 分类id
                    includes: [], // 包含的关键字
                    checkCode: '', // 检查代码
                    config: {}, // 标记配置
                    labels: [], // 标签
                    remark: '', // 备注
                    explainId: '', // 关联的文案id
                    createdName: '', // 创建人
                    created: '', // 创建时间
                    classifyId, // 分类id
                    errorTotal: count, // 错误总数
                    regionList: [], // 地区列表
                    tradeTypeList: [], // 交易类型列表
                    explainInfo: {}, // 文案标题
                    explainStatus: 1, // 文案状态 1-未配文 2-已配文 9-无需配文
                };
                // 查分类信息
                const classifyInfo = classifyStore.classifyDataList.value.find((item) => item.id === classifyId);
                if (classifyInfo) {
                    Object.assign(value, classifyInfo);
                    value.config = { ...defaultConfig, ...classifyInfo.config };
                } else {
                    return map;
                }
                // 查文案信息
                const explainInfo = classifyStore.explainDataList.value.find((item) => item.id === value.explainId);
                if (explainInfo) {
                    value.explainInfo = explainInfo;
                    value.explainStatus = 2; // 已配文
                } else {
                    value.explainId = '';
                    value.explainStatus = 1; // 未配文
                    if (value.remark.indexOf('无需配文') !== -1) {
                        value.explainStatus = 9; // 无需配文
                    }
                }
                map.set(classifyId, value);
            }
            const tar = map.get(classifyId);
            if (!tar.regionList.includes(region)) {
                tar.regionList.push(region);
            }
            if (!tar.tradeTypeList.includes(transType)) {
                tar.tradeTypeList.push(transType);
            }
            return map;
        }, new Map());
        return Array.from(map.values());
    });

    // 匹配数据
    let cacheToolsParams = {};
    const mateDataList = computed(() => {
        let dataList = handDataList.value;
        if (toolsParams.keyword) {
            // 通过关键字过滤
            dataList = dataList.filter((item) => (
                item.id.indexOf(toolsParams.keyword) !== -1
                || item.shortId.indexOf(toolsParams.keyword) !== -1
                || item.explainId.indexOf(toolsParams.keyword) !== -1
                || item.checkCode.indexOf(toolsParams.keyword) !== -1
                || item.remark.indexOf(toolsParams.keyword) !== -1
                || item.includes.some((one: string) => one.indexOf(toolsParams.keyword) !== -1)
                || item.labels.some((one: string) => one.indexOf(toolsParams.keyword) !== -1)
            ));
        }
        if (toolsParams.type) {
            // 通过是否配文过滤
            dataList = dataList.filter((item) => {
                switch (toolsParams.type) {
                    case '1':
                        // 未配文
                        return item.explainStatus === 1;
                    case '2':
                        // 已配文
                        return item.explainStatus === 2;
                    case '3':
                        // 无需配文
                        return item.explainStatus === 9;
                    default:
                        break;
                }
                return false;
            });
        }
        if (toolsParams.flag.length !== 0) {
            // 通过是否标记过滤
            dataList = dataList.filter((item) => toolsParams.flag.some((flagStr: string) => checkIsMateFlag(flagStr, item)));
        }
        if (toolsParams.tradeType.length !== 0) {
            // 通过交易类型过滤
            dataList = dataList.filter((item) => toolsParams.tradeType.some((one) => item.tradeTypeList.includes(one)));
        }
        if (toolsParams.region.length !== 0) {
            // 通过地区过滤
            dataList = dataList.filter((item) => toolsParams.region.some((one) => item.regionList.includes(one)));
        }
        if (toolsParams.order) {
            // 排序
            dataList.sort((a, b) => (toolsParams.order === 'descending' ? b.errorTotal - a.errorTotal : a.errorTotal - b.errorTotal));
        }
        if (!utils.isEqual(cacheToolsParams, toolsParams)) {
            pageModel.setTotal(dataList.length);
            pageModel.setPage(1);
            cacheToolsParams = utils.cloneDeep(toolsParams);
            statisticsData.showErrorTotal = dataList.reduce((total, item) => total + Number(item.errorTotal), 0);
        }
        return dataList;
    });

    // 展示数据
    const showDataList = computed(() => {
        const { sIndex, eIndex } = pageModel.createSliceParams();
        return mateDataList.value.slice(sIndex, eIndex).map((item) => {
            const itemInfo = {
                ...item,
                classifyWording: utils.getClassifyWording(item),
                labelWording: utils.getLabelWording(item),
                tradeTypeWording: (item.tradeTypeList || []).join('、'),
                regionWording: (item.regionList || []).join('、'),
                isHasExplain: !!item.explainId,
                explainWording: '',
                explainHtml: '',
            };
            switch (item.explainStatus) {
                case 1:
                    // 未配文
                    itemInfo.explainWording = '未配文';
                    break;
                case 2:
                    // 已配文
                    itemInfo.explainWording = itemInfo.explainInfo?.title;
                    itemInfo.explainHtml = `【${itemInfo.explainInfo?.title}】<br / >${itemInfo.explainInfo?.content.replace(/\n/g, '<br />') || ''}`;
                    break;
                case 9:
                    // 无需配文
                    itemInfo.explainWording = '无需配文';
                    break;
                default:
                    break;
            }
            return itemInfo;
        });
    });

    /**
     * 当改变排序时
     * <AUTHOR>
     * @date 2023-11-21
     * @param {Object} info
     */
    const onChangeSort = (info: any) => {
        toolsParams.order = info.order || '';
    };

    /**
     * 创建请求参数
     * <AUTHOR>
     * @date 2023-11-16
     * @returns {Object}
     */
    const createParams = () => {
        const {
            dataRange,
        } = toolsParams;
        const params = {
            startDate: dataRange[0], // 开始日期
            endDate: dataRange[1], // 结束日期
        };
        return params;
    };

    /**
     * 创建基础参数
     * <AUTHOR>
     * @date 2023-11-21
     * @param {Object} item
     * @returns {Object}
     */
    const createBaseParams = (item: any) => {
        const {
            dataRange,
        } = toolsParams;
        const baseParams = {
            startDate: dataRange[0], // 开始日期
            endDate: dataRange[1], // 结束日期
            classifyId: item.classifyId, // 分类id
            total: item.errorTotal, // 总数
        };
        return baseParams;
    };

    /**
     * 创建日志查询参数
     * 时间范围就查最近7天的
     * <AUTHOR>
     * @date 2023-11-15
     * @param {Object} params
     * @returns {Object}
     */
    const createFetchParams = (params: any) => {
        const {
            startDate, // 开始日期
            endDate, // 结束日期
        } = params;
        const fetchParams = {
            logStore: utils.createLogStore(), // 日志存储位置
            beginDate: `${startDate} 00:00:00`, // 开始日期
            endDate: `${endDate} 23:59:59`, // 结束日期
            offset: 0, // 偏移量
            limit: 100, // 每页条数
            // eslint-disable-next-line max-len
            query: '(is_mate_not_message or is_mate_has_message) | select regexp_extract(message, \'_classify_id_\\":\\"(\\S+?)\\"\', 1) as classifyId, regexp_extract(message, \'region\\":"(\\S+?)\\"\', 1) as region, regexp_extract(message, \'_trans_type_\\":"(\\S+?)\\"\', 1) as transType, regexp_extract(message, \'_trans_name_\\":"(\\S+?)\\"\', 1) as transName, count(*) as count group by classifyId, region, transType, transName order by count DESC limit 50000', // 查询条件
        };
        return fetchParams;
    };

    /**
     * 请求数据列表
     * <AUTHOR>
     * @date 2023-11-16
     * @param {Object} params 请求参数
     * @returns {Promise<AbcResponse>}
     */
    const requestClassifyStatisticsData = async (params: object) => {
        const fetchParams = createFetchParams(params);
        const fetchResponse = await SocialApi.fetchManagementLog(fetchParams);
        if (fetchResponse.status === false) {
            return fetchResponse;
        }
        statisticsData.logItems = (fetchResponse.data?.logItems || []).filter((item: any) => (
            item.classifyId !== 'null'
            && item.transType.indexOf('"') === -1
            && item.transType.indexOf('}') === -1
            && item.transType.indexOf('{') === -1
            && item.transType.indexOf(',') === -1
        ));
        cacheToolsParams = {};
        return fetchResponse;
    };

    /**
     * 请求保存
     * 成功后更新store的classifyItem
     * <AUTHOR>
     * @date 2023-11-14
     * @param {Object} explainId
     * @returns {Promise<AbcResponse>}
     */
    const requestClassifyUpdate = async (explainId: string) => {
        if (editData.explainId === explainId) {
            // 无需更新，视为更新成功
            return AbcResponse.success();
        }
        const classifyInfo = classifyStore.classifyDataList.value.find((item) => item.id === editData.classifyId);
        if (!classifyInfo) {
            return AbcResponse.error('未找到归类信息');
        }
        classifyModel.setClassifyInfo(classifyInfo);
        classifyModel.setExplainId(explainId);
        const classifyPostData = classifyModel.createClassifyPostData();
        const updateResponse = await SocialApi.updateCategory(editData.classifyId, classifyPostData);
        if (updateResponse.status === false) {
            return updateResponse;
        }
        classifyStore.updateClassifyItem(editData.classifyId, updateResponse.data);
        return updateResponse;
    };
    
    return {
        classifyStore,
        loadingModelData,
        loadingModelExplain,
        dialogModelExplain,
        dialogModelRemarkEdit,
        dialogModelLogList,
        pageModel,
        classifyModel,
        toolsParams,
        editData,
        statisticsData,
        flagOptions,
        regionOptions,
        tradeTypeOptions,
        showDataList,
        onChangeSort,
        createParams,
        createBaseParams,
        requestClassifyStatisticsData,
        requestClassifyUpdate,
    };
};