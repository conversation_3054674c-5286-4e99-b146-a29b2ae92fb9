<template>
    <div class="social-module__page-explain">
        <div class="tools-warpper">
            <el-date-picker
                v-model="toolsParams.dataRange"
                type="daterange"
                value-format="YYYY-MM-DD"
                :clearable="false"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="截止日期"
                @change="onClickFetchData"
            />
            <el-button	
                type="primary" 	
                :loading="loadingModelData.loading.value"	
                @click="onClickFetchData"	
            >	
                刷新数据	
            </el-button>
            <div class="line-box"></div>
            <el-input
                v-model="toolsParams.keyword"
                class="search-wrapper"
                :suffix-icon="Search"
                clearable
                placeholder="请输入关键词"
            />
            <el-select v-model="toolsParams.type" clearable placeholder="是否配文">
                <el-option
                    v-for="item in typeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
            </el-select>
            <el-select
                v-model="toolsParams.flag"
                clearable
                filterable
                multiple
                collapse-tags
                placeholder="标记筛选"
            >
                <el-option
                    v-for="item in flagOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
            </el-select>
            <el-select
                v-model="toolsParams.tradeType"
                clearable
                filterable
                multiple
                collapse-tags
                placeholder="报错接口"
            >
                <el-option
                    v-for="item in tradeTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
            </el-select>
            <el-select
                v-model="toolsParams.region"
                clearable
                filterable
                multiple
                collapse-tags
                placeholder="选择地区"
            >
                <el-option
                    v-for="item in regionOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
            </el-select>
        </div>
        <div class="content-wrapper">
            <el-table
                v-loading="loadingModelData.loading.value"
                :data="showDataList"
                :height="530"
                :default-sort="{ prop: 'errorTotal', order: toolsParams.order }"
                style="width: 100%;"
                @sort-change="onChangeSort"
            >
                <el-table-column
                    prop="id"
                    label="ID"
                    min-width="200"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="shortId"
                    label="短ID"
                    min-width="100"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="classifyWording"
                    label="匹配规则"
                    min-width="300"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="tradeTypeWording"
                    label="接口"
                    width="160"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="regionWording"
                    label="地区"
                    width="160"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="errorTotal"
                    label="数量"
                    sortable
                    width="160"
                    :sort-orders="['ascending', 'descending']"
                >
                    <template #default="scope">
                        <div class="align-center">
                            <span class="text-zhan">{{ scope.row.errorTotal }}</span>
                            <el-button
                                v-if="scope.row.errorTotal !== 0"
                                type="text"
                                style="margin-left: 8px;"
                                @click="onClickShowLogs(scope.row)"
                            >
                                查看
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="isHasExplain" label="配文" width="200">
                    <template #default="scope">
                        <div class="align-center">
                            <span class="explain-wording text-zhan">{{ scope.row.explainWording }}</span>
                            <el-popover
                                v-if="scope.row.isHasExplain"
                                placement="top-start"
                                :width="360"
                                show-arrow
                                popper-style="max-height: 260px; overflow-y: auto;"
                            >
                                <template #reference>
                                    <el-button type="text" style="margin-left: 8px;">详情</el-button>
                                </template>
                                <template #default>
                                    <span v-html="scope.row.explainHtml"></span>
                                </template>
                            </el-popover>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="labelWording"
                    label="标签"
                    width="160"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="remark"
                    label="备注"
                    min-width="200"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="remark"
                    label="操作"
                    width="222"
                    fixed="right"
                >
                    <template #default="scope">
                        <div class="align-center">
                            <el-button
                                type="primary"
                                :loading="loadingModelExplain.loading.value"
                                @click="onClickSelectExplain(scope.row)"
                            >
                                选择配文
                            </el-button>
                            <el-button @click="onClickRemarkEdit(scope.row)">编辑备注</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <div class="footer-box">
                <span>报错总数量：{{ statisticsData.showErrorTotal }}</span>
                <el-pagination
                    v-model:current-page="pageModel.params.page"
                    :page-size="pageModel.params.pageSize"
                    :total="pageModel.params.total"
                    background
                    layout="total, prev, pager, next"
                />
            </div>
        </div>

        <dialog-explain-list
            v-if="dialogModelExplain.visible.value"
            v-model="dialogModelExplain.visible.value"
            :is-show-clear-btn="!!editData.explainId"
            @select="onSelectExplainItem"
        ></dialog-explain-list>

        <dialog-remark-edit
            v-if="dialogModelRemarkEdit.visible.value"
            v-model="dialogModelRemarkEdit.visible.value"
            :classify-id="editData.classifyId"
        ></dialog-remark-edit>
        
        <dialog-log-list
            v-if="dialogModelLogList.visible.value"
            v-model="dialogModelLogList.visible.value"
            :base-params="editData.baseParams"
        ></dialog-log-list>
    </div>
</template>

<script lang="ts" setup>
import { onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { createPageExplainController } from './controller';
import * as utils from '../common/utils';

import explainInfoInterface from '../type/explainInfo';
import DialogExplainList from '../component/dialog-explain-list/index.vue';
import DialogRemarkEdit from '../component/dialog-remark-edit/index.vue';
import DialogLogList from '../component/dialog-log-list/index.vue';

const typeOptions = [
    { value: '1', label: '未配文' },
    { value: '2', label: '已配文' },
    { value: '3', label: '无需配文' },
];

const {
    classifyStore,
    loadingModelData,
    loadingModelExplain,
    dialogModelExplain,
    dialogModelRemarkEdit,
    dialogModelLogList,
    pageModel,
    toolsParams,
    editData,
    statisticsData,
    flagOptions,
    regionOptions,
    tradeTypeOptions,
    showDataList,
    onChangeSort,
    createParams,
    createBaseParams,
    requestClassifyStatisticsData,
    requestClassifyUpdate,
} = createPageExplainController();

onMounted(async () => {
    await classifyStore.initExplainDataList();
    await classifyStore.initClassifyDataList();
    onClickFetchData();
});

/**
 * 当点击拉取数据
 * <AUTHOR>
 * @date 2023-11-16
 */
const onClickFetchData = async () => {
    if (loadingModelData.loading.value === true) {
        return;
    }
    const params = createParams();
    loadingModelData.setLoading(true);
    const response = await requestClassifyStatisticsData(params);
    if (utils.isEqual(params, createParams())) {
        loadingModelData.setLoading(false);
        if (response.status === false) {
            return ElMessage.error('拉取数据失败: ' + response.message);
        }
    }
};

/**
 * 当点击显示日志数据
 * <AUTHOR>
 * @date 2023-11-21
 * @param {Object} item
 */
const onClickShowLogs = async (item: any) => {
    const baseParams = createBaseParams(item);
    editData.baseParams = baseParams;
    dialogModelLogList.show();
};

/**
 * 当选择一项配文时触发
 * <AUTHOR>
 * @date 2023-11-09
 * @param {Object} explainItem
 */
const onSelectExplainItem = async (explainItem: explainInfoInterface) => {
    dialogModelExplain.hide();
    loadingModelExplain.setLoading(true);
    const saveResponse = await requestClassifyUpdate(explainItem.id);
    loadingModelExplain.setLoading(false);
    if (saveResponse.status === false) {
        return ElMessage.error('操作失败: ' + saveResponse.message);
    }
    ElMessage.success('操作成功');
};

/**
 * 当点击选择配文时触发
 * <AUTHOR>
 * @date 2023-11-10
 * @param {Object} item
 */
const onClickSelectExplain = (item:any) => {
    editData.classifyId = item.classifyId;
    editData.explainId = item.explainId;
    dialogModelExplain.show();
};

/**
 * 当点击编辑备注时触发
 * <AUTHOR>
 * @date 2023-11-10
 * @param {Object} item
 */
const onClickRemarkEdit = (item:any) => {
    editData.classifyId = item.classifyId;
    dialogModelRemarkEdit.show();
};
</script>

<style lang="scss">
    .social-module__page-explain {
        height: calc(100vh - 56px);
        background-color: #fff;

        .tools-warpper {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid #e0e5ee;

            .el-date-editor {
                max-width: 260px;
            }

            .el-button {
                margin-left: 8px;
            }

            .line-box {
                height: 28px;
                width: 1px;
                background-color: #ddd;
                margin: 0 16px;
            }

            .search-wrapper {
                width: 200px;
            }

            .el-select {
                width: 140px;
                margin-left: 8px;

                .el-input {
                    height: 32px;
                }

                .el-select-tags-wrapper {
                    height: 100%;
                }

                .el-select__tags {
                    height: 100%;
                    padding-top: 2px;
                    box-sizing: border-box;
                }
            }
        }

        .content-wrapper {
            background-color: #fff;
            border-radius: 6px;
            padding: 16px;

            .el-table {
                min-height: 400px;
            }

            .el-pagination {
                margin-top: 16px;
                justify-content: flex-end;
            }

            .explain-wording {
                overflow: hidden;
                text-overflow: ellipsis;
                word-break: keep-all;
                white-space: nowrap;
            }

            .align-center {
                display: flex;
                align-items: center;
            }

            .text-zhan {
                display: inline-block;
                min-width: 50px;
            }

            .footer-box {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding-top: 16px;

                > span {
                    color: #606266;
                }

                .el-pagination {
                    margin-top: 0;
                }
            }
        }
    }
</style>../type