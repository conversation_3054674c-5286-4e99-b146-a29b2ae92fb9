<template>
    <div class="social-module__page-feature">
        <div class="tools-warpper">
            <el-select
                v-model="toolsParams.provinceNames"
                filterable
                multiple
                collapse-tags
                placeholder="先选择区域"
                @change="onChangeProvinceNames"
            >
                <el-option
                    v-for="item in provinceOptions"
                    :key="item"
                    :label="item"
                    :value="item"
                />
            </el-select>
            <el-input
                v-model="toolsParams.searchKeyword"
                :suffix-icon="Search"
                clearable
                placeholder="请输入关键词"
                style="width: 220px;"
            ></el-input>
            <el-popover
                placement="bottom-start"
                :width="650"
                trigger="hover"
            >
                <template #reference>
                    <el-button type="text">
                        医保接入说明<el-icon style="margin-left: 4px;"><Warning /></el-icon>
                    </el-button>
                </template>
                <div>
                    <p>下方表格里的地区都是支持医保的，可直接提工单给实施交付组安排实施</p>
                    <h4 style="margin: 8px 0;">特殊地区说明如下：</h4>
                    <p>
                        1、山东有部分地级市是地方医保（地纬），对接有门槛要求<br />
                        2、青海是国标，目前没有对接群和客户配合，所以暂时未对接<br />
                        3、宁夏是国标，但当前有政策限制，具体情况可以咨询冬哥<br />
                        4、北京、上海没见过协议，暂不知道如何对接
                    </p>
                    <h4 style="margin: 8px 0;">
                        如果是国标协议的对接文档，我们可以快速对接，没有对接门槛。<br />
                        而如果是地方医保，则有如下要求：
                    </h4>
                    <p>
                        1、医保局提供的最新对接资料<br />
                        2、医保局提供的对接沟通群或有医保对接人可以向其咨询问题<br />
                        3、有愿意配合的客户，最好是已经开通医保的机构（有专网，读卡器，扫码枪等基础医保设备）<br />
                        4、需要至少 5 家有刷医保需求的客户，才可以对接
                    </p>
                    <p style="margin-top: 8px;">如有其他疑问可以咨询 唐启涛</p>
                </div>
            </el-popover>
        </div>
        <div class="content-wrapper">
            <el-table
                v-loading="loadingModelData.loading.value"
                :max-height="maxHeight"
                :data="showDataList"
                highlight-current-row
                style="width: 100%; height: calc(100vh - 155px);"
            >
                <template v-for="item in showTableCol">
                    <el-table-column
                        v-if="item.children"
                        :key="item.label"
                        :label="item.label"
                    >
                        <template v-for="one in item.children">
                            <el-table-column
                                v-if="one.children"
                                :key="one.label"
                                :label="one.label"
                            >
                                <template v-for="two in one.children">
                                    <el-table-column
                                        v-if="two.children"
                                        :key="two.label"
                                        :label="two.label"
                                    >
                                    </el-table-column>
                                    <el-table-column-cont
                                        v-else
                                        :key="two.label"
                                        :item="two"
                                    ></el-table-column-cont>
                                </template>
                            </el-table-column>
                            <el-table-column-cont
                                v-else
                                :key="one.label"
                                :item="one"
                            ></el-table-column-cont>
                        </template>
                    </el-table-column>
                    <el-table-column-cont
                        v-else
                        :key="item.label"
                        :item="item"
                    ></el-table-column-cont>
                </template>
            </el-table>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { onMounted } from 'vue';
import { Search } from '@element-plus/icons-vue';
import { createPageFeatureController } from './controller';

import ElTableColumnCont from '../component/el-table-column-cont/index.vue';

const {
    loadingModelData,
    toolsParams,
    provinceOptions,
    showTableCol,
    showDataList,
    init,
    refreshConfigList,
} = createPageFeatureController();

onMounted(async () => {
    loadingModelData.setLoading(true);
    await init();
    await refreshConfigList();
    loadingModelData.setLoading(false);
});

const maxHeight = document.documentElement.clientHeight - 156;

/**
 * 当改变省时，触发更新数据
 * <AUTHOR>
 * @date 2024-03-20
 */
const onChangeProvinceNames = async () => {
    if (loadingModelData.loading.value === true) {
        return;
    }
    loadingModelData.setLoading(true);
    await refreshConfigList();
    loadingModelData.setLoading(false);
};
</script>

<style lang="scss">
    .social-module__page-feature {
        background-color: #fff;

        .tools-warpper {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid #e0e5ee;

            .el-select {
                width: 140px;
                margin-right: 12px;

                .el-input {
                    height: 30px;
                }

                .el-select-tags-wrapper {
                    height: 100%;
                }

                .el-select__tags {
                    height: 100%;
                    padding-top: 1px;
                    box-sizing: border-box;
                }
            }

            .el-input {
                margin-right: 12px;
            }
        }

        .content-wrapper {
            background-color: #fff;
            border-radius: 6px;
            padding: 16px;

            .el-table__body-wrapper {
                max-height: calc(100vh - 155px - 145px) !important;
            }

            .el-scrollbar__wrap {
                max-height: calc(100vh - 155px - 145px) !important;
            }
        }
    }
</style>