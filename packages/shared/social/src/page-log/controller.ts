/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import dayjs from 'dayjs';
import SocialA<PERSON> from '../api/social-api';
import AbcResponse from '../common/AbcResponse';
import * as utils from '../common/utils';

import { reactive, computed, watch } from 'vue';

import createDialogModel from '../model/dialog';
import createLoadingModel from '../model/loading';

export const createPageRemoteController = () => {
    const dialogModelFeature = createDialogModel();
    const dialogModelBugQuery = createDialogModel();
    const dialogModelFullScreenViewer = createDialogModel();
    const loadingModelSearch = createLoadingModel();
    const loadingModelTarget = createLoadingModel();

    // 请求参数
    const queryParams = reactive({
        dateRange: (() => {
            const date = utils.createDateFormat();
            return [
                `${date} 00:00:00`,
                `${date} 23:59:59`,
            ];
        })(),
        region: <any []>[], // 分区
        logStore: <any []>[], // 存储
        mark: <any []>[], // 标识
        keywords: '', // 搜索关键词
        activeTab: 'loglist',
        offset: 0,
        limit: 100,
    });

    // 请求返回数据
    const queryResponse = reactive({
        searchResult: <any> null,
        timelineData: <any> null,
        selectedItem: <any> null,
    });

    const regionOptions = utils.createRegionOptions();
    
    const logStoreOptions = utils.createLogStoreOptions();
    
    const markOptions = [
        { label: '成功标记', value: 'is_success_response' },
        { label: '失败标记', value: 'is_error_response' },
        // eslint-disable-next-line max-len
        { label: '进销存交易', value: '(商品盘存上传 or 商品库存变更 or 商品采购 or 商品采购退货 or 商品销售 or 商品销售退货 or 商品信息删除 or 定点医药机构商品库存信息查询 or 定点医药机构商品库存变更记录查询 or 定点医药机构商品采购信息查询 or 定点医药机构商品销售信息查询 or 定点医药机构入库商品追溯信息查询 or 定点医药机构商品销售追溯信息查询 or 商品库存信息 or 商品企业信息 or 定点结算追溯码信息采集 or 医药机构基本信息维护 or 商品领用信息)' },
        { label: '追溯码标记', value: 'drug_trac_codg' },
        { label: '进销存自动上报', value: '(自动进销存上报定时检查 or 自动进销存上报结果)' },
        { label: '中心数据拉取', value: '中心数据拉取' },
    ];

    // 搜索结果数据列表
    const searchResultDataList = computed(() => (queryResponse.searchResult?.dataList || [])
                    .map((item: any) => {
                        const itemInfo = {
                            ...item,
                            isSelected: item.id === queryResponse.selectedItem?.id, // 是否选中
                        };
                        return itemInfo;
                    })
                    .sort((a: any, b: any) => (a.id < b.id ? 1 : -1)));

    // 时间线数据列表
    const timelineDataList = computed(() => (queryResponse.timelineData?.dataList || [])
                    .map((item: any) => {
                        const itemInfo = {
                            ...item,
                            isSelected: item.id === queryResponse.selectedItem?.id, // 是否选中
                        };
                        return itemInfo;
                    })
                    .sort((a: any, b: any) => (a.id < b.id ? 1 : -1)));

    // 当前选择日志信息
    const targetLogInfo = computed(() => {
        const info = {
            region: '', // 地区
            clinicId: '', // 门店ID
            deviceCode: '', // 设备码
            tag: '', // 当前版本t
            tagPublishTime: '', // 当前版本发布时间
            statusType: '', // 状态类型
            statusWording: '', // 状态描述
            errorId: '', // 错误ID
            logData: <any> null, // 日志数据
        };
        if (queryResponse.selectedItem) {
            info.region = queryResponse.selectedItem.region;
            info.clinicId = queryResponse.selectedItem.clinicId;
            info.deviceCode = queryResponse.selectedItem.deviceCode;
            info.tag = queryResponse.selectedItem.tag;
            info.tagPublishTime = queryResponse.selectedItem.tagPublishTime;
            info.statusType = queryResponse.selectedItem.statusType;
            info.statusWording = queryResponse.selectedItem.statusWording;
            info.errorId = queryResponse.selectedItem.errorId;
            info.logData = utils.cloneDeep(queryResponse.selectedItem.logData);
            delete info.logData?.info;
        }
        return info;
    });

    const KEY_SEARCH_FILTER = 'social__page-log__search-filter';

    /**
     * 设置搜索过滤
     * <AUTHOR>
     * @date 2024-10-15
     * @param {Object} params
     */
    const setSearchFilter = (params: any) => {
        if (!params) {
            return;
        }
        const result = JSON.stringify(params);
        localStorage.setItem(KEY_SEARCH_FILTER, result);
    };

    /**
     * 拉取搜索过滤
     * <AUTHOR>
     * @date 2024-10-15
     * @returns {Object}
     */
    const getSearchFilter = () => {
        const result = localStorage.getItem(KEY_SEARCH_FILTER) || '';
        let params = null;
        try {
            params = result ? JSON.parse(result) : null;
        } catch (error) {
            console.log('getSearchFilter error', error);
        }
        return params;
    };

    watch(queryParams, () => {
        const params = {
            region: queryParams.region.slice(),
            logStore: queryParams.logStore.slice(),
        };
        setSearchFilter(params);
    });

    /**
     * 初始化查询参数
     * <AUTHOR>
     * @date 2025-01-24
     */
    const initQueryParams = () => {
        const params: any = getSearchFilter();
        if (params) {
            queryParams.region = params.region.slice();
            queryParams.logStore = params.logStore.slice();
        }
        if (queryParams.region.length === 0) {
            queryParams.region = regionOptions.map((item: any) => item.value);
        }
        if (queryParams.logStore.length === 0) {
            queryParams.logStore = logStoreOptions.filter((item: any) => item.isLogDefaultValue).map((item: any) => item.value);
        }
    };
    initQueryParams();

    const KEY_SEARCH_RECORD = 'social__page-log__search-record';

    /**
     * 设置搜索记录
     * <AUTHOR>
     * @date 2024-10-15
     * @param {String} keywords
     */
    const setSearchRecord = (keywords: string) => {
        if (!keywords) {
            return;
        }
        let dataList = getSearchRecord();
        dataList = dataList.filter((item: string) => item !== keywords);
        dataList.unshift(keywords);
        dataList = dataList.slice(0, 200);
        const result = JSON.stringify(dataList);
        localStorage.setItem(KEY_SEARCH_RECORD, result);
    };

    /**
     * 拉取搜索记录
     * <AUTHOR>
     * @date 2024-10-15
     * @returns {Array}
     */
    const getSearchRecord = () => {
        const result = localStorage.getItem(KEY_SEARCH_RECORD) || '';
        let dataList = [];
        try {
            dataList = result ? JSON.parse(result) : [];
        } catch (error) {
            console.log('getSearchRecord error', error);
        }
        return dataList;
    };

    /**
     * 创建日志文件名
     * <AUTHOR>
     * @date 2024-10-09
     * @returns {String}
     */
    const createFileName = () => {
        const name = queryResponse.selectedItem?.transType || '';
        return `${name || 'xxx'}.txt`;
    };

    /**
     * 创建文件内容
     * <AUTHOR>
     * @date 2024-10-09
     * @returns {String}
     */
    const createFileContent = () => {
        const {
            requestData,
            options,
            response,
        } = queryResponse.selectedItem.logData.data || {};
        const inputData = (() => {
            if (requestData) {
                return JSON.stringify(requestData, null, 4);
            }
            if (options?.body) {
                return JSON.stringify(options.body, null, 4);
            }
            return '';
        })();
        const outputData = (() => {
            if (response?.data) {
                return JSON.stringify(response.data, null, 4);
            }
            return '';
        })();
        if (!inputData && !outputData) {
            return '';
        }
        return `入参：${inputData}\n出参：${outputData}`;
    };

    /**
     * 创建日志搜索参数
     * <AUTHOR>
     * @date 2024-10-08
     * @returns {Object}
     */
    const createLogSearchParams = () => {
        const {
            dateRange: [
                startTime,
                endTime,
            ],
            keywords,
            region,
            logStore,
            mark,
        } = queryParams;
        const params = {
            beginDate: utils.createDateTimeFormat19(startTime), // 开始日期
            endDate: utils.createDateTimeFormat19(endTime), // 结束日期
            offset: 0, // 偏移量
            limit: 100, // 每页条数
            query: '"business:shebao_national_log" and __topic__ : abc-cis-shebao-stat-service', // 查询条件
            customParams: {
                region: region.slice(),
                logStore: logStore.slice(),
            },
        };
        if (keywords) {
            params.query += ` and ${keywords}`;
        }
        if (mark.length !== 0) {
            params.query += ` and ${mark.join(' and ')}`;
        }
        return params;
    };

    /**
     * 创建日志时间线参数
     * <AUTHOR>
     * @date 2024-10-08
     * @param {Object} item
     * @returns {Object}
     */
    const createLogTimelineParams = (item: any) => {
        const processId = item.processId;
        const date = dayjs(processId);
        const s = date.startOf('day');
        const e = date.endOf('day');
        const params = {
            region: item.area, // 分区
            beginDate: utils.createDateTimeFormat19(s), // 开始日期
            endDate: utils.createDateTimeFormat19(e), // 结束日期
            offset: 0, // 偏移量
            limit: 100, // 每页条数
            query: `"business:shebao_national_log" and __topic__ : abc-cis-shebao-stat-service and ${processId}`, // 查询条件
            customParams: {
                region: [item.area],
                logStore: logStoreOptions.map((item: any) => item.value),
            },
        };
        return params;
    };

    /**
     * 请求日志数据列表
     * <AUTHOR>
     * @date 2024-10-08
     * @param {Object} params
     * @returns {Promise<AbcResponse>}
     */
    const requestLogSearchResult = async (params: any) => {
        const response = await SocialApi.fetchManagementLogByCustomParams(params);
        if (response.status === false) {
            return response;
        }
        const dataList = (response.data.logItems || [])
                        .map((item: any) => createItemInfo(item))
                        .filter((item: any) => !!item);
        return AbcResponse.success({ dataList });
    };
    
    /**
     * 请求日志时间线
     * <AUTHOR>
     * @date 2024-10-08
     * @param {Object} params
     * @returns {Promise<AbcResponse>}
     */
    const requestLogTimeline = async (params: any) => {
        const response = await SocialApi.fetchManagementLogByCustomParams(params);
        if (response.status === false) {
            return response;
        }
        const dataList = (response.data.logItems || [])
                        .map((item: any) => createItemInfo(item))
                        .filter((item: any) => !!item);
        return AbcResponse.success({ dataList });
    };

    /**
     * 创建数据立碑
     * <AUTHOR>
     * @date 2024-10-09
     * @param {Object} item
     * @returns {Object}
     */
    const createItemInfo = (item: any) => {
        const message = item.message || item['message-index'] || '';
        try {
            const logData = utils.parseMessage(message);
            if (!logData) {
                throw new Error('utils.parseMessage error');
            }
            if (!logData.logId) {
                // 旧数据对齐，
                logData.logId = new Date(item.time).getTime();
            }
            const itemInfo = {
                clinicId: utils.parseClinicId(message),
                id: logData.logId, // 日志ID
                area: item.region, // 分区标识
                logStore: item.logStore, // 存储标识
                region: logData.region, // 地区
                datetime: logData.logId ? utils.createDateTimeFormat19(logData.logId) : '', // 日志时间
                deviceCode: logData.deviceCode, // 设备码
                tag: logData.buildInfo?.BUILD_TAG || '', // 当前版本tag
                tagPublishTime: logData.buildInfo?.BUILD_TIME || '', // 当前版本发布时间
                transType: logData.info._trans_type_ || '', // 交易类型
                transName: logData.info._trans_name_ || '', // 交易名称
                processId: (() => {
                    const processItem = (logData.process || [])[0];
                    return processItem?.processId || '';
                })(), // 进程ID
                processWording: (logData?.process || []).map((item: any) => item.processName).join(' / '),
                transWording: '',
                statusType: '',
                statusWording: '',
                errorId: '',
                logData,
            };
            if (logData.data?.name === 'fetchPack') {
                const url = logData.data.params?.url || '';
                logData.data.name = '接口调用';
                logData.data.type = url.split('/').slice(-1)[0];
            }
            // 交易描述
            itemInfo.transWording = (() => {
                const {
                    transType,
                    transName,
                    type,
                    name,
                    time,
                } = itemInfo.logData.data || {};
                return [
                    transType || type,
                    transName || name,
                    time,
                ].join(' / ');
            })();
            // 交易响应状态
            const response = logData.data?.response;
            itemInfo.statusType = (() => {
                if (response?.statusFlag === 'is_error_response') {
                    return 'danger';
                }
                if (response?.statusFlag === 'is_success_response') {
                    return 'success';
                }
                return 'success';
            })();
            itemInfo.statusWording = (() => {
                if (response?.statusFlag === 'is_error_response') {
                    return '失败';
                }
                if (response?.statusFlag === 'is_success_response') {
                    return '成功';
                }
                return '成功';
            })();
            itemInfo.errorId = (() => {
                if (response?.statusFlag === 'is_error_response') {
                    return response.errorId;
                }
                return '';
            })();
            return itemInfo;
        } catch (error) {
            console.log('createItemInfo error', error);
            console.log('message', message);
        }
        return null;
    };

    /**
     * 请求登录门店Href
     * <AUTHOR>
     * @date 2025-01-03
     * @param {String} clinicId
     * @returns {Promise<AbcResponse>}
     */
    const requestLoginClinicHref = async (clinicId: string) => {
        const params = {
            clinicId, // 门店ID
            clinicName: '', // 门店名称
            isAdmin: 1, // 管理员
            offset: 0,
            limit: 1,
        };
        const response = await SocialApi.fetchCrmClinicInfo(params);
        if (response.status === false) {
            return response;
        }
        const item = (response.data?.rows || [])[0];
        if (!item) {
            return AbcResponse.error('未找到当前门店成员');
        }
        return AbcResponse.success({
            openId: item.employeeOpenId || item.employeeMobile,
            clinicId,
        });
    };

    return {
        dialogModelFeature,
        dialogModelBugQuery,
        dialogModelFullScreenViewer,
        loadingModelSearch,
        loadingModelTarget,
        shortcuts: utils.createShortcuts(),
        queryParams,
        queryResponse,
        regionOptions,
        logStoreOptions,
        markOptions,
        searchResultDataList,
        timelineDataList,
        targetLogInfo,
        setSearchRecord,
        getSearchRecord,
        createFileName,
        createFileContent,
        createLogSearchParams,
        createLogTimelineParams,
        requestLogSearchResult,
        requestLogTimeline,
        requestLoginClinicHref,
    };
};