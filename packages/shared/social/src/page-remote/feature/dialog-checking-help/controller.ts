/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import dayjs from 'dayjs';
import AbcResponse from '../../../common/AbcResponse';
import { computed, reactive } from 'vue';
import * as utils from '../../../common/utils';

import createDialogModel from '../../../model/dialog';
import createLoadingModel from '../../../model/loading';

export const createCheckingHelpController = (props: any) => {
    const loadingModelQuery = createLoadingModel();
    const dialogModelCheckingInfoEdit = createDialogModel();

    const toolsParams = reactive({
        dataRange: [
            dayjs().startOf('month').format('YYYY-MM-DD'),
            dayjs().endOf('month').format('YYYY-MM-DD'),
        ],
    });

    const queryResponse = reactive({
        originData: <any> null,
        selectedItem: <any> null,
        editItems: <any []> [],
    });

    // 展示数据
    const showDataList = computed(() => {
        const items = [
            ...queryResponse.originData?.items || [],
        ];
        queryResponse.editItems.forEach((item: any) => {
            const target = items.find((one: any) => one.groupKey === item.groupKey);
            if (target) {
                Object.assign(target, item);
            } else {
                items.push(item);
            }
        });
        const dataList = items.map((item: any) => {
            const itemInfo = {
                ...item,
                ...createParams(),
                clrTypeWording: item.clrTypeWording || window.$national.tools.getClrTypeWording(item.clrType), // 清算类别
                insutypeWording: window.$national.tools.getInsutypeWording(item.insutype), // 险种类型
                clrOptinsWording: window.$national.tools.getCityAreaCodeWording(item.clrOptins, false, ''), // 经办机构
                medfeeSumamtWording: utils.isEmpty(item.medfeeSumamt) ? '' : utils.moneyStr(item.medfeeSumamt), // 医疗费用总额
                fundPaySumamtWording: utils.isEmpty(item.fundPaySumamt) ? '' : utils.moneyStr(item.fundPaySumamt), // 基金支付金额
                acctPayWording: utils.isEmpty(item.acctPay) ? '' : utils.moneyStr(item.acctPay), // 个人账户支出
                checkingStatusObject: (() => {
                    const wording = {
                        label: '',
                        style: {},
                        tips: '',
                    };
                    if (item.isNotNeedChecking) {
                        wording.label = '无需对账';
                        wording.style = 'color: #a8abb2';
                        wording.tips = item.notNeedCheckingTips;
                    } else if (item.isCheckingSuccess) {
                        wording.label = '对账成功';
                        wording.style = 'color: #606266';
                    } else if (item.stmtRslt === 1) {
                        wording.label = '对账失败';
                        wording.style = 'color: #FF9933';
                    } else {
                        wording.label = '未对账';
                        wording.style = 'color: #4C7DFA';
                    }
                    return wording;
                })(), // 处理状态
                isDisabledMarkBtn: (() => {
                    if (item.isInsertData) {
                        return true;
                    }
                    if (item.isNotNeedChecking) {
                        return true;
                    }
                    if (item.isCheckingSuccess) {
                        return true;
                    }
                    return false;
                })(), // 是否禁用标记按钮
            };
            return itemInfo;
        });
        return window.$national.tools.handleGroupDataSort(dataList);
    });

    /**
     * 日期快捷选择
     * <AUTHOR>
     * @date 2025-01-11
     * @returns {Array}
     */
    const shortcuts = [
        {
            text: '今天',
            value: () => {
                const date = dayjs();
                const s = date.startOf('day').toDate();
                const e = date.endOf('day').toDate();
                return [s, e];
            },
        },
        {
            text: '昨天',
            value: () => {
                const date = dayjs().subtract(1, 'day');
                const s = date.startOf('day').toDate();
                const e = date.endOf('day').toDate();
                return [s, e];
            },
        },
        {
            text: '本月',
            value: () => {
                const date = dayjs();
                const s = date.startOf('month').toDate();
                const e = date.endOf('month').toDate();
                return [s, e];
            },
        },
        {
            text: '上月',
            value: () => {
                const date = dayjs().subtract(1, 'month');
                const s = date.startOf('month').toDate();
                const e = date.endOf('month').toDate();
                return [s, e];
            },
        },
    ];

    /**
     * 创建查询参数
     * <AUTHOR>
     * @date 2024-08-06
     * @returns {Object}
     */
    const createParams = () => {
        const {
            dataRange, // 月份
        } = toolsParams;
        const params = {
            beginDate: dataRange[0], // 开始日期
            endDate: dataRange[1], // 截止日期
            ...(window.$national.config.checkingDataFetchConfig || {}), // 对账查询参数
        };
        return params;
    };

    /**
     * 更新编辑项
     * <AUTHOR>
     * @date 2025-01-11
     * @param {Object} editItems:any
     * @param {Object} updateInfo
     */
    const updateEditItems = (editItems: any, updateInfo: any) => {
        const target = queryResponse.editItems.find((item: any) => item.groupKey === editItems.groupKey);
        if (target) {
            Object.assign(target, updateInfo);
        }
    };

    /**
     * 匹配自动更新数据
     * <AUTHOR>
     * @date 2025-03-05
     * @param {AbcResponse} checkingResponse
     * @returns {Object}
     */
    const matchAutoUpdateData = (checkingResponse: AbcResponse) => {
        const info: any = {};
        // medfeeSumamt: 0, // 医疗费用总额
        // fundPaySumamt: 0, // 基金支付总额
        // acctPay: 0, // 个人账户支出总额
        // fixmedinsSetlCnt: 0, // 结算笔数
        const region = props.socialInfo.region;
        const message = checkingResponse.message;
        if (region.startsWith('xinjiang_')) {
            // 新疆
            // 医疗费用总额[1939.30]大于医保中心数据[1994.3]!
            // 医疗费总额[1614.09]小于医保中心数据[2766.12]!
            // 基金支付总额[1244.32]小于医保中心数据[2084.71]!
            // 个人账户支出总额[358]小于医保中心数据[669.64]!
            // 结算笔数[8]小于医保中心数据[21]!
            if (message.indexOf('医疗费用总额') !== -1) {
                const pattern = /医疗费用总额\[(.+?)\][小大]于医保中心数据\[(.+?)\]/i;
                const arr = message.match(pattern) || [];
                info.medfeeSumamt = arr[2];
            }
            if (message.indexOf('医疗费总额') !== -1) {
                const pattern = /医疗费总额\[(.+?)\][小大]于医保中心数据\[(.+?)\]/i;
                const arr = message.match(pattern) || [];
                info.medfeeSumamt = arr[2];
            }
            if (message.indexOf('基金支付总额') !== -1) {
                const pattern = /基金支付总额\[(.+?)\][小大]于医保中心数据\[(.+?)\]/i;
                const arr = message.match(pattern) || [];
                info.fundPaySumamt = arr[2];
            }
            if (message.indexOf('个人账户支出总额') !== -1) {
                const pattern = /个人账户支出总额\[(.+?)\][小大]于医保中心数据\[(.+?)\]/i;
                const arr = message.match(pattern) || [];
                info.acctPay = arr[2];
            }
            if (message.indexOf('结算笔数') !== -1) {
                const pattern = /结算笔数\[(.+?)\][小大]于医保中心数据\[(.+?)\]/i;
                const arr = message.match(pattern) || [];
                info.fixmedinsSetlCnt = arr[2];
            }
        }
        if (region.startsWith('neimenggu_')) {
            // 内蒙古
            // 医疗费总额(29154.79)与医保中心数据(511.86)不一致!
            // 基金支付总额(7366.75)与医保中心数据(0.00)不一致!
            // 个人账户支出总额(17063.82)与医保中心数据(0.00)不一致!
            // 结算笔数(255)与医保中心数据(14)不一致!
            if (message.indexOf('医疗费总额') !== -1) {
                const pattern = /医疗费总额\((.+?)\)与医保中心数据\((.+?)\)不一致/i;
                const arr = message.match(pattern) || [];
                info.medfeeSumamt = arr[2];
            }
            if (message.indexOf('基金支付总额') !== -1) {
                const pattern = /基金支付总额\((.+?)\)与医保中心数据\((.+?)\)不一致/i;
                const arr = message.match(pattern) || [];
                info.fundPaySumamt = arr[2];
            }
            if (message.indexOf('个人账户支出总额') !== -1) {
                const pattern = /个人账户支出总额\((.+?)\)与医保中心数据\((.+?)\)不一致/i;
                const arr = message.match(pattern) || [];
                info.acctPay = arr[2];
            }
            if (message.indexOf('结算笔数') !== -1) {
                const pattern = /结算笔数\((.+?)\)与医保中心数据\((.+?)\)不一致/i;
                const arr = message.match(pattern) || [];
                info.fixmedinsSetlCnt = arr[2];
            }
        }
        if (
            region.startsWith('gansu_')
            || region.startsWith('hunan_')
        ) {
            // 甘肃、湖南
            // 医疗费总额与医保中心数据不一致![机构:338.65,中心:1144.65]
            // 基金支付总额与医保中心数据不一致![机构:73.54,中心:527.49]
            // 个人账户支出总额与医保中心数据不一致!![机构:265.11,中心:617.16]
            // 结算笔数与医保中心数据不一致!![机构:2,中心:7]
            if (message.indexOf('医疗费总额与医保中心数据不一致') !== -1) {
                const pattern = /医疗费总额与医保中心数据不一致!\[机构:(.+?),中心:(.+?)\]/i;
                const arr = message.match(pattern) || [];
                info.medfeeSumamt = arr[2];
            }
            if (message.indexOf('基金支付总额与医保中心数据不一致') !== -1) {
                const pattern = /基金支付总额与医保中心数据不一致!\[机构:(.+?),中心:(.+?)\]/i;
                const arr = message.match(pattern) || [];
                info.fundPaySumamt = arr[2];
            }
            if (message.indexOf('个人账户支出总额与医保中心数据不一致') !== -1) {
                const pattern = /个人账户支出总额与医保中心数据不一致!!\[机构:(.+?),中心:(.+?)\]/i;
                const arr = message.match(pattern) || [];
                info.acctPay = arr[2];
            }
            if (message.indexOf('结算笔数与医保中心数据不一致') !== -1) {
                const pattern = /结算笔数与医保中心数据不一致!!\[机构:(.+?),中心:(.+?)\]/i;
                const arr = message.match(pattern) || [];
                info.fixmedinsSetlCnt = arr[2];
            }
        }
        if (region.startsWith('liaoning_')) {
            // 辽宁
            // 医疗费总额：17362.63与医保中心数据：59147.87不一致!
            // 基金支付总额：7694.3与医保中心数据：26013.83不一致!
            // 个人账户支出总额：8642.01，与医保中心数据24662.50不一致!
            // 结算笔数：67，与医保中心数据194不一致!
            if (message.indexOf('医疗费总额') !== -1) {
                const pattern = /医疗费总额：(.+?)与医保中心数据：(.+?)不一致/i;
                const arr = message.match(pattern) || [];
                info.medfeeSumamt = arr[2];
            }
            if (message.indexOf('基金支付总额') !== -1) {
                const pattern = /基金支付总额：(.+?)与医保中心数据：(.+?)不一致/i;
                const arr = message.match(pattern) || [];
                info.fundPaySumamt = arr[2];
            }
            if (message.indexOf('个人账户支出总额') !== -1) {
                const pattern = /个人账户支出总额：(.+?)，与医保中心数据(.+?)不一致/i;
                const arr = message.match(pattern) || [];
                info.acctPay = arr[2];
            }
            if (message.indexOf('结算笔数') !== -1) {
                const pattern = /结算笔数：(.+?)，与医保中心数据(.+?)不一致/i;
                const arr = message.match(pattern) || [];
                info.fixmedinsSetlCnt = arr[2];
            }
        }
        if (region.startsWith('henan_')) {
            // 河南 - 本地
            // 医疗费总额：0与医保中心数据：5468.73不一致!
            // 基金支付总额：0与医保中心数据：3685.91不一致!
            // 个人账户支出总额：0，与医保中心数据1782.82不一致!
            // 结算笔数：0，与医保中心数据41不一致!
            if (message.indexOf('医保中心数据') !== -1) {
                if (message.indexOf('医疗费总额') !== -1) {
                    const pattern = /医疗费总额：(.+?)与医保中心数据：(.+?)不一致/i;
                    const arr = message.match(pattern) || [];
                    info.medfeeSumamt = arr[2];
                }
                if (message.indexOf('基金支付总额') !== -1) {
                    const pattern = /基金支付总额：(.+?)与医保中心数据：(.+?)不一致/i;
                    const arr = message.match(pattern) || [];
                    info.fundPaySumamt = arr[2];
                }
                if (message.indexOf('个人账户支出总额') !== -1) {
                    const pattern = /个人账户支出总额：(.+?)，与医保中心数据(.+?)不一致/i;
                    const arr = message.match(pattern) || [];
                    info.acctPay = arr[2];
                }
                if (message.indexOf('结算笔数') !== -1) {
                    const pattern = /结算笔数：(.+?)，与医保中心数据(.+?)不一致/i;
                    const arr = message.match(pattern) || [];
                    info.fixmedinsSetlCnt = arr[2];
                }
            }

            // 河南 - 异地
            // 省平台医疗费总额：1406.55与定点医疗机构数据：0不一致!
            // 基金支付总额：1047.88与定点医疗机构数据：0不一致!
            // 个人账户支出总额：236.70与定点医疗机构数据：0不一致!
            // 结算笔数：6与定点医疗机构数据：0不一致!
            if (message.indexOf('定点医疗机构数据') !== -1) {
                if (message.indexOf('医疗费总额') !== -1) {
                    const pattern = /医疗费总额：(.+?)与定点医疗机构数据：(.+?)不一致/i;
                    const arr = message.match(pattern) || [];
                    info.medfeeSumamt = arr[2];
                }
                if (message.indexOf('基金支付总额') !== -1) {
                    const pattern = /基金支付总额：(.+?)与定点医疗机构数据：(.+?)不一致/i;
                    const arr = message.match(pattern) || [];
                    info.fundPaySumamt = arr[2];
                }
                if (message.indexOf('个人账户支出总额') !== -1) {
                    const pattern = /个人账户支出总额：(.+?)与定点医疗机构数据：(.+?)不一致/i;
                    const arr = message.match(pattern) || [];
                    info.acctPay = arr[2];
                }
                if (message.indexOf('结算笔数') !== -1) {
                    const pattern = /结算笔数：(.+?)与定点医疗机构数据：(.+?)不一致/i;
                    const arr = message.match(pattern) || [];
                    info.fixmedinsSetlCnt = arr[2];
                }
            }
        }
        if (JSON.stringify(info) === '{}') {
            return null;
        }
        return info;
    };

    /**
     * 请求对账数据
     * <AUTHOR>
     * @date 2024-08-07
     * @param {Object} params
     * @returns {Promise<AbcResponse>}
     */
    const requestCheckingData = async (params: any) => {
        const js = `
            const fetcParams = ${JSON.stringify(params)}
            const fetchResponse = await window.$national.api.fetchCheckingDataList(fetcParams)
            if (fetchResponse.status === true) {
                (fetchResponse.data.items || []).forEach((item) => Object.assign(item, fetcParams))
                // 删除明细列表，否则数据量太大
                window.remoteCheckingData = {
                    params: fetcParams,
                    response: fetchResponse,
                }
                response = {
                    status: true,
                    data: {
                        items: (fetchResponse.data.items || []).map((item) => ({
                            ...item,
                            settleItems: [],
                        }))
                    }
                }
            }
        `;
        const response = await utils.remoteRunJs(props.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    /**
     * 请求对账 - 一项数据
     * <AUTHOR>
     * @date 2024-08-07
     * @param {Object} item
     * @returns {Promise<AbcResponse>}
     */
    const requestCheckingItem = async (item: any, isMockSuccess = false) => {
        const js = `
            const that = {
                $national: window.$national,
                nationalSocialSecurity: window.$national.protocol.NationalSocialSecurity.instance,
            }
            const checkingPresenter = new window.$national.view.CheckingNewPresenter(that)
            // 对总账
            const item = ${JSON.stringify(item)}
            const params = {
                summaryItem: (() => {
                    if (!item.isInsertData) {
                        if (!window.remoteCheckingData) {
                            throw new Error('请重新查询对账数据后再发起对账')
                        }
                        const target = window.remoteCheckingData.response.data.items.find((one) => one.groupKey === item.groupKey)
                        if (!target) {
                            throw new Error('数据异常，重新打开该功能弹窗后再试')
                        }
                        item.settleItems = target.settleItems
                    }
                    return item
                })(),
                isMockSuccess: ${isMockSuccess},
                isNeedPostSettleItems: item.isInsertData ? false : true,
            }
            const checkingResponse = await checkingPresenter.requestCheckingTotal(params)
            if (checkingResponse.status === false) {
                throw checkingResponse
            }
            if (checkingResponse.data?.isNeedHandle) {
                // 对账不平
                if (checkingResponse.data.isNeedCheckingDetail && !${isMockSuccess}) {
                    // 对明细
                    const checkingDetailResponse = await checkingPresenter.requestCheckingDetail(params)
                    if (checkingDetailResponse.status === false) {
                        throw checkingDetailResponse
                    }
                }
                throw new Error(checkingResponse.data.stmtRsltDscr)
            }
            // 对账成功
            response = { status: true }
        `;
        const response = await utils.remoteRunJs(props.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    /**
     * 请求协助对账 - 标记
     * <AUTHOR>
     * @date 2024-11-28
     * @param {Object} item
     * @returns {Promise<AbcResponse>}
     */
    const requestCheckingDataMark = async (item: any) => {
        if (!item.isDisabledMarkBtn) {
            // 未对账，标记为已对账
            return await requestCheckingItem(item, true);
        }
        return AbcResponse.success();
    };

    return {
        loadingModelQuery,
        dialogModelCheckingInfoEdit,
        shortcuts,
        toolsParams,
        queryResponse,
        showDataList,
        createParams,
        updateEditItems,
        matchAutoUpdateData,
        requestCheckingData,
        requestCheckingItem,
        requestCheckingDataMark,
    };
};