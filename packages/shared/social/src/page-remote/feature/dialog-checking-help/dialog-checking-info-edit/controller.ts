/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import { computed, reactive } from 'vue';

export const createCheckingInfoEditController = (props: any) => {
    const formData = reactive({
        clrType: '', // 清算类别
        insutype: '', // 险种
        clrOptins: '', // 清算经办机构
        medfeeSumamt: '', // 医疗费用总额
        fundPaySumamt: '', // 基金支付总额
        acctPay: '', // 个人账户支付金额
        fixmedinsSetlCnt: '', // 定点医药机构结算笔数
    });

    // 是否禁用确认按钮
    const isDisabledConfirmBtn = computed(() => !(
        formData.clrType
        && formData.insutype
        && formData.clrOptins
        && formData.medfeeSumamt
        && formData.fixmedinsSetlCnt
    ));

    // 清算类别选项
    const clrTypeOptions = computed(() => window.$national.options.clrTypeOptions);
    
    // 险种类型选项
    const insutypeOptions = computed(() => window.$national.options.insutypeOptions);
    
    // 经办机构选项
    const clrOptinsOptions = computed(() => window.$national.options.clrOptinsOptions);

    /**
     * 初始化表单数据
     * <AUTHOR>
     * @date 2024-11-28
     */
    const initFormData = () => {
        const {
            clrType = '', // 清算类别
            insutype = '', // 险种
            clrOptins = '', // 清算经办机构
            medfeeSumamt = '', // 医疗费用总额
            fundPaySumamt = '', // 基金支付总额
            acctPay = '', // 个人账户支付金额
            fixmedinsSetlCnt = '', // 定点医药机构结算笔数
        } = props.item || {};
        formData.clrType = clrType; // 清算类别
        formData.insutype = insutype; // 险种
        formData.clrOptins = clrOptins; // 清算经办机构
        formData.medfeeSumamt = medfeeSumamt; // 医疗费用总额
        formData.fundPaySumamt = fundPaySumamt; // 基金支付总额
        formData.acctPay = acctPay; // 个人账户支付金额
        formData.fixmedinsSetlCnt = fixmedinsSetlCnt; // 定点医药机构结算笔数
    };

    return {
        formData,
        isDisabledConfirmBtn,
        clrTypeOptions,
        insutypeOptions,
        clrOptinsOptions,
        initFormData,
    };
};