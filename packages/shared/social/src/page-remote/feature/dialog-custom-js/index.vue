<template>
    <el-dialog
        v-model="isShowDialogCustomJs"
        title="自定义脚本"
        :close-on-click-modal="false"
        custom-class="social-module__feature__dialog-custom-js"
    >
        <el-tabs v-model="tab" type="border-card">
            <el-tab-pane label="代码编辑" name="code">
                <codemirror v-model="formData.code" :options="options"></codemirror>
                <el-button
                    type="primary"
                    :disabled="isDisabledQueryBtn"
                    :loading="loadingModelQuery.loading.value"
                    @click="onClickQuery"
                >
                    执行代码
                </el-button>
            </el-tab-pane>
            <el-tab-pane label="执行结果" name="result">
                <vue-json-viewer
                    :value="queryResponse.originData"
                    :expand-depth="6"
                ></vue-json-viewer>
            </el-tab-pane>
        </el-tabs>
    </el-dialog>
</template>

<script lang="ts" setup>
import VueJsonViewer from 'vue-json-viewer';
import { ref, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { Codemirror } from 'vue-codemirror';
import { createCustomJsController } from './controller';

const props = defineProps({
    deviceCode: {
        type: String,
        default: '',
    },
    modelValue: {
        type: Number,
        default: 0,
    },
});

const $emit = defineEmits([
    'update:modelValue', // 更新显示状态
]);

const isShowDialogCustomJs = computed({
    get() {
        return !!props.modelValue;
    },
    set(val:boolean) {
        $emit('update:modelValue', +val);
    },
});

const tab = ref('code');

const options = {
    mode: 'javascript',
    theme: 'default',
    indentUnit: 4,
    smartIndent: true,
    lineWrapping: true,
    viewportMargin: 20, // 设置默认显示 20 行
};

const {
    loadingModelQuery,
    formData,
    queryResponse,
    isDisabledQueryBtn,
    createParams,
    requestCode,
} = createCustomJsController(props);

/**
 * 当点击查询时
 * <AUTHOR>
 * @date 2024-08-07
 */
const onClickQuery = async () => {
    if (loadingModelQuery.loading.value) {
        return;
    }
    queryResponse.originData = null;
    loadingModelQuery.setLoading(true);
    const params = createParams();
    const response = await requestCode(params);
    loadingModelQuery.setLoading(false);
    if (response.status === false) {
        return ElMessage.error('执行失败: ' + response.message);
    }
    queryResponse.originData = response;
    tab.value = 'result';
};
</script>

<style lang="scss">
    .social-module__feature__dialog-custom-js {
        width: 1200px !important;

        .el-dialog__body {
            padding-top: 8px;
        }

        .el-tabs {
            height: 560px;

            .cm-editor {
                max-height: 450px;
                overflow: auto;
            }

            .el-button {
                margin-top: 16px;
            }
        }

        .jv-container {
            height: 490px;
            overflow: auto;
        }
    }
</style>