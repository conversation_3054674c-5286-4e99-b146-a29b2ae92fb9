<template>
    <el-dialog
        v-model="isShowDialogDeleteSettleData"
        title="删除结算数据"
        :close-on-click-modal="false"
        custom-class="social-module__feature__dialog-delete-settle-date"
    >
        <el-form
            :inline="true"
            label-position="left"
            label-width="80px"
            :model="formData"
        >
            <el-form-item label="msgid">
                <el-input
                    v-model="formData.msgid"
                    placeholder="请输入msgid，必填"
                    style="width: 240px;"
                ></el-input>
            </el-form-item>
            <el-form-item label="tradeType">
                <el-input
                    v-model="formData.tradeType"
                    placeholder="请输入tradeType，必填"
                    style="width: 240px;"
                ></el-input>
            </el-form-item>
            <el-form-item label="如何获取">
                <el-image
                    style="width: 50px; height: 50px;"
                    :src="img"
                    :zoom-rate="1.2"
                    :max-scale="7"
                    :min-scale="0.2"
                    :preview-src-list="[img]"
                    fit="cover"
                />
            </el-form-item>
            <div style="display: flex; justify-content: flex-end;">
                <el-button
                    type="primary"
                    plain
                    :disabled="disabledDeleteBtn"
                    :loading="loadingModelDelete.loading.value"
                    @click="onClickDeleteSettleData"
                >
                    执行删除
                </el-button>
            </div>
        </el-form>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus';
import { createDeleteSettleDateController } from './controller';
import { computed } from 'vue';
import img from './img.png';
import * as utils from '../../../common/utils';

const props = defineProps({
    deviceCode: {
        type: String,
        default: '',
    },
    modelValue: {
        type: Number,
        default: 0,
    },
});

const $emit = defineEmits([
    'update:modelValue', // 更新显示状态
]);

const isShowDialogDeleteSettleData = computed({
    get() {
        return !!props.modelValue;
    },
    set(val:boolean) {
        $emit('update:modelValue', +val);
    },
});

const {
    loadingModelDelete,
    formData,
    disabledDeleteBtn,
    createPostData,
    requestDeleteSettleData,
} = createDeleteSettleDateController(props);

/**
 * 当点击删除结算数据
 * <AUTHOR>
 * @date 2025-06-19
 */
const onClickDeleteSettleData = async () => {
    if (loadingModelDelete.loading.value) {
        return;
    }
    const confirmResponse = await utils.messageConfirm('请确认需要删除结算数据', '提示', {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
    });
    if (confirmResponse.status === false) {
        return confirmResponse;
    }
    loadingModelDelete.setLoading(true);
    const postData = createPostData();
    const requestResponse = await requestDeleteSettleData(postData);
    loadingModelDelete.setLoading(false);
    if (requestResponse.status === false) {
        return ElMessage.error('操作失败: ' + requestResponse.message);
    }
    ElMessage.success('操作成功');
    isShowDialogDeleteSettleData.value = false;
};
</script>

<style lang="scss">
    .social-module__feature__dialog-delete-settle-date {
        width: 360px !important;
        min-height: 260px;

        .el-dialog__body {
            padding-top: 8px;
        }

        .el-form-item {
            margin-right: 0 !important;
        }
    }
</style>