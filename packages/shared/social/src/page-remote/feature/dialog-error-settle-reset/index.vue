<template>
    <el-dialog
        v-model="isShowDialogErrorSettleReset"
        title="异常结算状态重置"
        :close-on-click-modal="false"
        custom-class="social-module__feature__dialog-error-settle-reset"
    >
        <el-form
            :inline="true"
            label-position="left"
            label-width="80px"
            :model="formData"
        >
            <el-form-item label="msgid">
                <el-input
                    v-model="formData.msgid"
                    placeholder="请输入msgid，必填"
                    style="width: 240px;"
                ></el-input>
            </el-form-item>
            <el-form-item label="tradeType">
                <el-input
                    v-model="formData.tradeType"
                    placeholder="请输入tradeType，必填"
                    style="width: 240px;"
                ></el-input>
            </el-form-item>
            <div style="display: flex; justify-content: flex-end;">
                <el-button
                    type="primary"
                    plain
                    :disabled="disabledResetBtn"
                    :loading="loadingModelReset.loading.value"
                    @click="onClickResetSettleData"
                >
                    执行重置
                </el-button>
            </div>
        </el-form>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus';
import { createResetErrorSettleController } from './controller';
import { computed } from 'vue';
import * as utils from '../../../common/utils';

const props = defineProps({
    deviceCode: {
        type: String,
        default: '',
    },
    modelValue: {
        type: Number,
        default: 0,
    },
});

const $emit = defineEmits([
    'update:modelValue', // 更新显示状态
]);

const isShowDialogErrorSettleReset = computed({
    get() {
        return !!props.modelValue;
    },
    set(val:boolean) {
        $emit('update:modelValue', +val);
    },
});

const {
    loadingModelReset,
    formData,
    disabledResetBtn,
    createPostData,
    requestResetSettleData,
} = createResetErrorSettleController(props);

/**
 * 当点击重置结算数据
 * <AUTHOR>
 * @date 2025-06-19
 */
const onClickResetSettleData = async () => {
    if (loadingModelReset.loading.value) {
        return;
    }
    const confirmResponse = await utils.messageConfirm('请确认需要重置结算数据状态为待处理', '提示', {
        confirmButtonText: '重置',
        cancelButtonText: '取消',
        type: 'warning',
    });
    if (confirmResponse.status === false) {
        return confirmResponse;
    }
    loadingModelReset.setLoading(true);
    const postData = createPostData();
    const requestResponse = await requestResetSettleData(postData);
    loadingModelReset.setLoading(false);
    if (requestResponse.status === false) {
        return ElMessage.error('操作失败: ' + requestResponse.message);
    }
    ElMessage.success('操作成功');
    isShowDialogErrorSettleReset.value = false;
};
</script>

<style lang="scss">
    .social-module__feature__dialog-error-settle-reset {
        width: 360px !important;
        min-height: 260px;

        .el-dialog__body {
            padding-top: 8px;
        }

        .el-form-item {
            margin-right: 0 !important;
        }
    }
</style>