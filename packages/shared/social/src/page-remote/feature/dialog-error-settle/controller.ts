/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import { computed, reactive } from 'vue';
import dayjs from 'dayjs';
import * as utils from '../../../common/utils';

import createLoadingModel from '../../../model/loading';
import createPageModel from '../../../model/page';
import createDialogModel from '../../../model/dialog';

export const createErrorSettleController = (props: any) => {
    const dialogModelPreSettleEdit = createDialogModel();
    const loadingModelQuery = createLoadingModel();
    const pageModel = createPageModel();
    pageModel.setPageSize(8);

    const toolsParams = reactive({
        month: window.$national.tools.getMonthFormat(), // 月份
    });

    const queryResponse = reactive({
        originData: <any> null,
        selectedItem: <any> null,
    });

    // 匹配数据
    let cacheToolsParams = {};
    const mateDataList = computed(() => {
        const dataList = (queryResponse.originData?.items || []);
        pageModel.setTotal(dataList.length);
        if (!utils.isEqual(cacheToolsParams, toolsParams)) {
            pageModel.setPage(1);
            cacheToolsParams = utils.cloneDeep(toolsParams);
        }
        return dataList;
    });

    // 展示数据
    const showDataList = computed(() => {
        const { sIndex, eIndex } = pageModel.createSliceParams();
        return mateDataList.value
                        .slice(sIndex, eIndex)
                        .map((item: any) => {
                            const itemInfo = {
                                ...item,
                                typeWording: (() => {
                                    // type 1 收费，-1 退费
                                    let wording = '';
                                    if (item.type === 1) {
                                        // 收费
                                        wording = '收费';
                                        if (item.tradeType === 24) {
                                            wording = '收费-共济';
                                        }
                                    }
                                    if (item.type === -1) {
                                        // 退费
                                        wording = '退费';
                                        if (item.tradeType === 25) {
                                            wording = '退费-共济';
                                        }
                                    }
                                    return wording;
                                })(), // 类型
                                medfeeSumamtWording: utils.isEmpty(item.medfeeSumamt) ? '' : utils.moneyStr(item.medfeeSumamt), // 医疗费用总额
                                fundPaySumamtWording: utils.isEmpty(item.fundPaySumamt) ? '' : utils.moneyStr(item.fundPaySumamt), // 基金支付金额
                                acctPayWording: utils.isEmpty(item.acctPay) ? '' : utils.moneyStr(item.acctPay), // 个人账户支出
                                settleTimeWording: utils.isEmpty(item.settleTime) ? '' : utils.createDateTimeFormat19(item.settleTime), // 结算发起时间
                                statusObject: (() => {
                                    const options = [
                                        { value: 1, name: '待处理', color: '#005ed9' },
                                        { value: 3, name: '待处理', color: '#005ed9' },
                                        { value: 10, name: '已处理', color: '#606266' },
                                        { value: 30, name: '已处理', color: '#606266' },
                                    ];
                                    return options.find((one) => one.value === item.status);
                                })(), // 处理状态
                                insuplcAdmdvsWording: window.$national.tools.getInsuplcAdmdvsWording(item.insuplcAdmdvs), // 参保地医保区划
                                clrTypeWording: item.clrTypeWording || window.$national.tools.getClrTypeWording(item.clrType), // 清算类别
                                insutypeWording: window.$national.tools.getInsutypeWording(item.insutype), // 险种类型
                                clrOptinsWording: window.$national.tools.getCityAreaCodeWording(item.clrOptins, false, ''), // 经办机构
                                disabledMark: (() => {
                                    if (item.status === 10) {
                                        return true;
                                    }
                                    if (item.status === 30) {
                                        return true;
                                    }
                                    return false;
                                })(), // 是否禁用标记按钮
                            };
                            return itemInfo;
                        });
    });

    /**
     * 创建查询参数
     * <AUTHOR>
     * @date 2024-08-06
     * @returns {Object}
     */
    const createParams = () => {
        const {
            month, // 月份
        } = toolsParams;
        const date = dayjs(`${month}-01`);
        const params = {
            beginDate: date.startOf('month').format('YYYY-MM-DD'), // 开始日期
            endDate: date.endOf('month').format('YYYY-MM-DD'), // 截止日期
            filterType: 1, // 0 正常入账；1 冲正数据；2 对账失败
            offset: 0,
            limit: 200,
        };
        return params;
    };

    /**
     * 请求异常结算数据
     * <AUTHOR>
     * @date 2024-08-07
     * @param {Object} params
     * @returns {Promise<AbcResponse>}
     */
    const requestErrorSettleData = async (params: any) => {
        const js = `
            const params = ${JSON.stringify(params)}
            response = await window.$national.api.fetchSettleByDay(params)
        `;
        const response = await utils.remoteRunJs(props.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    /**
     * 请求异常结算数据 - 处理
     * <AUTHOR>
     * @date 2024-11-28
     * @param {Object} item
     * @returns {Promise<AbcResponse>}
     */
    const requestErrorSettleHandle = async (item: any) => {
        const js = `
            const item = ${JSON.stringify(item)}
            const that = {
                $national: window.$national,
                nationalSocialSecurity: window.$national.protocol.NationalSocialSecurity.instance,
            }
            const checkingPresenter = new window.$national.view.CheckingNewPresenter(that)
            response = await (async () => {
                if (item.type === 1) {
                    // 收费，先冲正，冲正报错在提示要不要退费
                    const backoutResponse = await checkingPresenter.requestBackout({ rowData: item })
                    if (backoutResponse.status === true) {
                        return backoutResponse
                    }

                    // 没有明确不允许冲正的，不继续
                    if (!that.nationalSocialSecurity.escapeMessage.checkIsBackoutUnable(backoutResponse)) {
                        return backoutResponse
                    }

                    // 补结算的入参
                    const params = {
                        rowData: item,
                        settleResponse: null, // 结算响应
                    }

                    // 没结算ID，可以通过结算日志查询，来检查是否结算成功
                    if (!item.setlId) {
                        // 查询结算日志
                        const settleResponse = await checkingPresenter.requestSettleResponse(item)
                        const setlId = settleResponse.data?.output?.setlinfo?.setl_id || ''
                        if (
                            settleResponse.status === false
                            || !setlId
                        ) {
                            // 没有查到
                            return backoutResponse
                        }
                        // 查到了，那可以走退费了
                        item.setlId = setlId
                        params.settleResponse = settleResponse
                    }

                    // 有结算ID了，可以提示继续退费

                    // 退费，成功后写一笔收费在预结算日期，写一笔退费在当日
                    const settleRevokeResponse = await checkingPresenter.requestRepairSettle(params)
                    if (settleRevokeResponse.status === false) {
                        // todo 存在异常结算数据不能退费的问题，多次退费不成功，可以考虑不退了，直接将结算交易写入三方数据在进行对总账
                        return settleRevokeResponse
                    }

                    // 退费成功，标记异常结算已处理
                    const postResponse = await checkingPresenter.requestBackoutPost(item)
                    if (postResponse.status === false) {
                        return postResponse
                    }

                    // 处理完成，提示成功
                    return { status: true }
                } else if (
                    item.type === -1
                    && (item.isPreRefund === 1 || item.tradesType === 25)
                ) {
                    // 退费，预退就入账的
                    const params = {
                        rowData: item,
                        isMutualAid: item.tradesType === 25, // 是否共济退费
                    }
                    const settleRevokeResponse = await checkingPresenter.requestSettleRevoke(params)
                    if (settleRevokeResponse.status === false) {
                        return settleRevokeResponse
                    }
                    // 处理完成，提示成功
                    return { status: true }
                } else if (item.type === -1) {
                    // 退费，先检查退费成功没有
                    // 先查询结算信息是否未已退费状态
                    const ctx = {
                        rowData: {
                            psnNo: item.psnNo, // 人员编号
                            mdtrtId: item.mdtrtId, // 就诊ID
                            setlId: item.paidSetlId, // 结算ID
                        }
                    }
                    const settleInfoParams = checkingPresenter.createSettleInfoParams(ctx)
                    const settleInfoResponse = await that.nationalSocialSecurity.settleInfo(settleInfoParams, item.insuplcAdmdvs)
                    const refdSetlFlag = settleInfoResponse.data?.output?.setlinfo?.refd_setl_flag // 退费标识；0 未退；1 已退；字符串
                    if (refdSetlFlag === '0') {
                        // 未退，视为冲正成功
                        const postResponse = await checkingPresenter.requestBackoutPost(item)
                        if (postResponse.status === false) {
                            return postResponse
                        }
                    } else {
                        // 未知状态，尝试冲正
                        const backoutResponse = await checkingPresenter.requestBackout({ rowData: item })
                        if (backoutResponse.status === false) {
                            return backoutResponse
                        }
                    }
                    // 处理完成，提示成功
                    return { status: true }
                }
            })()
        `;
        const response = await utils.remoteRunJs(props.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        return response;
    };
    
    /**
     * 请求异常结算数据 - 标记
     * <AUTHOR>
     * @date 2024-11-28
     * @param {Object} item
     * @returns {Promise<AbcResponse>}
     */
    const requestErrorSettleMark = async (item: any) => {
        const js = `
            const item = ${JSON.stringify(item)}
            const that = {
                $national: window.$national,
                nationalSocialSecurity: window.$national.protocol.NationalSocialSecurity.instance,
            }
            const checkingPresenter = new window.$national.view.CheckingNewPresenter(that)
            response = await checkingPresenter.requestBackoutPost(item)
        `;
        const response = await utils.remoteRunJs(props.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    return {
        dialogModelPreSettleEdit,
        loadingModelQuery,
        pageModel,
        toolsParams,
        queryResponse,
        showDataList,
        createParams,
        requestErrorSettleData,
        requestErrorSettleHandle,
        requestErrorSettleMark,
    };
};