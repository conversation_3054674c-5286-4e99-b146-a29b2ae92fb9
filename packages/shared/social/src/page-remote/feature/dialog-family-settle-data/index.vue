<template>
    <el-dialog
        v-model="isShowDialogFamilySettleData"
        title="家庭医生签约结算数据"
        append-to-body
        :close-on-click-modal="false"
        custom-class="social-module__feature__dialog-family-settle-data"
    >
        <div class="tools-warpper">
            <el-date-picker
                v-model="toolsParams.dataRange"
                type="daterange"
                value-format="YYYY-MM-DD"
                :shortcuts="shortcuts"
                :clearable="false"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="截止日期"
            />
            <el-button
                type="primary"
                :loading="loadingModelQuery.loading.value"
                @click="onClickQuery"
            >
                查询
            </el-button>
            <div class="track"></div>
        </div>
        <div class="cont-wrapper">
            <el-table
                :data="showDataList"
                :height="440"
                style="width: 100%;"
            >
                <el-table-column
                    prop="psnName"
                    label="人员姓名"
                    min-width="120"
                    fixed
                />
                <el-table-column
                    prop="medfeeSumamt"
                    label="医疗费用总额"
                    min-width="140"
                />
                <el-table-column
                    prop="fundPaySumamt"
                    label="基金支付总额"
                    width="140"
                />
                <el-table-column
                    prop="acctPay"
                    label="个人账户总额"
                    width="140"
                />
                <el-table-column
                    prop="cashPayamt"
                    label="现金支付总额"
                    width="140"
                />
                <el-table-column
                    prop="psnNo"
                    label="人员编号"
                    width="260"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="mdtrtId"
                    label="就诊ID"
                    width="260"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="setlId"
                    label="结算ID"
                    width="260"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="setlTime"
                    label="结算时间"
                    width="170"
                    fixed="right"
                />
                <el-table-column
                    prop="refdSetlFlag"
                    label="退费结算标志"
                    width="120"
                    fixed="right"
                />
            </el-table>
        </div>
        <div class="footer-box">
            <span>
                医疗费用总额 <span class="copy-value" @click="onClickCopyValue(summaryData.medfeeSumamt)">{{ summaryData.medfeeSumamt }}</span>，
                基金支付总额 <span class="copy-value" @click="onClickCopyValue(summaryData.fundPaySumamt)">{{ summaryData.fundPaySumamt }}</span>，
                个人账户总额 <span class="copy-value" @click="onClickCopyValue(summaryData.acctPay)">{{ summaryData.acctPay }}</span>，
                现金支付总额 <span class="copy-value" @click="onClickCopyValue(summaryData.cashPayamt)">{{ summaryData.cashPayamt }}</span>
                <span class="tips">点击数值复制！</span>
            </span>
            <el-pagination
                v-model:current-page="pageModel.params.page"
                :page-size="pageModel.params.pageSize"
                :total="pageModel.params.total"
                :pager-count="3"
                background
                layout="total, prev, pager, next"
            ></el-pagination>
        </div>
    </el-dialog>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { ElMessage } from 'element-plus';
import { createFamilySettleDataController } from './controller';
import * as utils from '../../../common/utils';

const props = defineProps({
    deviceCode: {
        type: String,
        default: '',
    },
    socialInfo: {
        type: Object,
        required: true,
    },
    modelValue: {
        type: Number,
        default: 0,
    },
});

const $emit = defineEmits([
    'update:modelValue', // 更新显示状态
]);

const isShowDialogFamilySettleData = computed({
    get() {
        return !!props.modelValue;
    },
    set(val:boolean) {
        $emit('update:modelValue', +val);
    },
});

const {
    pageModel,
    loadingModelQuery,
    toolsParams,
    queryResponse,
    shortcuts,
    showDataList,
    summaryData,
    createParams,
    requestFamilySettleData,
} = createFamilySettleDataController(props);

/**
 * 当点击查询时
 * <AUTHOR>
 * @date 2024-08-07
 */
const onClickQuery = async () => {
    if (loadingModelQuery.loading.value) {
        return;
    }
    loadingModelQuery.setLoading(true);
    const params = createParams();
    const response = await requestFamilySettleData(params);
    loadingModelQuery.setLoading(false);
    if (response.status === false) {
        return ElMessage.error('查询失败: ' + response.message);
    }
    queryResponse.originData = response.data;
};

/**
 * 当复制值时
 * <AUTHOR>
 * @date 2025-01-13
 * @param {String} value
 */
const onClickCopyValue = (value: string) => {
    utils.copy(value);
    ElMessage.success('复制成功');
};
</script>

<style lang="scss">
    .social-module__feature__dialog-family-settle-data {
        width: 1200px !important;

        .el-dialog__body {
            padding-top: 8px;
        }

        .tools-warpper {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin-bottom: 12px;

            .el-date-editor {
                max-width: 260px;
                margin-right: 8px;
            }

            .track {
                flex: 1;
            }
        }

        .footer-box {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 16px;

            .copy-value {
                padding: 0 4px;
                cursor: pointer;

                &:hover {
                    color: #409eff;
                }
            }

            .tips {
                color: #e6a23c;
                margin-left: 12px;
            }

            > span {
                color: #606266;
            }

            .el-pagination {
                margin-top: 0;
            }
        }
    }
</style>