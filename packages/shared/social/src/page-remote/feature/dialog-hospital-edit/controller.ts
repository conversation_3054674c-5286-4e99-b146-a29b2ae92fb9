/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import { reactive } from 'vue';
import * as utils from '../../../common/utils';

import createLoadingModel from '../../../model/loading';

export const createHospitalEditController = (props: any) => {
    const loadingModelStatus = createLoadingModel();
    const loadingModelOpen = createLoadingModel();
    const loadingModelClose = createLoadingModel();

    const formData = reactive({
        time: 0,
        isEnable: false,
    });

    const timeOptions = [
        {
            value: 1000 * 60 * 10,
            label: '10分钟',
        },
        {
            value: 1000 * 60 * 30,
            label: '30分钟',
        },
        {
            value: 1000 * 60 * 60,
            label: '1小时',
        },
    ];

    /**
     * 初始化表单数据
     * <AUTHOR>
     * @date 2025-02-06
     */
    const initFormData = () => {
        const item = timeOptions[0];
        if (item) {
            formData.time = item.value;
        }
    };

    /**
     * 请求机构修改状态
     * <AUTHOR>
     * @date 2024-08-07
     * @returns {Promise<AbcResponse>}
     */
    const requestHospitalEditStatus = async () => {
        const js = `
            response = {
                status: true,
                data: {
                    isEnable: window.$national.config.isDisabledEditHospitalCodeName === false, // 是否允许机构修改
                }
            }
        `;
        const response = await utils.remoteRunJs(props.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        return response;
    };
    
    /**
     * 请求机构修改开启
     * <AUTHOR>
     * @date 2024-08-07
     * @param {Number} time
     * @returns {Promise<AbcResponse>}
     */
    const requestHospitalEditOpen = async (time: number) => {
        const js = `
            const time = ${time}
            if (window.hospitalEditTimer) {
                clearTimeout(window.hospitalEditTimer)
                window.hospitalEditTimer = null
            }
            window.$national.config.isDisabledEditHospitalCodeName = false
            window.onChangeDisabledEditHospitalCodeName?.()
            if (time !== 0) {
                window.hospitalEditTimer = setTimeout(() => {
                    window.$national.config.isDisabledEditHospitalCodeName = true
                    window.onChangeDisabledEditHospitalCodeName?.()
                }, time)
            }
            response = {
                status: true,
                data: null,
            }
        `;
        const response = await utils.remoteRunJs(props.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    /**
     * 请求机构修改关闭
     * <AUTHOR>
     * @date 2024-08-07
     * @returns {Promise<AbcResponse>}
     */
    const requestHospitalEditClose = async () => {
        const js = `
            if (window.hospitalEditTimer) {
                clearTimeout(window.hospitalEditTimer)
                window.hospitalEditTimer = null
            }
            window.$national.config.isDisabledEditHospitalCodeName = true
            window.onChangeDisabledEditHospitalCodeName?.()
            response = {
                status: true,
                data: null,
            }
        `;
        const response = await utils.remoteRunJs(props.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    return {
        loadingModelStatus,
        loadingModelOpen,
        loadingModelClose,
        formData,
        timeOptions,
        initFormData,
        requestHospitalEditStatus,
        requestHospitalEditOpen,
        requestHospitalEditClose,
    };
};