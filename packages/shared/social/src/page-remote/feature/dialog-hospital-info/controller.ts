/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import { computed, reactive } from 'vue';
import * as utils from '../../../common/utils';

import createLoadingModel from '../../../model/loading';
import createPageModel from '../../../model/page';

export const createHospitalInfoController = (props: any) => {
    const loadingModelQuery = createLoadingModel();
    const pageModel = createPageModel();

    const formData = reactive({
        fixmedinsType: '', // 机构类型
        fixmedinsCode: '', // 机构编号
        fixmedinsName: '', // 机构名称
    });

    const queryResponse = reactive({
        originData: <any> null,
    });

    // 是否禁用查询按钮
    const isDisabledQueryBtn = computed(() => !(
        formData.fixmedinsType
        && (formData.fixmedinsCode || formData.fixmedinsName)
    ));

    // 机构类型选项
    const fixmedinsTypeOptions = computed(() => window.$national.options.fixmedinsTypeOptions);

    // 展示数据
    const showDataList = computed(() => (queryResponse.originData?.output?.medinsinfo || [])
                    .slice(0, 200)
                    .map((item: any) => {
                        const itemInfo = {
                            ...item,
                        };
                        itemInfo.fixmedinsTypeWording = window.$national.tools.getFixmedinsTypeWording(item.fixmedins_type); // 机构类型
                        itemInfo.hospLvWording = window.$national.tools.getHospitalLevelWording(item.hosp_lv); // 机构等级
                        return itemInfo;
                    }));

    /**
     * 初始化表单数据
     * <AUTHOR>
     * @date 2024-08-14
     */
    const initFormData = () => {
        Object.assign(formData, {
            fixmedinsType: fixmedinsTypeOptions.value[0].value, // 机构类型
            fixmedinsCode: '', // 机构编号
            fixmedinsName: '', // 机构名称
        });
    };

    /**
     * 创建查询参数
     * <AUTHOR>
     * @date 2024-08-06
     * @returns {Object}
     */
    const createParams = () => {
        const {
            fixmedinsType, // 机构类型
            fixmedinsCode, // 机构编号
            fixmedinsName, // 机构名称
        } = formData;
        const params = {
            medinsinfo: {
                fixmedins_type: fixmedinsType, // 机构类型
                fixmedins_code: fixmedinsCode, // 机构编号
                fixmedins_name: fixmedinsName, // 机构名称
            },
        };
        return params;
    };

    /**
     * 请求机构信息
     * <AUTHOR>
     * @date 2024-08-07
     * @param {Object} params
     * @returns {Promise<AbcResponse>}
     */
    const requestHospitalInfo = async (params: any) => {
        const js = `
            const params = ${JSON.stringify(params)}
            response = await window.$national.protocol.NationalSocialSecurity.instance.hospitalInfo(params)
        `;
        const response = await utils.remoteRunJs(props.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    return {
        loadingModelQuery,
        pageModel,
        formData,
        queryResponse,
        isDisabledQueryBtn,
        fixmedinsTypeOptions,
        showDataList,
        initFormData,
        createParams,
        requestHospitalInfo,
    };
};