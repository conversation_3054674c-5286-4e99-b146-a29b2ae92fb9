/*
 * <AUTHOR> 
 * @DateTime 2025-01-11 10:01:38 
 */
import dayjs from 'dayjs';
import { computed, reactive } from 'vue';
import * as utils from '../../../common/utils';

import createLoadingModel from '../../../model/loading';

export const createTraceCodeController = (props: any) => {
    const loadingModelUpload = createLoadingModel();
    const loadingModelCompare = createLoadingModel();
    const loadingMarking = createLoadingModel();
    const loadingFailedData = createLoadingModel();

    // 类型常量
    const typeConstants = Object.freeze({
        SALE_TRACE_CODE: 'querySaleTraceCode', // 销售商品追溯码查询
        STOCK_TRACE_CODE: 'stockUploadTraceCodeQuery', // 入库商品追溯码查询
    });

    // 类型选项
    const typeOptions = [
        { value: typeConstants.SALE_TRACE_CODE, label: '销售商品追溯码查询' },
        { value: typeConstants.STOCK_TRACE_CODE, label: '入库商品追溯码查询' },
    ];

    const toolsParams = reactive({
        type: '', // 类型
        dateRange: [
            dayjs().format('YYYY-MM-DD'),
            dayjs().format('YYYY-MM-DD'),
        ],
    });
    toolsParams.type = typeConstants.SALE_TRACE_CODE;

    const inventoryUploadResponse = reactive({
        originData: <any> null,
    });

    const traceCodeCompareResponse = reactive({
        originData: <any> null,
    });
    // 上报失败的数据
    const failedDataResponse = reactive({
        originData: <any> null,
    });

    // // 是否 - 销售商品追溯码查询
    const isSaleTraceCode = computed(() => toolsParams.type === typeConstants.SALE_TRACE_CODE);
    // 进销存上报结果
    const inventoryUploadResult = computed(() => inventoryUploadResponse?.originData || {});
    // 进销存比对数据
    const traceCodeCompareResult = computed(() => traceCodeCompareResponse?.originData || {});
    // 上报失败的数据
    const failedDataResult = computed(() => failedDataResponse?.originData || []);
    /**
     * 创建查询参数
     * <AUTHOR>
     * @date 2024-08-06
     * @returns {Object}
     */
    const createParams = () => {
        const {
            dateRange: [
                beginDate,
                endDate,
            ],
            type,
        } = toolsParams;
        const params = {
            begndate: `${window.$national.tools.getDateFormat(beginDate)}`, // 开始时间
            enddate: `${window.$national.tools.getDateFormat(endDate)}`, // 结束时间
            type,
            isSaleTraceCode: isSaleTraceCode.value,
        };
        return params;
    };

    /**
     * @desc 请求进销存上报
     * <AUTHOR>
     * @date 2024-11-02
     */
    const requestUploadInventoryData = async () => {
        const js = `
            response = await this.$abcSocialSecurity.inventoryReportBatch()
        `;
        window.remoteSDK.rpcInvoker.invokeTimeoutOnce = 1000 * 60 * 60; // 1小时超时
        const response = await utils.remoteRunJs(props.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    /**
     * @desc 标记异常数据
     * <AUTHOR>
     * @date 2024-11-13
     */
    const requestMarkingInventoryData = async () => {
        const { begndate, enddate } = createParams();
        const params = {
            repeatSaleData: traceCodeCompareResult.value?.repeatSaleData || [],
            begndate,
            enddate,
        };
        const js = `
            const params = ${JSON.stringify(params)}
            response = await this.$abcSocialSecurity.requestHandleRepeatTraceSaleData(params)
        `;
        window.remoteSDK.rpcInvoker.invokeTimeoutOnce = 1000 * 60 * 60; // 1小时超时
        const response = await utils.remoteRunJs(props.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    /**
     * @desc 请求追溯码数据对比
     * <AUTHOR>
     * @date 2024-11-02
     */
    const requestCompareTraceCode = async (params: any) => {
        const js = `
            const params = ${JSON.stringify(params)}
            response = await this.$abcSocialSecurity.drugTraceCodeComparison(params)
        `;
        window.remoteSDK.rpcInvoker.invokeTimeoutOnce = 1000 * 60 * 60; // 1小时超时
        const response = await utils.remoteRunJs(props.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    /**
     * 查询上报失败的数据
     * <AUTHOR>
     * @date 2025-07-14
     */
    const requestQueryFailedData = async (params: any) => {
        const fetchParams = {
            beginDate: params.begndate,
            endDate: params.enddate,
            action: '',
            limit: 50,
            offset: 0,
            uploadStatus: '2',
            fixmedinsBchno: '',
            validCollected: '0',
        };
        const goodsLogsVoList = [];
        let isContinue = true;
        do {
            const js = `
                const params = ${JSON.stringify({
                ...fetchParams,
                offset: goodsLogsVoList.length,
            })}
                response = await this.$national.api.fetchInventoryDataList(params)
            `;
            const response = await utils.remoteRunJs(props.deviceCode, js);
            if (response.status === false) {
                return response;
            }
            goodsLogsVoList.push(...response.data.goodsLogsVoList);
            isContinue = goodsLogsVoList.length < response.data.total;
        } while (isContinue);
        return {
            status: true,
            data: goodsLogsVoList,
        };
    };

    const requestUploadFailedData = async (params: any) => {
        const js = `
            const that = {
                $national: window.$national,
                nationalSocialSecurity: window.$national.protocol.NationalSocialSecurity.instance,
            }
            const inventoryPresenter = new this.$national.view.InventoryNewMaintainPresenter(that)
            // 上传失败数据
            response = await inventoryPresenter.onClickUploadSingleItem(JSON.parse(${JSON.stringify(params)}), true)
        `;
        const response = await utils.remoteRunJs(props.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    return {
        loadingModelUpload,
        loadingModelCompare,
        loadingMarking,
        loadingFailedData,
        inventoryUploadResponse,
        inventoryUploadResult,
        traceCodeCompareResponse,
        traceCodeCompareResult,
        failedDataResponse,
        failedDataResult,
        toolsParams,
        typeOptions,
        isSaleTraceCode,
        createParams,
        requestUploadInventoryData,
        requestMarkingInventoryData,
        requestCompareTraceCode,
        requestQueryFailedData,
        requestUploadFailedData,
    };
};