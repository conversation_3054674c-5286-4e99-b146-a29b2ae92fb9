<template>
    <el-dialog
        v-model="isShowDialogInventory"
        title="进销存上报统计"
        :close-on-click-modal="false"
        custom-class="social-module__feature__dialog-inventory-stat"
    >
        <el-card class="box-card" style="margin: 20px 0;">
            <template #header>
                <div class="box-card-inventory-header">
                    <span>进销存数据上报</span>
                    <el-button
                        type="primary"
                        :loading="loadingModelUpload.loading.value"
                        @click="onClickUploadInventory"
                    >
                        上报
                    </el-button>
                </div>
            </template>
            <el-descriptions :column="2" border>
                <el-descriptions-item label="上报数据">{{ inventoryUploadResult?.uploadCount || '' }}</el-descriptions-item>
                <el-descriptions-item label="成功数量">{{ inventoryUploadResult?.uploadSuccessNum || '' }}</el-descriptions-item>
                <el-descriptions-item label="失败数量">{{ inventoryUploadResult?.uploadFailNum || '' }}</el-descriptions-item>
                <el-descriptions-item label="开始日期">{{ inventoryUploadResult?.pullDataStartTime || '' }}</el-descriptions-item>
            </el-descriptions>
        </el-card>
        <el-card class="box-card">
            <template #header>
                <div class="box-card-inventory-header">
                    <span>追溯码信息</span>
                    <div class="tool-wrapper">
                        <el-date-picker
                            v-model="toolsParams.dateRange"
                            type="daterange"
                            value-format="YYYY-MM-DD"
                            :clearable="false"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="截止日期"
                            style="max-width: 260px;"
                        ></el-date-picker>
                        <el-select
                            v-model="toolsParams.type"
                            style="max-width: 180px; margin: 0 8px;"
                        >
                            <el-option
                                v-for="item in typeOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            ></el-option>
                        </el-select>
                        <el-button
                            type="primary"
                            :loading="loadingMarking.loading.value"
                            @click="onClickMarking"
                        >
                            标记异常数据
                        </el-button>
                        <el-button
                            type="primary"
                            :loading="loadingModelCompare.loading.value"
                            @click="onClickCompareTraceCode"
                        >
                            数据对比
                        </el-button>
                        <el-button
                            type="primary"
                            :loading="loadingFailedData.loading.value"
                            @click="onClickQueryFailedData"
                        >
                            上报失败数据
                        </el-button>
                    </div>
                </div>
            </template>
            
            <el-descriptions :column="3" border>
                <el-descriptions-item label="追溯码采集总数">{{ traceCodeCompareResult?.abcTotalCount || '' }}</el-descriptions-item>
                <el-descriptions-item v-if="isSaleTraceCode" label="追溯码采集数【医保】">{{ traceCodeCompareResult?.abcSocialCount || '' }}</el-descriptions-item>
                <el-descriptions-item v-if="isSaleTraceCode" label="追溯码采集数【自费】">{{ traceCodeCompareResult?.abcSelfCount || '' }}</el-descriptions-item>
                <el-descriptions-item label="追溯码上报数量">{{ traceCodeCompareResult?.abcUploadTotalCount || '' }}</el-descriptions-item>
                <el-descriptions-item v-if="isSaleTraceCode" label="追溯码上报数【医保】">{{ traceCodeCompareResult?.abcUploadSocialCount || '' }}</el-descriptions-item>
                <el-descriptions-item v-if="isSaleTraceCode" label="追溯码上报数【自费】">{{ traceCodeCompareResult?.abcUploadSelfCount || '' }}</el-descriptions-item>
                <el-descriptions-item label="追溯码无效数量">{{ traceCodeCompareResult?.invalidTotalCount || '' }}</el-descriptions-item>
                <el-descriptions-item v-if="isSaleTraceCode" label="追溯码无效数量【医保】">{{ traceCodeCompareResult?.invalidSocialCount || '' }}</el-descriptions-item>
                <el-descriptions-item v-if="isSaleTraceCode" label="追溯码无效数量【自费】">{{ traceCodeCompareResult?.invalidSelfCount || '' }}</el-descriptions-item>
                <el-descriptions-item label="查询追溯码总数">{{ traceCodeCompareResult?.queryTotalCount || '' }}</el-descriptions-item>
                <el-descriptions-item v-if="isSaleTraceCode" label="查询追溯码总数【医保】">{{ traceCodeCompareResult?.querySocialCount || '' }}</el-descriptions-item>
                <el-descriptions-item v-if="isSaleTraceCode" label="查询追溯码总数【自费】">{{ traceCodeCompareResult?.querySelfCount || '' }}</el-descriptions-item>
                <el-descriptions-item label="追溯码不一致数据">
                    <div style="max-height: 200px; overflow: hidden; overflow-y: auto;">
                        {{ JSON.stringify(traceCodeCompareResult?.repeatSaleData) || '' }}
                    </div>
                </el-descriptions-item>
            </el-descriptions>
            <el-table
                :data="failedDataResult"
                border
                :width="1000"
                max-height="400"
            >
                <el-table-column prop="invChgTime" label="动作发生时间"></el-table-column>
                <el-table-column prop="psnName" label="患者姓名"></el-table-column>
                <el-table-column prop="fixmedinsHilistName" label="商品名称"></el-table-column>
                <el-table-column prop="medListCodg" label="医保编码"></el-table-column>
                <el-table-column prop="action" label="动作"></el-table-column>
                <el-table-column prop="uploadStatus" label="上报状态">
                    <!-- dengjie 2024-06-13 -->
                    <template #default="scope">
                        <el-tag
                            :type="scope.row.uploadStatus == '1' ? 
                                'success' : (scope.row.uploadStatus == '2' ? 'danger' 
                                    : (scope.row.uploadStatus == '3' ? 'warning' : 'info'))"
                        >
                            {{
                                scope.row.uploadStatus == '1'
                                    ? '上报成功'
                                    : scope.row.uploadStatus == '2'
                                        ? '上报失败'
                                        : scope.row.uploadStatus == '3'
                                            ? '待上报'
                                            : scope.row.uploadStatus == '4'
                                                ? '无需上报'
                                                : scope.row.uploadStatus
                            }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作">
                    <template #default="scope">
                        <el-button
                            size="small"
                            type="primary"
                            plain
                            @click="onClickUploadFailedData(scope.row)"
                        >
                            上传
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
    </el-dialog>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { ElMessage } from 'element-plus';
import { createTraceCodeController } from './controller';
import * as utils from '../../../common/utils';

const props = defineProps({
    deviceCode: {
        type: String,
        default: '',
    },
    socialInfo: {
        type: Object,
        required: true,
    },
    modelValue: {
        type: Number,
        default: 0,
    },
});
const $emit = defineEmits([
    'update:modelValue', // 更新显示状态
]);

const isShowDialogInventory = computed({
    get() {
        return !!props.modelValue;
    },
    set(val:boolean) {
        $emit('update:modelValue', +val);
    },
});
const {
    loadingModelUpload,
    loadingModelCompare,
    loadingMarking,
    loadingFailedData,
    inventoryUploadResponse,
    inventoryUploadResult,
    traceCodeCompareResponse,
    traceCodeCompareResult,
    failedDataResponse,
    failedDataResult,
    toolsParams,
    typeOptions,
    createParams,
    isSaleTraceCode,
    requestUploadInventoryData,
    requestMarkingInventoryData,
    requestCompareTraceCode,
    requestQueryFailedData,
    requestUploadFailedData,
} = createTraceCodeController(props);
/**
 * @desc 点击进销存上报按钮触发
 * <AUTHOR>
 * @date 2024-11-02
 */
const onClickUploadInventory = async () => {
    if (loadingModelUpload.loading.value) {
        return;
    }
    inventoryUploadResponse.originData = null;
    loadingModelUpload.setLoading(true);
    const response = await requestUploadInventoryData();
    loadingModelUpload.setLoading(false);
    if (response.status === false) {
        return ElMessage.error(`上报失败: ${response.message}`);
    }
    inventoryUploadResponse.originData = response.data;
};
/**
 * @desc 标记异常数据
 * <AUTHOR>
 * @date 2024-11-13
 */
const onClickMarking = async () => {
    if (loadingMarking.loading.value) {
        return;
    }
    inventoryUploadResponse.originData = null;
    loadingMarking.setLoading(true);
    const response = await requestMarkingInventoryData();
    loadingMarking.setLoading(false);
    if (response.status === false) {
        return ElMessage.error(`处理失败: ${response.message}`);
    }
    return ElMessage.success('处理成功');
};
/**
 * @desc 比较追溯码上传数据
 * <AUTHOR>
 * @date 2024-11-02
 */
const onClickCompareTraceCode = async () => {
    if (loadingModelCompare.loading.value) {
        return;
    }
    traceCodeCompareResponse.originData = null;
    loadingModelCompare.setLoading(true);
    const params = createParams();
    const response = await requestCompareTraceCode(params);
    if (!utils.isEqual(params, createParams())) {
        return;
    }
    loadingModelCompare.setLoading(false);
    if (response.status === false) {
        return ElMessage.error(`查询失败: ${response.message}`);
    }
    traceCodeCompareResponse.originData = response.data;
};
/**
 * @desc 查询上报失败的数据
 * <AUTHOR>
 * @date 2025-07-14
 */
const onClickQueryFailedData = async () => {
    if (loadingFailedData.loading.value) {
        return;
    }
    loadingFailedData.setLoading(true);
    const params = createParams();
    const response = await requestQueryFailedData(params);
    loadingFailedData.setLoading(false);
    if (response.status === false) {
        return ElMessage.error(`查询失败: ${response.message}`);
    }
    failedDataResponse.originData = response.data;
};

const onClickUploadFailedData = async (row: any) => {
    const response = await requestUploadFailedData({ ...row });
    if (response.status === false) {
        return ElMessage.error(`上传失败: ${response.message}`);
    }
    onClickQueryFailedData();
    return ElMessage.success('上传成功');
};
</script>

<style lang="scss">
.social-module__feature__dialog-inventory-stat {
    width: 1200px !important;

    .el-dialog__body {
        padding-top: 8px;
    }

    .box-card-inventory-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .tool-wrapper {
            display: flex;
            justify-content: flex-start;
            align-items: center;
        }
    }

    .el-descriptions__label {
        width: 200px !important;
    }
}
</style>