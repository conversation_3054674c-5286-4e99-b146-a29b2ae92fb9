<template>
    <el-dialog
        v-model="isShowDialogInventory"
        title="进销存上报查询"
        :close-on-click-modal="false"
        custom-class="social-module__feature__dialog-inventory"
    >
        <div class="tools-wrapper">
            <div class="row-1">
                <el-select
                    v-model="toolsParams.type"
                    style="width: 220px;"
                    @change="onChangeType"
                >
                    <el-option
                        v-for="item in typeOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    ></el-option>
                </el-select>
                <el-date-picker
                    v-model="toolsParams.dateRange"
                    type="daterange"
                    value-format="YYYY-MM-DD"
                    :clearable="false"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="截止日期"
                    style="max-width: 240px; margin-left: 8px;"
                ></el-date-picker>
            </div>
            <div class="row-2">
                <el-input
                    v-model="toolsParams.fixmedinsBchno"
                    clearable
                    placeholder="批次流水号"
                    style="width: 220px;"
                ></el-input>
                <el-input
                    v-model="toolsParams.medListCodg"
                    clearable
                    placeholder="医保目录编码"
                    style="width: 240px; margin-left: 8px;"
                ></el-input>
                <el-input
                    v-model="toolsParams.drugTracCodg"
                    clearable
                    placeholder="追溯码"
                    style="width: 240px; margin-left: 8px;"
                ></el-input>
                <el-button
                    type="primary"
                    :disabled="isDisabledQueryBtn"
                    style="margin-left: 8px;"
                    @click="onClickQuery"
                >
                    查询
                </el-button>
            </div>
        </div>
        <el-table
            v-loading="loadingModelQuery.loading.value"
            :data="showDataList"
            :height="440"
            border
        >
            <el-table-column
                prop="medinsListName"
                label="药品名称"
                min-width="200"
                fixed
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="medListCodg"
                label="医保编码"
                min-width="270"
                fixed
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="fixmedinsBchno"
                label="批次号"
                min-width="160"
            ></el-table-column>
            <el-table-column
                v-if="isSaleTraceCode || isStockTraceCode"
                prop="drugTracCodg"
                label="追溯码"
                min-width="270"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="trdnFlagWording"
                label="是否拆零"
                min-width="120"
            ></el-table-column>
            <el-table-column
                prop="valiFlagWording"
                label="是否有效"
                min-width="120"
            ></el-table-column>
            <el-table-column
                prop="opterName"
                label="经办人"
                min-width="120"
            ></el-table-column>
            <el-table-column
                v-if="isSaleTraceCode"
                prop="mdtrtSetlTypeWording"
                label="结算类型"
                min-width="120"
            ></el-table-column>
            <el-table-column
                v-if="isSaleTraceCode"
                prop="psnName"
                label="医保结算人"
                min-width="120"
            ></el-table-column>
            <el-table-column
                prop="updtTimeWording"
                label="上报时间"
                min-width="180"
                fixed="right"
            ></el-table-column>
        </el-table>
        <div class="footer-box">
            <span>医保中心共返回 {{ pageModel.params.total }} 条数据</span>
            <el-pagination
                v-model:current-page="pageModel.params.page"
                :page-size="pageModel.params.pageSize"
                :total="pageModel.params.total"
                background
                layout="total, prev, pager, next"
            ></el-pagination>
        </div>
    </el-dialog>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { ElMessage } from 'element-plus';
import { createTraceCodeController } from './controller';
import * as utils from '../../../common/utils';

const props = defineProps({
    deviceCode: {
        type: String,
        default: '',
    },
    socialInfo: {
        type: Object,
        required: true,
    },
    modelValue: {
        type: Number,
        default: 0,
    },
});

const $emit = defineEmits([
    'update:modelValue', // 更新显示状态
]);

const isShowDialogInventory = computed({
    get() {
        return !!props.modelValue;
    },
    set(val:boolean) {
        $emit('update:modelValue', +val);
    },
});

const {
    loadingModelQuery,
    pageModel,
    toolsParams,
    queryResponse,
    isDisabledQueryBtn,
    isSaleTraceCode,
    isStockTraceCode,
    isStockUpdate,
    typeOptions,
    showDataList,
    createParams,
    requestInventoryData,
} = createTraceCodeController(props);

/**
 * 当改变类型时
 * <AUTHOR>
 * @date 2024-10-24
 */
const onChangeType = () => {
    queryResponse.originData = null;
};

/**
 * 当点击查询时
 * <AUTHOR>
 * @date 2024-08-07
 */
const onClickQuery = async () => {
    if (loadingModelQuery.loading.value) {
        return;
    }
    queryResponse.originData = null;
    loadingModelQuery.setLoading(true);
    const params = createParams();
    const response = await requestInventoryData(params);
    if (!utils.isEqual(params, createParams())) {
        return;
    }
    loadingModelQuery.setLoading(false);
    if (response.status === false) {
        return ElMessage.error(`查询失败: ${response.message}`);
    }
    queryResponse.originData = response.data;
};
</script>

<style lang="scss">
    .social-module__feature__dialog-inventory {
        width: 1200px !important;

        .el-dialog__body {
            padding-top: 8px;
        }

        .tools-wrapper > div {
            display: flex;
            justify-content: flex-start;
            align-items: center;

            .btn {
                width: 80px;
            }

            &.row-1 {
                margin-bottom: 8px;
            }
        }

        .el-table {
            margin-top: 8px;

            .el-table__empty-text {
                margin-top: 150px;
            }
        }

        .footer-box {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 16px;

            > span {
                color: #606266;
            }

            .el-pagination {
                margin-top: 0;
            }
        }
    }
</style>