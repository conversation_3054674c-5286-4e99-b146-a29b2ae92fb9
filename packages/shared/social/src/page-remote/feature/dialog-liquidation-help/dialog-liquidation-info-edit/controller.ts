/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import { computed, reactive } from 'vue';
import * as utils from '../../../../common/utils';

export const createCheckingInfoEditController = (props: any) => {
    const formData = reactive({
        clrType: '', // 清算类别
        clrOptins: '', // 结算经办机构
        medfeeSumamt: '', // 医疗费用总额
        medSumfee: '', // 医保认可费用
        fundAppySum: '', // 统筹基金支付
        acctPay: '', // 个人账户支付
        cashPayamt: '', // 个人现金支付
        psntime: '', // 清算人次
        clrAppyEvtId: '', // 清算申请ID
    });

    // 是否禁用确认按钮
    const isDisabledConfirmBtn = computed(() => !(
        formData.clrType
        && formData.clrOptins
        && formData.medfeeSumamt
        && formData.psntime
    ));

    // 清算类别选项
    const clrTypeOptions = computed(() => window.$national.options.clrTypeOptions);
    
    // 经办机构选项
    const clrOptinsOptions = computed(() => window.$national.options.clrOptinsOptions);

    /**
     * 初始化表单数据
     * <AUTHOR>
     * @date 2024-11-28
     */
    const initFormData = () => {
        const {
            clrType = '', // 清算类别
            clrOptins = '', // 结算经办机构
            setlCenter = '', // 结算经办机构
            medfeeSumamt = '', // 医疗费用总额
            medSumfee = '', // 医保认可费用
            fundAppySum = '', // 统筹基金支付
            acctPay = '', // 个人账户支付
            cashPayamt = '', // 个人现金支付
            psntime = '', // 清算人次
            clrAppyEvtId = '', // 清算申请ID
        } = props.item || {};
        formData.clrType = clrType; // 清算类别
        formData.clrOptins = clrOptins || setlCenter; // 结算经办机构
        formData.medfeeSumamt = medfeeSumamt; // 医疗费用总额
        formData.medSumfee = medSumfee; // 医保认可费用
        formData.fundAppySum = fundAppySum; // 统筹基金支付
        formData.acctPay = acctPay; // 个人账户支付
        formData.cashPayamt = cashPayamt; // 个人现金支付
        formData.psntime = psntime; // 清算人次
        formData.clrAppyEvtId = clrAppyEvtId; // 清算申请ID
    };

    /**
     * 计算数据
     * <AUTHOR>
     * @date 2025-03-04
     */
    const calculateData = () => {
        if (
            !formData.medSumfee
            && formData.fundAppySum
            && formData.acctPay
        ) {
            // 自动计算医保认可费用
            formData.medSumfee = utils.add(formData.fundAppySum, formData.acctPay);
        }
        if (
            !formData.cashPayamt
            && formData.medfeeSumamt
            && formData.medSumfee
        ) {
            // 自动计算个人现金支付
            formData.cashPayamt = utils.red(formData.medfeeSumamt, formData.medSumfee);
        }
    };

    return {
        formData,
        isDisabledConfirmBtn,
        clrTypeOptions,
        clrOptinsOptions,
        initFormData,
        calculateData,
    };
};