<template>
    <el-dialog
        v-model="isShowDialogLiquidationInfoEdit"
        title="清算信息编辑"
        :close-on-click-modal="false"
        custom-class="social-module__feature__dialog-liquidation-info-edit"
    >
        <el-form
            :inline="true"
            :model="formData"
            label-width="96px"
        >
            <el-form-item label="清算类别">
                <el-select
                    v-model="formData.clrType"
                    style="width: 260px;"
                    placeholder="必填"
                >
                    <el-option
                        v-for="item in clrTypeOptions"
                        :key="item.value"
                        :label="`${item.value} - ${item.name}`"
                        :value="item.value"
                    ></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="经办机构">
                <el-select
                    v-model="formData.clrOptins"
                    style="width: 260px;"
                >
                    <el-option
                        v-for="item in clrOptinsOptions"
                        :key="item.value"
                        :label="`${item.value} - ${item.name}`"
                        :value="item.value"
                    ></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="医疗费用总额">
                <el-input-number
                    v-model="formData.medfeeSumamt"
                    :precision="2"
                    :step="0.01"
                    style="width: 260px;"
                ></el-input-number>
            </el-form-item>
            <el-form-item label="医保认可费用">
                <el-input-number
                    v-model="formData.medSumfee"
                    :precision="2"
                    :step="0.01"
                    style="width: 260px;"
                    @change="onChangeInputData"
                ></el-input-number>
            </el-form-item>
            <el-form-item label="统筹基金支付">
                <el-input-number
                    v-model="formData.fundAppySum"
                    :precision="2"
                    :step="0.01"
                    style="width: 260px;"
                    @change="onChangeInputData"
                ></el-input-number>
            </el-form-item>
            <el-form-item label="个人账户支付">
                <el-input-number
                    v-model="formData.acctPay"
                    :precision="2"
                    :step="0.01"
                    style="width: 260px;"
                    @change="onChangeInputData"
                ></el-input-number>
            </el-form-item>
            <el-form-item label="个人现金支付">
                <el-input-number
                    v-model="formData.cashPayamt"
                    :precision="2"
                    :step="0.01"
                    style="width: 260px;"
                    @change="onChangeInputData"
                ></el-input-number>
            </el-form-item>
            <el-form-item label="清算人次">
                <el-input-number
                    v-model="formData.psntime"
                    :precision="0"
                    :step="1"
                    :min="0"
                    style="width: 260px;"
                ></el-input-number>
            </el-form-item>
            <el-form-item label="清算申请ID">
                <el-tooltip
                    effect="dark"
                    content="赋值后不会调医保接口，而是将该笔数据标记为清算成功"
                    placement="top-start"
                >
                    <el-input
                        v-model="formData.clrAppyEvtId"
                        style="width: 260px;"
                        placeholder="不懂就留空"
                    ></el-input>
                </el-tooltip>
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button
                    type="primary"
                    :disabled="isDisabledConfirmBtn"
                    @click="onClickConfirm"
                >
                    确定
                </el-button>
                <el-button
                    @click="isShowDialogLiquidationInfoEdit = false"
                >
                    取消
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { computed, onMounted } from 'vue';
import { createCheckingInfoEditController } from './controller';

const props = defineProps({
    modelValue: {
        type: Number,
        default: 0,
    },
    item: {
        type: Object,
        required: true,
    },
});

const $emit = defineEmits([
    'update:modelValue', // 更新显示状态
    'confirm', // 确认
]);

const isShowDialogLiquidationInfoEdit = computed({
    get() {
        return !!props.modelValue;
    },
    set(val:boolean) {
        $emit('update:modelValue', +val);
    },
});

const {
    formData,
    isDisabledConfirmBtn,
    clrTypeOptions,
    clrOptinsOptions,
    initFormData,
    calculateData,
} = createCheckingInfoEditController(props);

onMounted(() => {
    initFormData();
});

/**
 * 当改变input数据时
 * <AUTHOR>
 * @date 2025-03-04
 */
const onChangeInputData = () => {
    calculateData();
};

/**
 * 当点击查询时
 * <AUTHOR>
 * @date 2024-08-07
 */
const onClickConfirm = async () => {
    $emit('confirm', formData);
    isShowDialogLiquidationInfoEdit.value = false;
};
</script>

<style lang="scss">
    .social-module__feature__dialog-liquidation-info-edit {
        width: 480px !important;

        .el-dialog__body {
            padding-top: 8px;
        }
    }
</style>