<template>
    <el-dialog
        v-model="isShowDialogLiquidationHelp"
        title="协助清算"
        :close-on-click-modal="false"
        custom-class="social-module__feature__dialog-liquidation-help"
    >
        <div class="tools-wrapper">
            <el-date-picker
                v-model="toolsParams.month"
                type="month"
                :clearable="false"
                style="width: 160px; margin-right: 8px;"
            ></el-date-picker>
            <el-button
                type="primary"
                :loading="loadingModelQuery.loading.value"
                @click="onClickQuery"
            >
                查询
            </el-button>
            <div class="track"></div>
            <el-button
                type="primary"
                plain
                @click="onClickInsert"
            >
                新增清算
            </el-button>
        </div>
        <el-table
            v-loading="loadingModelQuery.loading.value"
            :data="showDataList"
            :height="440"
            border
            style="width: 100%;"
        >
            <el-table-column
                prop="clrTypeWording"
                label="清算类别"
                min-width="140"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="clrOptinsWording"
                label="经办机构"
                width="120"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="medfeeSumamtWording"
                label="医疗费用总额"
                width="110"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="fundAppySumWording"
                label="基金支付总额"
                width="110"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="acctPayWording"
                label="个账支付总额"
                width="110"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="cashPayamtWording"
                label="个人现金支出"
                width="110"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="psntime"
                label="结算笔数"
                width="90"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="medSumfeeWording"
                label="医保拨付金额"
                width="110"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="checkingStatusObject"
                label="对账状态"
                width="82"
                fixed="right"
            >
                <template #default="scope">
                    <span :style="scope.row.checkingStatusObject.style">{{ scope.row.checkingStatusObject.label }}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="liquidationStatusObject"
                label="清算状态"
                width="82"
                fixed="right"
            >
                <template #default="scope">
                    <span :style="scope.row.liquidationStatusObject.style">{{ scope.row.liquidationStatusObject.label }}</span>
                </template>
            </el-table-column>
            <el-table-column
                label="操作"
                width="210"
                fixed="right"
            >
                <template #default="scope">
                    <el-button
                        v-if="scope.row.status === 1"
                        size="small"
                        type="primary"
                        plain
                        :loading="scope.row.loadingLiquidation"
                        @click="onClicktLiquidation(scope.row)"
                    >
                        清算
                    </el-button>
                    <el-button
                        v-if="scope.row.status === 0"
                        size="small"
                        type="primary"
                        plain
                        :loading="scope.row.loadingLiquidationRevoke"
                        @click="onClicktLiquidationRevoke(scope.row)"
                    >
                        撤销
                    </el-button>
                    <el-button
                        size="small"
                        type="primary"
                        plain
                        :loading="scope.row.loadingEdit"
                        @click="onClickEdit(scope.row)"
                    >
                        修改
                    </el-button>
                    <el-tooltip
                        effect="dark"
                        content="若清算状态为待清算，则会标记为：已清算<br />若清算状态为已清算，则会标记为：待清算"
                        raw-content
                        placement="top-start"
                    >
                        <el-button
                            size="small"
                            type="primary"
                            plain
                            :loading="scope.row.loadingMark"
                            :disabled="scope.row.isDisabledMarkBtn"
                            @click="onClickMark(scope.row)"
                        >
                            标记
                        </el-button>
                    </el-tooltip>
                </template>
            </el-table-column>
        </el-table>

        <dialog-liquidation-info-edit
            v-if="dialogModelLiquidationInfoEdit.visible.value"
            v-model="dialogModelLiquidationInfoEdit.visible.value"
            :device-code="props.deviceCode"
            :item="queryResponse.selectedItem"
            @confirm="onConfirmEdit"
        ></dialog-liquidation-info-edit>
    </el-dialog>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { ElMessage } from 'element-plus';
import { createLiquidationHelpController } from './controller';
import * as utils from '../../../common/utils';

import DialogLiquidationInfoEdit from './dialog-liquidation-info-edit/index.vue';

const props = defineProps({
    deviceCode: {
        type: String,
        default: '',
    },
    modelValue: {
        type: Number,
        default: 0,
    },
});

const $emit = defineEmits([
    'update:modelValue', // 更新显示状态
]);

const isShowDialogLiquidationHelp = computed({
    get() {
        return !!props.modelValue;
    },
    set(val:boolean) {
        $emit('update:modelValue', +val);
    },
});

const {
    loadingModelQuery,
    dialogModelLiquidationInfoEdit,
    toolsParams,
    queryResponse,
    showDataList,
    createParams,
    removeEditItem,
    requestLiquidationData,
    requestLiquidationItem,
    requestLiquidationRevokeItem,
    requestLiquidationDataMark,
} = createLiquidationHelpController(props);

/**
 * 当点击查询时
 * <AUTHOR>
 * @date 2024-08-07
 */
const onClickQuery = async () => {
    if (loadingModelQuery.loading.value) {
        return;
    }
    loadingModelQuery.setLoading(true);
    const params = createParams();
    const response = await requestLiquidationData(params);
    loadingModelQuery.setLoading(false);
    if (response.status === false) {
        return ElMessage.error('查询失败: ' + response.message);
    }
    queryResponse.originData = response.data;
};

/**
 * 当点击插入一项时
 * <AUTHOR>
 * @date 2025-01-11
 */
const onClickInsert = () => {
    queryResponse.selectedItem = null;
    dialogModelLiquidationInfoEdit.show();
};

/**
 * 当点击清算时
 * <AUTHOR>
 * @date 2024-11-27
 * @param {Object} item
 */
const onClicktLiquidation = async (item: any) => {
    if (item.unCheckingFee) {
        // 有未对账数据，不允许清算
        return ElMessage.error('存在未对账数据，不允许做清算');
    }
    item.loadingLiquidation = true;
    const liquidationResponse = await requestLiquidationItem(item);
    item.loadingLiquidation = false;
    if (liquidationResponse.status === false) {
        return ElMessage.error('清算失败: ' + liquidationResponse.message);
    }
    ElMessage.success('清算成功');
    removeEditItem(item);
    onClickQuery();
};

/**
 * 当点击清算撤销时
 * <AUTHOR>
 * @date 2024-11-27
 * @param {Object} item
 */
const onClicktLiquidationRevoke = async (item: any) => {
    item.loadingLiquidationRevoke = true;
    const liquidationRevokeResponse = await requestLiquidationRevokeItem(item);
    item.loadingLiquidationRevoke = false;
    if (liquidationRevokeResponse.status === false) {
        return ElMessage.error('撤销失败: ' + liquidationRevokeResponse.message);
    }
    ElMessage.success('撤销成功');
    removeEditItem(item);
    onClickQuery();
};

/**
 * 当点击编辑项时
 * <AUTHOR>
 * @date 2025-01-11
 * @param {Object} item
 */
const onClickEdit = (item: any) => {
    queryResponse.selectedItem = item;
    dialogModelLiquidationInfoEdit.show();
};

/**
 * 当点击标记对账成功
 * <AUTHOR>
 * @date 2024-11-27
 * @param {Object} item
 */
const onClickMark = async (item: any) => {
    if (item.isCheckingSuccess) {
        return ElMessage.error('标记失败: 该组数据已是对账成功，无需标记');
    }
    item.loadingMark = true;
    const markResponse = await requestLiquidationDataMark(item);
    item.loadingMark = false;
    if (markResponse.status === false) {
        return ElMessage.error('标记失败: ' + markResponse.message);
    }
    ElMessage.success('标记成功');
    onClickQuery();
};

/**
 * 当确认编辑时
 * <AUTHOR>
 * @date 2025-01-11
 * @param {Object} newItem
 */
const onConfirmEdit = (newItem: any) => {
    const params = createParams();
    const target = {
        id: queryResponse.selectedItem?.id || Date.now().toString(),
        isInsertData: !queryResponse.selectedItem?.id, // 是否新增数据 
        clrWay: '1', // 清算方式，默认按项目
        begndate: params.beginDate, // 开始日期
        enddate: params.endDate, // 结束日期
        setlym: utils.createMonthFormat6(params.beginDate), // 清算年月，yyyyMM
        status: 1, // 清算状态，默认待清算
        ...params,
        ...newItem,
    };
    const targetIndex = queryResponse.editItems.findIndex((item: any) => item.id === target.id);
    if (targetIndex === -1) {
        queryResponse.editItems.push(target);
    } else {
        queryResponse.editItems.splice(targetIndex, 1, target);
    }
};
</script>

<style lang="scss">
    .social-module__feature__dialog-liquidation-help {
        width: 1200px !important;

        .el-dialog__body {
            padding-top: 8px;
        }

        .tools-wrapper {
            display: flex;
            justify-content: flex-start;
            align-items: center;

            .track {
                flex: 1;
            }
        }

        .el-table {
            margin-top: 8px;

            .el-table__empty-text {
                margin-top: 150px;
            }
        }
    }
</style>