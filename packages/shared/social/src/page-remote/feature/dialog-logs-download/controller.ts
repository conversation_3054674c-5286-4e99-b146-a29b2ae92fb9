/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import AbcResponse from '../../../common/AbcResponse';

import createLoadingModel from '../../../model/loading';

export const createLogsDownloadController = (props: any) => {
    const loadingModelDownload = createLoadingModel();

    /**
     * 请求本地日志下载
     * <AUTHOR>
     * @date 2024-08-07
     * @returns {Promise<AbcResponse>}
     */
    const requestLogsDownload = async () => {
        let runResponse = null;
        try {
            const path = `/api/management/corp/oss-token?${new Date().getTime()}`;
            const ossToken = await fetch(path).then(rsp => rsp.json());
            const abc = await window.remoteSDK.mixUtils.getLogs(props.deviceCode, JSON.stringify({ ossToken: ossToken.data }));
            runResponse = AbcResponse.success({ url: abc.url });
        } catch (error: any) {
            runResponse = AbcResponse.error(error.message || '执行失败');
        }
        return runResponse;
    };

    return {
        loadingModelDownload,
        requestLogsDownload,
    };
};