<template>
    <el-dialog
        v-model="isShowDialogLogsDownload"
        title="本地日志下载"
        :close-on-click-modal="false"
        custom-class="social-module__feature__dialog-logs-download"
    >
        <div class="cont-wrapper">
            <el-button
                type="primary"
                plain
                :loading="loadingModelDownload.loading.value"
                @click="onClickLogsDownload"
            >
                发起下载
            </el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus';
import { computed, onMounted } from 'vue';
import { createLogsDownloadController } from './controller';
import * as utils from '../../../common/utils';

const props = defineProps({
    deviceCode: {
        type: String,
        default: '',
    },
    modelValue: {
        type: Number,
        default: 0,
    },
});

const $emit = defineEmits([
    'update:modelValue', // 更新显示状态
]);

const isShowDialogLogsDownload = computed({
    get() {
        return !!props.modelValue;
    },
    set(val:boolean) {
        $emit('update:modelValue', +val);
    },
});

const {
    loadingModelDownload,
    requestLogsDownload,
} = createLogsDownloadController(props);

onMounted(() => {
    // todo 可以去把门店的前置机信息查出来
});

/**
 * 当点击重启小明前置机
 * <AUTHOR>
 * @date 2024-08-07
 */
const onClickLogsDownload = async () => {
    if (loadingModelDownload.loading.value) {
        return;
    }
    loadingModelDownload.setLoading(true);
    const response = await requestLogsDownload();
    loadingModelDownload.setLoading(false);
    if (response.status === false) {
        return ElMessage.error('下载失败: ' + response.message);
    }
    ElMessage.success('下载成功');
    setTimeout(() => {
        utils.openDownloadPage(response.data.url);
    }, 800);
};
</script>

<style lang="scss">
    .social-module__feature__dialog-logs-download {
        width: 640px !important;

        .el-dialog__body {
            padding-top: 8px;
        }

        .cont-wrapper {
            width: 100%;
            height: 320px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }
</style>