/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import { computed, reactive } from 'vue';
import * as utils from '../../../common/utils';

import AbcResponse from '../../../common/AbcResponse';
import createLoadingModel from '../../../model/loading';

export const createDialogNetworkTestController = () => {
    const loadingModel = createLoadingModel();

    // 表单数据
    const formData = reactive({
        actions: <any []>[], // 命令动作选值
        command: '', // 命令
    });
    
    // 远端信息
    const remoteInfo = reactive({
        deviceCode: '', // 设备码
        execMessage: '', // 执行结果
    });

    // 是否禁止执行按钮
    const isDisabledExecuteBtn = computed(() => {
        if (loadingModel.loading.value) {
            return true;
        }
        if (isDisabledCommand.value) {
            return false;
        }
        return !formData.command;
    });

    // 是否禁止拷贝按钮
    const isDisabledCopyResultBtn = computed(() => !remoteInfo.execMessage);

    // 动作配置项
    const actionConfigItem = computed(() => {
        const action = formData.actions.slice(-1)[0];
        return actionConfigList.find((item: any) => item.value === action);
    });

    // 是否显示命令框
    const isDisabledCommand = computed(() => !!actionConfigItem.value?.isDisabledCommand);

    // 动作常量
    const actionConst = Object.freeze({
        CUSTOM: 'custom',
        PING: 'ping',
        TELNET: 'telnet',
        TRACERT: 'tracert',
        ROUTE: 'route',
        NETWORK_CARD: 'network-card',
        DNS: 'dns',
        OTHER: 'other',
    });

    // 动作选项
    const actionOptions = computed(() => actionConfigList.reduce((options: any[], item: any) => {
        if (!item.group) {
            // 无需分组的
            options.push(item);
            return options;
        } 
        // 带分组的
        let groupItem = options.find((one: any) => one.value === item.group);
        if (!groupItem) {
            groupItem = {
                value: item.group,
                label: createActionName(item.group),
                children: [],
            };
            options.push(groupItem);
        }
        const isExist = !!groupItem.children.find((one: any) => one.value === item.value);
        if (!isExist) {
            groupItem.children.push(item);
        }
            
        return options;
    }, []));

    /**
     * 初始化表单数据
     * <AUTHOR>
     * @date 2024-09-06
     */
    const initFormData = () => {
        formData.actions = [actionConst.CUSTOM]; // 命令动作选值
        formData.command = ''; // 命令
    };

    /**
     * 创建动作名称
     * <AUTHOR>
     * @date 2024-09-05
     * @param {String} action
     * @returns {String}
     */
    const createActionName = (action: string) => {
        const options = Object.freeze(([
            { value: actionConst.CUSTOM, label: '自定义命令' },
            { value: actionConst.PING, label: 'ping' },
            { value: actionConst.TELNET, label: 'telnet' },
            { value: actionConst.TRACERT, label: 'tracert' },
            { value: actionConst.ROUTE, label: '路由' },
            { value: actionConst.NETWORK_CARD, label: '网卡' },
            { value: actionConst.DNS, label: 'DNS' },
            { value: actionConst.OTHER, label: '其他' },
        ]));
        const target = options.find((item: any) => item.value === action);
        return target?.label || '';
    };

    /**
     * 创建ping命令
     * <AUTHOR>
     * @date 2024-09-05
     * @param {String} host 
     * @returns {String}
     */
    const createPingCommand = (host: string) => `ping ${host}`;

    /**
     * 创建telent命令
     * <AUTHOR>
     * @date 2024-09-05
     * @param {String} host 
     * @param {Number} port
     * @returns {String}
     */
    const createTelnetCommand = (host: string, port: number) => `telnet ${host} ${port}`;

    /**
     * 创建tracert命令
     * <AUTHOR>
     * @date 2024-09-05
     * @param {String} host 
     * @returns {String}
     */
    const createTracertCommand = (host: string) => `tracert ${host}`;

    /**
     * 请求telnet
     * <AUTHOR>
     * @date 2024-09-05
     * @param {String} command
     * @returns {Promise<AbcResponse>}
     */
    const requestTelnet = async (command: string) => {
        const [, host, port] = command.split(' ').filter((s: string) => !!s);
        const js = `
            let timer = null
            let socket = null
            response = await new Promise((resolve) => {
                socket = window.require('net').createConnection(${port}, '${host}', () => {
                    resolve({
                        status: true,
                        message: '',
                        data: {
                            result: 'Connected to ${host}:${port}'
                        }
                    })
                })
                socket.on('error', (err) => {
                    resolve({
                        status: false,
                        message: 'Error connecting to ${host}:${port}, message: ' + err.message,
                    })
                })
                timer = setTimeout(() => {
                    resolve({
                        status: false,
                        message: 'Connection timeout'
                    })
                }, 2000)
            })
            if (timer) {
                clearTimeout(timer)
            }
            if (socket) {
                socket.end()
            }
        `;
        const response = await utils.remoteRunJs(remoteInfo.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    /**
     * 请求执行命令
     * <AUTHOR>
     * @date 2024-09-05
     * @param {String} command
     * @returns {Promise<AbcResponse>}
     */
    const requestExecSync = async (command: string) => {
        // 检查
        if (
            command.startsWith('ping')
            && command.indexOf('-t') !== -1
        ) {
            // ping带循环，不允许
            return AbcResponse.error('不允许执行ping命令-t');
        }
        // 1、通过node插件执行，缺点就行报错信息会乱码
        // const js = `
        //     const buffer = await window.require('child_process').execSync('${command}')
        //     const result = window.require('iconv-lite').decode(buffer, 'GBK')
        //     response = {
        //         status: true,
        //         message: '',
        //         data: {
        //             result,
        //         }
        //     }
        // `;
        // const response = await utils.remoteRunJs(remoteInfo.deviceCode, js);

        // 2、通过底层方法调用，错误信息不会乱码
        const response = await utils.remoteRunShell(remoteInfo.deviceCode, command);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    /**
     * 请求刷新页面
     * <AUTHOR>
     * @date 2024-09-05
     * @returns {Promise<AbcResponse>}
     */
    const requestRestartPage = async () => {
        const js = `
            setTimeout(() => {
                window.location.reload()
            }, 3000)
            response = {
                status: true,
                message: '',
            }
        `;
        const response = await utils.remoteRunJs(remoteInfo.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    /**
     * 请求重启客户端
     * <AUTHOR>
     * @date 2024-09-05
     * @returns {Promise<AbcResponse>}
     */
    const requestRestartClient = async () => {
        const js = `
            setTimeout(() => {
                window.electron.remote.app.relaunch()
                window.electron.remote.app.exit()
            }, 3000)
            response = {
                status: true,
                message: '',
            }
        `;
        const response = await utils.remoteRunJs(remoteInfo.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    /**
     * 请求重启前置机
     * <AUTHOR>
     * @date 2024-09-05
     * @returns {Promise<AbcResponse>}
     */
    const requestRestartQzj = async () => {
        const js = `
            if (window.electron.appConfig.get('shebao.privateNetworkMode') === false) {
                throw new Error('非专网路由器模式，无需重启前置机')
            }
            if (!window.$national) {
                throw new Error('重启前置机依赖social插件，请检查机构region配置是否正确')
            }
            setTimeout(() => {
                window.$national.tools.resetartAbcGateWayDevice()
            }, 3000)
            response = {
                status: true,
                message: '',
            }
        `;
        const response = await utils.remoteRunJs(remoteInfo.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    /**
     * 请求获取电脑时间
     * <AUTHOR>
     * @date 2024-09-05
     * @returns {Promise<AbcResponse>}
     */
    const requestFetchDateTime = async () => {
        const js = `
            response = {
                status: true,
                message: '',
                data: {
                    date: new Date() + '',
                    result: '',
                }
            }
        `;
        const response = await utils.remoteRunJs(remoteInfo.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        response.data.result = utils.createDateTimeFormat19(response.data.date);
        return response;
    };
    
    /**
     * 请求校准电脑时间
     * <AUTHOR>
     * @date 2024-09-05
     * @returns {Promise<AbcResponse>}
     */
    const requestUpdateDateTime = async () => {
        const datetime = utils.createDateTimeFormat19();
        const [date, time] = datetime.split(' ');
        const js = `
            setTimeout(async () => {
                const buffer = await window.require('child_process').execSync('date ${date} && time ${time}')
                const result = window.require('iconv-lite').decode(buffer, 'GBK')
                window.electron.remote.app.relaunch()
                window.electron.remote.app.exit()
            }, 3000)
            response = {
                status: true,
                message: '执行成功，已校准时间为：${datetime}',
            }
        `;
        const response = await utils.remoteRunJs(remoteInfo.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    /**
     * 插入social的网络配置
     * <AUTHOR>
     * @date 2024-09-05
     * @returns {Promise<AbcResponse>}
     */
    const insertSoicalNetworkConfig = async () => {
        (window.$national?.options?.networkConfigList || []).forEach((item: any) => {
            const host = item.host || item.ip || ''; // 地址
            const port = item.port || (item.protocol === 'https' ? 443 : 80); // 端口
            actionConfigList.push({
                group: actionConst.TELNET,
                value: `${actionConst.TELNET}_${host}:${port}`,
                label: item.name || '',
                command: createTelnetCommand(host, port),
                isDisabledCommand: false,
            });
            actionConfigList.push({
                group: actionConst.TRACERT,
                value: `${actionConst.TRACERT}_${host}`,
                label: item.name || '',
                command: createTracertCommand(host),
                isDisabledCommand: false,
            });
        });
    };

    // 动作配置列表
    const actionConfigList = reactive([
        {
            value: `${actionConst.CUSTOM}`,
            label: '自定义',
            isDisabledCommand: false,
        },
        {
            group: actionConst.PING,
            value: `${actionConst.PING}_baidu`,
            label: '百度地址',
            command: createPingCommand('www.baidu.com'),
            isDisabledCommand: false,
        },
        {
            group: actionConst.PING,
            value: `${actionConst.PING}_abcyun`,
            label: 'abcyun.cn',
            command: createPingCommand('abcyun.cn'),
            isDisabledCommand: false,
        },
        {
            group: actionConst.TELNET,
            value: `${actionConst.TELNET}_baidu`,
            label: '百度地址',
            command: createTelnetCommand('www.baidu.com', 80),
            isDisabledCommand: false,
        },
        {
            group: actionConst.TELNET,
            value: `${actionConst.TELNET}_abcyun`,
            label: 'abcyun.cn',
            command: createTelnetCommand('abcyun.cn', 80),
            isDisabledCommand: false,
        },
        {
            group: actionConst.TRACERT,
            value: `${actionConst.TRACERT}_baidu`,
            label: '百度地址',
            command: createTracertCommand('www.baidu.com'),
            isDisabledCommand: false,
        },
        {
            group: actionConst.TRACERT,
            value: `${actionConst.TRACERT}_abcyun`,
            label: 'abcyun.cn',
            command: createTracertCommand('abcyun.cn'),
            isDisabledCommand: false,
        },
        {
            group: actionConst.ROUTE,
            value: `${actionConst.ROUTE}_select`,
            label: '查看',
            command: 'route print',
            isDisabledCommand: false,
        },
        {
            group: actionConst.ROUTE,
            value: `${actionConst.ROUTE}_create`,
            label: '新增',
            command: 'route add [目标网络] mask [子网掩码] [网关]',
            tips: '此命令执行后可能会影响门店网络',
            isDisabledCommand: false,
        },
        {
            group: actionConst.ROUTE,
            value: `${actionConst.ROUTE}_delete`,
            label: '删除',
            command: 'route delete [目标网络] mask [子网掩码]',
            tips: '此命令执行后可能会影响门店网络',
            isDisabledCommand: false,
        },
        {
            group: actionConst.NETWORK_CARD,
            value: `${actionConst.NETWORK_CARD}_select`,
            label: '查看',
            command: 'ipconfig /all',
            isDisabledCommand: false,
        },
        {
            group: actionConst.NETWORK_CARD,
            value: `${actionConst.NETWORK_CARD}_enable`,
            label: '启用',
            command: 'netsh interface set interface [网卡名称] enable',
            tips: '此命令执行后可能会影响门店网络',
            isDisabledCommand: false,
        },
        {
            group: actionConst.NETWORK_CARD,
            value: `${actionConst.NETWORK_CARD}_disabled`,
            label: '禁用',
            command: 'netsh interface set interface [网卡名称] disabled',
            tips: '此命令执行后可能会影响门店网络',
            isDisabledCommand: false,
        },
        {
            group: actionConst.DNS,
            value: `${actionConst.DNS}_select`,
            label: '查看',
            command: 'ipconfig /all',
            isDisabledCommand: false,
        },
        {
            group: actionConst.DNS,
            value: `${actionConst.DNS}_clear`,
            label: '清除缓存',
            command: 'ipconfig /flushdns',
            isDisabledCommand: false,
        },
        {
            group: actionConst.OTHER,
            value: `${actionConst.OTHER}_restart-page`,
            label: '刷新页面',
            tips: '此命令执行后会打断客户当前操作',
            exec: requestRestartPage,
            isDisabledCommand: true,
        },
        {
            group: actionConst.OTHER,
            value: `${actionConst.OTHER}_restart-client`,
            label: '重启客户端',
            tips: '此命令执行后会打断客户当前操作',
            exec: requestRestartClient,
            isDisabledCommand: true,
        },
        {
            group: actionConst.OTHER,
            value: `${actionConst.OTHER}_restart-qzj`,
            label: '重启前置机',
            tips: '此命令执行后可能会导致远程操作和连接中断',
            exec: requestRestartQzj,
            isDisabledCommand: true,
        },
        {
            group: actionConst.OTHER,
            value: `${actionConst.OTHER}_fetch-datetime`,
            label: '获取电脑时间',
            exec: requestFetchDateTime,
            isDisabledCommand: true,
        },
        {
            group: actionConst.OTHER,
            value: `${actionConst.OTHER}_update-datetime`,
            label: '校准电脑时间',
            exec: requestUpdateDateTime,
            isDisabledCommand: true,
            tips: '此命令执行后会打断客户当前操作',
        },
        {
            group: actionConst.OTHER,
            value: `${actionConst.OTHER}_restart-computer`,
            label: '重启电脑',
            command: 'shutdown /r /t 10', // 10秒后重庆电脑
            isDisabledCommand: false,
            tips: '此命令执行后会打断客户当前操作',
        },
    ]);

    return {
        loadingModel,
        formData,
        remoteInfo,
        isDisabledExecuteBtn,
        isDisabledCopyResultBtn,
        actionOptions,
        actionConfigList,
        actionConfigItem,
        isDisabledCommand,
        initFormData,
        requestTelnet,
        requestExecSync,
        requestUpdateDateTime,
        insertSoicalNetworkConfig,
    };
};