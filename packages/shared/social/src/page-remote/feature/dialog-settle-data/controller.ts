/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import dayjs from 'dayjs';
import SocialApi from '../../../api/social-api';
import AbcResponse from '../../../common/AbcResponse';

import { computed, reactive } from 'vue';
import * as utils from '../../../common/utils';

import createLoadingModel from '../../../model/loading';
import createDialogModel from '../../../model/dialog';

export const createSettleDataController = (props: any) => {
    const loadingModelQuery = createLoadingModel();
    const dialogModelDataList = createDialogModel();

    // 工具栏参数
    const toolsParams = reactive({
        dateRange: [
            dayjs().format('YYYY-MM-DD'),
            dayjs().format('YYYY-MM-DD'),
        ],
    });

    const formData = reactive({
        dataList: <any []>[],
        showConfig: null,
        showDataList: <any []>[],
    });

    // 医疗收费项目类别选项
    const mdtrtareaAdmvsOptions = computed(() => window.$national.options.mdtrtareaAdmvsOptions);

    // 医保信息
    const socialInfo: any = computed(() => {
        const socialInfo = props.socialInfo || {};
        const { localType, outType } = props.socialInfo?.basicInfo || {};
        const { localTypeConst, outTypeConst } = window.$national.constants; 
        return {
            ...socialInfo,
            mdtrtareaAdmvs: socialInfo.basicInfo?.mdtrtareaAdmvs || '', // 就医地医保区划
            isHospitalCity: localType === localTypeConst.HOSPITAL_CITY, // 市定点机构
            isHospitalProv: localType === localTypeConst.HOSPITAL_PROV, // 省定点机构
            isHospitalDouble: localType === localTypeConst.HOSPITAL_DOUBLE, // 双定点机构
            isCityYD: outType === outTypeConst.YD_CITY, // 市异地
            isProvYD: outType === outTypeConst.YD_PROV, // 省异地
        };
    });

    /**
     * 初始化表单数据
     * <AUTHOR>
     * @date 2024-10-17
     */
    const initFormData = () => {
        const params = {
            region: socialInfo.value.region, // 地区标识
            isHospital: socialInfo.value.isHospital, // 是否医疗机构
            isPharmacy: socialInfo.value.isPharmacy, // 是否零售药店
        };
        const target = configList.find((item: any) => item.mate(params));
        formData.dataList = (target?.options || [])
                        .filter((item: any) => item.show !== false)
                        .map((item: any) => {
                            const itemInfo = {
                                ...item,
                            };
                            if (!itemInfo.clrOptins) {
                                itemInfo.clrOptins = socialInfo.value.mdtrtareaAdmvs;
                            }
                            if (!itemInfo.clrTypeWording) {
                                itemInfo.clrTypeWording = window.$national.tools.getClrTypeWording(itemInfo.clrType);
                            }
                            if (!itemInfo.insutypeWording) {
                                itemInfo.insutypeWording = window.$national.tools.getInsutypeWording(itemInfo.insutype);
                            }
                            if (!itemInfo.clrOptinsWording) {
                                itemInfo.clrOptinsWording = window.$national.tools.getCityAreaCodeWording(itemInfo.clrOptins, false, '');
                            }
                            return itemInfo;
                        });
    };

    /**
     * 创建对账明细参数
     * <AUTHOR>
     * @date 2024-10-17
     * @param {Object} item
     * @returns {Object}
     */
    const createCheckingDetailParams = (item: any) => {
        const {
            dateRange: [
                beginDate, // 开始日期
                endDate, // 截止日期
            ],
        } = toolsParams;
        const {
            insutype, // 险种
            clrType, // 清算类别
            clrOptins, // 清算经办机构
            insuplcAdmdvs, // 参保地医保区划
        } = item;
        const checkingDetailParams = {
            beginDate, // 开始日期
            endDate, // 截止日期
            insutype, // 险种
            clrType, // 清算类别
            clrOptins, // 清算经办机构
            insuplcAdmdvs: insuplcAdmdvs || clrOptins, // 参保地医保区划
        };
        return checkingDetailParams;
    };

    /**
     * 创建就诊记录参数
     * <AUTHOR>
     * @date 2024-10-17
     * @param {Object} item
     * @returns {Object}
     */
    const createMedicalRecordParams = (item: any) => {
        const {
            dateRange: [
                beginDate, // 开始日期
                endDate, // 截止日期
            ],
        } = toolsParams;
        const medicalRecordParams = {
            insuplcAdmdvs: item.clrOptins, // 参保区划
            data: {
                begntime: `${window.$national.tools.getDateFormat(beginDate)} 00:00:00`, // 开始时间
                endtime: `${window.$national.tools.getDateFormat(endDate)} 23:59:59`, // 结束时间
                med_type: item.medType, // 医疗类别
                psn_no: '000', // 人员编号
            },
        };
        return medicalRecordParams;
    };

    /**
     * 请求中心结算数据拉取 - 对明细账
     * <AUTHOR>
     * @date 2024-08-07
     * @param {Object} params
     * @returns {Promise<AbcResponse>}
     */
    const requestSettleDataByCheckingDetail = async (params: any) => {
        const js = `
            const addParams = JSON.parse('${JSON.stringify(params)}')
            const timer = window.$national.tools.createTimer()

            // 初始化工具库
            const view = {
                $national: window.$national,
                nationalSocialSecurity: window.$national.protocol.NationalSocialSecurity.instance,
            }
            const checkingPresenter = new window.$national.view.CheckingNewPresenter(view)

            // 对总账
            const params = {
                isNeedPostSettleItems: false,
                summaryItem: Object.assign(checkingPresenter.createSummaryItem(), addParams),
            }
            const checkingTotalResponse = await checkingPresenter.requestCheckingTotal(params)
            if (checkingTotalResponse.status === false) {
                throw checkingTotalResponse
            }

            // 对明细
            const checkingDetailResponse = await checkingPresenter.requestCheckingDetail(params)
            if (checkingDetailResponse.status === false) {
                throw checkingDetailResponse
            }

            // 整理
            const dataList = (checkingDetailResponse.data?.dataList || [])
                .map((item) => ({
                    ...checkingPresenter.createSettleItemByStr(item),
                    clrOptins: addParams.clrOptins,
                }))
                .filter((item) => (
                    item.psnNo
                    && item.mdtrtId
                    && item.setlId
                    && item.psnNo !== 'null'
                    && item.mdtrtId !== 'null'
                    && item.setlId !== 'null'
                    && item.psnNo !== '000000000000000000000000000'
                ))

            
            for (let index = 0; index < dataList.length; index ++) {
                const item = dataList[index]

                // 查结算信息
                const settleInfoParams = {
                    data: {
                        psn_no: item.psnNo, // 人员编号
                        mdtrt_id: item.mdtrtId, // 就诊ID
                        setl_id: item.setlId, // 结算ID
                    },
                }
                const settleInfoResponse = await window.$national.protocol.NationalSocialSecurity.instance.settleInfo(settleInfoParams, addParams.insuplcAdmdvs)
                item.setlinfo = settleInfoResponse.data?.output?.setlinfo || null
                if (!item.setlinfo) {
                    continue
                }

                // 查就诊信息
                const medicalInfo = {
                    data: {
                        begntime: addParams.beginDate + ' 00:00:00', // 开始时间
                        endtime: addParams.endDate + ' 23:59:59', // 结束时间
                        psn_no: item.psnNo, // 人员编号
                        med_type: item.setlinfo.med_type, // 医疗类别
                        mdtrt_id: item.mdtrtId, // 就诊ID
                    },
                }
                const medicalInfoResponse = await window.$national.protocol.NationalSocialSecurity.instance.medicalInfo(medicalInfo, addParams.insuplcAdmdvs)
                item.medicalInfo = (medicalInfoResponse.data.output?.mdtrtinfo || [])[0] || null
            }

            const timestamp = Date.now() + ''
            
            // 上报日志
            window.$national.log.logReportRemote({
                transType: 'settleDataFetch',
                transName: '中心数据拉取',
                time: timer.end() + 'ms',
                params: addParams,
                response: {
                    status: true,
                    data: {
                        timestamp,
                        dataList,
                    }
                },
            })
            
            response = {
                status: true,
                data: {
                    count: dataList.length,
                    timestamp,
                }
            }
        `;
        window.remoteSDK.rpcInvoker.invokeTimeoutOnce = 1000 * 3600 * 10; // 10分钟超时
        const response = await utils.remoteRunJs(props.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    /**
     * 请求中心结算数据拉取 - 广西
     * <AUTHOR>
     * @date 2025-07-02
     * @param {Object} params
     * @returns {Promise<AbcResponse>}
     */
    const requestSettleDataToGuangxi = async (params: any) => {
        const queryParma = {
            ...params,
            refd_setl_flag: '',
            q_begntime: dayjs(params.beginDate).startOf('date').format('YYYY-MM-DD HH:mm:ss'),
            q_endtime: dayjs(params.endDate).endOf('date').format('YYYY-MM-DD HH:mm:ss'),
            med_type: '',
            page_num: '1',
            page_size: '99999',
        };
        const js = `
            const addParams = JSON.parse('${JSON.stringify(queryParma)}')
            const timer = window.$national.tools.createTimer()
            // 初始化工具库
            const nationalSocialSecurity = window.$national.protocol.NationalSocialSecurity.instance

            const paramsList = [
                {
                    isYD: true,
                    ...addParams,
                },
                {
                    ...addParams,
                }
            ]
            if (!nationalSocialSecurity.isHospitalCity) {
                paramsList.push(...[
                    {
                        isYD: true,
                        insuplc_admdvs:'459900',
                        ...addParams,
                    },
                    {
                        insuplc_admdvs:'459900',
                        ...addParams,
                    }
                ])
            }
            let dataList = [];
            for (let item of paramsList) {
                const params = {
                    data: item
                }
                const {
                    isYD,
                    insuplc_admdvs,
                } = params.data
                let result;
                if (isYD) {
                    const settleListInfo = await nationalSocialSecurity.searchSettleInfoYDList(params, insuplc_admdvs)
                    if (settleListInfo.status === false) {
                        continue;
                    }
                    result = settleListInfo.data.output.result
                } else {
                    const settleListInfo = await nationalSocialSecurity.searchSettleInfoList(params, insuplc_admdvs)
                    if (settleListInfo.status === false) {
                        continue;
                    }
                    result =  settleListInfo.data.output.result
                }
                dataList.push(...(result || []))
            }
            dataList = dataList.filter(item=> 
                                            item.insutype == addParams.insutype &&
                                            item.clr_type == addParams.clrType && 
                                            item.clr_optins.slice(0,4) ==  addParams.clrOptins.slice(0,4)
                                        );
            for (let index = 0; index < dataList.length; index ++) {
                const item = dataList[index]

                item.setlinfo = {...item};
                // 查就诊信息
                const medicalInfo = {
                    data: {
                        begntime: addParams.q_begntime, // 开始时间
                        endtime: addParams.q_endtime, // 结束时间
                        psn_no: item.psn_no + '', // 人员编号
                        med_type: item.med_type, // 医疗类别
                        mdtrt_id: '', // 就诊ID
                    },
                };
                const medicalInfoResponse = await nationalSocialSecurity.medicalInfo(medicalInfo);
                item.medicalInfo = (medicalInfoResponse.data.output?.mdtrtinfo || [])[0] || null;
            }
            const timestamp = Date.now() + ''
            // 上报日志
            window.$national.log.logReportRemote({
                transType: 'settleDataFetch',
                transName: '中心数据拉取',
                time: timer.end() + 'ms',
                params: addParams,
                response: {
                    status: true,
                    data: {
                        timestamp,
                        dataList,
                    }
                },
            })
            response = {
                status: true,
                data: {
                    count: dataList.length,
                    timestamp,
                }
            }
        `;
        window.remoteSDK.rpcInvoker.invokeTimeoutOnce = 1000 * 3600 * 10; // 10分钟超时
        const response = await utils.remoteRunJs(props.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    /**
     * 请求中心结算数据拉取 - 就诊记录查询
     * <AUTHOR>
     * @date 2024-08-07
     * @param {Object} params
     * @returns {Promise<AbcResponse>}
     */
    const requestSettleDataByMedicalRecord = async (params: any) => {
        const js = `
            const params = ${JSON.stringify(params)}
            const insuplcAdmdvs = params.insuplcAdmdvs
            const timer = window.$national.tools.createTimer()

            // 查询就诊记录
            const medicalInfoResponse = await window.$national.protocol.NationalSocialSecurity.instance.medicalInfo(params, insuplcAdmdvs)
            if (medicalInfoResponse.status === false) {
                throw medicalInfoResponse
            }
            const medicalInfoList = medicalInfoResponse.data.output?.mdtrtinfo || []
            if (medicalInfoList.length === 0) {
                throw new Error('中心无结算数据')
            }

            // 循环查结算信息
            const dataList = []
            for (let i = 0; i < medicalInfoList.length; i++) {
                const item = medicalInfoList[i]

                // 费用明细查询
                const chargeDetailParams = {
                    data: {
                        psn_no: item.psn_no,
                        mdtrt_id: item.mdtrt_id,
                    },
                }
                const chargeDetailResponse = await window.$national.protocol.NationalSocialSecurity.instance.chargeDetail(chargeDetailParams, insuplcAdmdvs)
                const chargeDetailList = chargeDetailResponse.data?.output || []

                // 结算信息查询
                const setlIdList = []
                chargeDetailList.forEach((one) => {
                    if (one.setl_id && !setlIdList.includes(one.setl_id)) {
                        setlIdList.push(one.setl_id)
                    }
                })
                for (let j = 0; j < setlIdList.length; j++) {
                    const settleInfoParams = {
                        data: {
                            psn_no: item.psn_no,
                            mdtrt_id: item.mdtrt_id,
                            setl_id: setlIdList[j],
                        },
                    }
                    const settleInfoResponse = await window.$national.protocol.NationalSocialSecurity.instance.settleInfo(settleInfoParams, insuplcAdmdvs)
                    dataList.push({
                        params: settleInfoParams,
                        medicalInfo: item,
                        setlinfo: settleInfoResponse.data?.output?.setlinfo || null,
                    })
                }
            }

            const timestamp = Date.now() + ''
            
            // 上报日志
            window.$national.log.logReportRemote({
                transType: 'settleDataFetch',
                transName: '中心数据拉取',
                time: timer.end() + 'ms',
                params,
                response: {
                    status: true,
                    data: {
                        timestamp,
                        dataList,
                    }
                },
            })
            
            response = {
                status: true,
                data: {
                    count: dataList.length,
                    timestamp,
                }
            }
        `;
        window.remoteSDK.rpcInvoker.invokeTimeoutOnce = 1000 * 3600 * 10; // 10分钟超时
        const response = await utils.remoteRunJs(props.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    /**
     * 请求中心结算数据拉取 - 就诊记录查询 - 共济
     * <AUTHOR>
     * @date 2024-08-07
     * @param {Object} params
     * @returns {Promise<AbcResponse>}
     */
    const requestSettleDataByMedicalRecordGj = async (params: any) => {
        const js = `
            const params = ${JSON.stringify(params)}
            const insuplcAdmdvs = params.insuplcAdmdvs
            const timer = window.$national.tools.createTimer()

            // 查询就诊记录
            const medicalInfoResponse = await window.$national.protocol.NationalSocialSecurity.instance.medicalInfo(params, insuplcAdmdvs)
            if (medicalInfoResponse.status === false) {
                throw medicalInfoResponse
            }
            const medicalInfoList = medicalInfoResponse.data.output?.mdtrtinfo || []
            if (medicalInfoList.length === 0) {
                throw new Error('中心无结算数据')
            }
            const dataList = medicalInfoList.map((item) => ({
                params: {},
                medicalInfo: item,
                setlinfo: {
                    psn_name: item.psn_name,
                    setl_time: item.endtime,
                    medfee_sumamt: 0,
                    fund_pay_sumamt: 0,
                    acct_pay: 0,
                    psn_no: item.psn_no,
                    mdtrt_id: item.mdtrt_id,
                    setl_id: '',
                    refd_setl_flag: '',
                }
            }))

            const timestamp = Date.now() + ''
            
            // 上报日志
            window.$national.log.logReportRemote({
                transType: 'settleDataFetch',
                transName: '中心数据拉取',
                time: timer.end() + 'ms',
                params,
                response: {
                    status: true,
                    data: {
                        timestamp,
                        dataList,
                    }
                },
            })
            
            response = {
                status: true,
                data: {
                    count: dataList.length,
                    timestamp,
                }
            }
        `;
        window.remoteSDK.rpcInvoker.invokeTimeoutOnce = 1000 * 3600 * 2; // 10分钟超时
        const response = await utils.remoteRunJs(props.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    /**
     * 查询近30天拉取日志
     * <AUTHOR>
     * @date 2024-10-21
     * @param {String} keywords
     * @returns {Promise<AbcResponse>}
     */
    const requestFetchRecord = async (keywords?: string) => {
        const date = dayjs();
        const s = date.subtract(30, 'day').startOf('day');
        const e = date.endOf('day');
        const params = {
            beginDate: utils.createDateTimeFormat19(s), // 开始日期
            endDate: utils.createDateTimeFormat19(e), // 结束日期
            offset: 0, // 偏移量
            limit: 100, // 每页条数
            query: `"business:shebao_national_log" and __topic__ : abc-cis-shebao-stat-service and 中心数据拉取 and ${socialInfo.value.clinicId}`, // 查询条件
            customParams: utils.createCustomParams(),
        };
        if (keywords) {
            params.query += ` and ${keywords}`;
        }
        const response = await SocialApi.fetchManagementLogByCustomParams(params);
        if (response.status === false) {
            return response;
        }
        const dataList = (response.data.logItems || [])
                        .map((item: any) => createItemInfo(item))
                        .filter((item: any) => !!item);
        return AbcResponse.success({ dataList });
    };

    /**
     * 创建数据立碑
     * <AUTHOR>
     * @date 2024-10-09
     * @param {Object} item
     * @returns {Object}
     */
    const createItemInfo = (item: any) => {
        const message = item.message || item['message-index'] || '';
        try {
            const logData = utils.parseMessage(message);
            if (!logData) {
                throw new Error('utils.parseMessage error');
            }
            const itemInfo = logData.data;
            if (!itemInfo) {
                throw new Error('itemInfo kong');
            }
            return itemInfo;
        } catch (error) {
            console.log('createItemInfo error', error);
            console.log('message', message);
        }
        return null;
    };

    /**
     * 请求中心结算数据导入 - 三方数据
     * <AUTHOR>
     * @date 2024-08-07
     * @param {Object} params
     * @returns {Promise<AbcResponse>}
     */
    const requestSettleDataImport = async (params: any) => {
        const js = `
            const params = ${JSON.stringify(params)}
            
            // 初始化工具库
            const view = {
                $national: window.$national,
                nationalSocialSecurity: window.$national.protocol.NationalSocialSecurity.instance,
            }
            const checkingPresenter = new window.$national.view.CheckingNewPresenter(view)
            const providePresenter = new window.$national.view.ProvidePresenter(window.$national)

            // 拉取日志
            const logParams = {
                date: window.$national.tools.getDateFormat(+params.timestamp),
                query: params.timestamp,
                limit: 1,
            }
            const logQueryService = window.$national.getService('log-query')
            const logResponse = await logQueryService.requestLogData(logParams)
            if (logResponse.status === false) {
                throw logResponse
            }
            const logItem = (logResponse.data?.dataList || [])[0] || null
            const dataList = logItem?.data.response.data.dataList || []
            
            // 导入数据
            const info = {
                successNum: 0,
                failNum: 0,
            }
            for (let index = 0; index < dataList.length; index ++) {
                const item = dataList[index]
                const iptOtpNo = item?.medicalInfo?.ipt_otp_no || '' // 门诊号
                if (iptOtpNo.startsWith('abc-')) {
                    continue
                }
                if (!item?.setlinfo) {
                    continue
                }
                const syncParams = {
                    settleInfo: item.setlinfo,
                    wltpayAmt: item.wltpayAmt || 0, // 医保钱包支付金额
                    isNeedValidate: true,
                }
                const syncResponse = await checkingPresenter.requestSettleDetailSync(syncParams)
                if (syncResponse.status === true) {
                    info.successNum += 1
                } else {
                    info.failNum += 1
                }
            }
            
            response = {
                status: true,
                data: info,
            }
        `;
        window.remoteSDK.rpcInvoker.invokeTimeoutOnce = 1000 * 3600 * 10; // 10分钟超时
        const response = await utils.remoteRunJs(props.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    /**
     * 创建参数 - 浙江异地
     * <AUTHOR>
     * @date 2024-10-17
     * @param {Object} item
     * @returns {Object}
     */
    const createParamsToZhejiangYD = () => {
        const {
            dateRange: [
                beginDate, // 开始日期
                endDate, // 截止日期
            ],
        } = toolsParams;
        const params = {
            beginDate: utils.createDateFormat(beginDate), // 开始日期
            endDate: utils.createDateFormat(endDate), // 截止日期
        };
        return params;
    };

    /**
     * 请求中心结算数据拉取 - 浙江异地数据
     * <AUTHOR>
     * @date 2024-08-07
     * @param {Object} params
     * @returns {Promise<AbcResponse>}
     */
    const requestSettleDataToZhejiangYD = async (params: any) => {
        const js = `
            const params = JSON.parse('${JSON.stringify(params)}')
            const timer = window.$national.tools.createTimer()

            // 初始化工具库
            const view = {
                $national: window.$national,
                nationalSocialSecurity: window.$national.protocol.NationalSocialSecurity.instance,
            }
            const checkingPresenter = new window.$national.view.CheckingNewPresenter(view)

            // 拉取未对账的异地数据
            const requestResponse = await checkingPresenter.requestSettleDataUncheckYd(params)
            if (requestResponse.status === false) {
                throw requestResponse
            }
            const dataList = requestResponse.data?.dataList || []
            
            for (let index = 0; index < dataList.length; index ++) {
                const item = dataList[index]

                // 查结算信息
                const settleInfoParams = {
                    data: {
                        psn_no: item.psnNo, // 人员编号
                        mdtrt_id: item.mdtrtSeq, // 就诊ID
                        setl_id: item.setlSn, // 结算ID
                    },
                }
                const settleInfoResponse = await window.$national.protocol.NationalSocialSecurity.instance.settleInfo(settleInfoParams, item.insuplcAdmdvs)
                item.setlinfo = settleInfoResponse.data?.output?.setlinfo || null
                if (!item.setlinfo) {
                    continue
                }

                // 查就诊信息
                const medicalInfo = {
                    data: {
                        begntime: addParams.beginDate + ' 00:00:00', // 开始时间
                        endtime: addParams.endDate + ' 23:59:59', // 结束时间
                        psn_no: item.psnNo, // 人员编号
                        med_type: item.setlinfo.med_type, // 医疗类别
                        mdtrt_id: item.mdtrtSeq, // 就诊ID
                    },
                }
                const medicalInfoResponse = await window.$national.protocol.NationalSocialSecurity.instance.medicalInfo(medicalInfo, item.insuplcAdmdvs)
                item.medicalInfo = (medicalInfoResponse.data.output?.mdtrtinfo || [])[0] || null
            }

            const timestamp = Date.now() + ''
            
            // 上报日志
            window.$national.log.logReportRemote({
                transType: 'settleDataFetch',
                transName: '中心数据拉取',
                time: timer.end() + 'ms',
                params: addParams,
                response: {
                    status: true,
                    data: {
                        timestamp,
                        dataList,
                    }
                },
            })
            
            response = {
                status: true,
                data: {
                    count: dataList.length,
                    timestamp,
                }
            }
        `;
        window.remoteSDK.rpcInvoker.invokeTimeoutOnce = 1000 * 3600 * 10; // 10分钟超时
        const response = await utils.remoteRunJs(props.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    const configList = [
        {
            mate: (params: any) => params.region.startsWith('anhui_'),
            options: [
                {
                    clrType: '11',
                    insutype: '310',
                    insutypeWording: '职工、居民',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
            ],
        },
        {
            mate: (params: any) => params.region.startsWith('henan_'),
            options: [
                {
                    clrType: '11',
                    insutype: '310',
                    insutypeWording: '职工、居民',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '14',
                    insutype: '310',
                    insutypeWording: '职工、居民',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
            ],
        },
        {
            mate: (params: any) => params.region.startsWith('guizhou_'),
            options: [
                {
                    clrType: '99970',
                    insutype: '310',
                    insutypeWording: '职工',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '99971',
                    insutype: '390',
                    insutypeWording: '居民',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
            ],
        },
        {
            mate: (params: any) => params.region === 'sichuan_chengdu',
            options: [
                {
                    clrType: '9950',
                    insutype: '310',
                    enableSetClrOptins: true,
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '99915',
                    insutype: '310',
                    enableSetClrOptins: true,
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '51',
                    insutype: '390',
                    enableSetClrOptins: true,
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '99939',
                    insutype: '390',
                    enableSetClrOptins: true,
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: 'S41',
                    insutype: '310',
                    clrOptins: '519900',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                    show: socialInfo.value.isHospitalDouble, // 省市双定点才能刷省卡
                },
                {
                    clrType: 'S11',
                    insutype: '310',
                    clrOptins: '519900',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                    show: socialInfo.value.isHospitalDouble, // 省市双定点才能刷省卡
                },
                {
                    medType: '41',
                    clrTypeWording: '药店购药（省内、省外）',
                    insutypeWording: '职工、居民',
                    clrOptinsWording: (() => {
                        // eslint-disable-next-line max-len
                        const clrOptins = (socialInfo.value.isHospitalDouble && socialInfo.value.isProvYD) ? '519900' : socialInfo.value.setlOptins.slice(0, 4) + '00';
                        return window.$national.tools.getCityAreaCodeWording(clrOptins, false, '');
                    })(),
                    clrOptins: '510000',
                    createParams: (item: any) => createMedicalRecordParams(item),
                    requestFetch: (params: any) => requestSettleDataByMedicalRecord(params),
                },
                {
                    medType: '12',
                    clrTypeWording: '门诊挂号（省内、省外）',
                    insutypeWording: '职工、居民',
                    clrOptinsWording: (() => {
                        // eslint-disable-next-line max-len
                        const clrOptins = (socialInfo.value.isHospitalDouble && socialInfo.value.isProvYD) ? '519900' : socialInfo.value.setlOptins.slice(0, 4) + '00';
                        return window.$national.tools.getCityAreaCodeWording(clrOptins, false, '');
                    })(),
                    clrOptins: '510000',
                    createParams: (item: any) => createMedicalRecordParams(item),
                    requestFetch: (params: any) => requestSettleDataByMedicalRecord(params),
                },
                {
                    medType: '11',
                    clrTypeWording: '普通门诊（省内、省外）',
                    insutypeWording: '职工、居民',
                    clrOptinsWording: (() => {
                        // eslint-disable-next-line max-len
                        const clrOptins = (socialInfo.value.isHospitalDouble && socialInfo.value.isProvYD) ? '519900' : socialInfo.value.setlOptins.slice(0, 4) + '00';
                        return window.$national.tools.getCityAreaCodeWording(clrOptins, false, '');
                    })(),
                    clrOptins: '510000',
                    createParams: (item: any) => createMedicalRecordParams(item),
                    requestFetch: (params: any) => requestSettleDataByMedicalRecord(params),
                },
                {
                    medType: '110102',
                    clrTypeWording: '共济下账（省内、省外）',
                    insutypeWording: '职工、居民',
                    disabledImport: true,
                    disabledImportTips: '异地共济缺少结算ID，无法导入',
                    clrOptinsWording: (() => {
                        // eslint-disable-next-line max-len
                        const clrOptins = (socialInfo.value.isHospitalDouble && socialInfo.value.isProvYD) ? '519900' : socialInfo.value.setlOptins.slice(0, 4) + '00';
                        return window.$national.tools.getCityAreaCodeWording(clrOptins, false, '');
                    })(),
                    clrOptins: '510000',
                    createParams: (item: any) => createMedicalRecordParams(item),
                    requestFetch: (params: any) => requestSettleDataByMedicalRecordGj(params),
                },
            ],
        },
        {
            mate: (params: any) => params.region.startsWith('sichuan_'),
            options: [
                {
                    clrType: '41',
                    insutype: '310',
                    enableSetClrOptins: true,
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '11',
                    insutype: '310',
                    enableSetClrOptins: true,
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '99939',
                    insutype: '390',
                    enableSetClrOptins: true,
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    medType: '41',
                    clrTypeWording: '药店购药（省内、省外）',
                    insutypeWording: '职工、居民',
                    clrOptinsWording: (() => {
                        const clrOptins = socialInfo.value.setlOptins.slice(0, 4) + '00';
                        return window.$national.tools.getCityAreaCodeWording(clrOptins, false, '');
                    })(),
                    clrOptins: '510000',
                    createParams: (item: any) => createMedicalRecordParams(item),
                    requestFetch: (params: any) => requestSettleDataByMedicalRecord(params),
                },
                {
                    medType: '12',
                    clrTypeWording: '门诊挂号（省内、省外）',
                    insutypeWording: '职工、居民',
                    clrOptinsWording: (() => {
                        const clrOptins = socialInfo.value.setlOptins.slice(0, 4) + '00';
                        return window.$national.tools.getCityAreaCodeWording(clrOptins, false, '');
                    })(),
                    clrOptins: '510000',
                    createParams: (item: any) => createMedicalRecordParams(item),
                    requestFetch: (params: any) => requestSettleDataByMedicalRecord(params),
                },
                {
                    medType: '11',
                    clrTypeWording: '普通门诊（省内、省外）',
                    insutypeWording: '职工、居民',
                    clrOptinsWording: (() => {
                        const clrOptins = socialInfo.value.setlOptins.slice(0, 4) + '00';
                        return window.$national.tools.getCityAreaCodeWording(clrOptins, false, '');
                    })(),
                    clrOptins: '510000',
                    createParams: (item: any) => createMedicalRecordParams(item),
                    requestFetch: (params: any) => requestSettleDataByMedicalRecord(params),
                },
                {
                    medType: '110102',
                    clrTypeWording: '共济下账（省内、省外）',
                    insutypeWording: '职工、居民',
                    disabledImport: true,
                    disabledImportTips: '异地共济缺少结算ID，无法导入',
                    clrOptinsWording: (() => {
                        const clrOptins = socialInfo.value.setlOptins.slice(0, 4) + '00';
                        return window.$national.tools.getCityAreaCodeWording(clrOptins, false, '');
                    })(),
                    clrOptins: '510000',
                    createParams: (item: any) => createMedicalRecordParams(item),
                    requestFetch: (params: any) => requestSettleDataByMedicalRecordGj(params),
                },
            ],
        },
        {
            mate: (params: any) => params.region === 'mountainwest_taiyuan',
            options: [
                {
                    clrType: '9950',
                    insutype: '310',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '99973',
                    insutype: '390',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '99915',
                    insutype: '310',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '99939',
                    insutype: '390',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '99972',
                    insutypeWording: '离休、一至六级残废军人',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '99922',
                    insutype: '310',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '99945',
                    insutype: '390',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '41',
                    insutype: '310',
                    clrOptins: '149900',
                    fetchTips: '省本级数据只限于多系统刷卡，对账失败后需要复位数据时才可拉取，省医保局复位后才能对账清算，是否继续？',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                    showConfig: {
                        isShowCopyBtn: true, // 是否显示复制按钮
                        isShowExportBtn: true, // 是否显示导出按钮
                    },
                    show: socialInfo.value.isHospitalDouble, // 省市双定点才能刷省卡
                },
                {
                    clrType: '11',
                    insutype: '310',
                    clrOptins: '149900',
                    fetchTips: '省本级数据只限于多系统刷卡，对账失败后需要复位数据时才可拉取，省医保局复位后才能对账清算，是否继续？',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                    showConfig: {
                        isShowCopyBtn: true, // 是否显示复制按钮
                        isShowExportBtn: true, // 是否显示导出按钮
                    },
                    show: socialInfo.value.isHospitalDouble, // 省市双定点才能刷省卡
                },
                {
                    clrType: '99952',
                    insutype: '310',
                    insutypeWording: '职工、居民',
                    clrOptins: '149900',
                    insuplcAdmdvs: '140000',
                    fetchTips: '省本级数据只限于多系统刷卡，对账失败后需要复位数据时才可拉取，省医保局复位后才能对账清算，是否继续？',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                    showConfig: {
                        isShowCopyBtn: true, // 是否显示复制按钮
                        isShowExportBtn: true, // 是否显示导出按钮
                    },
                    show: socialInfo.value.isHospitalDouble, // 省市双定点才能刷省卡
                },
            ],
        },
        {
            mate: (params: any) => params.region.startsWith('mountainwest_'),
            options: [
                {
                    clrType: '99915',
                    insutype: '310',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '99931',
                    insutype: '390',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '99939',
                    insutype: '390',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '99922',
                    insutype: '310',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '99945',
                    insutype: '390',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '99952',
                    insutype: '310',
                    insutypeWording: '职工、居民',
                    clrOptins: '140000',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                    show: socialInfo.value.isHospitalDouble, // 省市双定点才能刷省卡
                },
            ],
        },
        {
            mate: (params: any) => params.region.startsWith('liaoning_shenyang'),
            options: [
                {
                    clrType: '99959',
                    insutype: '310',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '99952',
                    insutype: '310',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '9914',
                    insutype: '310',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '99959',
                    insutype: '390',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '99939',
                    insutype: '390',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '99952',
                    insutype: '390',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '9914',
                    insutype: '390',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
            ],
        },
        {
            mate: (params: any) => params.region.startsWith('liaoning_dalian'),
            options: [
                {
                    clrType: '11',
                    insutype: '310',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '11',
                    insutype: '390',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
            ],
        },
        {
            mate: (params: any) => params.region.startsWith('liaoning_anshan'),
            options: [
                {
                    clrType: '11',
                    insutype: '310',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '99952',
                    insutype: '310',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
            ],
        },
        {
            mate: (params: any) => params.region.startsWith('liaoning_fushun'),
            options: [
                {
                    clrType: '11',
                    insutype: '310',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '11',
                    insutype: '390',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
            ],
        },
        {
            mate: (params: any) => params.region.startsWith('liaoning_jinzhou'),
            options: [
                {
                    clrType: '11',
                    insutype: '310',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '99939',
                    insutype: '310',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '99952',
                    insutype: '310',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '11',
                    insutype: '390',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '999391',
                    insutype: '390',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },

            ],
        },
        {
            mate: (params: any) => params.region.startsWith('liaoning_panjin'),
            options: [
                {
                    clrType: '11',
                    insutype: '310',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '21',
                    insutype: '310',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '99952',
                    insutype: '310',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '11',
                    insutype: '390',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '21',
                    insutype: '390',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '99952',
                    insutype: '390',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },

            ],
        },
        {
            mate: (params: any) => params.region.startsWith('liaoning_yingkou'),
            options: [
                {
                    clrType: '11',
                    insutype: '310',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '99952',
                    insutype: '310',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '11',
                    insutype: '390',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
            ],
        },
        {
            mate: (params: any) => params.region.startsWith('guangxi_nanning'),
            options: [
                {
                    clrType: '99970',
                    insutype: '310',
                    clrOptins: '459900',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataToGuangxi(params),
                },
                {
                    clrType: '99974',
                    insutype: '310',
                    clrOptins: '459900',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataToGuangxi(params),
                },
                {
                    clrType: '99980',
                    insutype: '310',
                    clrOptins: '459900',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataToGuangxi(params),
                },
                {
                    clrType: '99972',
                    insutype: '340',
                    clrOptins: '459900',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataToGuangxi(params),
                },
                {
                    clrType: '99976',
                    insutype: '350',
                    clrOptins: '459900',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataToGuangxi(params),
                },
                {
                    clrType: '99975',
                    insutype: '390',
                    clrOptins: '459900',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataToGuangxi(params),
                },
                {
                    clrType: '99970',
                    insutype: '310',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataToGuangxi(params),
                },
                {
                    clrType: '99974',
                    insutype: '310',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataToGuangxi(params),
                },
                {
                    clrType: '99980',
                    insutype: '310',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataToGuangxi(params),
                },
                {
                    clrType: '99972',
                    insutype: '340',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataToGuangxi(params),
                },
                {
                    clrType: '99976',
                    insutype: '340',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataToGuangxi(params),
                },
                {
                    clrType: '99971',
                    insutype: '390',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataToGuangxi(params),
                },
                {
                    clrType: '99975',
                    insutype: '390',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataToGuangxi(params),
                },
                {
                    clrType: '99981',
                    insutype: '390',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataToGuangxi(params),
                },
            ],
        },
        {
            mate: (params: any) => params.region.startsWith('guangxi_'),
            options: [
                {
                    clrType: '99970',
                    insutype: '310',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataToGuangxi(params),
                },
                {
                    clrType: '99974',
                    insutype: '310',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataToGuangxi(params),
                },
                {
                    clrType: '99980',
                    insutype: '310',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataToGuangxi(params),
                },
                {
                    clrType: '99972',
                    insutype: '340',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataToGuangxi(params),
                },
                {
                    clrType: '99976',
                    insutype: '340',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataToGuangxi(params),
                },
                {
                    clrType: '99971',
                    insutype: '390',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataToGuangxi(params),
                },
                {
                    clrType: '99975',
                    insutype: '390',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataToGuangxi(params),
                },
                {
                    clrType: '99981',
                    insutype: '390',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataToGuangxi(params),
                },
            ],
        },
        {
            mate: (params: any) => params.region.startsWith('shanxi_xian'),
            options: [
                {
                    clrType: '11',
                    insutype: '310',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataToGuangxi(params),
                },
                {
                    clrType: '11',
                    insutype: '390',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataToGuangxi(params),
                },
                {
                    clrType: '41',
                    insutype: '310',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataToGuangxi(params),
                },
                {
                    clrType: '41',
                    insutype: '390',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataToGuangxi(params),
                },
            ],
        },
        {
            mate: (params: any) => params.region.startsWith('shanxi_tongchuan'),
            options: [
                {
                    clrType: '11',
                    insutype: '310',
                    clrOptins: '610299',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataToGuangxi(params),
                },
            ],
        },
        {
            mate: (params: any) => params.region.startsWith('shanxi_'),
            options: [
                {
                    insutype: '310',
                    clrType: '11',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataToGuangxi(params),
                },
                {
                    insutype: '390',
                    clrType: '11',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataToGuangxi(params),
                },
            ],
        },
        {
            mate: (params: any) => params.region.startsWith('shandong_qingdao'),
            options: [
                {
                    clrType: '11',
                    clrTypeWording: '全部（不含无需对账）',
                    insutype: '310',
                    insutypeWording: '职工、居民',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
            ],
        },
        {
            mate: (params: any) => params.region.startsWith('zhejiang_'),
            options: [
                {
                    clrType: '11',
                    insutype: '310',
                    insutypeWording: '职工、居民',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '1402',
                    insutype: '310',
                    insutypeWording: '职工、居民',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                },
                {
                    clrType: '11',
                    insutype: '310',
                    insutypeWording: '职工',
                    clrOptins: '519900',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                    show: socialInfo.value.isHospitalDouble, // 省市双定点才能刷省卡
                },
                {
                    clrType: '1402',
                    insutype: '310',
                    insutypeWording: '职工',
                    clrOptins: '519900',
                    createParams: (item: any) => createCheckingDetailParams(item),
                    requestFetch: (params: any) => requestSettleDataByCheckingDetail(params),
                    show: socialInfo.value.isHospitalDouble, // 省市双定点才能刷省卡
                },
                {
                    clrTypeWording: '省内、省外',
                    insutypeWording: '职工、居民',
                    createParams: () => createParamsToZhejiangYD(),
                    requestFetch: (params: any) => requestSettleDataToZhejiangYD(params),
                    show: socialInfo.value.isHospitalDouble, // 省市双定点才能刷省卡
                },
            ],
        },
    ];

    return {
        loadingModelQuery,
        dialogModelDataList,
        toolsParams,
        formData,
        mdtrtareaAdmvsOptions,
        initFormData,
        requestFetchRecord,
        requestSettleDataImport,
    };
};