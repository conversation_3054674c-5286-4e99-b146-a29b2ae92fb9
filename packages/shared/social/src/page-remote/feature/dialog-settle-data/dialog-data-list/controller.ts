/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import AbcResponse from '../../../../common/AbcResponse';
import { computed, reactive } from 'vue';
import * as utils from '../../../../common/utils';

import createPageModel from '../../../../model/page';

export const createDataListController = (props: any) => {
    const pageModel = createPageModel();

    const toolsParams = reactive({
        source: 0,
    });

    // 来源选项
    const sourceOptions = [
        { value: 0, label: '中心全部数据' },
        { value: 1, label: 'ABC系统数据' },
        { value: 2, label: '其他系统数据' },
        { value: 3, label: '未知数据（共济）' },
    ];

    // 匹配数据
    let cacheToolsParams = {};
    const mateDataList = computed(() => {
        const dataList = props.dataList
                        .map((item: any) => ({
                            ...item,
                            source: (() => {
                                const iptOtpNo = item?.medicalInfo?.ipt_otp_no || '';
                                if (!iptOtpNo) {
                                    return 3;
                                }
                                return iptOtpNo.startsWith('abc-') ? 1 : 2;
                            })(),
                        }))
                        .filter((item: any) => {
                            if (toolsParams.source === 0) {
                                return true;
                            }
                            return toolsParams.source === item.source;
                        })
                        .sort((a: any, b: any) => {
                            const aTime = a.setlinfo?.setl_time ? new Date(a.setlinfo.setl_time).getTime() : 0;
                            const bTime = b.setlinfo?.setl_time ? new Date(b.setlinfo.setl_time).getTime() : 0;
                            return aTime < bTime ? -1 : 1;
                        });
        pageModel.setTotal(dataList.length);
        if (!utils.isEqual(cacheToolsParams, toolsParams)) {
            pageModel.setPage(1);
            cacheToolsParams = utils.cloneDeep(toolsParams);
        }
        return dataList;
    });

    // 汇总数据
    const summaryData = computed(() => mateDataList.value.reduce((summaryData: any, item: any) => {
        summaryData.medfeeSumamt = utils.add(summaryData.medfeeSumamt, item.setlinfo?.medfee_sumamt || item.medfeeSumamt || 0);
        summaryData.fundPaySumamt = utils.add(summaryData.fundPaySumamt, item.setlinfo?.fund_pay_sumamt || item.fundPaySumamt || 0);
        const acctPay = (() => {
            let acctPay = item.setlinfo ? item.setlinfo.acct_pay : item.acctPay;
            if (item.wltpayAmt > 0) {
                acctPay = utils.add(acctPay, item.wltpayAmt);
            }
            if (utils.isEmpty(acctPay)) {
                return '';
            }
            return utils.moneyStr(acctPay);
        })()
        summaryData.acctPay = utils.add(summaryData.acctPay, acctPay || 0);
        summaryData.cashPayamt = utils.add(summaryData.cashPayamt, item.setlinfo?.cash_payamt || 0);
        return summaryData;
    }, {
        medfeeSumamt: 0,
        fundPaySumamt: 0,
        acctPay: 0,
        cashPayamt: 0,
    }));

    // 展示数据
    const showDataList = computed(() => {
        const { sIndex, eIndex } = pageModel.createSliceParams();
        return mateDataList.value
                        .slice(sIndex, eIndex)
                        .map((item: any) => {
                            const wltpayAmt = item.wltpayAmt || 0;
                            const itemInfo = {
                                psnName: item.setlinfo?.psn_name || '', // 人员姓名
                                medfeeSumamt: (() => {
                                    const medfeeSumamt = item.setlinfo ? item.setlinfo.medfee_sumamt : item.medfeeSumamt;
                                    if (utils.isEmpty(medfeeSumamt)) {
                                        return '';
                                    }
                                    return utils.moneyStr(medfeeSumamt);
                                })(), // 医疗费用总额
                                fundPaySumamt: (() => {
                                    const fundPaySumamt = item.setlinfo ? item.setlinfo.fund_pay_sumamt : item.fundPaySumamt;
                                    if (utils.isEmpty(fundPaySumamt)) {
                                        return '';
                                    }
                                    return utils.moneyStr(fundPaySumamt);
                                })(), // 基金支付总额
                                acctPay: (() => {
                                    let acctPay = item.setlinfo ? item.setlinfo.acct_pay : item.acctPay;
                                    if (wltpayAmt > 0) {
                                        acctPay = utils.add(acctPay, wltpayAmt);
                                    }
                                    if (utils.isEmpty(acctPay)) {
                                        return '';
                                    }
                                    return utils.moneyStr(acctPay);
                                })(), // 个人账户总额
                                cashPayamt: (() => {
                                    const cashPayamt = item.setlinfo ? item.setlinfo.cash_payamt : item.cashPayamt;
                                    if (utils.isEmpty(cashPayamt)) {
                                        return '';
                                    }
                                    return utils.moneyStr(cashPayamt);
                                })(), // 现金支付总额
                                psnNo: item.setlinfo?.psn_no || item.psnNo || '', // 人员编号
                                mdtrtId: item.setlinfo?.mdtrt_id || item.mdtrtId || '', // 就诊ID
                                setlId: item.setlinfo?.setl_id || item.setlId || '', // 结算ID
                                setlTime: (() => {
                                    const setlTime = item.setlinfo?.setl_time || item.setlTime;
                                    return setlTime ? utils.createDateTimeFormat19(setlTime) : '';
                                })(), // 结算时间
                                refdSetlFlag: (() => {
                                    const refdSetlFlag = item.setlinfo ? item.setlinfo.refd_setl_flag : item.refdSetlFlag;
                                    if (refdSetlFlag === '0') {
                                        return '否';
                                    }
                                    if (refdSetlFlag === '1') {
                                        return '是';
                                    }
                                    return '';
                                })(), // 退费结算标志
                                sourceWording: sourceOptions.find((one: any) => one.value === item.source)?.label,
                                acctPayTips: (() => {
                                    if (wltpayAmt > 0) {
                                        return `包含医保钱包支付金额 ${utils.moneyStr(wltpayAmt)}`;
                                    }
                                    return '';
                                })(),
                            };
                            console.log('itemInfo', itemInfo);
                            return itemInfo;
                        });
    });

    /**
     * 请求复制数据 - 山西
     * <AUTHOR>
     * @date 2025-01-13
     * @returns {Promise<AbcResponse>}
     */
    const requestCopyDataMountainwest = () => {
        // '清算类别', '结算ID', '姓名', '总费用', '错误类型'
        const data = props.dataList.map((item: any) => [
            item.setlinfo?.clr_type || '', // 清算类别
            item.setlinfo?.setl_id || '', // 结算ID
            item.setlinfo?.psn_name || '', // 姓名
            item.setlinfo?.medfee_sumamt || '', // 总费用
            '多系统刷卡', // 错误类型
        ].join('\t')).join('\n');
        utils.copy(data);
        return AbcResponse.success();
    };

    /**
     * 请求导出数据 - 山西
     * <AUTHOR>
     * @date 2025-01-13
     * @returns {Promise<AbcResponse>}
     */
    const requestExportDataMountainwest = () => {
        const dataList = props.dataList.map((item: any) => ({
            清算类别: item.setlinfo?.clr_type || '', // 清算类别
            结算ID: item.setlinfo?.setl_id || '', // 结算ID
            姓名: item.setlinfo?.psn_name || '', // 姓名
            总费用: item.setlinfo?.medfee_sumamt || '', // 总费用
            错误类型: '多系统刷卡', // 错误类型
        }));
        const fileName = (() => {
            const name = props.socialInfo.basicInfo?.hospitalCode || '';
            const type = dataList[0]?.清算类别 || '';
            return `省医保复位数据：${name}_${type}`;
        })();
        utils.createExcelFile(dataList, fileName);
        return AbcResponse.success();
    };

    return {
        pageModel,
        toolsParams,
        sourceOptions,
        showDataList,
        summaryData,
        requestCopyDataMountainwest,
        requestExportDataMountainwest,
    };
};