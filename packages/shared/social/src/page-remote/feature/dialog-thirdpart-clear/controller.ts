/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import dayjs from 'dayjs';
import { reactive } from 'vue';
import * as utils from '../../../common/utils';

import createLoadingModel from '../../../model/loading';

export const createThirdpartClearController = (props: any) => {
    const loadingModelClear = createLoadingModel();

    const formData = reactive({
        month: utils.createMonthFormat(), // 月份
        clearMode: 0, // 清除模式，0 只清理未同步数据，1 清理所有数据
    });

    const clearModeOptions = [
        {
            value: 0,
            label: '清理未同步数据',
        },
        {
            value: 1,
            label: '清理所有数据',
        },
    ];

    /**
     * 创建post数据
     * <AUTHOR>
     * @date 2025-06-19
     * @returns {Object}
     */
    const createPostData = () => {
        const {
            month,
            clearMode,
        } = formData;
        const sDate = dayjs(`${month}-01`); // 开始日期
        const eDate = dayjs(`${month}-01`).endOf('month'); // 截止日期
        const postData = {
            beginDate: utils.createDateFormat(sDate),
            endDate: utils.createDateFormat(eDate),
            clearMode, // 清除模式，0 只清理未同步数据，1 清理所有数据
        };
        return postData;
    };

    /**
     * 请求三方数据清理
     * <AUTHOR>
     * @date 2025-06-19
     * @param {Object} data
     * @returns {Promise<AbcResponse>}
     */
    const requestThirdpartClear = async (data: any) => {
        const js = `
            const data = ${JSON.stringify(data)}
            response = await window.$national.api.thirdpartClear(data)
        `;
        const response = await utils.remoteRunJs(props.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    return {
        loadingModelClear,
        formData,
        clearModeOptions,
        createPostData,
        requestThirdpartClear,
    };
};