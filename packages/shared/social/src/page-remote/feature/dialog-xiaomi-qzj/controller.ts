/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import * as utils from '../../../common/utils';

import createLoadingModel from '../../../model/loading';

export const createXiaomiQzjController = (props: any) => {
    const loadingModelRestart = createLoadingModel();

    /**
     * 请求重启小米前置机
     * <AUTHOR>
     * @date 2024-08-07
     * @returns {Promise<AbcResponse>}
     */
    const requestRestartXiaomiQzj = async () => {
        const js = `
            response = await window.$national.tools.resetartAbcGateWayDevice()
            await window.$national.tools.delayPromise(3000)
        `;
        const response = await utils.remoteRunJs(props.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    return {
        loadingModelRestart,
        requestRestartXiaomiQzj,
    };
};