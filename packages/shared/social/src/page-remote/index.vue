<template>
    <div class="social-module__page-remote">
        <div class="left-wrapper">
            <el-input
                v-model="queryParams.keywords"
                class="search-wrapper"
                :suffix-icon="Search"
                clearable
                placeholder="clinicId/门店名"
                @input="_onChangeKeywords"
            />
            <div
                v-loading="loadingModelSearch.loading.value"
                class="search-result"
            >
                <ul
                    v-if="showDataList.length !== 0"
                    v-infinite-scroll="onLoadMoreSearchResult"
                    class="infinite-list"
                    style="overflow: auto;"
                >
                    <li 
                        v-for="item in showDataList"
                        :key="item.id"
                        :class="{
                            'item-wrapper': true,
                            'is-selected': item.isSelected,
                            'is-disabled': item.status === 0
                        }"
                        @click="onClickSelectRemote(item)"
                    >
                        <div class="one">
                            <span
                                class="name explain-wording"
                                :title="item.clinicName"
                            >
                                <span class="lab">门店名：</span>
                                <span class="bold">{{ item.clinicName }}</span>
                            </span>
                        </div>
                        <div class="two">
                            <span>
                                <span class="lab">设备码：</span>
                                <em class="device-code">{{ item.identity }}</em>
                            </span>
                            <div class="track"></div>
                            <el-tag
                                v-if="item.isSocialDevice"
                                class="status"
                                type="primary"
                            >
                                医保
                            </el-tag>
                            <el-tag
                                class="status"
                                :type="item.statusType"
                            >
                                {{ item.statusWording }}
                            </el-tag>
                        </div>
                    </li>
                </ul>
                <div v-else class="kong">
                    <span>暂无数据</span>
                    <span v-if="!queryParams.keywords">请输入搜索关键词</span>
                </div>
            </div>
        </div>
        <el-scrollbar
            v-loading="loadingModelTarget.loading.value"
            class="content-wrapper"
        >
            <template v-if="!!target.socialInfo">
                <el-descriptions
                    title="门店信息"
                    :column="3"
                    border
                    style="margin-top: 16px;"
                >
                    <template #extra>
                        <el-button
                            v-if="displayInfo.clinicId"
                            type="primary"
                            plain
                            class="link-btn-wrapper"
                            @click="onClickLoginClinic"
                        >
                            登录门店
                        </el-button>
                        <el-button 
                            type="primary"
                            @click="onClickRefresh"
                        >
                            刷新
                        </el-button>
                    </template>
                    <el-descriptions-item label="医保电脑">
                        <el-tag
                            size="small"
                            :type="displayInfo.socialCom.type"
                        >
                            {{ displayInfo.socialCom.text }}
                        </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="网络模式">{{ displayInfo.networkMode }}</el-descriptions-item>
                    <el-descriptions-item label="系统类型">{{ displayInfo.systemType }}</el-descriptions-item>
                    <el-descriptions-item label="开通状态">
                        <el-tag
                            size="small"
                            :type="displayInfo.openStatus.type"
                        >
                            {{ displayInfo.openStatus.text }}
                        </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="研发人员">{{ displayInfo.engineer }}</el-descriptions-item>
                    <el-descriptions-item label="运行模式">{{ displayInfo.runMode }}</el-descriptions-item>
                    <el-descriptions-item label="所属省级">{{ displayInfo.provinceName }}</el-descriptions-item>
                    <el-descriptions-item label="所属市级">{{ displayInfo.regionName }}</el-descriptions-item>
                    <el-descriptions-item label="经办机构">{{ displayInfo.setlOptins }}</el-descriptions-item>
                    <el-descriptions-item label="机构类型">{{ displayInfo.hospitalType }}</el-descriptions-item>
                    <el-descriptions-item label="机构代码">{{ displayInfo.hospitalCode }}</el-descriptions-item>
                    <el-descriptions-item label="机构名称">{{ displayInfo.hospitalName }}</el-descriptions-item>
                    <el-descriptions-item label="地区标识">{{ displayInfo.region }}</el-descriptions-item>
                    <el-descriptions-item label="协议类型">{{ displayInfo.protocolType }}</el-descriptions-item>
                    <el-descriptions-item label="接口厂商">{{ displayInfo.apiVersion }}</el-descriptions-item>
                </el-descriptions>
                <div v-if="!!target.$national" class="feature-wrapper">
                    <div
                        v-for="item in featureList"
                        :key="item.id"
                        class="item-wrapper"
                    >
                        <span class="name">{{ item.name }}</span>
                        <el-button type="primary" plain @click="onClickOpenFeature(item)">打开功能</el-button>
                    </div>
                </div>
            </template>
            <template v-else>
                <div class="kong">
                    <span>请选择远端设备</span>
                </div>
            </template>
        </el-scrollbar>
        <component
            :is="FeatureComponent"
            v-if="dialogModelFeature.visible.value"
            v-model="dialogModelFeature.visible.value"
            :device-code="deviceCode"
            :social-info="target.socialInfo"
        ></component>
    </div>
</template>

<script lang="ts" setup>
import * as utils from '../common/utils';
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { quickLoginAbc } from '@abc-oa/utils/src/utils';
import { useDebounceFn } from '@vueuse/core';
import { createPageRemoteController } from './controller';

const router = useRouter();

const {
    dialogModelFeature,
    loadingModelSearch,
    loadingModelTarget,
    queryParams,
    queryResponse,
    showDataList,
    target,
    deviceCode,
    region,
    featureList,
    displayInfo,
    createParams,
    requestDeviceList,
    requestSocialDevice,
    requestLoginClinicHref,
} = createPageRemoteController();

const FeatureComponent = ref(null);

onMounted(() => {
    // 通过路由传参，作为关键词搜索
    const clinicId = router.currentRoute.value.query?.clinicId || '';
    queryParams.keywords = clinicId as string;
    if (queryParams.keywords) {
        onChangeKeywords();
    }
});

/**
 * 当改变搜索关键词时
 * <AUTHOR>
 * @date 2024-08-06
 */
const onChangeKeywords = async () => {
    loadingModelSearch.setLoading(true);
    const params = createParams();
    const response1 = await requestDeviceList(params);
    if (!response1 || !utils.isEqual(params, createParams())) {
        return response1;
    }
    if (response1.status === false) {
        ElMessage.error('拉取数据失败: ' + response1.message);
        return response1;
    }
    const {
        clinicIds,
        deviceList,
    } = response1.data;
    const response2 = await requestSocialDevice(clinicIds);
    loadingModelSearch.setLoading(false);
    queryResponse.originData = {
        clinicIds,
        deviceList,
        deviceSocial: response2.data?.deviceSocial || [],
    };
    return queryResponse;
};
const _onChangeKeywords = useDebounceFn(onChangeKeywords, 600);

/**
 * 当加载更多搜索结果
 * <AUTHOR>
 * @date 2024-08-06
 */
const onLoadMoreSearchResult = async () => {
    // 暂时不用
};

/**
 * 当点击选择远端时
 * <AUTHOR>
 * @date 2024-08-14
 * @param {Object} item
 */
const onClickSelectRemote = async (item: any) => {
    if (item.status === 0) {
        return ElMessage.error('提示: 离线设备不允许选中');
    }
    loadingModelTarget.setLoading(true);
    target.remoteInfo = item;
    const response = await utils.requestSocialInfo(deviceCode.value);
    loadingModelTarget.setLoading(false);
    if (response.status === false) {
        ElMessage.error('拉取数据失败: ' + response.message);
        return response;
    }
    target.socialInfo = response.data;
    target.$national = null;
    const localSocialResponse = await utils.requestLoadSocial(region.value);
    if (localSocialResponse.status === false) {
        ElMessage.error('拉取$national失败: ' + localSocialResponse.message);
        return localSocialResponse;
    }
    target.$national = window.$national;
};

/**
 * 当点击登录门店
 * <AUTHOR>
 * @date 2025-01-03
 */
const onClickLoginClinic = async () => {
    const response = await requestLoginClinicHref(displayInfo.value.clinicId);
    if (response.status === false) {
        return ElMessage.error(response.message);
    }
    quickLoginAbc(response.data);
};

/**
 * 当点击刷新时
 * <AUTHOR>
 * @date 2024-08-14
 */
const onClickRefresh = () => {
    onClickSelectRemote(target.remoteInfo);
};

/**
 * 当点击打开功能时
 * <AUTHOR>
 * @date 2024-08-07
 * @param {Object} item
 */
const onClickOpenFeature = (item: any) => {
    if (
        item.checkSocialReg
            && !target.socialInfo.region
    ) {
        return ElMessage.error('功能不可用：当前门店没有region，或地区医保未对接');
    }
    if (
        item.checkSoicalOpe
            && !target.socialInfo.isOpenSocial
    ) {
        return ElMessage.error('功能不可用：当前门店还未开通医保');
    }
    if (
        item.checkSocialCom
            && !target.socialInfo.isSocialCom
    ) {
        return ElMessage.error('功能不可用：当前电脑不是医保电脑');
    }
    FeatureComponent.value = item.Component;
    dialogModelFeature.show();
};
</script>

<style lang="scss">
    .social-module__page-remote {
        display: flex;
        align-items: stretch;
        height: calc(100vh - 56px);
        background-color: #fff;

        .explain-wording {
            display: inline-block;
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: keep-all;
            white-space: nowrap;
        }

        .left-wrapper {
            width: 300px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
            background-color: #fff;
            padding: 16px;
            border-right: 1px solid #e0e5ee;

            .search-result {
                flex: 1;
                width: 100%;
                height: 100%;
                overflow: hidden;
                margin-top: 6px;

                .infinite-list {
                    height: 100%;
                }

                .item-wrapper {
                    height: 68px;
                    border-radius: 4px;
                    margin-top: 8px;
                    cursor: pointer;
                    border: 1px solid #dedfe0;
                    box-sizing: border-box;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    padding: 0 6px;

                    div {
                        line-height: 22px;
                    }

                    .lab {
                        color: #9c9d9f;
                    }

                    .bold {
                        font-weight: bold;
                    }

                    .device-code {
                        font-size: 16px;
                        color: #409eff;
                    }

                    .two {
                        display: flex;
                        justify-content: space-between;

                        .track {
                            flex: 1;
                        }

                        .el-tag {
                            margin-left: 4px;
                        }
                    }

                    &:hover {
                        background: #d9ecff;
                        border: 1px solid #a0cfff;
                    }

                    &.is-selected {
                        background: #d9ecff;
                        border: 1px solid #a0cfff;
                    }

                    &.is-disabled {
                        cursor: no-drop;
                    }
                }

                .kong {
                    margin-top: 120px;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;

                    span {
                        color: #aab4bf;
                        font-size: 12px;
                        line-height: 20px;
                    }
                }
            }
        }

        .content-wrapper {
            flex: 1;
            background-color: #fff;
            border-radius: 6px;
            padding: 0 16px;

            .kong {
                margin-top: 155px;
                display: flex;
                justify-content: center;
                align-items: center;

                span {
                    color: #aab4bf;
                    font-size: 12px;
                    line-height: 20px;
                }
            }

            .feature-wrapper {
                display: flex;
                flex-wrap: wrap;
                align-items: center;
                margin-bottom: 16px;

                .item-wrapper {
                    width: 168px;
                    padding: 12px 0;
                    margin-right: 12px;
                    margin-top: 12px;
                    border: 1px dashed #dadbe0;
                    border-radius: 2px;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;

                    .name {
                        font-size: 14px;
                        font-weight: bold;
                    }

                    .el-button {
                        margin-top: 12px;
                    }

                    &:hover {
                        background-color: rgba(0, 0, 0, .04);
                    }
                }
            }
        }
    }
</style>