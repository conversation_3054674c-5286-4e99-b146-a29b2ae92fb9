<template>
    <div class="social-module__page-version_chart-wrapper">
        <el-row style="width: 100%;" :gutter="24">
            <el-col :span="6" class="chart-container" v-for="(item, index) in dllNameList" :key="index">
                <h3>{{ item }}</h3>
                <canvas ref="chartRefs"></canvas>
            </el-col>
        </el-row>
    </div>
</template>

<script setup lang="ts">
    import { createPageVersionController } from "../controller";
    import {onMounted, ref, reactive, watch, computed,} from "vue";
    import VersionPieChartConfig from "../chart/VersionBeaChartConfig";

    const chartRefs = ref(null);
    const emit = defineEmits(['onClickChartItem']);

    const myChartMap = new Map()

    const {
        createChartViewController,
    } = createPageVersionController();

    const props = defineProps({
        originalList: {
            type: Array,
            required: true,
        },
    })

    const statisticData = computed(() => {
        return analyzeInstallData(props.originalList).byLibrary
    })

    const dllNameList = computed(() => {
        return Object.keys(statisticData.value)
    })

    const createChartView = () => {
        const dllNameListValue = dllNameList.value
        for (let i = 0; i < dllNameListValue.length; i++) {
            const key = dllNameListValue[i];
            const data = statisticData.value[key];
            const config = new VersionPieChartConfig({
                labels: Object.keys(data),
                datasets: [
                    {
                        data: Object.values(data).map(item => ({
                            count: item.deviceCount,
                            '门店数': item.clinicCount,
                        })),
                        hoverOffset: 4
                    }
                ],
            }, {
                title: key
            }).build();
            myChartMap.set(key, createChartViewController(chartRefs.value[i], config))
        }
    }

    onMounted(() => {
        createChartView()
    })




    const analyzeInstallData = (data) => {
        // 第一步：按 exeLibName 进行分组
        const groupedByExeLibName = data.reduce((result, item) => {
            if (!result[item.exeLibName]) {
                result[item.exeLibName] = [];
            }
            result[item.exeLibName].push(item);
            return result;
        }, {});

        // 第二步：按 exeLibVersion 进一步分组并统计数量
        const statistics = {};

        for (const exeLibName in groupedByExeLibName) {
            if (!statistics[exeLibName]) {
                statistics[exeLibName] = {};
            }

            // 按 exeLibVersion 分组
            const versionGroups = groupedByExeLibName[exeLibName].reduce((result, item) => {
                if (!result[item.exeLibVersion]) {
                    result[item.exeLibVersion] = [];
                }
                result[item.exeLibVersion].push(item);
                return result;
            }, {});

            // 统计每个版本的设备数量和门店数量
            for (const version in versionGroups) {
                // 获取不重复的设备ID
                const uniqueDeviceIds = new Set(versionGroups[version].map(item => item.deviceId));

                // 获取不重复的门店ID
                const uniqueClinicIds = new Set(versionGroups[version].map(item => item.clinicId));

                // 按门店ID对设备进行分组
                const clinicDevices = {};
                versionGroups[version].forEach(item => {
                    if (!clinicDevices[item.clinicId]) {
                        clinicDevices[item.clinicId] = {
                            clinicName: item.clinicName,
                            devices: new Set()
                        };
                    }
                    clinicDevices[item.clinicId].devices.add(item.deviceId);
                });

                // 转换为更易读的格式
                const clinicsWithDevices = Object.keys(clinicDevices).map(clinicId => ({
                    clinicId,
                    clinicName: clinicDevices[clinicId].clinicName,
                    deviceCount: clinicDevices[clinicId].devices.size,
                    deviceIds: Array.from(clinicDevices[clinicId].devices)
                }));

                statistics[exeLibName][version] = {
                    deviceCount: uniqueDeviceIds.size,
                    clinicCount: uniqueClinicIds.size,
                    deviceIds: Array.from(uniqueDeviceIds),
                    clinicIds: Array.from(uniqueClinicIds),
                    clinics: clinicsWithDevices
                };
            }
        }

        // 第三步：生成符合 DeviceStat 和 ClinicStat 接口的数组
        const result = {
            // 保留原始结构供详细分析
            byLibrary: statistics,

            // 按库名和版本分组的扁平结构
            summary: [],

            // 符合接口的统计数组
            deviceStats: [],
            clinicStats: []
        };

        // 生成扁平化的摘要
        for (const libName in statistics) {
            for (const version in statistics[libName]) {
                const stats = statistics[libName][version];

                result.summary.push({
                    exeLibName: libName,
                    version: version,
                    deviceCount: stats.deviceCount,
                    clinicCount: stats.clinicCount,
                });

                // 符合 DeviceStat 接口的数据
                result.deviceStats.push({
                    count: stats.deviceCount,
                    version: version,
                    exeLibName: libName
                });

                // 符合 ClinicStat 接口的数据
                result.clinicStats.push({
                    count: stats.clinicCount,
                    version: version,
                    exeLibName: libName
                });
            }
        }

        return result;
    };
</script>

<style scoped lang="scss">
    .social-module__page-version_chart-wrapper {
        width: 100%;
        height: 100%;
        overflow-y: auto;
        display: flex;
        justify-content: space-between;

        .chart-container {
            box-sizing: border-box;
            height: 600px;

            h3 {
                text-align: center;
                margin-top: 0;
                margin-bottom: 10px;
                font-size: 16px;
                color: #606266;
            }

            canvas {
                flex: 1;
                width: 100% !important;
                height: calc(100% - 30px) !important;
            }
        }
    }
</style>
