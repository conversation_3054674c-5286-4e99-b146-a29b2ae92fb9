<template>
    <div>
        <el-table :data="showDataList" style="width: 100%;" v-loading="loading" row-key="id" height="450">
            <el-table-column
                prop="clinicId"
                label="门店ID"
                width="300"
                fixed
                show-overflow-tooltip
            />
            <el-table-column
                prop="clinicName"
                label="门店名称"
                width="300"
                fixed
                show-overflow-tooltip
            />
            <el-table-column
                prop="deviceId"
                label="设备码"
                show-overflow-tooltip
                width="200"
            />
            <el-table-column
                prop="exeLibName"
                label="动态库名称"
                show-overflow-tooltip
                width="200"
            />
            <el-table-column
                prop="exeLibVersion"
                label="当前版本"
                show-overflow-tooltip
            />
            <el-table-column
                prop="isLocked"
                label="锁定状态"
                show-overflow-tooltip
            >
                <template #default="scope">
                    <el-tag v-if="scope.row.locked" type="info">已锁定</el-tag>
                    <el-tag v-else type="success">未锁定</el-tag>
                </template>
            </el-table-column>
            <el-table-column
                prop="installDate"
                label="安装时间"
                show-overflow-tooltip
            >
                <template #default="scope">
                    {{ dayjs(scope.row.installDate).format('YYYY-MM-DD HH:mm:ss') }}
                </template>
            </el-table-column>
            <el-table-column
                prop="created"
                label="打包时间"
                show-overflow-tooltip
            >
                <template #default="scope">
                    {{ dayjs(scope.row.created).format('YYYY-MM-DD HH:mm:ss') }}
                </template>
            </el-table-column>
            <el-table-column
                prop="remark"
                label="操作"
                fixed="right"
            >
                <template #default="scope">
                    <div style="display: flex; align-items: center;">
                        <el-button
                            size="small"
                            plain
                            type="primary"
                            :loading="scope.row.loadingDelete"
                            @click="onClickLockItem(scope.row)"
                        >
                            {{ scope.row.locked ? '解锁' : '锁定' }}
                        </el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>

        <el-pagination
            style="margin-top: 14px;"
            v-model:current-page="pageCurrent"
            :page-size="pageSize"
            :total="total"
            @current-change="emit('pageChange', $event)"
            background
            layout="total, prev, pager, next"
        />
    </div>
</template>

<script setup lang="ts">
    import { computed } from "vue";
    import dayjs from 'dayjs';

    const props = defineProps({
        loading: {
            type: Boolean,
            default: false,
        },
        currentPage: {
            type: Number,
            isRequired: true,
        },
        pageSize: {
            type: Number,
            isRequired: true,
        },
        showDataList: {
            type: Array,
            isRequired: true,
        },
        total: {
            type: Number,
            default: 0,
        }
    });

    const emit = defineEmits([
        'update:currentPage',
        'changeLockStatus',
        'pageChange',
    ])

    const pageCurrent = computed({
        get() {
            return props.currentPage;
        },
        set(val: number) {
            emit('update:currentPage', val);
        }
    });

    const onClickLockItem = async (row: any) => {
        emit('changeLockStatus', row);
    }

</script>

<style scoped lang="scss">
.el-pagination {
    justify-content: flex-end;
}
</style>
