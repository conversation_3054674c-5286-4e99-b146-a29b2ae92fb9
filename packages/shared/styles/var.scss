:root {
    // Color Palette
    --oa-black: #000;
    --oa-white: #fff;
    --oa-gray-1: #f6f6f6;
    --oa-gray-2: #f2f4f7;
    --oa-gray-3: #f0f0f0;
    --oa-gray-4: #eff3f6;
    --oa-gray-5: #c8c9cc;
    --oa-gray-6: #9c9d9f;
    --oa-gray-7: #7a8794;
    --oa-gray-8: #333;
    --oa-gray-9: #e0e5ee;
    --oa-red: #ff7d7d;
    --oa-red-light: #ee9d9d;
    --oa-blue: #287ef1;
    --oa-blue-2: #459eff;
    --oa-blue-light: #00baf7;
    --oa-orange: #f06833;
    --oa-green: #36b090;

    // Component Colors
    --oa-primary-color: var(--oa-blue);
    --oa-success-color: var(--oa-green);
    --oa-danger-color: var(--oa-red);
    --oa-warning-color: var(--oa-orange);
    --oa-text-color: var(--oa-gray-8);
    --oa-text-color-2: var(--oa-gray-7);
    --oa-text-color-3: var(--oa-gray-6);
    --oa-text-link-color: var(--oa-blue-light);
    --oa-active-color: var(--oa-gray-2);
    --oa-active-opacity: .6;
    --oa-disabled-opacity: .5;
    --oa-background-color: var(--oa-gray-1);
    --oa-background-color-light: var(--oa-white);

    // Padding
    --oa-padding-4: 4px;
    --oa-padding-8: 8px;
    --oa-padding-12: 12px;
    --oa-padding-14: 14px;
    --oa-padding-16: 16px;
    --oa-padding-24: 24px;
    --oa-padding-32: 32px;
    --oa-page-padding: var(--oa-padding-16);
    --oa-section-padding: var(--oa-padding-12);
    --oa-page-element-padding: var(--oa-padding-12) 20px;

    // Font
    --oa-font-size-10: 10px;
    --oa-font-size-12: 12px;
    --oa-font-size-14: 14px;
    --oa-font-size-16: 16px;
    --oa-font-size-18: 18px;
    --oa-font-size-20: 20px;
    --oa-font-size-22: 22px;
    --oa-font-size-24: 24px;
    --oa-font-weight-bold: 500;
    --oa-line-height-xs: 14px;
    --oa-line-height-sm: 18px;
    --oa-line-height-md: 20px;
    --oa-line-height-lg: 22px;
    --oa-base-font-family:
        -apple-system,
        blinkmacsystemfont,
        'Helvetica Neue',
        helvetica,
        segoe ui,
        arial,
        roboto,
        'PingFang SC',
        'miui',
        'Hiragino Sans GB',
        'Microsoft Yahei',
        sans-serif;
    --oa-price-integer-font-family: avenir-heavy, pingfang sc, helvetica neue, arial, sans-serif;

    // Animation
    --oa-animation-duration-base: .3s;
    --oa-animation-duration-fast: .2s;
    --oa-animation-timing-function-enter: ease-out;
    --oa-animation-timing-function-leave: ease-in;

    // Border
    --oa-border-color: var(--oa-gray-1);
    --oa-border-width-base: 1px;
    --oa-border-radius-2: 2px;
    --oa-border-radius-3: 3px;
    --oa-border-radius-4: 4px;
    --oa-border-radius-6: 6px;
    --oa-border-radius-8: 8px;
    --oa-border-radius-max: 999px;
    --oa-border-radius-small: var(--oa-border-radius-4);
    --oa-border-radius-default: var(--oa-border-radius-6);
    --oa-border-radius-medium: var(--oa-border-radius-8);
    --oa-border: 1px solid var(--oa-border-color);

    //  pharmacy-style
    --oa-pharmacy-border-color: var(--oa-gray-9);
    --oa-pharmacy-border: 1px solid var(--oa-pharmacy-border-color);
    --oa-pharmacy-header-height: 56px;
}

:root[data-theme="blue"] {
    --app-bg-color: linear-gradient(252deg, #5078c8, #578ecf 20%, #578ecf 80%, #5078c8);
    --oa-border-radius-default: var(--oa-border-radius-8);
}
