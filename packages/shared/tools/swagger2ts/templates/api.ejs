import BaseAPI from "./<%= requestFileName %>";

/**
* <%=description%>
*/
export class <%= className %> extends BaseAPI{
<% for (var i = 0; i < methods.length; i++) {%>
    <% method=methods[i] %>
    /**
    * <%=method.summary%><% method.parameters.forEach(parameter => {%>
    * @param {<%-parameter.paramType %>} <%=parameter.camelCaseName %> - <%=parameter.description _%>
    <%})%>
    */
    static <%=method.methodName%> (
    <% method.parameters.forEach(parameter => {_%>
    <%=parameter.camelCaseName %><%=parameter.required %>:<%-parameter.paramType %>,
    <%})_%>
    ) {
    <%if (method.queryParams.length > 0) {%>
    return this.<%=method.requestMethodName%><<%-method.responses.paramType%>>(`<%=method.path%>`, {
    params: {
    <% method.queryParams.forEach(parameter => { _%>
    <%=parameter.camelCaseName %>,
    <%})_%>
    }
    });
    <%} else if (method.bodyParams.length > 0) {%>
    <%if (method.requestMethodName === 'del') {%>
    return this.<%=method.requestMethodName%><<%-method.responses.paramType%>>(
        `<%=method.path%>`,
        {data: <%=method.bodyParams[0].camelCaseName%>}
    );
    <%} else {%>
    return this.<%=method.requestMethodName%><<%-method.responses.paramType%>>(
        `<%=method.path%>`,
        <%=method.bodyParams[0].camelCaseName%>
    );
    <%}%>
    <%} else {%>
    return this.<%=method.requestMethodName%><<%-method.responses.paramType%>>(`<%=method.path%>`);
    <%}%>
    }
<%}%>
}