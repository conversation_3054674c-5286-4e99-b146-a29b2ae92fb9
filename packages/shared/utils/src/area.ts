// 是否省
const isProvince = (code: string) => /^\d{2}0000$/.test(code);
// 是否市
const isCity = (code: string) => /^\d{4}00$/.test(code);
// 是否区
const isCounty = (code: string) => !isProvince(code) && !isCity(code);

const areaList:any = {
    province_list: {},
    city_list: {},
    county_list: {},
};

areaList.city_list['500200'] = '县';

const specialProvince: any = {
    autonomousRegions: {
        新疆: '新疆维吾尔自治区',
        西藏: '西藏自治区',
        广西: '广西壮族自治区',
        内蒙古: '内蒙古自治区',
        宁夏: '宁夏回族自治区',
    },
    municipalities: {
        北京: '北京市',
        天津: '天津市',
        上海: '上海市',
        重庆: '重庆市',
    },
    specialAdministrativeRegion: {
        香港: '香港特别行政区',
        澳门: '澳门特别行政区',
    },
};

export function convertAreaList(area: any, isShortName = false) {
    const areaList: any = {
        province_list: {},
        city_list: {},
        county_list: {},
    };

    Object.keys(area).forEach(key => {
        // @ts-ignore
        const value = area[key];
        Object.keys(value).forEach(code => {
            const name = value[code];
            if (isProvince(code)) {
                if (!isShortName) {
                    areaList.province_list[code] = name;
                } else {
                    areaList.province_list[code] = specialProvince.autonomousRegions[name]
                        || specialProvince.municipalities[name]
                        || specialProvince.specialAdministrativeRegion[name]
                        || `${name}省`;
                }
            } else if (isCity(code)) {
                areaList.city_list[code] = name;
            } else {
                areaList.county_list[code] = name;
            }
        });
    });

    areaList.city_list['500200'] = '县';
    return areaList;
}
export function convertAreaTree(area: any, isShortName = false) {
    if (!area) return [];
    const areaMap = new Map();
    const provinces = area[100000] || {};

    Object.keys(provinces).forEach(code => {
        let name = provinces[code];
        name = !isShortName
            ? name
            : specialProvince.autonomousRegions[name]
            || specialProvince.municipalities[name]
            || specialProvince.specialAdministrativeRegion[name]
            || `${name}省`;
        const province: any = {
            code,
            name,
            value: code,
            label: name,
            children: [],
        };
        const cities = area[code] || {};
        Object.keys(cities).forEach(cityCode => {
            const city: any = {
                code: cityCode,
                name: cities[cityCode],
                value: cityCode,
                label: cities[cityCode],
                children: [],
            };
            const counties = area[cityCode] || {};
            Object.keys(counties).forEach(countyCode => {
                city.children.push({
                    code: countyCode,
                    name: counties[countyCode],
                    value: countyCode,
                    label: counties[countyCode],
                });
            });
            province.children.push(city);
        });
        areaMap.set(code, province);
    });

    return Array.from(areaMap.values());
}
export default areaList;
