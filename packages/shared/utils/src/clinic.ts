export type NodeTypeClinic = { viewMode?: ClinicViewMode, clinicType: ClinicNodeType, nodeType:ClinicNodeType }
export type HisTypeClinic = { hisType: HisType }
// 门店类型
export enum ClinicNodeType {
    SINGLE = 0, // 单店
    CHAIN_ADMIN = 1, // 连锁总店
    CHAIN_SUB = 2, // 连锁子店
    TRIAL_TO_NORMALIZE = 3, // 试用门店转正
}
// 楚天云支付通道
export enum PayAccount {
    ABC = 0,
    CHU_TIAN_YUN = 10,
}
// 视图类型
export enum ClinicViewMode {
    NORMAL = 0, // 正常视图
    SINGLE = 1, // 单店视图
}

// 诊所类型
export enum HisType {
    NORMAL = 0, // 普通诊所
    DENTISTRY = 1, // 牙科诊所
    OPHTHALMOLOGY = 2, // 眼视光诊所
    HOSPITAL= 100, // 智慧医院
    PHARMACY= 10, // 药店
}

export const hisTypeOptions = [
    { label: '诊所管家', value: HisType.NORMAL },
    { label: '口腔管家', value: HisType.DENTISTRY },
    { label: '眼视光管家', value: HisType.OPHTHALMOLOGY },
    { label: '智慧医院', value: HisType.HOSPITAL },
    { label: '药店管家', value: HisType.PHARMACY },
];

/**
 * @desc 单店 需要过滤出单店视角的总部
 * <AUTHOR>
 * @date 2021-09-13 11:33:07
 */
export function isSingleClinic(clinic: NodeTypeClinic) {
    const {
        nodeType,
        clinicType,
        viewMode,
    } = clinic || {};
    return nodeType === ClinicNodeType.SINGLE
        || clinicType === ClinicNodeType.SINGLE
        || (viewMode === ClinicViewMode.SINGLE && isChainSubClinic({
            nodeType,
            clinicType,
        }));
}

/**
 * 连锁总部
 * @param clinic
 */
export function isChainAdminClinic(clinic: NodeTypeClinic) {
    const {
        nodeType,
        clinicType,
        viewMode,
    } = clinic || {};
    return !viewMode
        && (nodeType === ClinicNodeType.CHAIN_ADMIN
            || clinicType === ClinicNodeType.CHAIN_ADMIN);
}

/**
 * 连锁子店
 * @param clinic
 */
export function isChainSubClinic(clinic: NodeTypeClinic) {
    const {
        nodeType,
        clinicType,
        viewMode,
    } = clinic || {};
    return !viewMode
        && (nodeType === ClinicNodeType.CHAIN_SUB
            || clinicType === ClinicNodeType.CHAIN_SUB);
}

/**
 * 连锁类型
 * @param clinic
 */
export function isChainClinic(clinic: NodeTypeClinic) {
    return isChainAdminClinic(clinic) || isChainSubClinic(clinic);
}

/**
 * 总部管理类型
 * @param clinic
 */
export function isAdminClinic(clinic: NodeTypeClinic) {
    return isChainAdminClinic(clinic) || isSingleClinic(clinic);
}

/**
 * 获取连锁、子店、单店文案
 * @param clinic
 */
export function getClinicTypeDisplayName(clinic: NodeTypeClinic) {
    if (isChainAdminClinic(clinic)) {
        return '连锁总部';
    }
    if (isChainSubClinic(clinic)) {
        return '连锁子店';
    }
    if (isSingleClinic(clinic)) {
        return '单店';
    }
}

/**
 * 获取产品名：口腔管家、诊所管家、眼视光管家
 * @param clinic
 */
export function getHisTypeDisplayName(clinic: HisTypeClinic) {
    if (clinic.hisType === HisType.NORMAL) {
        return '诊所管家';
    }
    if (clinic.hisType === HisType.DENTISTRY) {
        return '口腔管家';
    }
    if (clinic.hisType === HisType.OPHTHALMOLOGY) {
        return '眼视光管家';
    }
    if (clinic.hisType === HisType.HOSPITAL) {
        return '智慧医院';
    }
    if (clinic.hisType === HisType.PHARMACY) {
        return '药店管家';
    }
}

// 版本
export const editionAllOptions = [
    // 诊所管家
    { value: '10', label: '基础版', hisType: HisType.NORMAL },
    { value: '20', label: '专业版', hisType: HisType.NORMAL },
    { value: '30', label: '旗舰版', hisType: HisType.NORMAL },
    { value: '40', label: '大客户', hisType: HisType.NORMAL },
    // 口腔管家
    { value: '10', label: '基础版', hisType: HisType.DENTISTRY },
    { value: '20', label: '专业版', hisType: HisType.DENTISTRY },
    { value: '30', label: '旗舰版', hisType: HisType.DENTISTRY },
    { value: '40', label: '大客户', hisType: HisType.DENTISTRY },
    // 眼视光管家
    { value: '210', label: '基础版', disabled: true, hisType: HisType.OPHTHALMOLOGY },
    { value: '220', label: '标准版', hisType: HisType.OPHTHALMOLOGY },
    { value: '230', label: '专业版', hisType: HisType.OPHTHALMOLOGY },
    { value: '240', label: '旗舰版', hisType: HisType.OPHTHALMOLOGY },
    // 药店管家
    { value: '2010', label: '基础版', hisType: HisType.PHARMACY },
    { value: '2020', label: '专业版', hisType: HisType.PHARMACY },
];
