/**
 * 动态加载 script，由于本工程不兼容 IE，所以该实现没有 IE 实现
 * @param url
 * @param id
 */
export async function loadScript(url: string, id?: string) {
    return new Promise((resolve, reject) => {
        const head = document.getElementsByTagName('head')[0];
        const script = document.createElement('script');
        script.type = 'text/javascript';
        if (id) {
            script.id = id;
        }
        script.onload = function () {
            resolve(true);
        };
        script.onerror = function () {
            reject();
        };
        script.src = url;
        head.appendChild(script);
    });
}

/**
 * 根据 id 移除 script
 * @param id
 */
export async function unloadScript(id: string) {
    const script = document.querySelector(`#${id}`);
    script?.parentNode?.removeChild(script);
}

export function copy(value: string, cb?: Function) {
    // 动态创建 textarea 标签
    const textarea: any = document.createElement('textarea');
    // 将该 textarea 设为 readonly 防止 iOS 下自动唤起键盘，同时将 textarea 移出可视区域
    textarea.readOnly = 'readonly';
    textarea.style.position = 'absolute';
    textarea.style.left = '-9999px';
    // 将要 copy 的值赋给 textarea 标签的 value 属性
    // 网上有些例子是赋值给innerText,这样也会赋值成功，但是识别不了\r\n的换行符，赋值给value属性就可以
    textarea.value = value;
    // 将 textarea 插入到 body 中
    document.body.appendChild(textarea);
    // 选中值并复制
    textarea.select();
    textarea.setSelectionRange(0, textarea.value.length);
    document.execCommand('Copy');
    document.body.removeChild(textarea);
    if (cb && Object.prototype.toString.call(cb) === '[object Function]') {
        cb();
    }
}
