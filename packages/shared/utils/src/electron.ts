export function isElectron() {
    return !!window.electronFlag;
}

export function openExternal(url: string) {
    if (isElectron()) {
        window.electron.remote.shell.openExternal(url);
    } else {
        window.open(url);
    }
}

export function openVoiceRecorder() {
    if (isElectron()) {
        try {
            // 参考voice-recorder-web项目的消息传递方式
            const electronAbcApp = window.electron?.remote?.app;
            if (electronAbcApp && electronAbcApp.openVoiceRecorder) {
                console.log('调用客户端语音采集功能');
                electronAbcApp.openVoiceRecorder();
            } else {
                console.warn('语音采集功能不可用，electronAbcApp未初始化或方法不存在');
            }
        } catch (error) {
            console.error('调用语音采集功能失败:', error);
        }
    } else {
        console.warn('语音采集功能仅在客户端环境下可用');
    }
}
