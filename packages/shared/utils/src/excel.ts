import { WorkBook, write, WritingOptions, utils, read } from 'xlsx';

/**
 * @desc 触发一个下载
 * <AUTHOR>
 * @date 2022-06-27 16:05:42
 * @params
 * @return
 */
export function openDownload(blob: Blob, fileName: string) {
    // 创建blob地址
    const blobUrl = URL.createObjectURL(blob);
    const aLink = document.createElement('a');
    aLink.href = blobUrl;
    aLink.download = fileName;
    let event = null;
    event = new MouseEvent('click');
    aLink.dispatchEvent(event);
}

/**
 * @desc workbook 转换成 blob对象
 * <AUTHOR>
 * @date 2022-06-27 16:06:46
 * @params
 * @return
 */
export function workbook2blob(workbook: WorkBook) {
    const wkOptions: WritingOptions = {
        bookType: 'xlsx',
        bookSST: false,
        type: 'binary',
    };

    const wb = write(workbook, wkOptions);

    // 字符串转换成ArrayBuffer
    function s2ab(s: string) {
        const buf = new ArrayBuffer(s.length);
        const view = new Uint8Array(buf);
        for (let i = 0; i != s.length; i++) {
            view[i] = s.charCodeAt(i) & 0xff;
        }
        return buf;
    }
    const blob = new Blob([s2ab(wb)], {
        type: 'application/octet-stream',
    });
    return blob;
}

/**
 * @desc 导出excel文件
 * <AUTHOR>
 * @date 2022-06-27 18:03:09
 */
export function exportExcelFile(sheetData: any[], fileName: string) {
    const sheet = utils.json_to_sheet(sheetData);
    const wb = utils.book_new();
    utils.book_append_sheet(wb, sheet, fileName);
    const workbookBlob = workbook2blob(wb);
    openDownload(workbookBlob, `${fileName}.xlsx`);
}

/**
 * @desc 导入excel文件
 * <AUTHOR>
 * @date 2022-06-27 18:07:24
 * @params
 * @return
 */
export function importExcelFile(file: File, callback: Function) {
    const reader = new FileReader();
    reader.onload = function (e: ProgressEvent<FileReader>) {
        const data = e?.target?.result;
        const workbook = read(data, { type: 'binary' });
        if (callback) {
            callback(workbook);
        }
    };
    reader.readAsBinaryString(file);
}
