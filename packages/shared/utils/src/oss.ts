// import { CorpAPI } from '@/api/corp-api';
// import {LoginShareAPI} from "@/api/login-share-api";

// 随机生成文件名
export const randomString = (len: number) => {
    len = len || 32;
    let chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz123456789';
    let maxPos = chars.length;
    let pwd = '';
    for (let i = 0; i < len; i++) {
        pwd += chars.charAt(Math.floor(Math.random() * maxPos));
    }
    return pwd;
};

// async function _getOSSToken() {
//     const isSharePage = location.host.includes('share');
//     const tokenRes = isSharePage ? await LoginShareAPI.getOSSTokenUsingGET_1() : await CorpAPI.getOSSTokenUsingGET();
//     return tokenRes;
// }
// export default class OSSUtil {
//     /**
//      * @desc
//      * <AUTHOR>
//      * @date 2020/12/03 18:06:50
//      * @params options: {bucket, region, rootDir, fileName}
//      * @return
//      */
//     static async upload(options: any, file: File, progressCallback = null) {
//         options.accessToken = await _getOSSToken();
//         const uploadRes = await this._upload(options, file, progressCallback);
//         // @ts-ignore
//         const { status, requestUrls } = uploadRes.res;
//         if (status === 200) {
//             return {
//                 percentage: 100,
//                 url: requestUrls[0],
//             };
//         }
//         throw new Error('上传失败');
//     }

//     // @ts-ignore
//     static async _upload({ accessToken, region, bucket, rootDir, fileName }, file, progressCallback = null) {
//         fileName = fileName || randomString(32) + '_' + new Date().getTime() + '.' + file.name.split('.').pop();

//         const data = await new OSS.Wrapper({
//             region,
//             bucket,
//             accessKeyId: accessToken.AccessKeyId,
//             accessKeySecret: accessToken.AccessKeySecret,
//             stsToken: accessToken.SecurityToken,
//             secure: true,
//         }).multipartUpload(`${rootDir}/${fileName}`, file, {
//             // fix: 下载文件时名字不能显示正确的文件名(OSS上跨域后 a标签的download属性不生效了)
//             headers: {
//                 'Content-Disposition': `filename=${encodeURIComponent(file.name)}`,
//             },
//             async progress(percentage: any) {
//                 // @ts-ignore
//                 progressCallback && progressCallback(percentage);
//             },
//         });

//         // @ts-ignore
//         const { requestUrls } = data.res;

//         /**
//          * @desc 会返回uploadId 以query形式拼接在url上，落库需要splice掉
//          * <AUTHOR> Yang
//          * @date 2020-09-21 17:26:52
//          */
//         if (requestUrls) {
//             // @ts-ignore
//             data.res.requestUrls = requestUrls.map((url: string) => url.split('?')[0]);
//         }
//         return data;
//     }

//     /**
//      * 删除文件
//      * @param options
//      * @param options.bucket bucket
//      * @param options.region region
//      * @param options.rootDir 文件路径
//      * @param options.fileName 文件名
//      */
//     static async delete(options: any) {
//         options.accessToken = await _getOSSToken();
//         const data = await this._delete(options);
//         const { res = {} } = data;
//         const { status } = res;
//         // status 为 204 表示删除成功
//         return status === 204;
//     }

//     // @ts-ignore
//     static async _delete({ accessToken, region, bucket, rootDir, fileName }) {
//         return await new OSS.Wrapper({
//             region,
//             bucket,
//             accessKeyId: accessToken.AccessKeyId,
//             accessKeySecret: accessToken.AccessKeySecret,
//             stsToken: accessToken.SecurityToken,
//             secure: true,
//         }).delete(`${rootDir}/${fileName}`);
//     }
// }
