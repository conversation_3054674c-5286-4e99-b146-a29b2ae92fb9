import axios from 'axios';
import { AbcResponse } from '@/common/AbcResponse';
import { ElMessage } from 'element-plus';

const service = axios.create({
    baseURL: import.meta.env.VITE_APP_BASE_API,
    timeout: 60000,
});

service.interceptors.request.use((config) => {
    config.url = encodeURI(<string>config.url);
    if (config.url.indexOf('?') !== -1) {
        config.url += '&' + new Date().getTime();
    } else {
        config.url += '?' + new Date().getTime();
    }

    return config;
});

service.interceptors.response.use((response) => {
    const { data } = response;
    // 自动解包
    return data.data || data;
}, (error) => {
    if (error.code === 'ECONNABORTED' && error.message.indexOf('timeout') !== -1) {
        console.warn('请求超时');
        return Promise.reject(AbcResponse.error('请求超时'));
    }

    if (error.status === 504 && error.error.indexOf('Gateway Timeout') !== -1) {
        return Promise.reject(AbcResponse.error('请求超时'));
    }

    if (error.status === 503) {
        return Promise.reject(AbcResponse.error('服务异常'));
    }

    if (error.message === 'Network Error') {
        ElMessage({
            message: '你的网络有问题，请检查网络设置！',
            type: 'error',
            duration: 3 * 1000,
        });
        return Promise.reject(AbcResponse.error('没有网络连接'));
    }

    const {
        data,
    } = error.response;

    const { code } = data.error || data.data || data;

    if (code === 401 || code === 402) {
        // 退出登录
        window.location.href = '/login';
        return;
    }

    return Promise.reject(data.error || data.data || data || {});
});

function useGet() {
    return service.get;
}

function usePost() {
    return service.post;
}

function usePut() {
    return service.put;
}

function useDelete() {
    return service.delete;
}

/**
 * 统一包装Response的fetch
 * <AUTHOR>
 * @date 2021-06-18
 * @param {any} ...args
 * @returns {Promise<Response>}
 */
async function fetchPack(options:object) {
    let response: AbcResponse | null = null;
    try {
        const data = await service(options);
        if (data?.error) {
            throw new Error(data.error.message);
        }
        response = AbcResponse.success(data);
    } catch (error: any) {
        console.log('fetchPack error', error);
        response = AbcResponse.error(error?.message || '接口异常', error);
    }
    return response;
}

export {
    service,
    useGet,
    usePost,
    usePut,
    useDelete,
    fetchPack,
};
