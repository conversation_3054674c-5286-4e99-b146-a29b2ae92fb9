import { ref, computed, onMounted, onUnmounted, Ref } from 'vue';

interface ResponsiveColumnsConfig {
    breakpoints?: { [key: number]: number };
    elementRef?: Ref<HTMLElement | null>;
}

export const useResponsiveColumns = (config: ResponsiveColumnsConfig = {}) => {
    const defaultBreakpoints = {
        1200: 4,
        960: 3,
        680: 2,
    };

    const breakpoints = config.breakpoints || defaultBreakpoints;
    const elementRef = config.elementRef || ref<HTMLElement | null>(null);
    const screenWidth = ref(window.innerWidth);

    const updateScreenWidth = () => {
        if (elementRef.value) {
            screenWidth.value = elementRef.value.clientWidth;
        } else {
            screenWidth.value = window.innerWidth;
        }
    };

    onMounted(() => {
        window.addEventListener('resize', updateScreenWidth);
        if (elementRef.value) {
            const resizeObserver = new ResizeObserver(updateScreenWidth);
            resizeObserver.observe(elementRef.value);
            onUnmounted(() => {
                resizeObserver.disconnect();
            });
        }
    });

    onUnmounted(() => {
        window.removeEventListener('resize', updateScreenWidth);
    });

    const columnCount = computed(() => {
        const sortedBreakpoints = Object.keys(breakpoints).map(Number).sort((a, b) => b - a);
        for (const breakpoint of sortedBreakpoints) {
            if (screenWidth.value > breakpoint) {
                return breakpoints[breakpoint];
            }
        }
        return 1;
    });

    return {
        columnCount,
    };
};
