import { TOKEN_KEY } from '@/common/constants';
import axios from 'axios';
import { AbcResponse } from '@/common/AbcResponse';
import { ElMessage } from 'element-plus';

const service = axios.create({
    baseURL: import.meta.env.VITE_APP_SHARE_DOMAIN,
    timeout: 60000,
});

service.interceptors.request.use((config) => {
    config.url = encodeURI(<string>config.url);
    if (config.url.indexOf('?') !== -1) {
        config.url += '&' + new Date().getTime();
    } else {
        config.url += '?' + new Date().getTime();
    }

    // @ts-ignore
    config.headers['share-user-token'] = localStorage.getItem(TOKEN_KEY);

    return config;
});

service.interceptors.response.use((response) => {
    const { data } = response;
    // 自动解包
    return data.data || data;
}, (error) => {
    if (error.code === 'ECONNABORTED' && error.message.indexOf('timeout') !== -1) {
        console.warn('请求超时');
        return Promise.reject(AbcResponse.error('请求超时'));
    }

    if (error.status === 504 && error.error.indexOf('Gateway Timeout') !== -1) {
        return Promise.reject(AbcResponse.error('请求超时'));
    }

    if (error.status === 503) {
        return Promise.reject(AbcResponse.error('服务异常'));
    }

    if (error.message === 'Network Error') {
        ElMessage({
            message: '你的网络有问题，请检查网络设置！',
            type: 'error',
            duration: 3 * 1000,
        });
        return Promise.reject(AbcResponse.error('没有网络连接'));
    }

    const {
        config,
        data,
    } = error.response;

    const { code, message } = data.error;

    if (code === 401 || code === 402) {
        // 退出登录
        window.location.href = '/login';
        return;
    }

    return Promise.reject(data.error);
});

function useGet() {
    return service.get;
}

function usePost() {
    return service.post;
}

function usePut() {
    return service.put;
}

function useDelete() {
    return service.delete;
}

export {
    service,
    useGet,
    usePost,
    usePut,
    useDelete,
};
