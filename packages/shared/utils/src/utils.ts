/*
 * <AUTHOR>
 * @DateTime 2023-11-14 17:17:50
 */
import dayjs from 'dayjs';
import qs from 'querystring';
import { openExternal } from './electron';

export function noop() {

}

export function isDef(val: any) {
    return typeof val !== 'undefined';
}

export function isFn(val: any) {
    return typeof val === 'function';
}

export function isArray(val: any) {
    return Array.isArray(val);
}

export function isString(val: any) {
    return typeof val === 'string';
}

export function isNumber(val: any) {
    return typeof val === 'number';
}

export function isPlainObject(val: any) {
    return Object.prototype.toString.call(val) === '[object Object]';
}

export function isObject(val: any) {
    return val !== null && typeof val === 'object';
}

export function isNone(val: any) {
    return val === null || val === undefined || val === '';
}

export function isEmptyValue(value: unknown) {
    if (Array.isArray(value)) {
        return !value.length;
    }
    if (isObject(value) && JSON.stringify(value) === '{}') {
        return true;
    }
    if (value === 0) {
        return false;
    }
    return !value;
}

export async function sleep(ms: number) {
    return new Promise(resolve => {
        setTimeout(() => {
            resolve(true);
        }, ms);
    });
}

export function clone(obj: any): any {
    if (obj == null || typeof obj != 'object') return obj;
    if (obj instanceof Array) {
        let copy = [];
        for (let i = 0, len = obj.length; i < len; ++i) {
            copy[i] = clone(obj[i]);
        }
        return copy;
    }
    if (obj instanceof Object) {
        let copy:Record<string, any> = {};
        for (let attr in obj) {
            if (obj.hasOwnProperty(attr)) copy[attr] = clone(obj[attr]);
        }
        return copy;
    }
}

export function keys<T>(o: T) {
    return Object.keys(o) as (keyof T)[];
}

export const dataURLToImage = (dataURL: string) => new Promise((resolve) => {
    const img: any = new Image();
    img.onload = () => resolve(img);
    img.src = dataURL;
});

export const canvasToFile = (canvas: HTMLCanvasElement, type: string, name: string, quality = 1): Promise<File | null> => new Promise(
    resolve => canvas.toBlob((blob: Blob | null) => {
        if (blob) {
            const file = new window.File([blob], name, { type });
            resolve(file);
        }
        resolve(null);
    }, type, quality),
);

export function flatDeepTree(source: any[], idKey = 'id', childKey = 'children') {
    const treeMap: any = new Map();
    let result: any[] = [];
    if (!source || !source.length) {
        return [];
    }
    source.forEach((item: any) => {
        treeMap.set(item[idKey], item);
        if (item[childKey] && item[childKey].length) {
            result = result.concat(flatDeepTree(item[childKey], idKey, childKey));
        }
    });
    result = result.concat(Array.from(treeMap.values()));
    return result;
}

export const dataURLtoFile = (dataUrl: string, filename?: string) => {
    let arr = dataUrl.split(',');
    let mime = arr?.[0]?.match(/:(.*?);/)?.[1];
    let bstr = atob(arr[1]), n = bstr.length;
    let u8arr = new Uint8Array(n);
    while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], filename || '', { type: mime });
};

export const encodeQueryObject = (obj: any, excludeEmpty = false) => {
    if (!isPlainObject(obj)) {
        return '';
    }
    let str = '?';
    for (const key in obj) {
        if (obj[key] || obj[key] === 0 || excludeEmpty) {
            str += ((str.length === 1 ? '' : '&') + `${key}=${obj[key] ?? ''}`);
        }
    }
    return str;
};

/**
 * 创建日期格式化函数，返回格式化后的日期字符串，格式为：YYYY-MM-DD HH:mm:ss
 * <AUTHOR>
 * @date 2023-11-22
 * @param {any} date
 * @returns {String}
 */
export const createDateTimeFormat19 = (date?: any) => {
    if (!date) {
        return '';
    }
    return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
};

/**
 * 是否空
 * <AUTHOR>
 * @date 2022-05-11
 * @param {any} value
 * @returns {Boolean}
 */
export const isEmpty = (value: any) => value === undefined || value === null || value === '';

/**
 * @description: 判断两个数组是否有相同的值
 * @date: 2024-06-13 14:49:13
 * @author: Horace
 * @param {any[]} array1
 * @param {any[]} array2
 * @return
*/
export function hasCommonValue(array1: any[], array2: any[]) {
    if (array1.length === 0 || array2.length === 0) {
        return false;
    }

    if (typeof array1[0] === 'object' && typeof array2[0] === 'object') {
        array1 = array1.map(item => JSON.stringify(item));
        array2 = array2.map(item => JSON.stringify(item));
    }

    return array1.some(item => array2.includes(item));
}

/**
 * 快捷登录
 */
export const quickLoginAbc = (params: any) => {
    const url = (() => {
        // openId=${mobileOrOpenId}&clinicId=${clinicId}
        let baseUrl = 'https://abcyun.cn/login/password';
        const querystring = qs.stringify(params);
        if (querystring) {
            baseUrl += `?${querystring}`;
        }
        return baseUrl;
    })();
    openExternal(url);
};
