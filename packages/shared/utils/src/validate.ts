import dayjs from 'dayjs';

/**
 * 校验 11 位手机号
 * @param value
 */
export const validateMobilePhone = (value: string) => {
    if (!value) {
        return false;
    } if (value.length !== 11) {
        return false;
    } if (!/^1[3|4|5|6|7|8|9]\d{9}$/.test(value)) {
        return false;
    }
    return true;
};

/**
 * @description: 校验结束时间不能小于开始时间
 * @date: 2024-01-24 11:19:50
 * @author: Horace
 * @param null:
 * @return
*/
export const validateEndDate = (beginDate: string, endDate: string) => {
    if (beginDate && endDate) {
        if (dayjs(beginDate).isAfter(dayjs(endDate))) {
            return false;
        }
    }
    return true;
};
