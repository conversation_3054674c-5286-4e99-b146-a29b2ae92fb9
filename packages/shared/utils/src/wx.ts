import { QiyeWeixinAPI } from '@/api/qiye-weixin-api';
import { AbcResponse } from '@/common/AbcResponse';
import { loadScript, unloadScript } from '@/utils/dom';
import { isComWx } from '@/utils/ua';
import { isLocal } from '@/utils/env';

const APPID = import.meta.env.VITE_APP_WW_APP_ID;
const AGENTID = import.meta.env.VITE_APP_WW_AGENT_ID;
export async function loadWWLoginScript() {
    return loadScript('//wwcdn.weixin.qq.com/node/wework/wwopen/js/wwLogin-1.2.4.js', 'wwlogin');
}

export function unloadWWLoginScript() {
    unloadScript('wwlogin');
}

/**
 * 挂载授权二维码
 * @param id 挂载点
 */
export function mountAuthQrCode(id: string, from?: string, state?: string) {
    const redirectUrl = encodeURIComponent(`${import.meta.env.VITE_APP_WW_DOMAIN}/wework-callback?from=${from || '/'}`);
    // eslint-disable-next-line no-undef
    if (!isLocal) {
        window.location.replace(
            `https://login.work.weixin.qq.com/wwlogin/sso/login?login_type=CorpApp&appid=${APPID}&agentid=${AGENTID}&redirect_uri=${redirectUrl}&state=STATE`,
        );
    } else {
        new WwLogin({
            id,
            appid: APPID,
            agentid: import.meta.env.VITE_APP_WW_AGENT_ID,
            redirect_uri: redirectUrl,
            state,
            href: '',
            lang: 'zh',
        });
    }
}

export function navigateToAuth(from?: string) {
    const redirectUrl = encodeURIComponent(`${import.meta.env.VITE_APP_WW_DOMAIN}/wework-callback?from=${from}`);
    // eslint-disable-next-line max-len
    window.location.replace(`https://open.weixin.qq.com/connect/oauth2/authorize?appid=ww1c19d79cfd0283ac&redirect_uri=${redirectUrl}&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect`);
}

export async function jsConfig(jsApiList: Array<string>) {
    try {
        const {
            noncestr,
            signature,
            timestamp,
        } = await QiyeWeixinAPI.jsApiSignUsingPOST({
            url: window.location.href,
        });
        return new Promise((resolve) => {
            wx.config({
                beta: true,
                debug: false,
                appId: APPID,
                timestamp,
                nonceStr: noncestr,
                signature,
                jsApiList,
            });
            wx.ready(() => {
                resolve(AbcResponse.success());
            });
            wx.error((res: any) => {
                console.error('jsConfig, e2', res);
                resolve(AbcResponse.error('wx jssdk签名失败', res));
            });
        });
    } catch (e) {
        console.error('jsConfig, e1', e);
        return AbcResponse.error('请求 jssdk 签名参数失败', e);
    }
}

export async function agentJsConfig(jsApiList: Array<string>): Promise<AbcResponse> {
    try {
        const {
            noncestr,
            signature,
            timestamp,
        } = await QiyeWeixinAPI.agentJsApiSignUsingPOST({
            url: window.location.href,
        });
        return new Promise((resolve) => {
            wx.agentConfig({
                beta: true,
                debug: false,
                corpid: APPID,
                agentid: import.meta.env.VITE_APP_WW_AGENT_ID,
                timestamp,
                nonceStr: noncestr,
                signature,
                jsApiList,
                success: () => {
                    resolve(AbcResponse.success());
                },
                fail: (res: any) => {
                    resolve(AbcResponse.error('agentJsConfig签名失败.fail', res));
                },
            });
        });
    } catch (e) {
        console.error('jsConfig, e1', e);
        return AbcResponse.error('agentJsConfig签名失败.catch', e);
    }
}

export async function wechatPay(data: any) {
    return new Promise((resolve, reject) => {
        // @ts-ignore
        if (!window.WeixinJSBridge) {
            // eslint-disable-next-line prefer-promise-reject-errors
            reject({
                errMsg: 'WeixinJSBridge 不存在',
            });
        }

        // @ts-ignore
        window.WeixinJSBridge.invoke('getBrandWCPayRequest', data, (res: any) => {
            if (res.err_msg == 'get_brand_wcpay_request:ok') {
                resolve(res);
            } else if (res.err_msg == 'get_brand_wcpay_request:cancel') {
                // eslint-disable-next-line prefer-promise-reject-errors
                reject({
                    errMsg: 'requestPayment:fail cancel',
                });
            } else if (res.err_msg == 'get_brand_wcpay_request:fail') {
                // eslint-disable-next-line prefer-promise-reject-errors
                reject({
                    ...res,
                    errMsg: 'get_brand_wcpay_request:fail',
                });
            }
        });
    });
}

export async function oAuth(fromPath: string) {
    const appid = import.meta.env.VITE_APP_WX_APP_ID;
    const redirectUri = `${window.location.origin}/share/auth-callback?from=${encodeURIComponent(fromPath)}`;
    // 微信重定向
    // eslint-disable-next-line max-len
    const __REDIRECT_URL__USER_INFO__ = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appid}&redirect_uri=${encodeURIComponent(redirectUri)}&response_type=code&scope=snsapi_base#wechat_redirect`;
    window.location.href = __REDIRECT_URL__USER_INFO__;
}

/**
 * 获取企业微信的进入入口
 * https://developer.work.weixin.qq.com/document/path/94764
 */
export async function getQiyeWxEntry(): Promise<AbcResponse> {
    const { status, message, data } = await agentJsConfig([
        'shareToExternalChat',
        'getCurExternalContact',
        'shareToExternalContact',
        'sendChatMessage',
        'getCurExternalChat',
        'getContext']);
    if (status === false) {
        console.error('agentConfig 失败', message, data);
    }
    return new Promise((resolve) => {
        wx.invoke('getContext', {
        }, (res: any) => {
            if (res.err_msg == 'getContext:ok') {
                resolve(AbcResponse.success({
                    entry: res.entry, // 返回进入H5页面的入口类型，目前有normal、contact_profile、single_chat_tools、group_chat_tools、chat_attachment、single_kf_tools
                    shareTicket: res.shareTicket, // 可用于调用getShareInfo接口
                }));
            } else {
                resolve(AbcResponse.error(res));
            }
        });
    });
}

/**
 * 是否从客服工具栏进入页面
 */
export async function isKfToolsEntry() {
    if (!isComWx) {
        return false;
    }
    const { status, data } = await getQiyeWxEntry();
    if (!status) {
        return false;
    }
    return data.entry === 'single_kf_tools' || data.entry === 'single_chat_tools';
}

/**
 * 获取当前外部联系人userid
 */
export async function getCurExternalContact(): Promise<AbcResponse> {
    const { status, message, data } = await agentJsConfig([
        'getCurExternalContact',
        'getContext',
        'sendChatMessage',
    ]);
    console.log('agentJsConfig', message, data);
    if (status === false) {
        console.error('agentConfig 失败', message, data);
    }
    return new Promise((resolve) => {
        wx.invoke('getCurExternalContact', {
        }, (res: any) => {
            console.log('getCurExternalContact', res);
            if (res.err_msg == 'getCurExternalContact:ok') {
                resolve(AbcResponse.success({
                    userId: res.userId, // 返回当前外部联系人userId
                }));
            } else {
                // 错误处理
                resolve(AbcResponse.error(res));
            }
        });
    });
}

/**
 * @description: 打开默认浏览器
 * @date: 2024-06-19 18:14:35
 * @author: Horace
 * @param {string} url
 * @return
*/
export async function openDefaultBrowser(url: string) {
    const { status, message, data } = await agentJsConfig([
        'openDefaultBrowser',
    ]);
    if (status === false) {
        console.error('agentConfig 失败', message, data);
    }
    return new Promise((resolve) => {
        wx.invoke('openDefaultBrowser', {
            url,
        }, (res: any) => {
            if (res.err_msg == 'openDefaultBrowser:ok') {
                resolve(AbcResponse.success());
            } else {
                resolve(AbcResponse.error(res));
            }
        });
    });
}
