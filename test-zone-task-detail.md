# ZoneTaskDetail 字段增强测试

## 修改内容总结

### 1. 增加的字段
- **name**: 步骤名称 - 用户可以为每个灰度步骤设置一个有意义的名称
- **started**: 开始时间 - 用户可以预设每个步骤的开始执行时间

### 2. 界面修改
- 在 `CreateTaskDialog.vue` 中增加了两个新的输入字段
- 重新设计了布局，使用更合理的列宽分配
- 添加了表头说明，让用户更清楚各字段的含义
- 使用日期时间选择器来输入开始时间

### 3. 数据结构修改
- 更新了 `DetailItem` 接口，包含新字段
- 添加了相应的表单验证规则
- 更新了初始化和重置逻辑

### 4. 详情页面修改
- 在 `TaskDetailDialog.vue` 中增加了新字段的显示
- 添加了状态格式化函数
- 优化了表格列的显示

## 字段映射关系

| 用户输入字段 | ZoneTaskDetail 字段 | 说明 |
|-------------|-------------------|------|
| name | name | 步骤名称 |
| description | - | 步骤描述（前端使用） |
| percentage | regionCount | 放量比例 1-100 |
| started | started | 开始时间 |

## 提交数据结构

```typescript
interface DetailItem {
    step: string;        // 步骤序号
    name: string;        // 步骤名称 -> ZoneTaskDetail.name
    description: string; // 描述
    percentage: number;  // 放量百分比 -> ZoneTaskDetail.regionCount
    started: string;     // 开始时间 -> ZoneTaskDetail.started
}
```

## 验证规则
- name: 必填，步骤名称
- description: 必填，描述信息
- percentage: 必填，1-100之间的数字，总和必须等于100%
- started: 必填，开始时间

## 界面布局
- 步骤名称: 6列宽度
- 描述: 6列宽度  
- 放量比例: 4列宽度
- 开始时间: 5列宽度
- 操作按钮: 3列宽度

总计24列，符合Element Plus的栅格系统。
